#!/usr/bin/env bash


##########[ ACTIONS ]##########

# Select available actions:
#   enabled:  ENABLE_XXX=1
#   disabled: ENABLE_XXX=0

# Database Stage
ENABLE_DATABASE_STAGE=1
# Database Live
ENABLE_DATABASE_LIVE=1

# Fileadmin Stage
ENABLE_FILEADMIN_STAGE=1
# Fileadmin Live
ENABLE_FILEADMIN_LIVE=1



##########[ DATABASE ]##########

# Database Stage
DATABASE__STAGE__REMOTE_SSH="<EMAIL>"
DATABASE__STAGE__REMOTE_COMMAND="/kunden/579120_50968/webseiten/rhenag/stage/current/vendor/bin/typo3cms database:export -e \"[bf]e_sessions\" -e \"cache_*\" -e \"cf_*\" -e \"sys_log\" | gzip"

# Database Live
DATABASE__LIVE__REMOTE_SSH="<EMAIL>"
DATABASE__LIVE__REMOTE_COMMAND="/kunden/579120_50968/webseiten/rhenag/live/current/vendor/bin/typo3cms database:export -e \"[bf]e_sessions\" -e \"cache_*\" -e \"cf_*\" -e \"sys_log\" | gzip"



##########[ FILEADMIN ]##########

# Fileadmin Local
FILEADMIN__LOCAL__DIRECTORY="public/fileadmin" # relative to project root, no leading slash
FILEADMIN__LOCAL__TMP_DIRECTORY="/tmp/rhenag/fileadmin"

# Fileadmin Stage
FILEADMIN__STAGE__REMOTE_SSH="<EMAIL>"
FILEADMIN__STAGE__REMOTE_DIRECTORY="/kunden/579120_50968/webseiten/rhenag/stage/shared/public/fileadmin"
FILEADMIN__STAGE__EXCLUDES=""

# Fileadmin Live
FILEADMIN__LIVE__REMOTE_SSH="<EMAIL>"
FILEADMIN__LIVE__REMOTE_DIRECTORY="/kunden/579120_50968/webseiten/rhenag/live/shared/public/fileadmin"
FILEADMIN__LIVE__EXCLUDES="--exclude _processed_"



##########[ PASSWORDS ]##########

# IMPORTANT: Password files are a fallback solution if no public key can be stored on the remote server.
#            Never use password files, if the SSH server allows login with private key!
#            Create the password files in the root folder of your project to automate the SSH login.
#            Each password file only contains the required password in a single line, nothing else.

# Stage
STAGE__PW_FILE=".PASSWORD_stage"
# Live
LIVE__PW_FILE=".PASSWORD_live"



##########[ VARIABLES ]##########

BASE_DIRECTORY=$(cd $(dirname $(readlink -f "$0"))/../../../ && pwd)
ERROR=0



##########[ PREPARATION ]##########

if [ ${1} = "all" ]; then
    ddev dump db ${2}
    ddev dump fileadmin ${2}
    exit 0
elif [ ${1} = "db" ]; then
    if [ ${2} = "stage" ] && [  $ENABLE_DATABASE_STAGE = 1 ]; then
        DATABASE__REMOTE_SSH=$DATABASE__STAGE__REMOTE_SSH
        DATABASE__REMOTE_COMMAND=$DATABASE__STAGE__REMOTE_COMMAND
        PW_FILE="${BASE_DIRECTORY}/${STAGE__PW_FILE}"
    elif [ ${2} = "live" ] && [  $ENABLE_DATABASE_LIVE = 1 ]; then
        DATABASE__REMOTE_SSH=$DATABASE__LIVE__REMOTE_SSH
        DATABASE__REMOTE_COMMAND=$DATABASE__LIVE__REMOTE_COMMAND
        PW_FILE="${BASE_DIRECTORY}/${LIVE__PW_FILE}"
    else
        ERROR=1
    fi
elif [ ${1} = "fileadmin" ]; then
    FILEADMIN__DIRECTORY=$(cd ${BASE_DIRECTORY}/${FILEADMIN__LOCAL__DIRECTORY} && pwd)
    if [ ${2} = "stage" ] && [  $ENABLE_FILEADMIN_STAGE = 1 ]; then
        FILEADMIN__REMOTE_SSH=$FILEADMIN__STAGE__REMOTE_SSH
        FILEADMIN__REMOTE_DIRECTORY=$FILEADMIN__STAGE__REMOTE_DIRECTORY
        FILEADMIN__EXCLUDES=$FILEADMIN__STAGE__EXCLUDES
        PW_FILE="${BASE_DIRECTORY}/${STAGE__PW_FILE}"
    elif [ ${2} = "live" ] && [  $ENABLE_FILEADMIN_LIVE = 1 ]; then
        FILEADMIN__REMOTE_SSH=$FILEADMIN__LIVE__REMOTE_SSH
        FILEADMIN__REMOTE_DIRECTORY=$FILEADMIN__LIVE__REMOTE_DIRECTORY
        FILEADMIN__EXCLUDES=$FILEADMIN__LIVE__EXCLUDES
        PW_FILE="${BASE_DIRECTORY}/${LIVE__PW_FILE}"
    else
        ERROR=1
    fi
else
    ERROR=1
fi



##########[ EXECUTION ]##########

echo ""
if [ $ERROR = 1 ]; then
    echo -n "ddev dump: "
    echo -e "\033[31minvalid arguments"
    echo -e "\033[0m"
    echo -n "Usage:"
    echo -e "\033[35m"
    echo "    ddev dump ACTION TARGET"
    echo -e "\033[0m"
    echo -n "Available commands:"
    echo -e "\033[32m"
    if [ $ENABLE_DATABASE_STAGE = 1 ]; then
        echo "    ddev dump db stage"
    fi
    if [ $ENABLE_DATABASE_LIVE = 1 ]; then
        echo "    ddev dump db live"
    fi
    if [ $ENABLE_FILEADMIN_STAGE = 1 ]; then
        echo "    ddev dump fileadmin stage"
    fi
    if [ $ENABLE_FILEADMIN_LIVE = 1 ]; then
        echo "    ddev dump fileadmin live"
    fi
    echo -e "\033[0m"
    echo "Missing something? Enable it here:"
    echo -en "\033[36m"
    echo "    ${0}"
    echo -e "\033[0m"
elif [ ${1} = "db" ]; then
    echo "Starting database sync for \"${2}\"..."

    if [ -f "$PW_FILE" ]; then
        echo "Using SSH password from: $PW_FILE"
        sshpass -V && sshpass -f ${PW_FILE} ssh ${DATABASE__REMOTE_SSH} ${DATABASE__REMOTE_COMMAND} | gzip -dc | ddev import-db && ddev exec typo3cms database:updateschema
    else
        echo "Using SSH private key."
        ssh ${DATABASE__REMOTE_SSH} ${DATABASE__REMOTE_COMMAND} | gzip -dc | ddev import-db && ddev exec typo3cms database:updateschema
    fi

    echo -e "\033[32m"
    echo "Database update DONE!"
    echo -e "\033[32m"
elif [ ${1} = "fileadmin" ]; then
    echo "Starting fileadmin sync for \"${2}\"..."
    echo ""
    echo "Base directory:"
    echo -en "\033[36m"
    echo "    ${BASE_DIRECTORY}"
    echo -e "\033[0m"
    echo "Remote fileadmin:"
    echo -en "\033[36m"
    echo "    ${FILEADMIN__REMOTE_SSH}:${FILEADMIN__REMOTE_DIRECTORY}"
    echo -e "\033[0m"
    echo "Local mount point:"
    echo -en "\033[36m"
    echo "    ${FILEADMIN__LOCAL__TMP_DIRECTORY}"
    echo -e "\033[0m"
    echo "Synchronizing with:"
    echo -en "\033[36m"
    echo "    ${FILEADMIN__DIRECTORY}"
    echo -e "\033[0m"

    mkdir -p ${FILEADMIN__LOCAL__TMP_DIRECTORY}

    if [ -f "$PW_FILE" ]; then
        echo "Using SSH password from: $PW_FILE"
        sshfs -o password_stdin ${FILEADMIN__REMOTE_SSH}:${FILEADMIN__REMOTE_DIRECTORY} ${FILEADMIN__LOCAL__TMP_DIRECTORY} < ${PW_FILE}
    else
        echo "Using SSH private key."
        sshfs ${FILEADMIN__REMOTE_SSH}:${FILEADMIN__REMOTE_DIRECTORY} ${FILEADMIN__LOCAL__TMP_DIRECTORY}
    fi

    echo ""
    rsync -aP ${FILEADMIN__EXCLUDES} ${FILEADMIN__LOCAL__TMP_DIRECTORY}/* ${FILEADMIN__DIRECTORY}

    echo ""
    echo "Unmount:"
    echo -en "\033[36m"
    echo "    ${FILEADMIN__LOCAL__TMP_DIRECTORY}"
    echo -e "\033[0m"
    umount ${FILEADMIN__LOCAL__TMP_DIRECTORY}

    echo -e "\033[32m"
    echo "Fileadmin sync DONE!"
    echo -e "\033[0m"
else
    echo "You should never see this!"
    echo ""
fi
