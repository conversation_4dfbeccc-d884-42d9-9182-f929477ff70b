name: CI-CD Stage

on:
  workflow_dispatch:
  push:
    branches:
      - develop
jobs:
  phplint:
    name: Test/Lint PHP
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: StephaneBour/actions-php-lint@7.4
  deploy:
    name: Deploy code to stage
    runs-on: ubuntu-latest
    needs: phplint
    steps:
      - name: Checkout
        uses: actions/checkout@v1
      - name: Setup node
        uses: actions/setup-node@v1
        with:
          node-version: 14
      # - name: Install frontend dependencies
      #   run: cd frontend/core && npm ci
     # - name: Frontend installation && build process for TYPO3
     #   run: cd frontend && npm run deploy
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 7.4
          extensions: mbstring, bcmath, gd
          tools: composer:2.5
      - name: Composer install
        run: composer install --no-dev
        env:
          COMPOSER_AUTH: '{"http-basic": {"elts.typo3.com": {"username": "vancado", "password":"${{ secrets.ELTS_TOKEN }}"}}, "github-oauth": {"github.com": "${{ secrets.VNC_GITHUB_TOKEN }}"}}'
      - name: Deploy
        uses: deployphp/action@v1
        with:
          # Private key for connecting to remote hosts. To generate private key:
          # `ssh-keygen -o -t rsa -C '<EMAIL>'`.
          # Required.
          private-key: ${{ secrets.PRIVATE_KEY }}
          # `deploy all`.
          # Required.
          dep: deploy stage --tag=${{ env.GITHUB_REF }}

          # Deployer version to download from deployer.org.
          # First, the action will check for Deployer binary at those paths:
          # - `vendor/bin/deployer.phar`
          # - `vendor/bin/dep`
          # - `deployer.phar`
          # If the binary not found, phar version will be downloaded from
          # deployer.org.
          # Optional.
          deployer-version: "7.0.1"
