# Rhenag
got new elts access token 
## Initial Setup

Follow these steps after cloning the repo into an empty folder.
All shell commands should be executed inside this folder, if not mentioned differently.

### Start ddev and install TYPO3
On first start, TYPO3 is installed automatically and the configuration wizard runs.
```bash
ddev start
```
Use the following settings if asked:
- Database connection: ```mysqli```
- Database username: ```db```
- Database password: ```db```
- Databse hostname: ```db```
- Databse port: ```3306```
- Unix socket: _empty_
- Use existing database: ```y```
- Name of database: ```db```

The following is required but _can be chosen randomly_ as it will be replaced on DB sync anyway.
- Username for admin account: ```admin```
- Password for admin account: ```admin123``` _(needs to fulfill TYPO3 minimum security requirements)_
- Name of the TYPO3 site: _empty_
- Site type: ```no```
- Web server type: ```none```

### Use environment configuration from dist
```bash
cp .env.dist .env
cp .htaccess.dist public/.htaccess
```

### Get current database and fileadmin and restart ddev
```bash
ddev dump all stage # you may also use: ddev dump all live
ddev restart
```

## Database and Fileadmin

Database and Fileadmin sync works via ddev command:

# Dump
stage|live destinations may disabled in script
# Database Stage
ddev dump db stage

# Database Live
ddev dump db live

# Fileadmin Stage
ddev dump fileadmin stage

# Fileadmin Live
ddev dump fileadmin live

# Database + Fileadmin all Stage
ddev dump all stage

# Database + Fileadmin all Live
ddev dump all live
```

### Prerequisites

The following packages are required in the host system to make the dump scripts work:
- sshpass _(optional if SSH login via password is used)_
- sshfs
- rsync

Install them all at once with this command:
```bash
# Required packages
sudo apt-get install sshpass sshfs rsync
```

### Dump configuration

The available actions and the corresponding commands can be configured in the script:
```
.ddev/commands/host/dump
```

# Frontend build process
building the FE is performed by gulp as usual, but in a slightly different way than usual:
````
 ~/rhenag/packages/rhenag/Resources/Public/Vendor/Vancado/trunk
````

# TYPO3 Update Version 10 notes:
- Set empty Backend Layout field in both rootpages to flux_grid. 
- remove baseUrl setting in rootpage template  
- after db transformation execute flux update script in extension manager