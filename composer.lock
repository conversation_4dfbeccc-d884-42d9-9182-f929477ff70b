{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "2b3498aa65dfa71a40fa3896819106f4", "packages": [{"name": "doctrine/annotations", "version": "1.14.4", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "2.13.9", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c480849ca3ad6706a39c970cdfe6888fa8a058b8", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.9"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2022-05-02T20:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/31610dbb31faa98e6b5447b62340826f54fbc4e9", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "1.4.10 || 2.0.3", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.4"}, "time": "2024-12-07T21:18:45+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-12-29T14:50:06+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.15.4", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "e50b83a2f1f296ca61394fe88fbfe3e896a84cf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/e50b83a2f1f296ca61394fe88fbfe3e896a84cf4", "reference": "e50b83a2f1f296ca61394fe88fbfe3e896a84cf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.15.4"}, "time": "2022-02-21T09:13:59+00:00"}, {"name": "fluidtypo3/flux", "version": "9.7.4", "source": {"type": "git", "url": "https://github.com/FluidTYPO3/flux.git", "reference": "76a5d5318346caffd15c2f07a746fabf52ea0bbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FluidTYPO3/flux/zipball/76a5d5318346caffd15c2f07a746fabf52ea0bbd", "reference": "76a5d5318346caffd15c2f07a746fabf52ea0bbd", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pdo": "*", "php": "^7.4.0 || ^8", "typo3/cms-backend": "^8.7 || ^9 || ^10 || ^11 || dev-master", "typo3/cms-core": "^8.7 || ^9 || ^10 || ^11 || dev-master", "typo3/cms-fluid": "^8.7 || ^9 || ^10 || ^11 || dev-master", "typo3/cms-frontend": "^8.7 || ^9 || ^10 || ^11 || dev-master", "typo3/cms-recordlist": "^8.7 || ^9 || ^10 || ^11 || dev-master"}, "replace": {"typo3-ter/flux": "self.version"}, "require-dev": {"mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpspec/prophecy": "*", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^5.7 || ^9.5", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"fluidtypo3/vhs": "ViewHelper library for Fluid templates."}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "flux"}}, "autoload": {"psr-4": {"FluidTYPO3\\Flux\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "description": "The flux package from FluidTYPO3", "homepage": "http://fluidtypo3.org", "support": {"issues": "https://github.com/FluidTYPO3/flux/issues", "source": "https://github.com/FluidTYPO3/flux/tree/9.7.4"}, "time": "2023-07-15T17:31:28+00:00"}, {"name": "fluidtypo3/vhs", "version": "7.0.7", "source": {"type": "git", "url": "https://github.com/FluidTYPO3/vhs.git", "reference": "bcebcbf8b3b80d49391b1968fc7f5766c057293e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FluidTYPO3/vhs/zipball/bcebcbf8b3b80d49391b1968fc7f5766c057293e", "reference": "bcebcbf8b3b80d49391b1968fc7f5766c057293e", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.4.0 || ^8", "typo3/cms-backend": "^10 || ^11 || ^12 || dev-main", "typo3/cms-core": "^10 || ^11 || ^12 || dev-main", "typo3/cms-extbase": "^10 || ^11 || ^12 || dev-main", "typo3/cms-fluid": "^10 || ^11 || ^12 || dev-main", "typo3/cms-frontend": "^10 || ^11 || ^12 || dev-main"}, "replace": {"typo3-ter/vhs": "self.version"}, "require-dev": {"fluidtypo3/development": "^5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpstan/phpstan": "1.12.1", "phpunit/phpunit": "^5.7 || ^9.5", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"ext-json": "Enable use of v:format.json.encode and v:format.json.decode", "ext-tidy": "Allows to make use of the tidy ViewHelper v:format.tidy", "ext-zlib": "Enable use of v:format.placeholder.lipsum with default lipsum text"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "vhs"}}, "autoload": {"psr-4": {"FluidTYPO3\\Vhs\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "This is a collection of ViewHelpers for performing rendering tasks that are not natively provided by TYPO3's Fluid templating engine.", "homepage": "https://fluidtypo3.org", "keywords": ["TYPO3 CMS", "fedext", "fluid", "templating", "utility", "viewhelper"], "support": {"chat": "https://typo3.slack.com/archives/C79562JES", "docs": "https://viewhelpers.fluidtypo3.org/fluidtypo3/vhs/", "issues": "https://github.com/FluidTYPO3/vhs/issues", "source": "https://github.com/FluidTYPO3/vhs"}, "time": "2024-11-11T13:59:48+00:00"}, {"name": "georgringer/news", "version": "10.0.3", "source": {"type": "git", "url": "https://github.com/georgringer/news.git", "reference": "5325ea6a7bf490a9deabdf1f86372ad128a5e205"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/georgringer/news/zipball/5325ea6a7bf490a9deabdf1f86372ad128a5e205", "reference": "5325ea6a7bf490a9deabdf1f86372ad128a5e205", "shasum": ""}, "require": {"typo3/cms-core": "^10.4 || ^11"}, "conflict": {"symfony/finder": "2.7.44 || 2.8.37 || 3.4.7 || 4.0.7"}, "replace": {"typo3-ter/news": "self.version"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "typo3/cms-install": "^10.4 || ^11"}, "suggest": {"georgringer/news-tagsuggest": "On the fly creation of tag records within a news record", "georgringer/numbered-pagination": "Improved pagination API", "reelworx/rx-shariff": "GDPR compliant social sharing"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"app-dir": ".Build", "web-dir": ".Build/public", "extension-key": "news"}}, "autoload": {"psr-4": {"GeorgRinger\\News\\": "Classes"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://ringer.it", "role": "Developer"}], "description": "Versatile news system based on Extbase & Fluid and using the latest technologies provided by TYPO3 CMS.", "homepage": "https://extensions.typo3.org/extension/news", "keywords": ["article", "extension", "news", "typo3"], "support": {"docs": "https://docs.typo3.org/p/georgringer/news/main/en-us/", "issues": "https://github.com/georgringer/news/issues", "source": "https://github.com/georgringer/news"}, "funding": [{"url": "https://paypal.me/GeorgRinger/10", "type": "custom"}, {"url": "https://www.amazon.de/hz/wishlist/ls/8F573K08TSDG", "type": "custom"}, {"url": "https://github.com/georgringer", "type": "github"}, {"url": "https://www.patreon.com/georgringer", "type": "patreon"}], "time": "2022-11-21T12:32:44+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:07+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T12:31:43+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:00:37+00:00"}, {"name": "helhum/config-loader", "version": "v0.12.6", "source": {"type": "git", "url": "https://github.com/helhum/config-loader.git", "reference": "4538ad189ebbb319d97697f350401150dd7a6155"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/helhum/config-loader/zipball/4538ad189ebbb319d97697f350401150dd7a6155", "reference": "4538ad189ebbb319d97697f350401150dd7a6155", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "^8.5", "symfony/yaml": "^2.8 || ^3.3 || ^4.0 || ^5.0 || ^6.0"}, "suggest": {"ext-yaml": "For improved performance when parsing yaml files you should use the PECL YAML Parser php extension", "symfony/yaml": "To be able to parse yaml files, you will need symfony/yaml"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"psr-4": {"Helhum\\ConfigLoader\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Generic config loader with context and environment support.", "support": {"issues": "https://github.com/helhum/config-loader/issues", "source": "https://github.com/helhum/config-loader/tree/v0.12.6"}, "funding": [{"url": "https://www.paypal.me/helhum/19.99", "type": "custom"}, {"url": "https://github.com/helhum", "type": "github"}], "time": "2024-12-16T10:48:26+00:00"}, {"name": "helhum/dotenv-connector", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/helhum/dotenv-connector.git", "reference": "9ce9bb6ba94e09536b7345ad22a42bdcfbdfef90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/helhum/dotenv-connector/zipball/9ce9bb6ba94e09536b7345ad22a42bdcfbdfef90", "reference": "9ce9bb6ba94e09536b7345ad22a42bdcfbdfef90", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.0", "symfony/dotenv": "^3.3.0 || ^4.0 || ^5.0"}, "conflict": {"symfony/dotenv": ">=5.1.0"}, "require-dev": {"composer/composer": "^1.0 || 2.0.*@dev", "composer/semver": "^1.0 || 2.0.*@dev", "mikey179/vfsstream": "^1.6.0", "phpunit/phpunit": "^5"}, "type": "composer-plugin", "extra": {"class": "Helhum\\DotEnvConnector\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Helhum\\DotEnvConnector\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Makes it possible to set environment variables for composer projects.", "homepage": "https://github.com/helhum/dotenv-connector", "keywords": ["12factor", "composer", "dotenv", "env"], "support": {"issues": "https://github.com/helhum/dotenv-connector/issues", "source": "https://github.com/helhum/dotenv-connector/tree/master"}, "funding": [{"url": "https://www.paypal.me/helhum/19.99", "type": "custom"}, {"url": "https://github.com/helhum", "type": "github"}], "time": "2020-06-01T16:26:28+00:00"}, {"name": "helhum/typo3-console", "version": "v6.7.7", "source": {"type": "git", "url": "https://github.com/TYPO3-Console/TYPO3-Console.git", "reference": "4b7123a8198978b46bd2370c685d44432242a2b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-Console/TYPO3-Console/zipball/4b7123a8198978b46bd2370c685d44432242a2b3", "reference": "4b7123a8198978b46bd2370c685d44432242a2b3", "shasum": ""}, "require": {"helhum/config-loader": ">=0.9 <0.13", "php": ">=7.2", "symfony/console": "^4.4 || ^5.0", "symfony/process": "^4.4 || ^5.0", "typo3/cms-backend": "^10.4.22", "typo3/cms-core": "^10.4.22", "typo3/cms-extbase": "^10.4.22", "typo3/cms-extensionmanager": "^10.4.22", "typo3/cms-fluid": "^10.4.22", "typo3/cms-frontend": "^10.4.22", "typo3/cms-install": "^10.4.22"}, "conflict": {"doctrine/dbal": "2.13.0 || 2.13.1"}, "replace": {"typo3-ter/typo3-console": "self.version"}, "require-dev": {"nimut/testing-framework": "^6.0.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpspec/prophecy": "*", "phpunit/phpunit": "^8.5.18", "symfony/expression-language": "^4.4 || ^5.0", "symfony/filesystem": "^4.4 || ^5.0", "typo3-console/create-reference-command": "@dev", "typo3-console/php-server-command": "@dev", "typo3/cms-filemetadata": "^10.4.22", "typo3/cms-reports": "^10.4.22"}, "bin": ["typo3cms"], "type": "library", "extra": {"typo3/cms": {"app-dir": ".Build", "web-dir": ".Build/public"}, "branch-alias": {"dev-latest": "6.x-dev"}}, "autoload": {"psr-4": {"Helhum\\Typo3Console\\": ["Classes/Console/", "Classes/Compatibility/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://helhum.io", "role": "Developer"}], "description": "A reliable and powerful command line interface for TYPO3 CMS", "homepage": "https://insight.helhum.io/post/104528981610/about-the-beauty-and-power-of-typo3-console", "keywords": ["cli", "command", "commandline", "console", "typo3"], "support": {"docs": "https://docs.typo3.org/p/helhum/typo3-console/main/en-us/", "issues": "https://github.com/TYPO3-Console/TYPO3-Console/issues", "source": "https://github.com/TYPO3-Console/TYPO3-Console"}, "funding": [{"url": "https://www.paypal.me/helhum/19.99", "type": "custom"}, {"url": "https://github.com/helhum", "type": "github"}], "time": "2023-01-08T21:22:53+00:00"}, {"name": "in2code/powermail", "version": "8.5.1", "source": {"type": "git", "url": "https://github.com/in2code-de/powermail.git", "reference": "3841cb350306896812bd1ba228795948f85ef438"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/in2code-de/powermail/zipball/3841cb350306896812bd1ba228795948f85ef438", "reference": "3841cb350306896812bd1ba228795948f85ef438", "shasum": ""}, "require": {"ext-curl": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-json": "*", "typo3/cms-core": "^10.4"}, "replace": {"typo3-ter/powermail": "self.version"}, "require-dev": {"behat/behat": "^3.5", "behat/mink-extension": "^2.3", "behat/mink-goutte-driver": "^1.2", "behat/mink-selenium2-driver": "^1.3", "friendsofphp/php-cs-fixer": "^2.19", "helmich/typo3-typoscript-lint": "^2.5", "mikey179/vfsstream": "^1.6", "nimut/testing-framework": "^5.0", "phpmd/phpmd": "^2.8", "phpunit/phpunit": "^6.0", "se/selenium-server-standalone": "^3", "squizlabs/php_codesniffer": "^3.5", "typo3/cms-core": "^10.4"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"web-dir": ".Build/Web", "extension-key": "powermail", "cms-package-dir": "{$vendor-dir}/typo3/cms"}}, "autoload": {"psr-4": {"In2code\\Powermail\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.in2code.de", "role": "Product and technical owner"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.in2code.de", "role": "Developer"}], "description": "Powermail is a well-known, editor-friendly, powerful and easy to use mailform extension with a lots of features", "homepage": "https://github.com/einpraegsam/powermail", "keywords": ["form", "mailform", "spamprevention", "typo3"], "support": {"issues": "https://github.com/in2code-de/powermail/issues", "source": "https://github.com/in2code-de/powermail/tree/8.5.1"}, "time": "2024-09-17T06:49:35+00:00"}, {"name": "in2code/powermail_cond", "version": "8.2.2", "source": {"type": "git", "url": "https://github.com/in2code-de/powermail_cond.git", "reference": "97e0a7d32de304aaf173f6036cf4867caee78d90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/in2code-de/powermail_cond/zipball/97e0a7d32de304aaf173f6036cf4867caee78d90", "reference": "97e0a7d32de304aaf173f6036cf4867caee78d90", "shasum": ""}, "require": {"ext-json": "*", "in2code/powermail": ">=8.0.0 < 9.0.0"}, "replace": {"typo3-ter/powermail-cond": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "helmich/typo3-typoscript-lint": "^2.5", "mikey179/vfsstream": "^1.6", "nimut/testing-framework": "^5.0", "phpmd/phpmd": "^2.8", "phpunit/phpunit": "^6.0", "squizlabs/php_codesniffer": "^3.5", "typo3/cms-core": "^10.4"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"web-dir": ".Build/public", "extension-key": "powermail_cond"}}, "autoload": {"psr-4": {"In2code\\PowermailCond\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.in2code.de", "role": "Product owner"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.in2code.de", "role": "Developer"}], "description": "Add conditions (via AJAX) to powermail forms for fields and pages", "homepage": "https://github.com/einpraegsam/powermail_cond", "keywords": ["form", "mailform", "spamprevention", "typo3"], "support": {"issues": "https://github.com/in2code-de/powermail_cond/issues", "source": "https://github.com/in2code-de/powermail_cond/tree/8.2.2"}, "time": "2021-11-22T11:20:38+00:00"}, {"name": "in2code/typoscript2ce", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/einpraegsam/typoscript2ce.git", "reference": "d531d522d04e0e349f62f7941b8b4948db323993"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/einpraegsam/typoscript2ce/zipball/d531d522d04e0e349f62f7941b8b4948db323993", "reference": "d531d522d04e0e349f62f7941b8b4948db323993", "shasum": ""}, "require": {"typo3/cms-core": ">=10.4.0 <12.0.0"}, "replace": {"typo3-ter/typoscript2ce": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "typoscript2ce"}}, "autoload": {"psr-4": {"In2code\\Typoscript2ce\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Render typoscript as content element", "homepage": "https://github.com/einpraegsam/typoscript2ce", "keywords": ["TypoScript", "content element", "typo3"], "support": {"issues": "https://github.com/einpraegsam/typoscript2ce/issues", "source": "https://github.com/einpraegsam/typoscript2ce/tree/5.0.0"}, "time": "2021-05-06T10:52:43+00:00"}, {"name": "jweiland/glossary2", "version": "4.3.3", "source": {"type": "git", "url": "https://github.com/jweiland-net/glossary2.git", "reference": "404afd16b732d8f036cf005234940877848c9143"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jweiland-net/glossary2/zipball/404afd16b732d8f036cf005234940877848c9143", "reference": "404afd16b732d8f036cf005234940877848c9143", "shasum": ""}, "require": {"typo3/cms-core": "^9.5.17 || ^10.4.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "nimut/testing-framework": "^5.0"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"app-dir": ".build", "web-dir": ".build/public", "extension-key": "glossary2"}}, "autoload": {"psr-4": {"JWeiland\\Glossary2\\": "Classes"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}], "description": "This TYPO3 extension creates a glossary with A-Z links for you", "homepage": "http://www.jweiland.net", "keywords": ["TYPO3 CMS", "glossar", "glossary2", "typo3"], "support": {"email": "<EMAIL>", "issues": "https://github.com/jweiland-net/glossary2/issues", "source": "https://github.com/jweiland-net/glossary2"}, "time": "2021-09-18T00:37:47+00:00"}, {"name": "lochmueller/autoloader", "version": "7.4.6", "source": {"type": "git", "url": "https://github.com/lochmueller/autoloader.git", "reference": "522160a36b145f88a281c2dc5266f4777d203d57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lochmueller/autoloader/zipball/522160a36b145f88a281c2dc5266f4777d203d57", "reference": "522160a36b145f88a281c2dc5266f4777d203d57", "shasum": ""}, "require": {"php": "^7.4||^8.0", "typo3/cms-core": "^10.4.6||^11.1"}, "replace": {"typo3-ter/autoloader": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^1.2", "rector/rector": "^0.12.9", "scrutinizer/ocular": "^1.3", "typo3/testing-framework": "^6.2"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"Package": {"partOfMinimalUsableSystem": true}, "web-dir": ".Build/Web", "extension-key": "autoloader"}}, "autoload": {"psr-4": {"HDNET\\Autoloader\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/lochmueller", "role": "Developer"}], "description": "Automatic components loading of ExtBase extensions to get more time for coffee in the company ;) This ext is not a PHP SPL autoloader or class loader - it is better! Loads CommandController, Xclass, Hooks, FlexForms, Slots, TypoScript, TypeConverter, BackendLayouts and take care of createing needed templates, TCA configuration or translations at the right location.", "homepage": "https://github.com/lochmueller/autoloader", "keywords": ["TYPO3 CMS", "autoloader", "component", "extbase", "magic"], "support": {"issues": "https://github.com/lochmueller/autoloader/issues", "source": "https://github.com/lochmueller/autoloader/tree/7.4.6"}, "funding": [{"url": "https://paypal.me/lochmueller", "type": "custom"}, {"url": "https://github.com/lochmueller", "type": "github"}], "time": "2023-05-11T07:59:29+00:00"}, {"name": "lochmueller/focuspoint", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/lochmueller/focuspoint.git", "reference": "967745a7e78b8c5ea53da17fda32ef2e41241000"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lochmueller/focuspoint/zipball/967745a7e78b8c5ea53da17fda32ef2e41241000", "reference": "967745a7e78b8c5ea53da17fda32ef2e41241000", "shasum": ""}, "require": {"lochmueller/autoloader": "^7.0", "php": "^7.4||^8.0", "typo3/cms-core": "^10.4||^11.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "scrutinizer/ocular": "^1.3", "typo3/testing-framework": "^6.2"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"Package": {"partOfMinimalUsableSystem": true}, "web-dir": ".Build/Web", "extension-key": "focuspoint", "cms-package-dir": "{$vendor-dir}/typo3/cms"}}, "autoload": {"psr-4": {"HDNET\\Focuspoint\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Focuspoint integrate the focal point method to crop images in the frontend of the web page. Use the jQuery-focuspoint plugin (https://github.com/jonom/jquery-focuspoint example http://jonom.github.io/jquery-focuspoint/demos/helper/index.html) to crop the images. Use the function as wizard in the file list view and directly in the content element.", "homepage": "https://github.com/lochmueller/focuspoint", "keywords": ["TYPO3 CMS", "focuspoint"], "support": {"issues": "https://github.com/lochmueller/focuspoint/issues", "source": "https://github.com/lochmueller/focuspoint/tree/5.1.0"}, "funding": [{"url": "https://paypal.me/lochmueller", "type": "custom"}, {"url": "https://github.com/lochmueller", "type": "github"}], "time": "2022-12-28T18:25:47+00:00"}, {"name": "lolli42/finediff", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/lolli42/FineDiff.git", "reference": "807deaf7aa119cf47b58e90ba7bf37c8c8264c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lolli42/FineDiff/zipball/807deaf7aa119cf47b58e90ba7bf37c8c8264c7a", "reference": "807deaf7aa119cf47b58e90ba7bf37c8c8264c7a", "shasum": ""}, "require": {"php": ">=7.2.0", "symfony/polyfill-mbstring": "^1.23"}, "replace": {"cogpowered/finediff": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "phpstan/phpstan": "^1.9.14", "phpunit/phpunit": "^8.5.33 || ^9.6.11 || ^10.3.2"}, "type": "library", "autoload": {"psr-4": {"cogpowered\\FineDiff\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "maintainer"}], "description": "PHP implementation of a Fine granularity Diff engine", "homepage": "https://github.com/lolli42/FineDiff", "keywords": ["diff", "finediff", "opcode", "string", "text"], "support": {"issues": "https://github.com/lolli42/FineDiff/issues", "source": "https://github.com/lolli42/FineDiff/tree/1.0.4"}, "time": "2024-07-09T14:52:10+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8", "reference": "e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.1"}, "time": "2024-12-07T09:39:29+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "c00d78fb6b29658347f9d37ebe104bffadf36299"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/c00d78fb6b29658347f9d37ebe104bffadf36299", "reference": "c00d78fb6b29658347f9d37ebe104bffadf36299", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.0.0"}, "time": "2024-10-13T11:29:49+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "si<PERSON><PERSON><PERSON><PERSON>/tscobj", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/simonschaufi/t3ext-tscobj.git", "reference": "b57c80be6031059d62f5d538c7b84a8b3e687dc5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simonschaufi/t3ext-tscobj/zipball/b57c80be6031059d62f5d538c7b84a8b3e687dc5", "reference": "b57c80be6031059d62f5d538c7b84a8b3e687dc5", "shasum": ""}, "require": {"php": ">= 7.2", "typo3/cms-backend": "^9.5 || ^10.4 || ^11.2", "typo3/cms-core": "^9.5 || ^10.4 || ^11.2", "typo3/cms-frontend": "^9.5 || ^10.4 || ^11.2"}, "replace": {"typo3-ter/tscobj": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "helmich/typo3-typoscript-lint": "^2.5", "squizlabs/php_codesniffer": "^3.6", "typo3/tailor": "^1.3"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"web-dir": ".Build/public", "extension-key": "tscobj", "cms-package-dir": "{$vendor-dir}/typo3/cms"}}, "autoload": {"psr-4": {"Causal\\Tscobj\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.causal.ch", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A plugin which lets you use any TypoScript object as a normal content element.", "homepage": "https://github.com/xperseguers/t3ext-tscobj", "keywords": ["TypoScript", "content object", "typo3"], "support": {"issues": "https://github.com/xperseguers/t3ext-tscobj/issues", "source": "https://github.com/simonschaufi/t3ext-tscobj/tree/v2.0.0"}, "funding": [{"url": "https://www.paypal.me/simonschaufi/10", "type": "custom"}, {"url": "https://github.com/simonschaufi", "type": "github"}], "time": "2021-07-06T23:48:53+00:00"}, {"name": "symfony/cache", "version": "v5.4.46", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "0fe08ee32cec2748fbfea10c52d3ee02049e0f6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/0fe08ee32cec2748fbfea10c52d3ee02049e0f6b", "reference": "0fe08ee32cec2748fbfea10c52d3ee02049e0f6b", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.4.46"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-04T11:43:55+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "517c3a3619dadfa6952c4651767fcadffb4df65e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/517c3a3619dadfa6952c4651767fcadffb4df65e", "reference": "517c3a3619dadfa6952c4651767fcadffb4df65e", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0|^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/config", "version": "v5.4.46", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "977c88a02d7d3f16904a81907531b19666a08e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/977c88a02d7d3f16904a81907531b19666a08e78", "reference": "977c88a02d7d3f16904a81907531b19666a08e78", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v5.4.46"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-30T07:58:02+00:00"}, {"name": "symfony/console", "version": "v5.0.11", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "95794074741645473221fb126d5cb4057ad25bf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/95794074741645473221fb126d5cb4057ad25bf1", "reference": "95794074741645473221fb126d5cb4057ad25bf1", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v5.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-07-06T13:22:03+00:00"}, {"name": "symfony/dependency-injection", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "e5ca16dee39ef7d63e552ff0bf0a2526a1142c92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/e5ca16dee39ef7d63e552ff0bf0a2526a1142c92", "reference": "e5ca16dee39ef7d63e552ff0bf0a2526a1142c92", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<5.3", "symfony/finder": "<4.4", "symfony/proxy-manager-bridge": "<4.4", "symfony/yaml": "<4.4.26"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "require-dev": {"symfony/config": "^5.3|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4.26|^5.0|^6.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-20T10:51:57+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/605389f2a7e5625f273b53960dc46aeaf9c62918", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/dotenv", "version": "v4.4.37", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "fcedd6d382b3afc3e1e786aa4e4fc4cf06f564cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/fcedd6d382b3afc3e1e786aa4e4fc4cf06f564cf", "reference": "fcedd6d382b3afc3e1e786aa4e4fc4cf06f564cf", "shasum": ""}, "require": {"php": ">=7.1.3"}, "require-dev": {"symfony/process": "^3.4.2|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v4.4.37"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:41:36+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/72982eb416f61003e9bb6e91f8b3213600dcf9e9", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f", "reference": "e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/expression-language", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "a784b66edc4c151eb05076d04707906ee2c209a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/a784b66edc4c151eb05076d04707906ee2c209a9", "reference": "a784b66edc4c151eb05076d04707906ee2c209a9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-04T14:55:40+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "57c8294ed37d4a055b77057827c67f9558c95c54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/57c8294ed37d4a055b77057827c67f9558c95c54", "reference": "57c8294ed37d4a055b77057827c67f9558c95c54", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/process": "^5.4|^6.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-22T13:05:35+00:00"}, {"name": "symfony/finder", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "63741784cd7b9967975eec610b256eed3ede022b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/63741784cd7b9967975eec610b256eed3ede022b", "reference": "63741784cd7b9967975eec610b256eed3ede022b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-28T13:32:08+00:00"}, {"name": "symfony/http-foundation", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "3f38b8af283b830e1363acd79e5bc3412d055341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/3f38b8af283b830e1363acd79e5bc3412d055341", "reference": "3f38b8af283b830e1363acd79e5bc3412d055341", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "^1.0|^2.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T18:58:02+00:00"}, {"name": "symfony/mailer", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "f732e1fafdf0f4a2d865e91f1018aaca174aeed9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/f732e1fafdf0f4a2d865e91f1018aaca174aeed9", "reference": "f732e1fafdf0f4a2d865e91f1018aaca174aeed9", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=7.2.5", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2.6|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<4.4"}, "require-dev": {"symfony/http-client": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/mime", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/8c1b9b3e5b52981551fc6044539af1d974e39064", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.35|>=6,<6.3.12|>=6.4,<6.4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/process": "^5.4|^6.4", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-23T20:18:32+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78", "reference": "d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v5.4.47", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5d1662fb32ebc94f17ddb8d635454a776066733d", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-06T11:36:42+00:00"}, {"name": "symfony/property-access", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "111e7ed617509f1a9139686055d234aad6e388e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/111e7ed617509f1a9139686055d234aad6e388e0", "reference": "111e7ed617509f1a9139686055d234aad6e388e0", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/property-info": "^5.2|^6.0"}, "require-dev": {"symfony/cache": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/property-info", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "a0396295ad585f95fccd690bc6a281e5bd303902"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/a0396295ad585f95fccd690bc6a281e5bd303902", "reference": "a0396295ad585f95fccd690bc6a281e5bd303902", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/string": "^5.1|^6.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4|^2", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-25T16:14:41+00:00"}, {"name": "symfony/routing", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "dd08c19879a9b37ff14fd30dcbdf99a4cf045db1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/dd08c19879a9b37ff14fd30dcbdf99a4cf045db1", "reference": "dd08c19879a9b37ff14fd30dcbdf99a4cf045db1", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.3", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^5.3|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-12T18:20:21+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f37b419f7aea2e9abf10abd261832cace12e3300", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/string", "version": "v5.4.47", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "136ca7d72f72b599f2631aca474a4f8e26719799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/136ca7d72f72b599f2631aca474a4f8e26719799", "reference": "136ca7d72f72b599f2631aca474a4f8e26719799", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-10T20:33:58+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/42f18f170aa86d612c3559cfb3bd11a375df32c8", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-08T15:21:10+00:00"}, {"name": "symfony/var-exporter", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "862700068db0ddfd8c5b850671e029a90246ec75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/862700068db0ddfd8c5b850671e029a90246ec75", "reference": "862700068db0ddfd8c5b850671e029a90246ec75", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/yaml", "version": "v5.3.14", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "c441e9d2e340642ac8b951b753dea962d55b669d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/c441e9d2e340642ac8b951b753dea962d55b669d", "reference": "c441e9d2e340642ac8b951b753dea962d55b669d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"symfony/console": "^4.4|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.14"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-26T16:05:39+00:00"}, {"name": "t3monitor/t3monitoring_client", "version": "9.2.3", "source": {"type": "git", "url": "https://github.com/georgringer/t3monitoring_client.git", "reference": "cdb0e8eee0f0bb6b0e242ebc94b6cf9061846b6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/georgringer/t3monitoring_client/zipball/cdb0e8eee0f0bb6b0e242ebc94b6cf9061846b6e", "reference": "cdb0e8eee0f0bb6b0e242ebc94b6cf9061846b6e", "shasum": ""}, "require": {"typo3/cms-core": "^9.5 | ^10 || ^11", "typo3/cms-reports": "^9.5 | ^10 || ^11"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "t3monitoring_client"}}, "autoload": {"psr-4": {"T3Monitor\\T3monitoringClient\\": "Classes"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Client extension for the t3monitoring service", "support": {"issues": "https://github.com/georgringer/t3monitoring_client/issues", "source": "https://github.com/georgringer/t3monitoring_client/tree/9.2.3"}, "funding": [{"url": "https://paypal.me/GeorgRinger/10", "type": "custom"}, {"url": "https://www.amazon.de/hz/wishlist/ls/8F573K08TSDG", "type": "custom"}, {"url": "https://github.com/georgringer", "type": "github"}, {"url": "https://www.patreon.com/georgringer", "type": "patreon"}], "time": "2022-11-30T14:35:45+00:00"}, {"name": "typo3/class-alias-loader", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/TYPO3/class-alias-loader.git", "reference": "cf2aebabe1886474da7194e1531900039263b3e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/class-alias-loader/zipball/cf2aebabe1886474da7194e1531900039263b3e0", "reference": "cf2aebabe1886474da7194e1531900039263b3e0", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=7.1"}, "replace": {"helhum/class-alias-loader": "*"}, "require-dev": {"composer/composer": "^1.1@dev || ^2.0@dev", "mikey179/vfsstream": "~1.4.0@dev", "phpunit/phpunit": ">4.8 <9"}, "type": "composer-plugin", "extra": {"class": "TYPO3\\ClassAliasLoader\\Plugin", "branch-alias": {"dev-main": "1.1.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\ClassAliasLoader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Amends the composer class loader to support class aliases to provide backwards compatibility for packages", "homepage": "http://github.com/TYPO3/class-alias-loader", "keywords": ["alias", "autoloader", "classloader", "composer"], "support": {"issues": "https://github.com/TYPO3/class-alias-loader/issues", "source": "https://github.com/TYPO3/class-alias-loader/tree/v1.2.0"}, "time": "2024-10-11T08:11:39+00:00"}, {"name": "typo3/cms-about", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/about.git", "reference": "4a80b330164a0067aee96dd438a4bf7cbc785e10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/about/zipball/4a80b330164a0067aee96dd438a4bf7cbc785e10", "reference": "4a80b330164a0067aee96dd438a4bf7cbc785e10", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "about"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\About\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Shows info about TYPO3, installed extensions and a separate module for available modules.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-adminpanel", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/adminpanel.git", "reference": "98604e724ce63c33f310da77ea34f46d36ccbf45"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/adminpanel/zipball/98604e724ce63c33f310da77ea34f46d36ccbf45", "reference": "98604e724ce63c33f310da77ea34f46d36ccbf45", "shasum": ""}, "require": {"psr/http-message": "^1.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "symfony/var-dumper": "^4.4 || ^5.0", "typo3/cms-backend": "10.4.37", "typo3/cms-core": "10.4.37", "typo3/cms-fluid": "10.4.37", "typo3/cms-frontend": "10.4.37", "typo3fluid/fluid": "^2.6.10"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "adminpanel"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Adminpanel\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "The TYPO3 admin panel provides a panel with additional functionality in the frontend (Debugging, Caching, Preview...)", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-adminpanel/10.4/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-backend", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/backend.git", "reference": "55b4bf5bab5734ff88400d1c7b3b97e005dd12fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/backend/zipball/55b4bf5bab5734ff88400d1c7b3b97e005dd12fd", "reference": "55b4bf5bab5734ff88400d1c7b3b97e005dd12fd", "shasum": ""}, "require": {"psr/event-dispatcher": "^1.0", "typo3/cms-core": "10.4.37", "typo3/cms-recordlist": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "replace": {"typo3/cms-context-help": "*", "typo3/cms-cshmanual": "*", "typo3/cms-func-wizards": "*", "typo3/cms-wizard-crpages": "*", "typo3/cms-wizard-sortpages": "*"}, "suggest": {"typo3/cms-install": "To generate url to install tool in environment toolbar"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Backend\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "backend"}, "branch-alias": {"dev-master": "10.4.x-dev"}, "typo3/class-alias-loader": {"class-alias-maps": ["Migrations/Code/ClassAliasMap.php"]}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Backend\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Classes for the TYPO3 backend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-belog", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/belog.git", "reference": "b88e57edbc99e31426cb80a7a297efb4b77a444c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/belog/zipball/b88e57edbc99e31426cb80a7a297efb4b77a444c", "reference": "b88e57edbc99e31426cb80a7a297efb4b77a444c", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "belog"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Belog\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Displays backend log, both per page and system wide. Available as the module Tools>Log (system wide overview) and Web>Info/Log (page relative overview).", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-beuser", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/beuser.git", "reference": "bc6ec9fe7dbd636ef30c8d8158105084b162e4a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/beuser/zipball/bc6ec9fe7dbd636ef30c8d8158105084b162e4a4", "reference": "bc6ec9fe7dbd636ef30c8d8158105084b162e4a4", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "beuser"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Beuser\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Backend user administration and overview. Allows you to compare the settings of users and verify their permissions and see who is online.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-cli", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/TYPO3/cms-cli.git", "reference": "215a0bf5c446b4e0b20f4562bbaf3d6215a5ee82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/cms-cli/zipball/215a0bf5c446b4e0b20f4562bbaf3d6215a5ee82", "reference": "215a0bf5c446b4e0b20f4562bbaf3d6215a5ee82", "shasum": ""}, "require": {"php": "^7.0"}, "bin": ["typo3"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "TYPO3 command line binary", "homepage": "https://typo3.org", "support": {"issues": "https://github.com/TYPO3/cms-cli/issues", "source": "https://github.com/TYPO3/cms-cli/tree/master"}, "time": "2018-03-08T20:16:43+00:00"}, {"name": "typo3/cms-composer-installers", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/TYPO3/CmsComposerInstallers.git", "reference": "29fb214976fe05f39c7d0619ce1d648d59c50a08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/CmsComposerInstallers/zipball/29fb214976fe05f39c7d0619ce1d648d59c50a08", "reference": "29fb214976fe05f39c7d0619ce1d648d59c50a08", "shasum": ""}, "require": {"composer-plugin-api": "^1.0.0 || ^2.0.0", "php": "^7.2 || ^8.0"}, "conflict": {"composer/installers": "<2.0.0"}, "replace": {"lw/typo3cms-installers": "*", "netresearch/composer-installers": "*"}, "require-dev": {"composer/composer": "1.2.*@dev || 2.0.*@dev", "friendsofphp/php-cs-fixer": "^2.18", "overtrue/phplint": "^2.0", "phpunit/phpunit": "^8.5"}, "type": "composer-plugin", "extra": {"class": "TYPO3\\CMS\\Composer\\Installer\\Plugin", "branch-alias": {"dev-main": "3.1.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 CMS Core Team", "homepage": "https://forge.typo3.org/projects/typo3cms-core", "role": "Developer"}, {"name": "The TYPO3 Community", "homepage": "https://typo3.org/community/", "role": "Contributor"}], "description": "TYPO3 CMS Installers", "homepage": "https://github.com/TYPO3/CmsComposerInstallers", "keywords": ["cms", "core", "extension", "installer", "typo3"], "support": {"general": "https://typo3.org/support/", "issues": "https://github.com/TYPO3/CmsComposerInstallers/issues", "source": "https://github.com/TYPO3/CmsComposerInstallers/tree/v3.1.4"}, "time": "2024-08-13T14:56:37+00:00"}, {"name": "typo3/cms-core", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/core.git", "reference": "34e4a9807eb365e202773c82dff82038da8966d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/core/zipball/34e4a9807eb365e202773c82dff82038da8966d3", "reference": "34e4a9807eb365e202773c82dff82038da8966d3", "shasum": ""}, "require": {"doctrine/annotations": "^1.7", "doctrine/dbal": "~2.10.0 || ~2.11.2 || ~2.13.1", "doctrine/instantiator": "^1.1", "doctrine/lexer": "^1.0", "egulias/email-validator": "^2.1", "enshrined/svg-sanitize": "^0.15.4", "ext-json": "*", "ext-libxml": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-xml": "*", "guzzlehttp/guzzle": "^6.5.8", "guzzlehttp/psr7": "^1.8.5", "lolli42/finediff": "^1.0.1", "masterminds/html5": "^2.7.6", "nikic/php-parser": "^4.10.4", "php": "^7.2", "psr/container": "^1.0", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "psr/log": "^1.0", "symfony/config": "^4.4 || ^5.0", "symfony/console": "^4.4 || ^5.0", "symfony/dependency-injection": "^4.4 || ^5.0", "symfony/event-dispatcher-contracts": "^1.1 || ^2.0", "symfony/expression-language": "^4.4 || ^5.0", "symfony/filesystem": "^4.4 || ^5.0", "symfony/finder": "^4.4 || ^5.0", "symfony/http-foundation": "^4.4 || ^5.0", "symfony/mailer": "^4.4 || ^5.0", "symfony/mime": "^4.4.16 || ^5.1.8", "symfony/polyfill-intl-icu": "^1.6", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-intl-normalizer": "^1.22", "symfony/polyfill-mbstring": "^1.16", "symfony/polyfill-php73": "^1.16", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.23", "symfony/routing": "^4.4 || ^5.0", "symfony/yaml": "^4.4 || ^5.0", "typo3/class-alias-loader": "^1.0", "typo3/cms-cli": "^2.0", "typo3/cms-composer-installers": "^2.0 || ^3.0", "typo3/html-sanitizer": "^2.1.1", "typo3/phar-stream-wrapper": "^3.1.7", "typo3/symfony-psr-event-dispatcher-adapter": "^1.0 || ^2.0", "typo3fluid/fluid": "^2.6.10"}, "conflict": {"guzzlehttp/guzzle": "6.5.0", "hoa/core": "*", "typo3/cms": "*"}, "provide": {"psr/http-client-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "replace": {"typo3/cms-lang": "*", "typo3/cms-saltedpasswords": "*", "typo3/cms-sv": "*"}, "require-dev": {"codeception/codeception": "^4.0", "codeception/module-asserts": "^1.1", "codeception/module-filesystem": "^1.0", "codeception/module-webdriver": "^1.0.1", "friendsofphp/php-cs-fixer": "^2.19 || ^3.0", "mikey179/vfsstream": "^1.6.11", "phpspec/prophecy": "^1.14.0", "phpstan/phpstan": "^0.12.64", "typo3/cms-styleguide": "~10.0.4", "typo3/testing-framework": "^6.16.7"}, "suggest": {"ext-fileinfo": "Used for proper file type detection in the file abstraction layer", "ext-gd": "GDlib/Freetype is required for building images with text (GIFBUILDER) and can also be used to scale images", "ext-intl": "TYPO3 with unicode-based filesystems", "ext-mysqli": "", "ext-openssl": "OpenSSL is required for sending SMTP mails over an encrypted channel endpoint, and for extensions such as \"rsaauth\"", "ext-zip": "", "ext-zlib": "TYPO3 uses zlib for amongst others output compression and un/packing t3x extension files"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Core\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "core"}, "branch-alias": {"dev-master": "10.4.x-dev"}, "typo3/class-alias-loader": {"class-alias-maps": ["Migrations/Code/ClassAliasMap.php"]}}, "autoload": {"files": ["Resources/PHP/GlobalDebugFunctions.php"], "psr-4": {"TYPO3\\CMS\\Core\\": "Classes/"}, "classmap": ["Resources/PHP/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "The core library of TYPO3.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-extbase", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/extbase.git", "reference": "9b9c88efe129b9ca04e63876a1e123d892072404"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/extbase/zipball/9b9c88efe129b9ca04e63876a1e123d892072404", "reference": "9b9c88efe129b9ca04e63876a1e123d892072404", "shasum": ""}, "require": {"phpdocumentor/reflection-docblock": "^5.2", "phpdocumentor/type-resolver": "^1.3", "symfony/dependency-injection": "^4.4 || ^5.0", "symfony/property-access": "^4.4 || ^5.0", "symfony/property-info": "^4.4 || ^5.0", "typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-scheduler": "Additional scheduler tasks"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Extbase\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "extbase"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Extbase\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "A framework to build extensions for TYPO3 CMS.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-extensionmanager", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/extensionmanager.git", "reference": "7aac8dfe5ff6fd6cd847bd5436be1b6fd4765293"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/extensionmanager/zipball/7aac8dfe5ff6fd6cd847bd5436be1b6fd4765293", "reference": "7aac8dfe5ff6fd6cd847bd5436be1b6fd4765293", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "extensionmanager"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Extensionmanager\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 Extension Manager", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-felogin", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/felogin.git", "reference": "7563a4666816206b0557416d5a83ddbe5ec1fc8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/felogin/zipball/7563a4666816206b0557416d5a83ddbe5ec1fc8e", "reference": "7563a4666816206b0557416d5a83ddbe5ec1fc8e", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "felogin"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Felogin\\": "Classes/", "TYPO3\\CMS\\FrontendLogin\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "A template-based plugin to log in Website Users in the Frontend", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-felogin/10.4/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-filelist", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/filelist.git", "reference": "59b8fe14415b85cc0ad600dbd2fb065a9cdf4684"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/filelist/zipball/59b8fe14415b85cc0ad600dbd2fb065a9cdf4684", "reference": "59b8fe14415b85cc0ad600dbd2fb065a9cdf4684", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "filelist"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Filelist\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Listing of files in the directory", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-fluid", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/fluid.git", "reference": "ec89e645ff50a202236dc3f66b9d07c2aa6d5b03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/fluid/zipball/ec89e645ff50a202236dc3f66b9d07c2aa6d5b03", "reference": "ec89e645ff50a202236dc3f66b9d07c2aa6d5b03", "shasum": ""}, "require": {"symfony/dependency-injection": "^4.4 || ^5.0", "typo3/cms-core": "10.4.37", "typo3/cms-extbase": "10.4.37", "typo3fluid/fluid": "^2.6.10"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "fluid"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Fluid\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Fluid is a next-generation templating engine which makes the life of extension authors a lot easier!", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/other/typo3/view-helper-reference/10.4/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-fluid-styled-content", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/fluid_styled_content.git", "reference": "ebcad8581a7f81bc69bf4aebb2ef99a590f84909"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/fluid_styled_content/zipball/ebcad8581a7f81bc69bf4aebb2ef99a590f84909", "reference": "ebcad8581a7f81bc69bf4aebb2ef99a590f84909", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37", "typo3/cms-fluid": "10.4.37", "typo3/cms-frontend": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "fluid_styled_content"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\FluidStyledContent\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "A set of common content elements based on Fluid for Frontend output.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-fluid-styled-content/10.4/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-form", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/form.git", "reference": "63c317bfebbdd49ab1777f2e26170319b6174123"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/form/zipball/63c317bfebbdd49ab1777f2e26170319b6174123", "reference": "63c317bfebbdd49ab1777f2e26170319b6174123", "shasum": ""}, "require": {"psr/http-message": "^1.0", "symfony/expression-language": "^4.4 || ^5.0", "typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-filelist": "Listing of files in the directory", "typo3/cms-impexp": "Import and Export of records from TYPO3 in a custom serialized format (.T3D) for data exchange with other TYPO3 systems."}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "form"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Form\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Form Library, Plugin and Editor", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-form/10.4/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-frontend", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/frontend.git", "reference": "d97ae4ed2f9f2062374536a00ff6f7bc1cba85ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/frontend/zipball/d97ae4ed2f9f2062374536a00ff6f7bc1cba85ad", "reference": "d97ae4ed2f9f2062374536a00ff6f7bc1cba85ad", "shasum": ""}, "require": {"ext-libxml": "*", "symfony/polyfill-mbstring": "^1.16", "typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-adminpanel": "Provides additional information and functionality for backend users in the frontend."}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Frontend\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "frontend"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Frontend\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Classes for the frontend of TYPO3.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-impexp", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/impexp.git", "reference": "a78f42a4e4977ffdead66ed7774c07e366fe7b91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/impexp/zipball/a78f42a4e4977ffdead66ed7774c07e366fe7b91", "reference": "a78f42a4e4977ffdead66ed7774c07e366fe7b91", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "impexp"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Impexp\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Import and Export of records from TYPO3 in a custom serialized format (.T3D) for data exchange with other TYPO3 systems.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-indexed-search", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/indexed_search.git", "reference": "8d19809b2df90c425f06f3002039f290150230ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/indexed_search/zipball/8d19809b2df90c425f06f3002039f290150230ee", "reference": "8d19809b2df90c425f06f3002039f290150230ee", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-scheduler": "For garbage collection of search statistics"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"extension-key": "indexed_search"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\IndexedSearch\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Indexed Search Engine for TYPO3 pages, PDF-files, Word-files, HTML and text files. Provides a backend module for statistics of the indexer and a frontend plugin. Documentation can be found in the extension \"doc_indexed_search\".", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-indexed-search/10.4/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-info", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/info.git", "reference": "875f32625542aebd78e806024484b81c2789e8ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/info/zipball/875f32625542aebd78e806024484b81c2789e8ec", "reference": "875f32625542aebd78e806024484b81c2789e8ec", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "replace": {"typo3/cms-info-pagetsconfig": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "info"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Info\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Shows various infos", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-install", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/install.git", "reference": "3e82477beb50e2595665ed572f52a1e9e130ded6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/install/zipball/3e82477beb50e2595665ed572f52a1e9e130ded6", "reference": "3e82477beb50e2595665ed572f52a1e9e130ded6", "shasum": ""}, "require": {"doctrine/dbal": "~2.10.0 || ~2.11.2 || ~2.13.1", "nikic/php-parser": "^4.10.4", "symfony/finder": "^4.4 || ^5.0", "typo3/cms-core": "10.4.37", "typo3/cms-extbase": "10.4.37", "typo3/cms-fluid": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Install\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "install"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Install\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "The Install Tool mounted as the module Tools>Install in TYPO3.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-recordlist", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/recordlist.git", "reference": "47cffea95b30726f130eb2d2bb6a7836f6fe5145"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/recordlist/zipball/47cffea95b30726f130eb2d2bb6a7836f6fe5145", "reference": "47cffea95b30726f130eb2d2bb6a7836f6fe5145", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "recordlist"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Recordlist\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "List of database-records", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-recycler", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/recycler.git", "reference": "3b52b00608896d08feab5cab63a45bb297df603e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/recycler/zipball/3b52b00608896d08feab5cab63a45bb297df603e", "reference": "3b52b00608896d08feab5cab63a45bb297df603e", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "require-dev": {"ext-libxml": "*"}, "suggest": {"typo3/cms-scheduler": "Remove deleted records after given time"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"extension-key": "recycler"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Recycler\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "The recycler offers the possibility to restore deleted records or remove them from the database permanently. These actions can be applied to a single record, multiple records, and recursively to child records (ex. restoring a page can restore all content elements on that page). Filtering by page and by table provides a quick overview of deleted records before taking action on them.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-recycler/10.4/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-redirects", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/redirects.git", "reference": "0570a85564e4304b3387dfdc232418221db193d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/redirects/zipball/0570a85564e4304b3387dfdc232418221db193d9", "reference": "0570a85564e4304b3387dfdc232418221db193d9", "shasum": ""}, "require": {"doctrine/dbal": "~2.10.0 || ~2.11.2 || ~2.13.1", "psr/http-message": "^1.0", "psr/log": "^1.0", "typo3/cms-backend": "10.4.37", "typo3/cms-core": "10.4.37", "typo3fluid/fluid": "^2.6.10"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-reports": "Get reports of redirects", "typo3/cms-scheduler": "Execute commands to update redirect status"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "redirects"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Redirects\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Custom redirects in TYPO3.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-redirects/10.4/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-reports", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/reports.git", "reference": "72be1d9bc437f4eccd1d0699bcf46481b02ffad6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/reports/zipball/72be1d9bc437f4eccd1d0699bcf46481b02ffad6", "reference": "72be1d9bc437f4eccd1d0699bcf46481b02ffad6", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-scheduler": "Determine system's status and send it via email"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "reports"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Reports\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "The reports module groups several system reports.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-rte-ckeditor", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/rte_ckeditor.git", "reference": "8a0faa5853db10ae3c440f8a16fcd1c1c4025536"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/rte_ckeditor/zipball/8a0faa5853db10ae3c440f8a16fcd1c1c4025536", "reference": "8a0faa5853db10ae3c440f8a16fcd1c1c4025536", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-setup": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "rte_ckeditor"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\RteCKEditor\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Integration of CKEditor as Rich Text Editor.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-rte-ckeditor/10.4/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-scheduler", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/scheduler.git", "reference": "c153fa77b6fc5a218c60b4ad574b74c3238168f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/scheduler/zipball/c153fa77b6fc5a218c60b4ad574b74c3238168f8", "reference": "c153fa77b6fc5a218c60b4ad574b74c3238168f8", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"extension-key": "scheduler"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Scheduler\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "The TYPO3 Scheduler let's you register tasks to happen at a specific time", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-scheduler/10.4/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-seo", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/seo.git", "reference": "24e7bce1d8815ee8694661562ff2b11f2d767749"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/seo/zipball/24e7bce1d8815ee8694661562ff2b11f2d767749", "reference": "24e7bce1d8815ee8694661562ff2b11f2d767749", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37", "typo3/cms-extbase": "10.4.37", "typo3/cms-frontend": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "seo"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Seo\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "SEO features for TYPO3.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-seo/10.4/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-setup", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/setup.git", "reference": "70dca91a9f494005731a788a95c8e9ede31566a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/setup/zipball/70dca91a9f494005731a788a95c8e9ede31566a4", "reference": "70dca91a9f494005731a788a95c8e9ede31566a4", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "setup"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Setup\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Allows users to edit a limited set of options for their user profile, eg. preferred language and their name and email address.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-sys-note", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/sys_note.git", "reference": "1f8569de667af8039698a3dae5e7dc3b36d3215a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/sys_note/zipball/1f8569de667af8039698a3dae5e7dc3b36d3215a", "reference": "1f8569de667af8039698a3dae5e7dc3b36d3215a", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "sys_note"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\SysNote\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Records with messages which can be placed on any page and contain instructions or other information related to a page or section.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-t3editor", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/t3editor.git", "reference": "c0d4c770c405b85b78d14406049a27a1ce9b8968"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/t3editor/zipball/c0d4c770c405b85b78d14406049a27a1ce9b8968", "reference": "c0d4c770c405b85b78d14406049a27a1ce9b8968", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "t3editor"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\T3editor\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "JavaScript-driven editor with syntax highlighting and codecompletion. Based on CodeMirror.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-tstemplate", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/tstemplate.git", "reference": "cf923ac575c85529bc82131ea66feaa187853ae9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/tstemplate/zipball/cf923ac575c85529bc82131ea66feaa187853ae9", "reference": "cf923ac575c85529bc82131ea66feaa187853ae9", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "tstemplate"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Tstemplate\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Framework for management of TypoScript template records for the CMS frontend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/cms-viewpage", "version": "v10.4.37", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/viewpage.git", "reference": "0489b4e7fde3997551896be3c4597a0669db5819"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/viewpage/zipball/0489b4e7fde3997551896be3c4597a0669db5819", "reference": "0489b4e7fde3997551896be3c4597a0669db5819", "shasum": ""}, "require": {"typo3/cms-core": "10.4.37"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "viewpage"}, "branch-alias": {"dev-master": "10.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Viewpage\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "Shows the frontend webpage inside the backend frameset.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2023-04-11T13:10:15+00:00"}, {"name": "typo3/html-sanitizer", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/TYPO3/html-sanitizer.git", "reference": "c672a2e02925de8eed0dcaeb3a3c90d3642049a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/html-sanitizer/zipball/c672a2e02925de8eed0dcaeb3a3c90d3642049a0", "reference": "c672a2e02925de8eed0dcaeb3a3c90d3642049a0", "shasum": ""}, "require": {"ext-dom": "*", "masterminds/html5": "^2.7.6", "php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\HtmlSanitizer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTML sanitizer aiming to provide XSS-safe markup based on explicitly allowed tags, attributes and values.", "support": {"issues": "https://github.com/TYPO3/html-sanitizer/issues", "source": "https://github.com/TYPO3/html-sanitizer/tree/v2.2.0"}, "time": "2024-07-12T15:52:25+00:00"}, {"name": "typo3/minimal", "version": "v10.4.0", "source": {"type": "git", "url": "https://github.com/TYPO3/minimal.git", "reference": "858bc4b51f9183306f4301db3cead2f99a0d48ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/minimal/zipball/858bc4b51f9183306f4301db3cead2f99a0d48ff", "reference": "858bc4b51f9183306f4301db3cead2f99a0d48ff", "shasum": ""}, "require": {"typo3/cms-backend": "^10.4", "typo3/cms-core": "^10.4", "typo3/cms-extbase": "^10.4", "typo3/cms-extensionmanager": "^10.4", "typo3/cms-filelist": "^10.4", "typo3/cms-fluid": "^10.4", "typo3/cms-frontend": "^10.4", "typo3/cms-install": "^10.4", "typo3/cms-recordlist": "^10.4"}, "type": "metapackage", "extra": {"branch-alias": {"dev-master": "10.4.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 CMS Core Team", "homepage": "https://forge.typo3.org/projects/typo3cms-core", "role": "Developer"}, {"name": "The TYPO3 Community", "homepage": "https://typo3.org/community/", "role": "Contributor"}], "description": "Minimal required set of TYPO3 extensions", "support": {"general": "https://typo3.org/support/", "irc": "irc://irc.freenode.net/#typo3-cms", "issues": "https://forge.typo3.org", "news": "nntp://lists.typo3.org", "source": "https://github.com/TYPO3/minimal/tree/v10.4.0"}, "time": "2020-04-21T08:52:32+00:00"}, {"name": "typo3/phar-stream-wrapper", "version": "v3.1.8", "source": {"type": "git", "url": "https://github.com/TYPO3/phar-stream-wrapper.git", "reference": "a931b28f422a60052db85c0a84a05a366453b2c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/phar-stream-wrapper/zipball/a931b28f422a60052db85c0a84a05a366453b2c0", "reference": "a931b28f422a60052db85c0a84a05a366453b2c0", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.0 || ~8.0 || ~8.1 || ~8.2 || ~8.3"}, "require-dev": {"ext-xdebug": "*", "phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^5.1"}, "suggest": {"ext-fileinfo": "For PHP builtin file type guessing, otherwise uses internal processing"}, "type": "library", "extra": {"branch-alias": {"dev-master": "v3.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\PharStreamWrapper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Interceptors for PHP's native phar:// stream handling", "homepage": "https://typo3.org/", "keywords": ["phar", "php", "security", "stream-wrapper"], "support": {"issues": "https://github.com/TYPO3/phar-stream-wrapper/issues", "source": "https://github.com/TYPO3/phar-stream-wrapper/tree/v3.1.8"}, "time": "2024-12-09T23:06:33+00:00"}, {"name": "typo3/symfony-psr-event-dispatcher-adapter", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/TYPO3/symfony-psr-event-dispatcher-adapter.git", "reference": "c93fd7cc9f215cfbbc6ce89089eadabedf65a21f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/symfony-psr-event-dispatcher-adapter/zipball/c93fd7cc9f215cfbbc6ce89089eadabedf65a21f", "reference": "c93fd7cc9f215cfbbc6ce89089eadabedf65a21f", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/event-dispatcher-contracts": "^2.0"}, "type": "library", "autoload": {"psr-4": {"TYPO3\\SymfonyPsrEventDispatcherAdapter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Adapter to provide compatibility with the Symfony's event dispatcher interface in all versions with the PSR-14 specification.", "homepage": "https://typo3.org/", "keywords": ["adapter", "events", "psr", "psr-14"], "support": {"issues": "https://github.com/TYPO3/symfony-psr-event-dispatcher-adapter/issues", "source": "https://github.com/TYPO3/symfony-psr-event-dispatcher-adapter/tree/v2.1.0"}, "time": "2021-03-02T09:36:49+00:00"}, {"name": "typo3fluid/fluid", "version": "2.7.4", "source": {"type": "git", "url": "https://github.com/TYPO3/Fluid.git", "reference": "24f4494083c8d304680e4c9c38667dff33720dd4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/Fluid/zipball/24f4494083c8d304680e4c9c38667dff33720dd4", "reference": "24f4494083c8d304680e4c9c38667dff33720dd4", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"ext-json": "*", "ext-mbstring": "*", "friendsofphp/php-cs-fixer": "^3.4", "phpstan/phpstan": "^1.7", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5.33 || ^9.5.28 || ^10.0.16"}, "suggest": {"ext-json": "PHP JSON is needed when using JSONVariableProvider: A relatively rare use case"}, "bin": ["bin/fluid"], "type": "library", "autoload": {"psr-4": {"TYPO3Fluid\\Fluid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "description": "The TYPO3 Fluid template rendering engine", "homepage": "https://github.com/TYPO3/Fluid", "support": {"docs": "https://docs.typo3.org/other/typo3fluid/fluid/main/en-us/", "issues": "https://github.com/TYPO3/Fluid/issues", "source": "https://github.com/TYPO3/Fluid"}, "time": "2023-03-23T12:04:09+00:00"}, {"name": "vancado/rhenag", "version": "10.4.0", "dist": {"type": "path", "url": "packages/rhenag", "reference": "db67c388f574d553b780359ef8cd8dc8e2c2b6e5"}, "replace": {"typo3-ter/rhenag": "self.version", "vancado/rhenag": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "rhenag"}}, "autoload": {"psr-4": {"Vancado\\Rhenag\\": "Classes"}}, "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "Rhenag Konfiguration Basispaket", "transport-options": {"relative": true}}, {"name": "vancado/rhenag-components", "version": "10.4.1", "dist": {"type": "path", "url": "packages/rhenag_components", "reference": "315209bb329214e556381ce7a406ec64d58124b0"}, "replace": {"typo3-ter/rhenag-components": "self.version", "vancado/rhenag-components": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "rhenag_components"}}, "autoload": {"psr-4": {"Vancado\\RhenagComponents\\": "Classes"}}, "autoload-dev": {"psr-4": {"Vancado\\RhenagComponents\\Tests\\": "Tests"}}, "authors": [{"name": "Vancado", "role": "Developer"}], "description": "Templates for components", "transport-options": {"relative": true}}, {"name": "vancado/rhenag-events", "version": "10.4.1", "dist": {"type": "path", "url": "packages/rhenag_events", "reference": "95d18c857b278f1972218c37e9deab4dd855573c"}, "replace": {"typo3-ter/rhenag-events": "self.version", "vancado/rhenag-events": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "rhenag_events"}}, "autoload": {"psr-4": {"Vancado\\RhenagEvents\\": "Classes"}}, "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "Rhenag Events - Seminare und Fachveranstaltungen", "transport-options": {"relative": true}}, {"name": "vancado/rhenag-slider", "version": "10.4.1", "dist": {"type": "path", "url": "packages/rhenag_slider", "reference": "65cb6832b5e1443b40089a8c3c884b40ececc6b5"}, "replace": {"typo3-ter/rhenag-slider": "self.version", "vancado/rhenag-slider": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "rhenag_slider"}}, "autoload": {"psr-4": {"Vancado\\RhenagSlider\\": "Classes"}}, "authors": [{"name": "<PERSON>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "role": "Developer"}], "description": "Rhenag Slider", "transport-options": {"relative": true}}, {"name": "vancado/vnc-episerver", "version": "10.4.1", "dist": {"type": "path", "url": "packages/vnc_episerver", "reference": "881cd305688a0b69b12faf909e0cf484d40d40a4"}, "replace": {"typo3-ter/vnc-episerver": "self.version", "vancado/vnc-episerver": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "vnc_episerver"}}, "autoload": {"psr-4": {"Vancado\\VncEpiserver\\": "Classes"}}, "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "Formular zur Kommunikation mit epi-Server API", "transport-options": {"relative": true}}, {"name": "vancado/vnc-page-hits", "version": "10.4.1", "dist": {"type": "path", "url": "packages/vnc_page_hits", "reference": "3abadd6f5903d8034bdd38f1db5727306cba1d30"}, "replace": {"typo3-ter/vnc-page-hits": "self.version", "vancado/vnc-page-hits": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "vnc_page_hits"}}, "autoload": {"psr-4": {"Vancado\\VncPageHits\\": "Classes"}}, "autoload-dev": {"psr-4": {"Vancado\\VncPageHits\\Tests\\": "Tests"}}, "transport-options": {"relative": true}}, {"name": "vancado/vnc-testimonials", "version": "10.4.1", "dist": {"type": "path", "url": "packages/vnc_testimonials", "reference": "9d9ddd15c7f1ed5906dd61c81b8b886be308b22d"}, "replace": {"typo3-ter/vnc-testimonials": "self.version", "vancado/vnc-testimonials": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "vnc_testimonials"}}, "autoload": {"psr-4": {"Vancado\\VncTestimonials\\": "Classes"}}, "autoload-dev": {"psr-4": {"Vancado\\VncTestimonials\\Tests\\": "Tests"}}, "transport-options": {"relative": true}}, {"name": "vancado/vnc-warnings", "version": "10.4.0", "dist": {"type": "path", "url": "packages/vnc_warnings", "reference": "e46952d048b3908d718ae9b90f713e106c7836da"}, "replace": {"typo3-ter/vnc-warnings": "self.version", "vancado/vnc-warnings": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "vnc_warnings"}}, "autoload": {"psr-4": {"Vancado\\VncWarnings\\": "Classes"}}, "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "Vancado Warnings Extension rhenag edition", "transport-options": {"relative": true}}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "yoast-seo-for-typo3/yoast_seo", "version": "8.3.1", "source": {"type": "git", "url": "https://github.com/Yoast/Yoast-SEO-for-TYPO3.git", "reference": "6ce23d61c91afa5adf71e15b54c1e9aa2fd95511"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Yoast/Yoast-SEO-for-TYPO3/zipball/6ce23d61c91afa5adf71e15b54c1e9aa2fd95511", "reference": "6ce23d61c91afa5adf71e15b54c1e9aa2fd95511", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": "^7.2 || ^8.0", "typo3/cms-backend": "^9.5|^10.4|^11.0", "typo3/cms-core": "^9.5|^10.4|^11.0", "typo3/cms-extbase": "^9.5|^10.4|^11.0", "typo3/cms-fluid": "^9.5|^10.4|^11.0", "typo3/cms-frontend": "^9.5|^10.4|^11.0", "typo3/cms-install": "^9.5|^10.4|^11.0", "typo3/cms-seo": "^9.5|^10.4|^11.0"}, "replace": {"typo3-ter/yoast-seo": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "overtrue/phplint": "^2.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "saschaegerer/phpstan-typo3": "^0.13", "slam/phpstan-extensions": "^5.0", "typo3/tailor": "^1.1"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"web-dir": "public", "extension-key": "yoast_seo", "cms-package-dir": "{$vendor-dir}/typo3/cms"}, "branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"YoastSeoForTypo3\\YoastSeo\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "Team Yoast", "email": "<EMAIL>", "homepage": "https://yoast.com"}, {"name": "Team MaxServ", "email": "<EMAIL>", "homepage": "https://maxserv.com"}], "description": "Yoast SEO for TYPO3", "homepage": "https://yoast.com", "keywords": ["MaxServ", "TYPO3 CMS", "seo", "yoast"], "support": {"docs": "https://docs.typo3.org/p/yoast-seo-for-typo3/yoast_seo/main/en-us/", "issues": "https://github.com/Yoast/Yoast-SEO-for-TYPO3/issues", "source": "https://github.com/Yoast/Yoast-SEO-for-TYPO3"}, "time": "2022-08-03T09:43:46+00:00"}], "packages-dev": [{"name": "helmich/typo3-typoscript-parser", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/martin-helmich/typo3-typoscript-parser.git", "reference": "2c350d1a9148e4fc14d33602bb7fc18fe79b284b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/martin-helmich/typo3-typoscript-parser/zipball/2c350d1a9148e4fc14d33602bb7fc18fe79b284b", "reference": "2c350d1a9148e4fc14d33602bb7fc18fe79b284b", "shasum": ""}, "require": {"php": ">=7.4", "symfony/config": "~3.0|~4.0|~5.0|~6.0", "symfony/dependency-injection": "~3.0|~4.0|~5.0|~6.0", "symfony/yaml": "~3.0|~4.0|~5.0|~6.0"}, "require-dev": {"php-vfs/php-vfs": "^1.3", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "symfony/phpunit-bridge": "~2.7|~3.0|~4.0|~5.0|~6.0", "vimeo/psalm": "^4.20"}, "type": "library", "autoload": {"psr-4": {"Helmich\\TypoScriptParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Parser for the TYPO3 configuration language TypoScript.", "homepage": "https://github.com/martin-helmich", "support": {"issues": "https://github.com/martin-helmich/typo3-typoscript-parser/issues", "source": "https://github.com/martin-helmich/typo3-typoscript-parser/tree/v2.5.0"}, "funding": [{"url": "https://donate.helmich.me", "type": "custom"}, {"url": "https://github.com/martin-helmich", "type": "github"}], "time": "2022-12-13T15:27:29+00:00"}, {"name": "nette/utils", "version": "v3.2.10", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/a4175c62652f2300c8017fb7e640f9ccb11648d2", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2", "shasum": ""}, "require": {"php": ">=7.2 <8.4"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "~2.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.10"}, "time": "2023-07-30T15:38:18+00:00"}, {"name": "phpstan/phpstan", "version": "1.10.27", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "a9f44dcea06f59d1363b100bb29f297b311fa640"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/a9f44dcea06f59d1363b100bb29f297b311fa640", "reference": "a9f44dcea06f59d1363b100bb29f297b311fa640", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2023-08-05T09:57:55+00:00"}, {"name": "rector/rector", "version": "0.17.0", "source": {"type": "git", "url": "https://github.com/rectorphp/rector.git", "reference": "d8da002b107c9b64d464bb48101290d4d078df4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rectorphp/rector/zipball/d8da002b107c9b64d464bb48101290d4d078df4b", "reference": "d8da002b107c9b64d464bb48101290d4d078df4b", "shasum": ""}, "require": {"php": "^7.2|^8.0", "phpstan/phpstan": "^1.10.15"}, "conflict": {"rector/rector-doctrine": "*", "rector/rector-downgrade-php": "*", "rector/rector-phpunit": "*", "rector/rector-symfony": "*"}, "bin": ["bin/rector"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.15-dev"}}, "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Instant Upgrade and Automated Refactoring of any PHP code", "keywords": ["automation", "dev", "migration", "refactoring"], "support": {"issues": "https://github.com/rectorphp/rector/issues", "source": "https://github.com/rectorphp/rector/tree/0.17.0"}, "funding": [{"url": "https://github.com/tomasvotruba", "type": "github"}], "time": "2023-06-01T09:42:59+00:00"}, {"name": "ssch/typo3-rector", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/sabbelasichon/typo3-rector.git", "reference": "fcbab6d4d884e1913b4bfb45594191ebebb01fd6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabbelasichon/typo3-rector/zipball/fcbab6d4d884e1913b4bfb45594191ebebb01fd6", "reference": "fcbab6d4d884e1913b4bfb45594191ebebb01fd6", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-simplexml": "*", "helmich/typo3-typoscript-parser": "^2.4.1", "nette/utils": "^3.0 || ^4.0", "nikic/php-parser": "^4.17", "php": "^7.4 || ^8.0", "phpstan/phpstan": "^1.9.7", "rector/rector": "0.17.0", "symfony/console": "^4.0 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/finder": "^5.4 || ^6.0", "symfony/polyfill-php80": "^1.26", "symfony/polyfill-php81": "^1.26", "symfony/string": "^5.0 || ^6.0", "symfony/yaml": "^5.0 || ^6.0", "webmozart/assert": "^1.0"}, "conflict": {"phpstan/phpstan": ">= 1.10.28"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "symfony/dependency-injection": "^5.0 || ^6.0", "symfony/http-kernel": "^5.0 || ^6.0", "symplify/easy-coding-standard": "^12.0"}, "type": "rector-extension", "extra": {"rector": {"includes": ["config/config.php"]}, "branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Ssch\\TYPO3Rector\\": "src", "Ssch\\TYPO3Rector\\PHPStan\\": "utils/phpstan/src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "Instant fixes for your TYPO3 code by using <PERSON>.", "homepage": "https://packagist.org/packages/ssch/typo3-rector", "keywords": ["dev"], "support": {"chat": "https://typo3.slack.com/archives/C019R5LAA6A", "docs": "https://github.com/sabbelasichon/typo3-rector/tree/main/docs", "issues": "https://github.com/sabbelasichon/typo3-rector/issues", "source": "https://github.com/sabbelasichon/typo3-rector"}, "funding": [{"url": "https://paypal.me/schreiberten", "type": "custom"}, {"url": "https://github.com/sabbelasichon", "type": "github"}], "time": "2024-01-16T00:48:10+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "platform-overrides": {"php": "7.4"}, "plugin-api-version": "2.6.0"}