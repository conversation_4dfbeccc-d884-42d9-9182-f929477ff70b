base: 'https://evu-kooperationen.rhenag.de/'
baseVariants:
  -
    base: 'https://evu.kooperationen.ddev.site/'
    condition: 'applicationContext == "Testing"'
  -
    base: 'https://stagingevu.rhgdev.de/'
    condition: 'applicationContext == "Production/Staging"'
  -
    base: 'https://stageevu.rhenag.de/'
    condition: 'applicationContext == "Production/Staging"'
errorHandling:
  -
    errorCode: 404
    errorHandler: Page
    errorContentSource: 't3://page?uid=113'
flux_content_types: ''
flux_page_templates: ''
languages:
  -
    title: Deutsch
    enabled: true
    base: /
    typo3Language: de
    locale: de_DE.UTF8
    iso-639-1: de
    navigationTitle: Deutsch
    hreflang: de-DE
    direction: ''
    flag: global
    languageId: 0
    websiteTitle: 'EVU Kooperationen'
rootPageId: 110
routeEnhancers:
  PageTypeSuffix:
    type: PageType
    default: .html
    map:
      rss.feed: 13
      .json: 26
      .conditions: 3132
      yoast-snippetpreview.json: 1480321830
routes:
  -
    route: sitemap.xml
    type: uri
    source: 'https://evu-kooperationen.rhenag.de/?sitemap=pages&type=1533906435'
  -
    route: robots.txt
    type: staticText
    content: "User-agent: *\r\nDisallow: /typo3/\r\nDisallow: /typo3_src/\r\nAllow: /typo3/sysext/frontend/Resources/Public/*\r\n"
websiteTitle: 'EVU Kooperationen'
imports:
  -
    resource: 'EXT:rhenag/Configuration/SiteConfiguration/evukooperationen.yaml'
