base: 'https://www.rhenag.de'
baseVariants:
  -
    base: 'https://stage.rhenag.de/'
    condition: 'applicationContext == "Production/Staging"'
  -
    base: 'https://rhenag.ddev.site/'
    condition: 'applicationContext == "Testing"'
  -
    base: 'https://staging.rhgdev.de/'
    condition: 'applicationContext == "Production/Staging"'
errorHandling:
  -
    errorCode: '404'
    errorHandler: Page
    errorContentSource: 't3://page?uid=108'
flux_content_types: ''
flux_page_templates: ''
languages:
  -
    title: Deutsch
    enabled: true
    base: /
    typo3Language: de
    locale: de_DE.UTF8
    iso-639-1: de
    navigationTitle: Deutsch
    hreflang: de-DE
    direction: ''
    flag: global
    languageId: '0'
rootPageId: 1
routeEnhancers:
  PageTypeSuffix:
    type: PageType
    default: .html
    map:
      rss.feed: 13
      .json: 26
      .conditions: 3132
      yoast-snippetpreview.json: 1480321830
  Seminare:
    type: Plugin
    limitToPages: [175,171]
    routePath: '/{action}/{seminar}'
    namespace: 'tx_rhenagevents_seminar'
    aspects:
      action:
        type: StaticValueMapper
        map:
          details: 'show'
      seminar:
        type: PersistedAliasMapper
        tableName: tx_rhenagevents_domain_model_seminar
        routeFieldName: slug
  Events:
    type: Plugin
    limitToPages: [175,171]
    routePath: '/{action}/{event}'
    namespace: 'tx_rhenagevents_event'
    aspects:
      action:
        type: StaticValueMapper
        map:
          details: 'show'
      event:
        type: PersistedAliasMapper
        tableName: tx_rhenagevents_domain_model_event
        routeFieldName: slug
routes:
  -
    route: sitemap.xml
    type: uri
    source: 'https://www.rhenag.de/?sitemap=pages&type=1533906435'
  -
    route: robots.txt
    type: staticText
    content: "User-agent: *\r\nDisallow: /typo3/\r\nDisallow: /typo3_src/\r\nAllow: /typo3/sysext/frontend/Resources/Public/*\r\n"
imports:
  -
    resource: 'EXT:rhenag/Configuration/SiteConfiguration/rhenag.yaml'


