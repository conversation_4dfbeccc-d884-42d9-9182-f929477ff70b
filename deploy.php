<?php
namespace Deployer;

require 'recipe/typo3.php';
require 'contrib/rsync.php';
require 'contrib/cachetool.php';

// Project name
set('application', 'RHENAG');
set('ssh_multiplexing', true); // Speed up deployment

set('rsync_src', function () {
    return __DIR__; // If your project isn't in the root, you'll need to change this.
});

set('typo3_webroot', 'public');

set('release_name', function () {
    return (string) run('date +"%Y-%m-%d_%H-%M-%S"');
});

set('shared_files', array(
    '{{typo3_webroot}}/typo3conf/LocalConfiguration.php',
    '{{typo3_webroot}}/.htaccess',
    '.env'
));

set('shared_dirs', [
    'var',
    '{{typo3_webroot}}/fileadmin',
    '{{typo3_webroot}}/typo3temp',
    '{{typo3_webroot}}/uploads'
]);

add('rsync', [
    'exclude' => [
        '.ddev',
        '.git',
        '/.env',
        '/storage/',
        '/node_modules/',
        '.github',
        'deploy.php'
    ],
    'options' => [
        'links'
    ]
]);

// Writable dirs by web server
add('writable_dirs', []);
set('allow_anonymous_stats', false);

// Hosts

host('stage')
    ->set('keep_releases', 5)
    ->set('hostname', 'rhenag.de')
    ->set('remote_user', 'ssh-579120-vancado')
    ->set('deploy_path', '/kunden/579120_50968/webseiten/rhenag/stage');



host('live')
    ->set('keep_releases', 5)
    ->set('hostname', 'rhenag.de')
    ->set('remote_user', 'ssh-579120-vancado')
    ->set('deploy_path', '/kunden/579120_50968/webseiten/rhenag/live');

// [Optional] if deploy fails automatically unlock.
after('deploy:failed', 'deploy:unlock');
//after('deploy:symlink', 'cachetool:clear:opcache');

desc('Update TYPO3 language files');
task('typo3:languageUpdate', function () {
    cd('{{release_path}}');
    run('exec vendor/bin/typo3cms language:update');
});

desc('Update TYPO3 database schema');
task('typo3:updateSchema', function () {
    cd('{{release_path}}');
    run('exec vendor/bin/typo3cms database:updateschema');
});

desc('Clear TYPO3 caches');
task('typo3:clearCacheAll', function () {
    cd('{{release_path}}');
    run('exec vendor/bin/typo3cms cache:flush');
});

task('deploy', [
    'deploy:info',
    'deploy:setup',
    'deploy:lock',
    'deploy:release',
    'rsync', // Deploy code & built assets
    'deploy:shared',
    'deploy:symlink',
    'deploy:unlock',
    'typo3:updateSchema',
    'typo3:languageUpdate',
    'typo3:clearCacheAll',
    'deploy:cleanup',
]);
