# Load default processing options
imports:
    - { resource: "EXT:rte_ckeditor/Configuration/RTE/Processing.yaml" }
    - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Base.yaml" }
    - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Plugins.yaml" }

# Add configuration for the editor
# For complete documentation see http://docs.ckeditor.com/#!/api/CKEDITOR.config
editor:
  config:
    contentsCss: ["EXT:rte_ckeditor/Resources/Public/Css/contents.css", "EXT:rhenag/Resources/Public/Css/rte.css"]
    format_tags: "p;h1;h2;h3"
    stylesSet:
      - { name: "Größere Typo - bigger", element: ['h1', 'h2', 'h3', 'h4','h5', 'h6', 'p', 'td', 'th'], attributes: { 'class': 'bigger' }}
      - { name: "<PERSON><PERSON> small", element: ['h1', 'h2', 'h3', 'h4','h5', 'h6', 'p', 'td', 'th'], attributes: { 'class': 'small' }}
      - { name: "Kleinere Typo - smaller", element: ['h1', 'h2', 'h3', 'h4','h5', 'h6', 'p', 'td', 'th'], attributes: { 'class': 'smaller' }}
      - { name: "Paragraph mit Spalten - columnized", element: ['h1', 'h2', 'h3', 'h4','h5', 'h6', 'p', 'td', 'th'], attributes: { 'class': 'columnized' }}
      - { name: "Spalten-Titel - col-title", element: ['h1', 'h2', 'h3', 'h4','h5', 'h6', 'p', 'td', 'th'], attributes: { 'class': 'col-title' }}
      - { name: "Zentriert - align-center", element: ['h1', 'h2', 'h3', 'h4','h5', 'h6', 'p', 'td', 'th'], attributes: { 'class': 'align-center' }}
      - { name: "Überschrift's Vorzeile - preline", element: ['h1', 'h2', 'h3', 'h4','h5', 'h6', 'p', 'td', 'th'], attributes: { 'class': 'preline' }}
      - { name: "Col List", element: ['ul'], attributes: { 'class': 'col-list' }}
      - { name: styles, groups: [ styles, format ] }
      - { name: basicstyles, groups: [ basicstyles ] }
      - { name: paragraph, groups: [ list, indent, blocks, align ] }
      - { name: links, groups: [ links ] }
      - { name: clipboard, groups: [ clipboard, cleanup, undo ] }
      - { name: editing, groups: [ spellchecker ] }
      - { name: insert, groups: [ insert ] }
      - { name: tools, groups: [ table, specialchar, insertcharacters ] }
      - { name: document, groups: [ mode ] }
      - { name: tabletools }
    justifyClasses:
      - text-left
      - text-center
      - text-right
      - text-justify

    extraPlugins:
      - justify
      - autolink
      - tabletools

    removePlugins:
      - image

    removeButtons:
      - Anchor
      - Strike
      - Subscript
      - Superscript
      - Blockquote
      - Scayt
      - HorizontalRule
      - softHyphen

    classesAnchor:
      downloadLink:
        class: 'link-download'
        type: 'file'
        title: 'Dateidownload'
      externalLink:
        class: ''
        type: 'url'
        titleText: ''
        target: '_blank'
      internalLink:
        class: ''
        type: 'page'
        titleText: ''
        target: ''
      mail:
        class: ''
        type: 'mail'
        titleText: ''

