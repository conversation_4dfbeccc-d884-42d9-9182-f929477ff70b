routeEnhancers:
  RhenagEventsPlugin:
    type: Extbase
    extension: RhenagEvents
    plugin: Event
    routes:
      -
        routePath: '/{slug}'
        _controller: 'Event::show'
        _arguments:
          slug: event
    defaultController: 'Event::list'
    aspects:
      slug:
        type: PersistedAliasMapper
        tableName: tx_rhenagevents_domain_model_event
        routeFieldName: slug
        routeValuePrefix: ''
    defaults:
      page: '0'
    requirements:
      page: \d+
  RhenagSeminarsPlugin:
    type: Extbase
    extension: RhenagEvents
    plugin: Seminar
    routes:
      -
        routePath: '/{slug}'
        _controller: 'Seminar::show'
        _arguments:
          slug: seminar
    defaultController: 'Seminar::list'
    aspects:
      slug:
        type: PersistedAliasMapper
        tableName: tx_rhenagevents_domain_model_seminar
        routeFieldName: slug
        routeValuePrefix: ''
    defaults:
      page: '0'
    requirements:
      page: \d+
  RhenagEventsArchivePlugin:
    type: Extbase
    extension: RhenagEvents
    plugin: Event
    routes:
      -
        routePath: '/{slug}'
        _controller: 'Event::show'
        _arguments:
          slug: event
    defaultController: 'Event::archiv'
    aspects:
      slug:
        type: PersistedAliasMapper
        tableName: tx_rhenagevents_domain_model_event
        routeFieldName: slug
        routeValuePrefix: ''
    defaults:
      page: '0'
    requirements:
      page: \d+
  News:
    type: Extbase
    extension: News
    plugin: Pi1
    routes:
      - routePath: '/'
        _controller: 'News::list'
      - routePath: '/page-{page}'
        _controller: 'News::list'
        _arguments:
          page: '@widget_0/currentPage'
      - routePath: '/{news-title}'
        _controller: 'News::detail'
        _arguments:
          news-title: news
      - routePath: '/{category-name}'
        _controller: 'News::list'
        _arguments:
          category-name: overwriteDemand/categories
      - routePath: '/{tag-name}'
        _controller: 'News::list'
        _arguments:
          tag-name: overwriteDemand/tags
    defaultController: 'News::list'
    defaults:
      page: '0'
    aspects:
      news-title:
        type: PersistedAliasMapper
        tableName: tx_news_domain_model_news
        routeFieldName: path_segment
      page:
        type: StaticRangeMapper
        start: '1'
        end: '100'
      category-name:
        type: PersistedAliasMapper
        tableName: sys_category
        routeFieldName: slug
      tag-name:
        type: PersistedAliasMapper
        tableName: tx_news_domain_model_tag
        routeFieldName: slug