<?php
use In2code\Powermail\Domain\Model\Field;
use In2code\Powermail\Domain\Model\Page;
use In2code\Powermail\Utility\ConfigurationUtility;



/**
 * Fieldtypes
 *        radio
 *
 */
$typeSponsoringradioform = 'pages, title, type, settings, ' .
    '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' . Field::TABLE_NAME . '.palette1;1, ' .
    '--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' . Field::TABLE_NAME . '.sheet1, ' .
    '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
        Field::TABLE_NAME . '.validation_title;21, ' .
    '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
        Field::TABLE_NAME . '.prefill_title;33, ' .
    '--palette--;Layout;43, ' .
    'description, ' .
    '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
        Field::TABLE_NAME . '.marker_title;5, ' .
    '--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
        'tabs.access, sys_language_uid, ' .
    'l10n_parent, l10n_diffsource, hidden, starttime, endtime';
	/**
	 * Fieldtypes
	 *        radio
	 *
	 */
$typeSponsoringradioformat = 'pages, title, type, settings, ' .
	                           '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' . Field::TABLE_NAME . '.palette1;1, ' .
	                           '--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' . Field::TABLE_NAME . '.sheet1, ' .
	                           '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
	                           Field::TABLE_NAME . '.validation_title;21, ' .
	                           '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
	                           Field::TABLE_NAME . '.prefill_title;33, ' .
	                           '--palette--;Layout;43, ' .
	                           'description, ' .
	                           '--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
	                           Field::TABLE_NAME . '.marker_title;5, ' .
	                           '--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:' .
	                           'tabs.access, sys_language_uid, ' .
	                           'l10n_parent, l10n_diffsource, hidden, starttime, endtime';

	$GLOBALS['TCA']['tx_powermail_domain_model_field']['types']['sponsoringradioform']['showitem']   = 'pages,title,type,settings,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.palette1;1,--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.sheet1,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.validation_title;21,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.prefill_title;33,--palette--;Layout;43,description,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.marker_title;5,--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tabs.access,sys_language_uid,l10n_parent,l10n_diffsource,hidden,starttime,endtime';
	$GLOBALS['TCA']['tx_powermail_domain_model_field']['types']['sponsoringradioformat']['showitem'] = 'pages,title,type,settings,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.palette1;1,--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.sheet1,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.validation_title;21,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.prefill_title;33,--palette--;Layout;43,description,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.marker_title;5,--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tabs.access,sys_language_uid,l10n_parent,l10n_diffsource,hidden,starttime,endtime';
	$GLOBALS['TCA']['tx_powermail_domain_model_field'][ 'types' ][ 1 ][ 'showitem' ] = 'pages,title,type,settings,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.palette1;1,--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.sheet1,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.validation_title;21,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.prefill_title;33,--palette--;Layout;43,description,--palette--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tx_powermail_domain_model_field.marker_title;5,--div--;LLL:EXT:powermail/Resources/Private/Language/locallang_db.xlf:tabs.access,sys_language_uid,l10n_parent,l10n_diffsource,hidden,starttime,endtime';
    