#
lib.searchfield = USER
lib.searchfield {
userFunc = TYPO3\CMS\Extbase\Core\Bootstrap->run
vendorName = TYPO3\CMS
extensionName = IndexedSearch
pluginName = Pi2
switchableControllerActions {
Search {
1 = form
2 = search

}
    }
features.requireCHashArgumentForActionArguments = 0
view = < plugin.tx_indexedsearch.view
view.partialRootPaths.10 = EXT:rhenag/Resources/Private/Extensions/indexedsearch/Resources/Private/Partials/Flyout/
view.templateRootPaths.10 = EXT:rhenag/Resources/Private/Extensions/indexedsearch/Resources/Private/Templates/Flyout/
settings = < plugin.tx_indexedsearch.settings

}

plugin.tx_indexedsearch.settings.displayRules = 0
plugin.tx_indexedsearch.settings.displayAdvancedSearchLink = 0
