#  Powermail Config


plugin.tx_powermail.settings.setup.main.inIframe = 0

plugin.tx_powermail {

	view {

		partialRootPaths.120 = EXT:rhenag_components/Resources/Private/Partials/		
	}

}


plugin.tx_powermail.settings.setup {
    # Prefill all fields {timestamppreventduplicates} with current timestamp
    prefill.timestamppreventduplicates = TEXT
    prefill.timestamppreventduplicates.data = date:U

    # turn unique validation off per default
    validation.unique.timestamppreventduplicates = 0

    # remove timestamp from powermail all variable
    excludeFromPowermailAllMarker {
        confirmationPage.excludeFromMarkerNames := addToList(timestamppreventduplicates)
        submitPage.excludeFromMarkerNames := addToList(timestamppreventduplicates)
        receiverMail.excludeFromMarkerNames := addToList(timestamppreventduplicates)
        senderMail.excludeFromMarkerNames := addToList(timestamppreventduplicates)
        optinMail.excludeFromMarkerNames := addToList(timestamppreventduplicates)
    }
}

# turn validation on if GET/POST param with field timestamppreventduplicates exists
[traverse(request.getQueryParams(), 'tx_powermail_pi1/field/timestamppreventduplicates') > 0]
    plugin.tx_powermail.settings.setup.validation.unique.timestamppreventduplicates = 1
[end]

# V 9.5 komp notation remove on updatate
[globalVar = GP:tx_powermail_pi1|field|timestamppreventduplicates > 0]
    plugin.tx_powermail.settings.setup.validation.unique.timestamppreventduplicates = 1
[global]