########################################
# Verfügb. Sprachen im System festlegen
########################################
config.linkVars = L
# -- Wenn Language Variable "L" n.A. -> Deutsch

config.sys_language_uid = 0
config.language = de
config.locale_all = de_DE.utf-8
config.htmlTag_langKey = de-DE
#plugin.tx_indexedsearch._DEFAULT_PI_VARS.lang = 0

plugin.tx_cookieconsent2._LOCAL_LANG.de {
    message =Cookies erleichtern die Bereitstellung unserer Dienste. Mit der Nutzung unserer Dienste erklären Sie sich damit einverstanden, dass wir Cookies verwenden.
    dismiss =OK!
    learnMore=Weiterlesen


}



# -- Wenn Language Variable "L" = 1 -> Englisch
#[globalVar=GP:L=1]
#config.sys_language_uid = 1
#config.language = en
#config.locale_all = en_EN
#config.htmlTag_langKey = en-US
#plugin.tx_indexedsearch._DEFAULT_PI_VARS.lang = 1
#[global]
