/*
Renders Mobile and regular Navigation, Social Media Links & meta Navigation
on Homepage online..
*/
lib.mainNavigation = COA
lib.mainNavigation {
	10 = COA
	10 {
		wrap = <ul class="main-nav">|</ul>

		10 = HMENU
		10 {
			excludeUidList = {$plugin.tx_rhenag.metaPages}
			entryLevel = 0
			1 = TMENU
			1 {
				expAll = 1
				NO = 1
				NO {
					wrapItemAndSub = <li>|</li>
					ATagParams.dataWrap = data-target="{field:subtitle}"
					#ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}

				ACT < .NO
				ACT {
					wrapItemAndSub = <li class="active">|</li>
				}

				IFSUB < .NO
				IFSUB {
					wrapItemAndSub = <li class="has-sub">|</li>
					ATagParams.dataWrap = data-target="{field:subtitle}"
					#ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}
				ACTIFSUB < .IFSUB
				ACTIFSUB {
					wrapItemAndSub = <li class="has-sub active">|</li>
				}
			}
			2 = TMENU
			2 {
				expAll = 0
				
				stdWrap {
					dataWrap = <div class="navbar-dropdown-container"><ul class="sub-nav">|</ul></div>
				}

				NO = 1
				NO {
					wrapItemAndSub = <li>|</li>
					ATagParams.dataWrap >
				}

				ACT < .NO
				ACT {
					wrapItemAndSub = <li class="active">|</li>
				}
			}
		}
	}
}
lib.navFlyoutContact = COA
lib.navFlyoutContact {
	wrap = <div class="nav-flyout" id="flyout-contact"><div class="fluid-container"><div class="row">|</div></div></div>

	10 = RECORDS
	10 {
		tables = tt_content
		source = {$plugin.tx_rhenag.flyout1}
		wrap = <div class="col col-1-5">|</div>
	}
    # social media links
    20 = COA
    20 {
        wrap = <div class="col col-1-5"><p class="col-title">Netzwerke</p><ul class="col-list">|</ul></div>
        10 = TEXT
        10 {
            value = Linkedin
            typolink {
                parameter = https://www.linkedin.com/company/rhenag-rheinische-energie-ag/
                ATagParams = class="link-some link-linkedin"
                target = _blank
                extTarget = _blank
            }

            wrap = <li>|</li>
        }
        20 = TEXT
        20 {
            value = XING
            typolink {
                parameter = https://www.xing.com/companies/rhenagrheinischeenergieag
                ATagParams = class="link-some link-xing"
                target = _blank
                extTarget = _blank
            }
            wrap = <li>|</li>
        }
        30 = TEXT
        30 {
            value = Youtube
            typolink {
                parameter = https://www.youtube.com/user/rhenag/videos
                ATagParams = class="link-some link-youtube"
                target = _blank
                extTarget = _blank
            }
            wrap = <li>|</li>
        }
    }

	30 = RECORDS
	30 {
		tables = tt_content
		source = {$plugin.tx_rhenag.flyout3}
		wrap = <div class="col col-1-5">|</div>
	}

	40 < lib.navFlyoutContact.30
	40.source = {$plugin.tx_rhenag.flyout4}

	50 < lib.navFlyoutContact.30
	50.source = {$plugin.tx_rhenag.flyout5}
}


lib.metaNavigation = COA
lib.metaNavigation {
	wrap = <ul class="meta-nav">|</ul>
	20 = TEXT
	20 {
		wrap = <li class="rhenag">|</li>
		value = <i class="btr icon-energie"></i>rhenag-Energie
		typolink {
			parameter = https://www.rhenag-energie.de/
            extTarget = _blank
		}
	}
	30 = TEXT
	30 {
		wrap = <li>|</li>
		value = <i class="btr bt-user-female"></i>Kontakt
		typolink {
			parameter = {$plugin.tx_rhenag.flyoutKontakt}
			ATagParams = class="" data-action="flyout" data-target="#flyout-contact"
		}
	}
	# 40 Element shows up on scrolling down with different design. Hidden on inital load
    40 < .30
	40.wrap = <li id="navi-contact">|</li>
    50 = TEXT
    50 {
        wrap = <li>|</li>
        value = <i class="btr bt-search"></i>Suche
        typolink {
            parameter = {$plugin.tx_rhenag.flyoutSuche}
            ATagParams = class="" data-action="flyout" data-target="#flyout-search"
        }
    }
}


lib.sidebarNavigation = COA
lib.sidebarNavigation {
	10 = COA
	10 {		

		10 = HMENU
		10 {
			excludeUidList = {$plugin.tx_rhenag.metaPages}
			entryLevel = 2
			1 = TMENU
			1 {
				wrap = <ul class="sub-menu">|</ul>

				expAll = 1
				NO = 1
				NO {
					wrapItemAndSub = <li>|</li>
					ATagParams.dataWrap = data-target="{field:subtitle}"
					#ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}

				ACT < .NO
				ACT {
					wrapItemAndSub = <li class="active">|</li>
				}

				IFSUB < .NO
				IFSUB {
					wrapItemAndSub = <li>|</li>
					ATagParams.dataWrap = data-target="{field:subtitle}"
					#ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}
			}
		}
	}
}

lib.extraNavigation = COA
lib.extraNavigation {
	10 = COA
	10 {
		wrap = <div class="extra-nav-container"><ul class="extra-nav">|</ul></div>

		10 = HMENU
		10 {
			excludeUidList = {$plugin.tx_rhenag.metaPages}
			entryLevel = 0
			1 = TMENU
			1 {
				expAll = 1
				NO = 1
				NO {
					wrapItemAndSub = <li>|</li>
					ATagParams.dataWrap = data-target="{field:subtitle}"
					#ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}

				ACT < .NO
				ACT {
					wrapItemAndSub = <li class="active">|</li>
				}

				IFSUB < .NO
				IFSUB {
					wrapItemAndSub = <li>|</li>
					ATagParams.dataWrap = data-target="{field:subtitle}"
					#ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}
			}
			2 = TMENU
			2 {
				expAll = 0
				
				stdWrap {
					dataWrap = <div class="extra-navbar-dropdown-container"><ul class="extra-sub-nav">|</ul></div>
				}

				NO = 1
				NO {
					wrapItemAndSub = <li>|</li>
					ATagParams.dataWrap >
				}

				ACT < .NO
				ACT {
					wrapItemAndSub = <li class="active">|</li>
				}
			}
		}
	}
}




