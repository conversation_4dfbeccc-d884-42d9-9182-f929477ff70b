/*
Renders Mobile and regular Navigation, Social Media Links & meta Navigation
on Homepage online
*/

lib.mobileNavigation = COA
lib.mobileNavigation {	
	

	20 = COA
	20 {
		wrap = <nav id="mobileNav"><ul>|</ul></nav>
		5 = TEXT
		5.value (
			<li class="search-container"><form action="index.php?id={$plugin.tx_rhenag.searchPageId}" method="post" class="hidden-phone navbar-search">
				<input name="tx_indexedsearch[sword]" placeholder="Suche" class="search-query" type="text"/>
				<input type="hidden" name="scols" value="tt_content.header-bodytext-imagecaption" />
				<input type="hidden" name="stype" value="L0" />
				<button class='' name="tx_indexedsearch[submit_button]">
					<i class="btr bt-search"></i>
				</button>
			</form></li>
		)
		7 = TEXT
		7.value (
			<li><a href="/"><i class="btr bt-home"></i></a></li>
		)
		10 = HMENU
		10 {
			#special = list
			#special.value = {$plugin.tx_rhenag.mainPages}
			excludeUidList = {$plugin.tx_rhenag.metaPages}
			entryLevel = 0
			1 = TMENU
			1 {
				expAll = 1
				NO = 1
				NO {
					wrapItemAndSub = <li>|</li>
					ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}

				ACT < .NO
				ACT {
					wrapItemAndSub = <li class="active">|</li>
				}

				IFSUB < .NO
				IFSUB {
					wrapItemAndSub = <li>|</li>
					ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}

			}
			2 = TMENU
			2 {
				expAll = 1
				
				stdWrap {
					dataWrap = <ul>|</ul>
				}

				NO = 1
				NO {
					wrapItemAndSub = <li>|</li>
					ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}

				ACT < .NO
				ACT {
					wrapItemAndSub = <li class="active">|</li>
				}

				IFSUB < .NO
				IFSUB {
					wrapItemAndSub = <li>|</li>
					ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}
			}
			3 = TMENU
			3 {
				expAll = 0
				
				stdWrap {
					dataWrap = <ul class="sub-nav">|</ul>
				}

				NO {
					wrapItemAndSub = <li>|</li>
					ATagTitle.field = nav_title // title
					allStdWrap.insertData = 1
				}
			}
		}
		20 = TEXT
		20.value (
			<li class="meta-item"><a href="https://rhenag-energie.de/privatkunden/" target="_blank">rhenag-Energie</a></li>
			<li class="meta-item"><a href="/kontakt">Kontakt</a></li>
		)
	}

}
