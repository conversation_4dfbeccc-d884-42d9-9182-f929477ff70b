TCEFORM {
	tx_powermail_domain_model_page {
		css {
			removeItems = layout1, layout2, layout3
			addItems {
				full = Groß
				half = <PERSON><PERSON><PERSON>
				half-last = <PERSON><PERSON><PERSON> H<PERSON>
			}
		}
	}

	tx_powermail_domain_model_field {
		css {
			removeItems = layout1, layout2, layout3
			addItems {
				field-full = Groß
				field-medium = Mittel
				field-small = Klein
			}
		}
	}
}

# The label could also be written with LLL: to localize the label
# Example to grab a value from locallang.xml or locallang.xlf
#tx_powermail.flexForm.type.addFieldOptions.new = LLL:EXT:ext/Resources/Private/Language/locallang.xlf:label

# Tell powermail that the new fieldtype will transmit anything else then a string (0:string, 1:array, 2:date, 3:file)
# Example for dataType array
#tx_powermail.flexForm.type.addFieldOptions.new.dataType = 1

# The new field is not just a "show some text" field. It's a field where the user can send values and powermail stores the values?
# You can tell powermail that this new field should be exportable in backend module and via CommandController
#tx_powermail.flexForm.type.addFieldOptions.new.export = 1
# <PERSON><PERSON>uch<PERSON> zu<PERSON> in ext_tables.php
#$GLOBALS['TCA']['tx_powermail_domain_model_field']['types']['sponsoringradioform']['showitem']


# Reset.html mit clearfix
tx_powermail.flexForm.type.addFieldOptions.reset  = Reset

# Sponsoringfiormular Feld "Form" (i.S.v. Art und Weise)
tx_powermail.flexForm.type.addFieldOptions.sponsoringradioform = Sponsoring (Anzeigenform)
tx_powermail.flexForm.type.addFieldOptions.sponsoringradioform.dataType = 1
tx_powermail.flexForm.type.addFieldOptions.sponsoringradioform.export = 1

# Sponsoringformular Feld "Format" (shape)
tx_powermail.flexForm.type.addFieldOptions.sponsoringradioformat = Sponsoring (Anzeigenformat)
tx_powermail.flexForm.type.addFieldOptions.sponsoringradioformat.dataType = 1
tx_powermail.flexForm.type.addFieldOptions.sponsoringradioformat.export = 1

# Sponsoringformular Feld "Höhe x Breite"
tx_powermail.flexForm.type.addFieldOptions.sponsoringheightwidth = Höhe x Breite
tx_powermail.flexForm.type.addFieldOptions.sponsoringheightwidth.dataType = 1
tx_powermail.flexForm.type.addFieldOptions.sponsoringheightwidth.export = 1

# Sponsoringformular Feld "Input Feld mit kleinem Label (ohne Nummerierung)"
tx_powermail.flexForm.type.addFieldOptions.sponsoringinput = Input Feld mit kleinem Label (ohne Nummerierung)
tx_powermail.flexForm.type.addFieldOptions.sponsoringinput.dataType = 0
tx_powermail.flexForm.type.addFieldOptions.sponsoringinput.export = 1

# Sponsoringformular Feld "Radio mit optionalem extra Textinput"
tx_powermail.flexForm.type.addFieldOptions.radiomittext = Radio mit optionalem extra Textinput
tx_powermail.flexForm.type.addFieldOptions.radiomittext.dataType = 1
tx_powermail.flexForm.type.addFieldOptions.radiomittext.export = 1


# Feld für Umfrage
tx_powermail.flexForm.type.addFieldOptions.poll = Umfrage
tx_powermail.flexForm.type.addFieldOptions.poll.dataType = 1
tx_powermail.flexForm.type.addFieldOptions.poll.export = 1
tx_powermail.flexForm.addField.settings\.flexform\.main\.progressbar._sheet = main
tx_powermail.flexForm.addField.settings\.flexform\.main\.progressbar.label = Fortschrittsbalken anzeigen
tx_powermail.flexForm.addField.settings\.flexform\.main\.progressbar.config.type = check
tx_powermail.flexForm.addField.settings\.flexform\.main\.progressbar.config.eval = trim