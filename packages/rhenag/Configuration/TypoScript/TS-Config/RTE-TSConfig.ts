# RTE Typical default configuration (TYPO3 default plus preconfigured extra-options to enable/disable with ease)
/*
from:
https://docs.typo3.org/typo3cms/extensions/rtehtmlarea/DefaultConfigurations/Typical/Index.html

This Configuration is based on the "Typical Default TYPO3 Konfiguration" ands adds some selected extra features
which are included and preprepared for configuration / customization.
but initally disabled by showButtons := removeFromList(foo,baz)!

*/


## Let use all the space available for more comfort.
TCEFORM.tt_content.bodytext.RTEfullScreenWidth = 100%
RTE.default >
RTE.default {
	## Markup options
	enableWordClean = 1
	removeTrailingBR = 1
	removeComments = 1
	removeTags = center, font, o:p, sdfield, strike
	removeTagsAndContents = link, meta, script, style, title
	useCSS = 0
	contentCSS = typo3conf/ext/rhenag/Resources/Public/Css/rte.css


	classesParagraph := addToList( bigger, small, smaller, col-title, columnized, align-center, preline )
	classesCharacter := addToList( bigger, small, smaller, col-title, columnized, align-center, preline )

	## Toolbar options
	## The TCA configuration may add buttons to the toolbar
	#
	showButtons = blockstylelabel, blockstyle, colors, textstyle
	showButtons := addToList(formatblock, bold, italic,underline,strike,blockstyle, subscript, superscript,fontstyle,fontsize,fontcolor)
	showButtons := addToList(orderedlist, unorderedlist, outdent, indent, textindicator)
	showButtons := addToList(insertcharacter, link, table, findreplace, chMode, removeformat, undo, redo, about)
	showButtons := addToList(toggleborders, tableproperties)
	showButtons := addToList(rowproperties, rowinsertabove, rowinsertunder, rowdelete, rowsplit)
	showButtons := addToList(columninsertbefore, columninsertafter, columndelete, columnsplit)
	showButtons := addToList(cellproperties, cellinsertbefore, cellinsertafter, celldelete, cellsplit, cellmerge)
	showButtons := addToList(image)


	# preconfigured extra options - disabled by Button-removal to macht default config
	#
	showButtons := removeFromList(textstylelabel, subscript, superscript, fontstyle,fontsize,fontcolor,textindicator)
	# splitted up table related buttons for more flexibility
	#showButtons := removeFromList(table,cellproperties, cellinsertbefore, cellinsertafter, celldelete, cellsplit, cellmerge,columninsertbefore, columninsertafter, columndelete, columnsplit,toggleborders, tableproperties)



	# Remap tags
	tags {
		b.remap = strong
		i.remap = em
	}


	# end images configD

	# Remove typical uncommon Items from Dropdown
	# Keep only Headlines 1-6 and rename them
	#buttons.formatblock.addItems = dev
	buttons.formatblock.removeItems = pre, div, address, article, aside, blockquote, footer, header, nav, section, h4, h5, h6
	buttons.formatblock.items {
		p.label = Absatz
		h1.label = Überschrift h1
		h2.label = Überschrift h2
		h3.label = Überschrift h3
		#h4.label = Überschrift h4
		#h5.label = Überschrift h5
		#h6.label = Überschrift h6
	}

	## Enable status bar
	showStatusBar = 1
	## Allow <BR> tags in RTE
	keepButtonGroupTogether = 1
	## Hide infrequently used block types in the block formatting selector


	## List all class selectors that are allowed on the way to the database
	## Adding img tag to the default list
	proc.allowTagsOutside := addToList(img)
	proc.denyTags := addToList(font, u, strike, nobr, center)
	proc.dontConvBRtoParagraph = 1
	## Do not remove img tags
	proc.entryHTMLparser_db.tags.img >
	proc.entryHTMLparser_db.allowTags := addToList(strong)
	proc.exitHTMLparser_rte.allowTags := addToList(strong)
	proc.allowedClasses = link-extern, link-extern-2, link-intern, link-with-arrow, link-with-arrow-down, link-download, mail, link-normal, link-highlighted, link-callback
	proc.allowedClasses := addToList(link-extern, link-extern-2, link-intern, link-with-arrow, link-with-arrow-down, link-download, mail, link-normal, link-highlighted, link-callback)
	proc.allowedClasses := addToList(align-left, align-center, align-right, align-justify)
	proc.allowedClasses := addToList(csc-frame-frame1, csc-frame-frame2)
	proc.allowedClasses := addToList(component-items, action-items)
	proc.allowedClasses := addToList(component-items-ordered, action-items-ordered)
	proc.allowedClasses := addToList(indent)
	proc.allowedClasses := addToList(bigger, small, smaller, col-title)
	proc.allowedClasses := addToList( btn, btn-lg, btn-primary, btn-secondary )
	proc.allowedClasses := addToList(btn-secondary btn, btn-secondary btn-lg,btn-primary btn, btn-primary btn-lg)
	proc.allowedClasses := addToList(col-list)
	proc.allowedClasses := addToList(columnized, preline, align-center)
	proc.allowedClasses := addToList(h1, h2, h3)
	proc.allowedClasses := addToList(h1-align-center, h2-align-center, h3-align-center)
	proc.allowedClasses := addToList(h1-alternative, h2-alternative)
	proc.allowedClasses := addToList(h1-alternative-align-center, h2-alternative-align-center)

	## Restrict the list of class selectors presented by the RTE to the following for the specified tags:
	# writing the class names after the select box entry for more info
	buttons.blockstyle.postfixLabelWithClassName = 1

	buttons.blockstyle.tags.div.allowedClasses = align-left, align-center, align-right, bigger, small, smaller, col-title, columnized, align-center,  preline
	buttons.blockstyle.tags.div.allowedClasses := addToList(csc-frame-frame1, csc-frame-frame2, bigger, small, smaller, col-title, columnized, align-center,  preline)
	buttons.blockstyle.tags.table.allowedClasses = csc-frame-frame1, csc-frame-frame2
	buttons.blockstyle.tags.td.allowedClasses = align-left, align-center, align-right, bigger, small, smaller, col-title
	buttons.blockstyle.tags.p.allowedClasses = bigger, small, smaller, col-title, columnized, align-center, preline
	buttons.textstyle.tags.span.allowedClasses = important, name-of-person, detail
	buttons.textstyle.tags.h1.allowedClasses = h1, h2, h3, h1-alternative, h2-alternative
	buttons.textstyle.tags.h2.allowedClasses = h1, h2, h3, h1-alternative, h2-alternative
	buttons.textstyle.tags.h3.allowedClasses = h1, h2, h3, h1-alternative, h2-alternative
	buttons.textstyle.tags.h1.allowedClasses = h1-align-center, h2-align-center, h3-align-center, h1-alternative-align-center, h2-alternative-align-center
	buttons.textstyle.tags.h2.allowedClasses = h1-align-center, h2-align-center, h3-align-center, h1-alternative-align-center, h2-alternative-align-center
	buttons.textstyle.tags.h3.allowedClasses = h1-align-center, h2-align-center, h3-align-center, h1-alternative-align-center, h2-alternative-align-center

	## These classes should also be in the list proc.allowedClasses (see above -> must match!)
	buttons.link.properties.class.allowedClasses = link-extern, link-extern-2, link-intern, link-with-arrow, link-with-arrow-down, link-download, mail, link-normal, link-highlighted, btn-primary btn , btn-secondary btn,btn-primary btn-lg,btn-secondary btn-lg, link-callback
	buttons.link.page.properties.class.default =
	buttons.link.url.properties.class.default =
	buttons.link.file.properties.class.default = link-download
	buttons.link.mail.properties.class.default = mail

	## Configuration specific to the TableOperations feature
	## Remove the following fieldsets from the properties popups
	disableAlignmentFieldsetInTableOperations = 1
	disableSpacingFieldsetInTableOperations = 1
	disableColorFieldsetInTableOperations = 1
	disableLayoutFieldsetInTableOperations = 1

	## Show borders on table creation
	buttons.toggleborders.setOnTableCreation = 1


}

#}

RTE.classes {
	bigger {
		name = Größere Typo
	}

	small {
		name = Kleine Typo
	}

	smaller {
		name = Kleinere Typo
	}

	col-title {
		name = Spalten-Titel
	}

	align-left {
		name = Linksbündig
		value = text-align: left;
	}

	align-justify {
		name = Blocksatz
		value = text-align: justify;
	}

	align-center {
		name = Zentriert
		value = text-align: center;
	}

	align-right {
		name = Rechtsbündig
		value = text-align: right;
	}

	col-list {
		name = Spalten-Liste
	}

	columnized {
		name = Paragraph mit Spalten
	}

	preline {
		name = Überschrift's Vorzeile
	}


}

RTE.classesAnchor {
	linkExtern {
		class = link-extern
		type = url
		titleText = Externer Link
	}

	linkExtern2 {
		class = link-extern-2
		type = url
		titleText = Externer Link
	}

	linkCallback {
		class = link-callback
		type = page
		titleText = Link mit Telefon Icon
	}

	link-normal {
		class = link-normal
		type = url
		titleText = Normaler Link
	}

	link-highlighted {
		class = link-highlighted
		type = url
		titleText = Highlighted Link
	}

	linkWithArrow {
		class = link-with-arrow
		type = page
		titleText = Link mit Pfeil
	}

	# Das gleiche nochmal fuer externe Links
	linkWithArrowExternal {
		class = link-with-arrow
		type = url
		titleText = Link mit Pfeil
	}

	linkWithArrowDown {
		class = link-with-arrow-down
		type = page
		titleText = Link mit Pfeil unten
	}

	# Das gleiche nochmal fuer externe Links

	linkWithArrowDownExternal {
		class = link-with-arrow-down
		type = page
		titleText = Link mit Pfeil unten
	}

	linkIntern {
		class = link-intern
		type = page
		titleText = Interne Seite
	}

	linkDownload {
		class = link-download
		type = file
		titleText = Dateidownload
	}

	mail {
		class = mail
		type = mail
		titleText = E-Mail Adresse
	}

	// Button Styles in Link-Wiz, reworked, mp 25.09.19
	btn {
		class = btn
		type = url,page
		titleText = Button
	}

	btn-lg {
		class = btn-lg
		type = url,page
		titleText = Groß
	}

	btn-primary {
		class = btn-primary
		type = url,page
		titleText = Primärer Button
	}

	btn-secondary {
		class = btn-secondary
		type = url,page
		titleText = Sekundärer Button
	}


	/*
	btn-primary {
	class = btn btn-primary
	type = page
	titleText = Primärer Button
	#value = background-color: #ff8d2f; border-radius: 1em; color: #fff; line-height: 1;  padding: 0.4em 0.8em;  text-decoration: none;  transition: all 0s ease 0s, all 0.5s ease 0s, all 0s ease-in 0;
	}
	# Das gleiche nochmal fuer externe Links
	btn-primary {
	class = btn btn-primary
	type = url
	titleText = Primärer Button
	#value = background-color: #ff8d2f; border-radius: 1em; color: #fff; line-height: 1;  padding: 0.4em 0.8em;  text-decoration: none;  transition: all 0s ease 0s, all 0.5s ease 0s, all 0s ease-in 0;
	}
	btn-primary {
	class = btn-primary btn-lg
	type = page
	titleText = Primärer Button Groß
	}
	# Das gleiche nochmal fuer externe Links
	btnPrimaryBtnLgExternal {
	class = btn-primary btn-lg
	type = url
	titleText = Primärer Button Groß
	}
	btnSecondary {
	class = btn btn-secondary
	type = page
	titleText = Sekundärer Button
	}
	btnSecondaryExternal {
	class = btn btn-secondary
	type = url
	titleText = Sekundärer Button
	}
	btnSecondaryLg {
	class = btn-secondary btn-lg
	type = page
	titleText = Sekundärer Button Groß
	}
	btnSecondaryLgExternal {
	class = btn-secondary btn-lg
	type = url
	titleText = Sekundärer Button Groß
	}
	*/
}

# Button "colors", "fonts", "fontsize" -> settings

RTE.colors {
	vnc_orange {
		name = Vancado Orange
		value = #EA4D04
	}

	vnc_lightblue {
		name = Vancado Hellblau
		value = #2980b9
	}

	vnc_darkbrown {
		name = Vancado Dunkelbraun
		value = #302a29
	}
}

# General configuration of the available fonts:
RTE.fonts {
	sansserif {
		name = Sans Serif Font
		value = Arial, Helvetica, sans-serif
	}

	serif {
		name = Serif Font
		value = 'Times New Roman', Times, serif
	}
}

# Button "colors", "fonts", "fontsize" -> items
# Specific setting for the fontsize selector:
RTE.default.buttons.fontsize.removeItems = 1,2,3,4,5,6,7
RTE.default.buttons.fontsize.addItems = normal, small
# Specific setting for the fontstyle selector:
RTE.default.buttons.fontstyle.removeItems = *
RTE.default.buttons.fontstyle.addItems = sansserif, serif, noFace
# Specific setting for the font color selector:
RTE.default.colors = vnc_orange,vnc_lightblue,vnc_darkbrown


## front end RTE configuration for the general public
RTE.default.FE < RTE.default
RTE.default.FE.showStatusBar = 0
RTE.default.FE.hideButtons = chMode, underline, strikethrough, subscript, superscript, lefttoright, righttoleft, left, center, right, justifyfull, table, inserttag, findreplace, removeformat, copy, cut, paste
RTE.default.FE.userElements >