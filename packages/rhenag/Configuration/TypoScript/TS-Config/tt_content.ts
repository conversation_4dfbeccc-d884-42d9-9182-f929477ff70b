# IMPORTANT!!!!!
# Included in Page-TS config!!!!!!

TCEFORM.tt_content.header_layout {
    altLabels {
        0 = Nicht Sichtbar
        1 = Überschrift 1
        2 = Überschrift 1 Zentriert
        3 = Überschrift 1 Alternativ
        4 = Überschrift 1 Alternativ Zentriert
        5 = Überschrift 2
        6 = Überschrift 2 Zentriert
        7 = Überschrift 2 Alternativ
        8 = Überschrift 2 Alternativ Zentriert
    }
 
    removeItems = 100
    addItems {
        6 = Überschrift 2 Zentriert
        7 = Überschrift 2 Alternativ
        8 = Überschrift 2 Alternativ Zentriert
    }

}

# Table Layouts
TCEFORM.tt_content.layout.types.table {
    removeItems = 1,2,3
    addItems {
           4 = Prices Striped
           5 = Prices Light
           6 = Left Align Striped
           7 = Left Align Light
    }
}

// Vers 8.7 fluidcontent default anpassungen, mp 11.2018
// unterbindet standard wrap aller Celemente mit ehem. section_frame div class= usw.
#TCAdefaults.tt_content.frame_class = none
// deaktiviert das feld im Form
#TCEFORM.tt_content.frame_class.disabled = 1


TCEMAIN.permissions {
    # User can do anything (default):
    user = 31
    # Group can do anything
    # (normally "delete" is disabled)
    group = 31
    # Everybody can at least see the page
    # (normally everybody can do nothing)
    everybody = show
}