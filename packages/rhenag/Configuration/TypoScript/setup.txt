<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Config/Config.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Page/Page.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Language/Language.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Navigation/MainNavigation.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Navigation/MobileNavigation.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Libraries/Libraries.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Extensions/indexed_search.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Extensions/powermail.ts">
<INCLUDE_TYPOSCRIPT: source="FILE:typo3conf/ext/rhenag/Configuration/TypoScript/Extensions/news.ts">

plugin.tx_rhenag {
	view {
		templateRootPaths.0 = {$plugin.tx_rhenag.view.templateRootPaths.0}
		partialRootPaths.0 = {$plugin.tx_rhenag.view.partialRootPaths.0}
		layoutRootPaths.0 = {$plugin.tx_rhenag.view.layoutRootPaths.0}
	}
	#By default the following settings only will have relevance if you have fluidcontent_core extension loaded
	settings{
		container {
			types {
				default = div
				Example = div
			}
		}
		flyoutSearch = {$plugin.tx_rhenag.searchPageId}
	}
}
