<html data-namespace-typo3-fluid="true" lang="en"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers">

  <div class="dpnglossary details">
    <dl>
      <dt>{f:translate(key: 'tx_dpnglossary_domain_model_term.name')}: {term.name}</dt>
      <dd><strong>{f:translate(key: 'tx_dpnglossary_domain_model_term.descriptions')}:</strong>
        <f:for each="{term.descriptions}" as="description" iteration="iterator">
          <h3>{description.meaning}</h3>
          <div class="text">
            <f:format.html>{description.text}</f:format.html>
          </div>
          <f:if condition="false === {iterator.isLast}">
            <hr/>
          </f:if>
        </f:for>
      </dd>
      <f:if condition="{term.synonyms}">
        <dd>{f:translate(key: 'tx_dpnglossary_domain_model_term.synonyms')}: {f:if(condition: synonymIterator.isLast, then: '{synonym.name}', else: '{synonym.name}, ') -> f:for(each: term.synonyms, iteration: 'synonymIterator', as: 'synonym')}</dd>
      </f:if>
      <f:if condition="{term.termType}">
        <dd>
          {f:translate(key: 'tx_dpnglossary_domain_model_term.term_type')}: {term.termType}
        </dd>
      </f:if>
      <f:if condition="{term.termLang}">
        <dd>{f:translate(key: 'tx_dpnglossary_domain_model_term.term_lang')}: {term.termLang}</dd>
      </f:if>
      <f:if condition="{term.media}">
        <dd class="media">
          {f:translate(key: 'tx_dpnglossary_domain_model_term.media')}:<br/>
          <f:for each="{term.media}" as="media">
            <div class="mediafile">
              <f:media file="{media}" title="{media.originalResource.title}" alt="{media.originalResource.alternative}" width="700" height="m300"/>
            </div>
          </f:for>
        </dd>
      </f:if>
    </dl>
  </div>

</html>
