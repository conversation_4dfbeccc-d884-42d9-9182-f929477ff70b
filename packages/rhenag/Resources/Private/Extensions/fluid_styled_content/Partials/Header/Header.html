<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:switch expression="{layout}">
	<f:case value="0">
		<f:comment> -- do not show header -- </f:comment>
	</f:case>
	<f:case value="1">
		<h1 class="h1">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h1>
	</f:case>
	<f:case value="2">
		<h1 class="h1" style="text-align:center">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h1>
	</f:case>
	<f:case value="3">
		<h1 class="h1-alternative">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h1>
	</f:case>
	<f:case value="4">
		<h1 class="h1-alternative" style="text-align:center">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h1>
	</f:case>

	<f:case value="5">
		<h2 class="h2">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h2>
	</f:case>
	<f:case value="6">
		<h2 class="h2" style="text-align:center">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h2>
	</f:case>
	<f:case value="7">
		<h2 class="h2-alternative">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h2>
	</f:case>
	<f:case value="8">
		<h2 class="h2-alternative" style="text-align:center">
			<f:link.typolink parameter="{link}">{header}</f:link.typolink>
		</h2>
	</f:case>
	<f:defaultCase>
		<f:if condition="{default}">
			<f:render partial="Header/Header" arguments="{
                header: header,
                layout: default,
                link: link}" />
		</f:if>
	</f:defaultCase>
</f:switch>

</html>