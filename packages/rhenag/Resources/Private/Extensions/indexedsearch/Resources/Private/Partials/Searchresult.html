

<div class="tx-indexedsearch-res">
	<h3>
		<span class="tx-indexedsearch-icon">{row.icon -> f:format.raw()}</span>
		<f:if condition="{settings.displayResultNumber}"><span class="tx-indexedsearch-result-number">{row.result_number}</span></f:if>
		<span class="tx-indexedsearch-title{row.CSSsuffix}">{row.title -> f:format.raw()}</span>
		<span class="tx-indexedsearch-percent">{row.rating}</span>
	</h3>
		<p class="tx-indexedsearch-description">{row.description -> f:format.raw()}</p>
	<f:if condition="{row.subresults}">
		<p class="tx-indexedsearch-list">
			<f:for each="{row.subresults.items}" as="subrow">
				<f:render partial="Searchresult" arguments="{row: subrow}" />
			</f:for>
		</p>
	</f:if>
</div>

