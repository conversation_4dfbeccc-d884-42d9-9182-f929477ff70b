<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
	 xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
	 xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
	 xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">



<f:comment><f:render partial="Form" arguments="{_all}" /></f:comment>
	<f:if condition="{v:page.info(field: 'uid')}=={settings.targetPid}">
		<f:then>
			<f:render partial="Form" arguments="{_all}" />
		</f:then>
		<f:else>
			<f:render partial="Flyout/Form" arguments="{_all}" />
		</f:else>
	</f:if>



</html>
