<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
	"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>indexed_search template</title>
</head>

<body>

<h1>Indexed Search: Default template</h1>


<h2>TEMPLATE_SEARCH_FORM</h2>
<p><em>Template for displaying the search form.</em></p>

<!-- ###SEARCH_FORM### begin -->
<div class="tx-indexedsearch-searchbox">
<form action="###ACTION_URL###" method="post" id="tx_indexedsearch">
	<input type="hidden" name="tx_indexedsearch[_sections]" value="0" />
	<input type="hidden" name="tx_indexedsearch[_freeIndexUid]" id="tx_indexedsearch_freeIndexUid" value="_" />
	<input type="hidden" name="tx_indexedsearch[pointer]" id="tx_indexedsearch_pointer" value="0" />
	<!-- ###HIDDEN_FIELDS### begin -->
	<input type="hidden" name="###HIDDEN_FIELDNAME###" value="###HIDDEN_VALUE###" />
	<!-- ###HIDDEN_FIELDS### end -->

	<div class="input-append searchfield">
		<input type="search" name="tx_indexedsearch[sword]" placeholder="Suchen" value="###SWORD_VALUE###" class="ui-autocomplete-input tx-indexedsearch-searchbox-sword sword" ###PLACEHOLDER### />
		<button type="submit" name="tx_indexedsearch[submit_button]" value="###FORM_SUBMIT###" class="tx-indexedsearch-searchbox-button submit"><i class="btr bt-search"></i></button>
	</div>

		
	<p>###LINKTOOTHERMODE###</p>
</form>
</div>
<!-- ###SEARCH_FORM### end -->
<br /><br />




<h2>TEMPLATE_RULES</h2>
<p><em>Template for displaying the search rules.</em></p>

<!-- ###RULES### begin -->
	<div class="tx-indexedsearch-rules">
		<h2>###RULES_HEADER###</h2>
		<p>###RULES_TEXT###</p>
	</div>
<!-- ###RULES### end -->
<br /><br />




<h2>TEMPLATE_RESULT_SECTION_LINKS</h2>
<p><em>Template for the section links in section mode.</em></p>

<!-- ###RESULT_SECTION_LINKS### begin -->
	<div class="tx-indexedsearch-sec">
		<table cellpadding="0" cellspacing="0" border="0" summary="Result links">
			###LINKS###
		</table>
	</div>
<!-- ###RESULT_SECTION_LINKS### end -->

<!-- ###RESULT_SECTION_LINKS_LINK### begin -->
			<tr>
				<td width="100%">--&gt; ###LINK###</td>
			</tr>
<!-- ###RESULT_SECTION_LINKS_LINK### end -->
<br /><br />




<h2>TEMPLATE_SECTION_HEADER</h2>
<p><em>Template for the section title in section mode.</em></p>
<!-- ###SECTION_HEADER### begin -->
	<div class="tx-indexedsearch-secHead"><a name="###ANCHOR_URL###"></a>
		<table cellpadding="0" cellspacing="0" border="0" summary="Section header">
			<tr>
				<td class="tx-indexedsearch-title title" width="100%">###SECTION_TITLE###</td>
				<td class="tx-indexedsearch-result-count result-count" nowrap="nowrap">###RESULT_COUNT### ###RESULT_NAME###</td>
			</tr>
		</table>
	</div>

<!-- ###SECTION_HEADER### end -->
<br /><br />




<h2>TEMPLATE_RESULT_OUTPUT</h2>
<p><em>Template for the search result list.</em></p>

<!-- ###RESULT_OUTPUT### begin -->
	<div class="tx-indexedsearch-res">
		<table cellpadding="0" cellspacing="0" border="0" summary="Result row">
			<!-- ###HEADER_ROW### begin -->
			<tr>
				<td class="tx-indexedsearch-icon icon" nowrap="nowrap">###ICON###</td>
				<td class="tx-indexedsearch-title###CSSSUFFIX### title" width="100%">###TITLE###</td>
			</tr>
			<!-- ###HEADER_ROW### end -->

			<!-- ###ROW_LONG### begin -->
			<tr>
				<td>&nbsp;</td>
				<td class="tx-indexedsearch-descr descr" width="100%" >###DESCRIPTION###</td>
			</tr>
			<!-- ###ROW_LONG### end -->

			<!-- ###ROW_SHORT### begin -->
			<tr>
				<td>&nbsp;</td>
				<td class="tx-indexedsearch-descr descr" width="100%" >###DESCRIPTION###</td>
			</tr>
			<!-- ###ROW_SHORT### end -->

			<!-- ###ROW_SUB### begin -->
			<tr>
				<td>&nbsp;</td>
				<td class="tx-indexedsearch-list list" width="100%" ><br />###TEXT_ROW_SUB###<br /><br /></td>
			</tr>
			<!-- ###ROW_SUB### end -->
		</table>
		<br />
	</div>
<!-- ###RESULT_OUTPUT### end -->
<br /><br />


</body>
</html>
