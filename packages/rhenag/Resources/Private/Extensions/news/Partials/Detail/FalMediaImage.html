{namespace n=<PERSON><PERSON><PERSON>\News\ViewHelpers}

<div class="mediaelement mediaelement-image mage-popup-link">
	
	<f:if condition="{mediaElement.link}">
		<f:then>
			<f:link.page pageUid="{mediaElement.link}" target="{n:targetLink(link:mediaElement.link)}">
				<f:image image="{mediaElement}" title="{mediaElement.title}" alt="{mediaElement.alternative}" maxWidth="{settings.detail.media.image.maxWidth}" maxHeight="{settings.detail.media.image.maxHeight}" />
			</f:link.page>
		</f:then>
		<f:else>
			<f:if condition="{settings.detail.media.image.lightbox.enabled}">
				<f:then>
					<a href="{f:uri.image(image:'{mediaElement}', width:'{settings.detail.media.image.lightbox.width}', height:'{settings.detail.media.image.lightbox.height}')}" title="{mediaElement.title}" class="{settings.detail.media.image.lightbox.class}" rel="{settings.detail.media.image.lightbox.rel}">
						<f:image image="{mediaElement}" title="{mediaElement.title}" alt="{mediaElement.alternative}" maxWidth="{settings.detail.media.image.maxWidth}" maxHeight="{settings.detail.media.image.maxHeight}" />
					</a>
				</f:then>
				<f:else>
					<a href="{f:uri.image(treatIdAsReference:'1', src:'{mediaElement.uid}', maxWidth:'900')}" class="image-popup-link" title="{mediaElement.description}">
						<f:image image="{mediaElement}" title="{mediaElement.title}" alt="{mediaElement.alternative}" maxWidth="{settings.detail.media.image.maxWidth}" maxHeight="{settings.detail.media.image.maxHeight}" />
					</a>
				</f:else>
			</f:if>
		</f:else>
	</f:if>

<f:if condition="{mediaElement.description}">
	<p class="news-img-caption">
		{mediaElement.description}
	</p>
</f:if>
</div>
<style>
.news .article .news-img-wrap a{
	border: none;
	padding: 0;
}
.news-img-wrap{
	z-index: 50;
}
</style>