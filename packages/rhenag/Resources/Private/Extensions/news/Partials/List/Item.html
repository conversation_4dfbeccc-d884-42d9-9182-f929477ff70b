{namespace n=<PERSON><PERSON><PERSON>\News\ViewHelpers}

<div class="article articletype-{newsItem.type}{f:if(condition: newsItem.istopnews, then: ' topnews')}" itemscope="itemscope" itemtype="http://schema.org/Article">
	<n:excludeDisplayedNews newsItem="{newsItem}" />
	<div class="news-header">
		<p>
			<!-- date -->
			<span class="news-list-date" style="{f:if(condition: newsItem.firstCategory, then: '', else:'border:none;')}">
				<time datetime="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}">
					<f:format.date format="{f:translate(key:'dateFormat')}">{newsItem.datetime}</f:format.date>
					<meta itemprop="datePublished" content="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}" />
				</time>
			</span>

			<f:if condition="{newsItem.firstCategory}">
				<!-- first category -->
				<span class="news-list-category">{newsItem.firstCategory.title}</span>
			</f:if>

			<f:if condition="{newsItem.tags}">
				<!-- Tags -->
				<span class="news-list-tags" itemprop="keywords">
					<f:for each="{newsItem.tags}" as="tag">
						{tag.title}
					</f:for>
				</span>
			</f:if>

			<!-- author -->
			<f:if condition="{newsItem.author}">
				<span class="news-list-author">
					<f:translate key="author" arguments="{0:newsItem.author}"/>
				</span>
			</f:if>
		</p>
		<h3 class="promobox-title">{newsItem.title}</h3>

	</div>
	<div class="clearfix"></div>

    <f:if condition="{newsItem.mediaPreviews}">
        <!-- media preview element -->
        <f:then>
            <div class="news-img-wrap">
                <n:link newsItem="{newsItem}" settings="{settings}" title="{newsItem.title}">
                    <f:alias map="{mediaElement: newsItem.mediaPreviews.0}">
                        <f:if condition="{mediaElement.originalResource.type} == 2">
                            <f:image image="{mediaElement}" title="{mediaElement.originalResource.title}" alt="{mediaElement.originalResource.alternative}" loading="{settings.list.media.image.lazyLoading}" maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}" maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"/>
                        </f:if>
                        <f:if condition="{mediaElement.originalResource.type} == 4">
                            <f:render partial="Detail/MediaVideo" arguments="{mediaElement: mediaElement}"/>
                        </f:if>
                        <f:if condition="{mediaElement.originalResource.type} == 5">
                            <f:image image="{mediaElement}" title="{mediaElement.originalResource.title}" alt="{mediaElement.originalResource.alternative}" loading="{settings.list.media.image.lazyLoading}" maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}" maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"/>
                        </f:if>
                    </f:alias>
                </n:link>
            </div>
        </f:then>
        <f:else>
            <f:if condition="{settings.displayDummyIfNoMedia}">
                <div class="news-img-wrap">
					<span class="no-media-element">
						<n:link newsItem="{newsItem}" settings="{settings}" title="{newsItem.title}">
							<f:image src="{settings.list.media.dummyImage}" title="" alt="" loading="{settings.list.media.image.lazyLoading}" maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}" maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"/>
						</n:link>
					</span>
                </div>
            </f:if>
        </f:else>
    </f:if>


	<!-- teaser -->
	<div class="teaser-text">

		<f:if condition="{newsItem.teaser}">
			<f:then>
				<div itemprop="description">{newsItem.teaser -> f:format.crop(maxCharacters: '{settings.cropMaxCharacters}', respectWordBoundaries:'1') -> f:format.html()}</div>
			</f:then>
			<f:else>
				<div itemprop="description">{newsItem.bodytext -> f:format.crop(maxCharacters: '{settings.cropMaxCharacters}', respectWordBoundaries:'1') -> f:format.html()}</div>
			</f:else>
		</f:if>

		<n:link newsItem="{newsItem}" settings="{settings}" class="link-with-arrow" title="{newsItem.title}">
			<f:translate key="more-link" />
		</n:link>
	</div>
</div>