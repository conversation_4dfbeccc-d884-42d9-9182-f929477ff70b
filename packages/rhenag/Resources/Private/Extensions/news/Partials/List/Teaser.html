{namespace n=<PERSON><PERSON><PERSON>\News\ViewHelpers}
<div class="home-news-item {f:if(condition:'{iterator.isLast}', then:'last')}">
	<span class="news-list-date">
		<time datetime="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}">
			<f:format.date format="{f:translate(key:'dateFormat')}">{newsItem.datetime}</f:format.date>
			<meta itemprop="datePublished" content="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}" />
		</time>
	</span>
	<h3 class="teaser-landingpage-title home-news-header">{newsItem.title}</h3>
	<f:if condition="{newsItem.teaser}">
		<f:then>
			<div itemprop="description">{newsItem.teaser -> f:format.crop(maxCharacters: '{settings.cropMaxCharacters}', respectWordBoundaries:'1') -> f:format.html()}</div>
		</f:then>
		<f:else>
			<div itemprop="description">{newsItem.bodytext -> f:format.crop(maxCharacters: '{settings.cropMaxCharacters}', respectWordBoundaries:'1') -> f:format.html()}</div>
		</f:else>
	</f:if>
	<n:link newsItem="{newsItem}" settings="{settings}" class="link-with-arrow" title="{newsItem.title}">
		<f:translate key="more-link"/>
	</n:link>
</div>