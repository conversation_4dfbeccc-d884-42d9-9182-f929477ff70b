{namespace n=<PERSON><PERSON>inger\News\ViewHelpers}

<f:layout name="Detail.html" />

<!--
	=====================
		News/Detail.html
-->

<f:section name="content">
	<f:if condition="{newsItem}">
		<f:then>
			<n:format.nothing>
				<f:if condition="{newsItem.alternativeTitle}">
					<f:then>
						<n:titleTag>
							<f:format.htmlentitiesDecode>{newsItem.alternativeTitle}</f:format.htmlentitiesDecode>
						</n:titleTag>
					</f:then>
					<f:else>
						<n:titleTag>
							<f:format.htmlentitiesDecode>{newsItem.title}</f:format.htmlentitiesDecode>
						</n:titleTag>
					</f:else>
				</f:if>
				<f:render partial="Detail/Opengraph" arguments="{newsItem: newsItem, settings:settings}" />
			</n:format.nothing>

			<p>
				<span class="news-list-date"  style="{f:if(condition: newsItem.firstCategory, then: '', else:'border:none;')}">
					<time datetime="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}">
						<f:format.date format="{f:translate(key:'dateFormat')}">{newsItem.datetime}</f:format.date>
						<meta itemprop="datePublished" content="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}" />
					</time>
				</span>
				<f:if condition="{newsItem.categories}">
					<f:render partial="Category/Items" arguments="{categories:newsItem.categories, settings:settings}" />
				</f:if>
			</p>
			<h1 class="h1 text-align-left">{newsItem.title}</h1>
			
			<f:if condition="{newsItem.teaser}">
				<!-- teaser -->
				<div class="teaser-text" itemprop="description">
					<f:format.html>{newsItem.teaser}</f:format.html>
				</div>
			</f:if>

			<f:if condition="{newsItem.contentElements}">
				<!-- content elements -->
				<f:cObject typoscriptObjectPath="lib.tx_news.contentElementRendering">{newsItem.contentElementIdList}</f:cObject>
			</f:if>

			<f:comment><f:render partial="Detail/FalMediaContainer" arguments="{media: newsItem.falMedia, settings:settings}" /></f:comment>
			<f:render partial="Detail/MediaContainer" arguments="{media: newsItem.media, settings:settings}" />

			<!-- main text -->
			<div class="news-text-wrap" itemprop="articleBody">
				<f:format.html>{newsItem.bodytext}</f:format.html>
			</div>

			<div class="news-related-wrap">
				<f:if condition="{newsItem.falRelatedFiles}">
					<h3 class="h2 h2-alternative">
						<f:translate key="related-files" />
					</h3>
					<f:for each="{newsItem.falRelatedFiles}" as="relatedFile">
						<div>
							<a class="link-download" href="{relatedFile.originalResource.publicUrl -> f:format.htmlspecialchars()}" target="_blank">
								{f:if(condition:relatedFile.originalResource.title, then:relatedFile.originalResource.title, else:relatedFile.originalResource.name)}
							</a>
							<span class="news-related-files-size">
								{relatedFile.originalResource.size -> f:format.bytes()}
							</span>
						</div>
					</f:for>
				</f:if>
			</div>

			<f:if condition="{settings.backPid}">
				<!-- Link Back -->
				<div class="news-backlink-wrap">
					<f:link.page pageUid="{settings.backPid}" class="link-with-arrow">
						<f:translate key="back-link" />
					</f:link.page>
				</div>
			</f:if>

		</f:then>
		<f:else>

		</f:else>
	</f:if>
</f:section>