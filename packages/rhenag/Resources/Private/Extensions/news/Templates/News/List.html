{namespace n=<PERSON><PERSON><PERSON>\News\ViewHelpers}
<f:layout name="General" />
<!--
	=====================
		Templates/News/List.html
-->

<f:section name="content">
	<!--TYPO3SEARCH_end-->
	<f:if condition="{news}">
		<f:then>
			<f:if condition="{settings.templateLayout} == 10">
				<f:then>
					<div class="tmpl home-news equal-height news-{f:count(subject: news)}">
						<f:for each="{news}" as="newsItem" iteration="iterator">
							<f:render partial="List/Teaser" arguments="{newsItem: newsItem, settings:settings, iterator:iterator}" />
						</f:for>
						<div class="clearfix"></div>
					</div>
				</f:then>
				<f:else>
                    <div class="news-list-view" id="news-container-{contentObjectData.uid}">
                        <f:if condition="{settings.hidePagination}">
                            <f:then>

                                <f:for each="{news}" as="newsItem" iteration="iterator">
                                    <f:render partial="List/Item" arguments="{newsItem: newsItem,settings:settings,iterator:iterator}" />
                                </f:for>
                            </f:then>
                            <f:else>
                                <f:if condition="{settings.list.paginate.insertAbove}">
                                    <f:render partial="List/Pagination" arguments="{pagination: pagination.pagination, paginator: pagination.paginator}" />
                                </f:if>
                                <f:for each="{pagination.paginator.paginatedItems}" as="newsItem" iteration="iterator">
                                    <f:render partial="List/Item" arguments="{newsItem: newsItem,settings:settings,iterator:iterator}" />
                                </f:for>
                                <f:if condition="{settings.list.paginate.insertBelow}">
                                    <f:render partial="List/Pagination" arguments="{pagination: pagination.pagination, paginator: pagination.paginator}" />
                                </f:if>
                            </f:else>
                        </f:if>
                    </div>
				</f:else>
			</f:if>			
		</f:then>
		<f:else>
			<div class="no-news-found">
				<f:translate key="list_nonewsfound" />
			</div>
		</f:else>
	</f:if>
	<!--TYPO3SEARCH_begin-->
</f:section>