{namespace vh=In2code\Powermail\ViewHelpers}
<div class="powermail_fieldwrap powermail_fieldwrap_type_radio powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses} powermail_fieldwrap_type_{field.pollStyle}">
    <f:if condition="{field.pollIcon}">
    <div class="svg-icon" style="display:block">
        <f:render partial="SvgIcon" section="{field.pollIcon}" arguments="{_all}" />
    </div>
    </f:if>
    <f:render partial="Form/FieldLabel" arguments="{_all}" />

    

    <div class="{settings.styles.framework.fieldWrappingClasses}">
        <f:for each="{field.modifiedSettings}" as="setting" iteration="index">
            <div class="{settings.styles.framework.radioClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')} progressbar__radio">

                <label>
                    <f:form.radio
                            property="{field.marker}"
                            value="{setting.value}"
                            checked="{vh:Misc.PrefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
                            id="powermail_field_{field.marker}_{index.cycle}"
                            additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field, iteration:index)}"
                            class="powermail_radio" />
                    <span class="progressbar__radioBox"></span>
                    <span class="progressbar__label">
                        <vh:string.escapeLabels>{setting.label}</vh:string.escapeLabels>
                    </span>

                </label>

            </div>
        </f:for>

        <f:if condition="{settings.validation.client}">
            <div class="powermail_field_error_container powermail_field_error_container_{field.marker}"></div>
        </f:if>
    </div>
</div>
