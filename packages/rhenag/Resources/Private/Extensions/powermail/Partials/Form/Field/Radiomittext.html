{namespace vh=In2code\Powermail\ViewHelpers}
{namespace v=FluidTYPO3\Vhs\ViewHelpers}
<div class="powermail_fieldwrap powermail_fieldwrap_type_radio powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<label class="{settings.styles.framework.labelClasses}" style="padding-left:0;" title="{field.description}">
		<f:format.raw>{field.title}</f:format.raw><f:if condition="{field.mandatory}"><span class="mandatory">*</span></f:if>
	</label>

	<div class="{settings.styles.framework.fieldWrappingClasses}">
		<div class="row">
			<f:for each="{field.modifiedSettings}" as="setting" iteration="index">

				<div class="col-4 {settings.styles.framework.radioClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')}">
					<label>
					<div class="col-2">
						<f:form.radio
								property="{field.marker}"
								value="{setting.value}"
								checked="{vh:Misc.PrefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
								id="powermail_field_{field.marker}_{index.cycle}"
								additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field, iteration:index)}"
								class="powermail_radio" />
					</div>
						<div class="col-8">
							{v:iterator.explode(content: '{setting.label}', glue: '#')-> v:variable.set(name: 'setting.labelArray')}
							<f:format.raw>{setting.labelArray.0}</f:format.raw><br />
							<f:if condition="{setting.labelArray.1}">
								<f:form.textfield
										type="text"
										property="{field.marker}input"
										placeholder=""
										value="{vh:Misc.PrefillField(field:field, mail:mail)}"
										class="powermail_radio-additionalinput"
										additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field)}"
										id="powermail_field_{field.marker}input_{index.cycle}" />
							</f:if>
						</div>
					</label>
				</div>

			</f:for>
		</div>

		<f:if condition="{settings.validation.client}">
			<div class="powermail_field_error_container powermail_field_error_container_{field.marker}"></div>
		</f:if>
	</div>
</div>
