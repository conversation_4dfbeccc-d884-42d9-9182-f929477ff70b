{namespace vh=In2code\Powermail\ViewHelpers}
{namespace v=FluidTYPO3\Vhs\ViewHelpers}


<div class="powermail_fieldwrap powermail_fieldwrap_type_input powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<label for="powermail_field_{field.marker}" class="col-12 {settings.styles.framework.labelClasses}" style="padding-left:0;" title="{field.description}">
		<f:format.raw>{field.title}</f:format.raw><f:if condition="{field.mandatory}"><span class="mandatory">*</span></f:if>
	</label>
	{v:iterator.explode(content: '{field.description}', glue: '#')-> v:variable.set(name: 'field.descriptionArray')}
		<div class="col-6 {settings.styles.framework.fieldWrappingClasses}" style="padding-left:0;">
		<label class=" small-label {settings.styles.framework.labelClasses}">Breite in mm</label>
		<f:form.textfield
				type="{vh:Validation.FieldTypeFromValidation(field:field)}"
				property="{field.marker}.0"
				placeholder="{field.placeholder}"
				value="{vh:Misc.PrefillField(field:field, mail:mail)}"
				class="powermail_input {settings.styles.framework.fieldClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')}"
				additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field)}"
				id="powermail_field_width_{field.marker}" />
	</div>
	<div class="col-6 {settings.styles.framework.fieldWrappingClasses}" style="padding-left:0;">
		<label  class=" small-label {settings.styles.framework.labelClasses}">Höhe in mm</label>
		<f:form.textfield
				type="{vh:Validation.FieldTypeFromValidation(field:field)}"
				property="{field.marker}.1"
				placeholder="{field.placeholder}"
				value="{vh:Misc.PrefillField(field:field, mail:mail)}"
				class="powermail_input {settings.styles.framework.fieldClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')}"
				additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field)}"
				id="powermail_field_height_{field.marker}" />
	</div>


</div>