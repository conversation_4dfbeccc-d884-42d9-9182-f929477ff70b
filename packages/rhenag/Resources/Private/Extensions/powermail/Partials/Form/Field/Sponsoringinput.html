{namespace vh=In2code\Powermail\ViewHelpers}
{namespace v=FluidTYPO3\Vhs\ViewHelpers}
<div class="powermail_fieldwrap powermail_fieldwrap_type_input powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<label for="powermail_field_{field.marker}" class="{settings.styles.framework.labelClasses} small-label" title="{field.description}">
		<f:format.raw>{field.title}</f:format.raw><f:if condition="{field.mandatory}"><span class="mandatory">*</span></f:if>
	</label>

	<div class="{settings.styles.framework.fieldWrappingClasses}">
		<f:form.textfield
				type="{vh:Validation.FieldTypeFromValidation(field:field)}"
				property="{field.marker}"
				placeholder="{field.placeholder}"
				value="{vh:Misc.PrefillField(field:field, mail:mail)}"
				class="powermail_input {settings.styles.framework.fieldClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')}"
				additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field)}"
				id="powermail_field_{field.marker}" />
	</div>
</div>
