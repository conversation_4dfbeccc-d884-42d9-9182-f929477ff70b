{namespace vh=In2code\Powermail\ViewHelpers}

<fieldset class="form-page page-{page.css} powermail_fieldset powermail_fieldset_{page.uid}">
	<legend class="powermail_legend"><f:format.raw>{page.title}</f:format.raw></legend>
	<f:for each="{page.fields}" as="field">
		<vh:misc.createRowTags columns="{settings.styles.numberOfColumns}" class="{settings.styles.framework.rowClasses}" iteration="{iteration}">
		<div class="form-field {field.css}">
			<f:render partial="Form/Field/{vh:String.Upper(string:field.type)}" arguments="{field:field}" />
		</div>
		</vh:misc.createRowTags>
	</f:for>
	<div class="clearfix"></div>
</fieldset>
