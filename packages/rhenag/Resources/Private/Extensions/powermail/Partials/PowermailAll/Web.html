{namespace vh=In2code\Powermail\ViewHelpers}

<f:comment>
	Mail: {mail}
	Answer: {answer}
</f:comment>

<tr>
	 <td class="powermail_all_label powermail_all_type_{answer.field.type} powermail_all_marker_{answer.field.marker}">
				 <strong>{answer.field.title}</strong>
	</td>
	<td class="powermail_all_value powermail_all_type_{answer.field.type} powermail_all_marker_{answer.field.marker}">
		<f:if condition="{vh:Condition.IsArray(val:answer.value)}">
			<f:else>
				<f:format.raw>
					<vh:Misc.ManipulateValueWithTypoScript answer="{answer}" type="{type}">{answer.value}</vh:Misc.ManipulateValueWithTypoScript>
				</f:format.raw>
			</f:else>
			<f:then>
				<f:for each="{answer.value}" as="subValue" iteration="index">
					<f:if condition="{subValue}">
						<f:if condition="{answer.field.marker} == 'teilnehmer2' || {answer.field.marker} == 'teilnehmer3' || {answer.field.marker} == 'teilnehmer4'">
							<f:then>
								<hr />
							</f:then>
							<f:else>
								<vh:Misc.ManipulateValueWithTypoScript answer="{answer}" type="{type}">{subValue  -> f:format.raw()}</vh:Misc.ManipulateValueWithTypoScript>
									<f:if condition="{index.isLast}">
							<f:else>, </f:else>
									</f:if>
							</f:else>
						</f:if>
					</f:if>
				</f:for>
			</f:then>
		</f:if>
	</td>
</tr>
