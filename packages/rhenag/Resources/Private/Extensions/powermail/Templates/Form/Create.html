{namespace vh=In2code\Powermail\ViewHelpers}
<f:layout name="Default" />

Render Powermail Thx Page
{powermail_rte}									Variable is filled with values from RTE in backend
{powermail_all}									Outputs all fields
{mail}											Complete Mail Object
{marker1}, {firstname}, etc.. 					Outputs a field
{label_marker1}, {label_firstname}, etc... 		Outputs a label to a field
{ttContentData}									All values from content element with plugin
{uploadService}									All values from uploaded files
NOTE: See example section after main section



<f:section name="main">
	<f:alias map="{flashMessageClass:'powermail_message_error'}">
		<f:render partial="Misc/FlashMessages" arguments="{_all}" />
	</f:alias>


	<div class="{settings.styles.framework.createClasses}" data-powermail-form="{mail.form.uid}">
		<f:if condition="{optinActive}">
			<f:else>
				<vh:Misc.Variables mail="{mail}">
					<f:format.html parseFuncTSPath="lib.parseFunc_RTE">{powermail_rte}</f:format.html>
				</vh:Misc.Variables>

				<f:render partial="Misc/GoogleAdwordsConversion" />
			</f:else>
			<f:then>
				<f:format.raw><f:translate key="optin_seeMail" /></f:format.raw>
			</f:then>
		</f:if>
	</div>
</f:section>







THIS IS ONLY AN EXAMPLE SECTION

<f:section name="example">
	<f:alias map="{flashMessageClass:'powermail_message_error'}">
		<f:render partial="Misc/FlashMessages" arguments="{_all}" />
	</f:alias>


	1. Get values from RTE from Backend:
	<vh:Misc.Variables mail="{mail}">
		<f:format.html parseFuncTSPath="lib.parseFunc_RTE">
			{powermail_rte}
		</f:format.html>
	</vh:Misc.Variables>


	2. Get all Values by using powermail_all
	<vh:string.escapeLabels>
		{powermail_all}
	</vh:string.escapeLabels>

	3. Get some lonely fields
	Hi {firstname} {lastname}, thank you for your email.<br />
	{label_email}: {email}<br />
	{label_text}: {text}

</f:section>
