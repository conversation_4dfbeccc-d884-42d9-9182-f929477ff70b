{namespace vh=In2code\Powermail\ViewHelpers}
<f:layout name="Default" />

Render Powermail Form
{form} 			All Forms with their Pages and their Fields
{ttContentData}		All values from content element with plugin


<f:section name="main">
	<f:if condition="{form}">
		<f:then>
			<div class="container-fluid">
				<f:form
						action="{action}"
						section="c{ttContentData.uid}"
						name="field"
						enctype="multipart/form-data"
						additionalAttributes="{vh:Validation.EnableParsleyAndAjax(form:form)}"
						addQueryString="{settings.misc.addQueryString}"
						id="powermail_form_{form.uid}"
						class="powermail_form powermail_form_{form.uid} {form.css} {settings.styles.framework.formClasses} {vh:Misc.MorestepClass(activate:settings.main.moresteps)} {f:if(condition: settings.main.progressbar, then: 'progressbar')}">

					<f:if condition="{form.css} != 'nolabel'">
						<h3>{form.title}</h3>
					</f:if>
					<f:render partial="Misc/FormError" arguments="{_all}" />

					<f:for each="{form.pages}" as="page">
						<f:render partial="Form/Page" arguments="{_all}" />
					</f:for>
					<div class="clearfix"></div>
					<f:form.hidden name="mail[form]" value="{form.uid}" class="powermail_form_uid" />
					<f:render partial="Misc/HoneyPod" arguments="{form:form}" />
				</f:form>
			</div>
		</f:then>
		<f:else>
			<f:translate key="error_no_form" />
		</f:else>
	</f:if>

</f:section>

<f:section name="iframe">

	<f:if condition="{form}">
		<f:then>
			<div class="container-fluid">
				<f:form
						action="{action}"
						section="c{ttContentData.uid}"
						name="field"
						enctype="multipart/form-data"
						additionalAttributes="{vh:Validation.EnableParsleyAndAjax(form:form)}"
						addQueryString="{settings.misc.addQueryString}"
						class="powermail_form powermail_form_{form.uid} {form.css} {settings.styles.framework.formClasses} {vh:Misc.MorestepClass(activate:settings.main.moresteps)}">

					<f:if condition="{form.css} != 'nolabel'">
						<h3>{form.title}</h3>
					</f:if>
					<f:render partial="Misc/FormError" arguments="{_all}" />

					<f:for each="{form.pages}" as="page">
						<f:render partial="Form/Page" arguments="{_all}" />
					</f:for>
					<div class="clearfix"></div>
					<f:form.hidden name="mail[form]" value="{form.uid}" class="powermail_form_uid" />
					<f:render partial="Misc/HoneyPod" arguments="{form:form}" />
				</f:form>
			</div>
		</f:then>
		<f:else>
			<f:translate key="error_no_form" />
		</f:else>
	</f:if>

</f:section>