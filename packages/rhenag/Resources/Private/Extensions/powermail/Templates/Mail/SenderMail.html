{namespace vh=In2code\Powermail\ViewHelpers}
<f:layout name="Mail" />

Render Powermail Mail to Sender
{powermail_rte}									Variable is filled with values from RTE in backend
{powermail_all}									Outputs all fields
{marker1}, {firstname}, etc.. 					Outputs a field
{label_marker1}, {label_firstname}, etc... 		Outputs a label to a field
{mail}											Complete Mail Object
{email}											Email Configuration
{settings}										TypoScript Settings
NOTE: See example section after main section

<f:section name="main">
	<vh:Misc.Variables mail="{mail}" type="mail" function="sender">
		<f:format.html parseFuncTSPath="lib.parseFunc_powermail">{powermail_rte}</f:format.html>
	</vh:Misc.Variables>

	<f:comment><f:render partial="Mail/DisclaimerLink" arguments="{_all}" /></f:comment>
</f:section>






THIS IS ONLY AN EXAMPLE SECTION

<f:section name="example">

	1. Get values from RTE from Backend:
	<vh:Misc.Variables mail="{mail}" type="mail" function="sender">
		<f:format.html parseFuncTSPath="lib.parseFunc_powermail">{powermail_rte}</f:format.html>
	</vh:Misc.Variables>


	2. Get all Values by using powermail_all
	<vh:string.escapeLabels>
		{powermail_all}
	</vh:string.escapeLabels>

	3. Get some lonely fields
	Hi {firstname} {lastname}, this is a test...

</f:section>
