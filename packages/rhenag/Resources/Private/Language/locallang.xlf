<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<xliff version="1.0">
	<file source-language="en" datatype="plaintext" original="messages" product-name="rhenag" date="2016-07-27T10:54:26+02:00">
		<header/>
		<body>
			<trans-unit id="flux.home-page">
				<source>Startseite</source>
			</trans-unit>
			<trans-unit id="flux.home-page.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.home-page.sheets.services">
				<source>Services</source>
			</trans-unit>			
			<trans-unit id="flux.home-page.fields.showServices">
				<source>Show Services</source>
			</trans-unit>
			<trans-unit id="flux.home-page.fields.textServices">
				<source>Services Text</source>
			</trans-unit>
			<trans-unit id="flux.home-page.fields.optionalService">
				<source>Optional Service</source>
			</trans-unit>
			<trans-unit id="flux.home-page.fields.optionalServiceText">
				<source>Optional Service Text</source>
			</trans-unit>
			<trans-unit id="flux.with-sidebar">
				<source>Inhaltsseite (Contentpage)</source>
			</trans-unit>
			<trans-unit id="flux.with-sidebar.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.landing-page">
				<source>Verteilerseite</source>
			</trans-unit>
            <trans-unit id="flux.landing-page.description">
				<source></source>
			</trans-unit>
            <trans-unit id="flux.campaign-page">
                <source>Kampagnenseite ohne Navigationen</source>
            </trans-unit>
            <trans-unit id="flux.campaign-page.description">
                <source></source>
            </trans-unit>
            <trans-unit id="flux.iframe-page">
                <source>iFrame Page (Ohne Design für externe Seiten)</source>
            </trans-unit>
			<trans-unit id="flux.menu">
				<source>Einfaches Menu</source>
			</trans-unit>
			<trans-unit id="flux.menu.description">
				<source>Einfaches Menü für Linklisten</source>
			</trans-unit>
			<trans-unit id="flux.standard.sheets.theme">
				<source>Theme</source>
			</trans-unit>

			<trans-unit id="page_configuration.theme">
				<source>Theme</source>
			</trans-unit>

			<trans-unit id="search.placeholder">
				<source>Energie</source>
			</trans-unit>

			<trans-unit id="flux.containerpromobox">
				<source>Layout - Zwei Spalten für Promoboxen (1/2 - 1/2)</source>
			</trans-unit>
			<trans-unit id="flux.containerpromobox.description">
				<source></source>
			</trans-unit>	
			<trans-unit id="flux.container">
				<source>Layout - Eine Spalte</source>
			</trans-unit>
			<trans-unit id="flux.container.description">
				<source></source>
			</trans-unit>	
			<trans-unit id="flux.container25">
				<source>Layout - Vier Spalten (1/4 - 1/4 - 1/4 - 1/4)</source>
			</trans-unit>
			<trans-unit id="flux.container25.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.container3366">
				<source>Layout - Zwei Spalten (1/3 - 2/3)</source>
			</trans-unit>
			<trans-unit id="flux.container3366.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.container6633">
				<source>Layout - Zwei Spalten (2/3 - 1/3)</source>
			</trans-unit>
			<trans-unit id="flux.container6633.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.containersplit">
				<source>Layout - Zwei Spalten (1/2 - 1/2)</source>
			</trans-unit>
			<trans-unit id="flux.containersplit.description">
				<source></source>
			</trans-unit>	
			<trans-unit id="flux.containersplit2">
				<source>Layout - Zwei Spalten (1/2 - 1/2)</source>
			</trans-unit>
			<trans-unit id="flux.containersplit2.description">
				<source></source>
			</trans-unit>		
			<trans-unit id="flux.section">
				<source>Section</source>
			</trans-unit>
			<trans-unit id="flux.section.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.newContentWizard.layoutelemente">
				<source>Layout-Elemente</source>
			</trans-unit>

		</body>
	</file>
</xliff>
