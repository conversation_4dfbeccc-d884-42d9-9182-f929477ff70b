<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">


	<f:layout name="Content" />


	<f:section name="Configuration">
		<flux:form id="container">
			<flux:form.option.icon value="/typo3conf/ext/rhenag/Resources/Public/Icons/Content/icon-layout-100.svg" />
			<flux:form.option.group value="Layout-Elemente" />
			<flux:form.content name="column" label="Container Inhalt">
				<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_landingpageteaser2,rhenagcomponents_landingpageteaser,rhenagcomponents_textimage,rhenagcomponents_contentaccordion,rhenagcomponents_contentimagetextbox,rhenagcomponents_tarifboxes,rhenag_icontext,rhenagcomponents_icontext,rhenag_typoscript,table,list"
				/>

			</flux:form.content>
		</flux:form>

	</f:section>


	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag/Resources/Private/Language/locallang.xlf:flux.container" />
		</h4>


	</f:section>



	<f:section name="Main">

		<div class="fluid-container container-100">
			<div class="row">
				<div class="col-12">
					<flux:content.render area="column" />
				</div>
			</div>
		</div>

	</f:section>
</div>