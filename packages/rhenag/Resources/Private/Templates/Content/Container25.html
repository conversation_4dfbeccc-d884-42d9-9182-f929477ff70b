<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">


	<f:layout name="Content" />


	<f:section name="Configuration">
		<flux:form id="container25">
			<flux:form.option.icon value="/typo3conf/ext/rhenag/Resources/Public/Icons/Content/icon-layout-25-25-25-25.svg" />
			<flux:form.option.group value="Layout-Elemente" />
			<flux:grid>
				<flux:grid.row>
					<flux:grid.column colPos="11" name="column" label="Column 1" style="width:25%">
						<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_textimage,rhenag_containersplit2,shortcut,rhenagcomponents_tarifboxes,rhenagcomponents_contentimagetextbox,rhenagcomponents_contentaccordion,rhenagcomponents_downloadboxes,table,list" />
					</flux:grid.column>
					<flux:grid.column colPos="12" name="column2" label="Column 2" style="width:25%">
						<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_textimage,rhenag_containersplit2,shortcut,rhenagcomponents_tarifboxes,rhenagcomponents_contentimagetextbox,rhenagcomponents_contentaccordion,rhenagcomponents_downloadboxes,table,list" />
					</flux:grid.column>
					<flux:grid.column colPos="13" name="column3" label="Column 3" style="width:25%">
						<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_textimage,rhenag_containersplit2,shortcut,rhenagcomponents_tarifboxes,rhenagcomponents_contentimagetextbox,rhenagcomponents_contentaccordion,rhenagcomponents_downloadboxes,table,list" />
					</flux:grid.column>
					<flux:grid.column colPos="14" name="column4" label="Column 4" style="width:25%">
						<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_textimage,rhenag_containersplit2,shortcut,rhenagcomponents_tarifboxes,rhenagcomponents_contentimagetextbox,rhenagcomponents_contentaccordion,rhenagcomponents_downloadboxes,table,list" />
					</flux:grid.column>
				</flux:grid.row>
			</flux:grid>
		</flux:form>

	</f:section>


	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag/Resources/Private/Language/locallang.xlf:flux.container25" />
		</h4>
	</f:section>



	<f:section name="Main">

		<div class="fluid-container container-25">
			<div class="row">
				<div class="col-3">
					<flux:content.render area="column" />
				</div>
				<div class="col-3">
					<flux:content.render area="column2" />
				</div>
				<div class="col-3">
					<flux:content.render area="column3" />
				</div>
				<div class="col-3">
					<flux:content.render area="column4" />
				</div>
			</div>
		</div>

	</f:section>
</div>