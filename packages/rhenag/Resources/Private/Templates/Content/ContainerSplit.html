<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">


	<f:layout name="Content" />


	<f:section name="Configuration">
		<flux:form id="containersplit">
			<flux:form.option.icon value="EXT:rhenag/Resources/Public/Icons/Content/icon-layout-50-50.svg" />
			<flux:form.option.group value="Layout-Elemente" />
			<flux:grid>
				<flux:grid.row>
					<flux:grid.column colPos="11" name="column" label="Column 1" style="width:50%">
						<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_contentinfobox,rhenagcomponents_textimage,rhenagcomponents_contentaccordion,rhenagcomponents_contentimagetextbox,rhenagcomponents_tarifboxes,rhenagcomponents_downloadboxes,table,list,html"
						/>

					</flux:grid.column>
					<flux:grid.column colPos="12" name="column2" label="Column 2" style="width:50%">
						<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_contentinfobox,rhenagcomponents_textimage,rhenagcomponents_contentaccordion,rhenagcomponents_contentimagetextbox,rhenagcomponents_tarifboxes,rhenagcomponents_downloadboxes,table,list,html"
						/>

					</flux:grid.column>
				</flux:grid.row>
			</flux:grid>
		</flux:form>

	</f:section>


	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag/Resources/Private/Language/locallang.xlf:flux.containersplit" />
		</h4>


	</f:section>



	<f:section name="Main">

		<div class="fluid-container container-split">
			<div class="row">
				<div class="col-6">
					<flux:content.render area="column" />
				</div>
				<div class="col-6">
					<flux:content.render area="column2" />
				</div>
			</div>
		</div>

	</f:section>
</div>