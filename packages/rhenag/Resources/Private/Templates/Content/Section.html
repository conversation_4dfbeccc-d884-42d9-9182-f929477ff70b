<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">


	<f:layout name="Content" />


	<f:section name="Configuration">
		<flux:form id="section">
			<flux:form.option.icon value="EXT:rhenag/Resources/Public/Icons/Content/icon-section.svg" />
			<flux:form.option.group value="Layout-Elemente" />
			<flux:field.select name="sectionTheme" items="{
                    0:{0:'light',1:'light'},
                    1:{0:'dark',1:'dark'},
                    2:{0:'darker',1:'darker'}}" default="light" label="Section Theme" />
			<flux:form.content name="column" label="Section Inhalt">
				<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_textimage,rhenag_container,rhenagcomponents_contentspecialteaser" />

			</flux:form.content>
		</flux:form>

	</f:section>


	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag/Resources/Private/Language/locallang.xlf:flux.section" />
		</h4>


	</f:section>



	<f:section name="Main">

		<section class="section {sectionTheme}">
			<flux:content.render area="column" />
		</section>

	</f:section>
</div>