<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers">

	<f:layout name="CampaignPage" />

	<f:section name="Configuration">
		<flux:form id="campaign-page">
			<flux:field.input name="pageClass" default="some-css-class" />
			<flux:field.select name="theme" label="Theme" items="{
                           0:{0:'Haupt-Theme',1:'theme-default'},
                           1:{0:'Zweites-Theme',1:'theme-secondary'}
                           }" />

		</flux:form>
		<flux:grid>
			<!-- Edit this grid to change the "backend layout" structure -->
			<flux:grid.row>
				<flux:grid.column colPos="0" name="Top" label="Bereich oben">
					<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_campaignpageteaser" />

				</flux:grid.column>
			</flux:grid.row>
			<flux:grid.row>
				<flux:grid.column colPos="1" name="Bottom" label="Bereich unten">
					<flux:form.variable name="allowedContentTypes" value="rhenag_section,rhenag_containerpromobox,rhenag_container,rhenag_containersplit,rhenag_container25,rhenag_container6633,rhenag_container3366,rhenagcomponents_contentspecialteaser" />

				</flux:grid.column>
			</flux:grid.row>
			<flux:grid.row>
				<flux:grid.column colPos="2" name="Contact" label="Kontaktbereich">
					<flux:form.variable name="allowedContentTypes" value="rhenag_section" />

				</flux:grid.column>
			</flux:grid.row>
		</flux:grid>
	</f:section>





	<f:section name="Main">

		<div class="page">
			<f:cObject typoscriptObjectPath="lib.mobileHeader" />
			<f:render partial="CampaignHeader" />
			<!--TYPO3SEARCH_begin-->
			<v:content.render column="0" as="topElements" render="false">
				<f:if condition="{topElements}">
					<f:then>
						<div class="CampaignContent">
							<v:content.render column="0" />
						</div>
					</f:then>
					<f:else>
						<div class="topSpacer"></div>
					</f:else>
				</f:if>
			</v:content.render>

			<v:content.render column="1" />
			<div class="CampaignContact">
				<v:content.render column="2" />
			</div>

			<!--TYPO3SEARCH_end-->
			<f:render partial="GoTop" />
			<f:render partial="CampaignFooter" />
		</div>
	</f:section>
</div>