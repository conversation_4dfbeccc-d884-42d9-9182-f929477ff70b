<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers">

	<f:layout name="HomePage" />

	<f:section name="Configuration">
		<flux:form id="home-page">

			<flux:field.input name="pageClass" default="some-css-class" />
			<flux:field.select name="theme" label="Theme" items="{
						0:{0:'Haupt-Theme',1:'theme-default'},
						1:{0:'Zweites-Theme',1:'theme-secondary'}
					}" />
			<flux:form.sheet name="services">
				<flux:field.checkbox name="showServices" />
				<flux:field.text name="textServices" enableRichText="true" />
				<flux:field.checkbox name="optionalService" />
				<flux:field.text name="optionalServiceText" enableRichText="true" />
				<flux:field.inline.fal name="optionalServiceImage" minItems="0" maxItems="1" showThumbs="1" />
			</flux:form.sheet>


		</flux:form>
		<flux:grid>
			<!-- Edit this grid to change the "backend layout" structure -->
			<flux:grid.row>
				<flux:grid.column colPos="0" name="Top" label="Bereich oben">
					<flux:form.variable name="allowedContentTypes" value="rhenagslider_hometeasercontainer,rhenagslider_homevideowithteasers" />

				</flux:grid.column>
			</flux:grid.row>
			<flux:grid.row>
				<flux:grid.column colPos="1" name="Bottom" label="Bereich unten">
					<flux:form.variable name="allowedContentTypes" value="rhenag_section,rhenag_containerpromobox,rhenag_container,rhenag_containersplit,rhenag_container25,rhenag_container6633,rhenag_container3366,rhenagcomponents_contentspecialteaser" />

				</flux:grid.column>
			</flux:grid.row>
		</flux:grid>
	</f:section>





	<f:section name="Main">



		<div class="page" id="page">
			<f:cObject typoscriptObjectPath="lib.mobileHeader" />
			<a href="#top" id="top"></a>
			<div class="navbar">
				<div class="fluid-container">
					<div class="row">
						<div class="col-12">
							<div class="navbar-flex-container">
								<!-- Top row: Meta-Nav only, right aligned -->
								<div class="navbar-meta-row">
									<div class="meta-nav-container">
										<f:cObject typoscriptObjectPath="lib.metaNavigation" />
									</div>
								</div>
								<!-- Bottom row: Logo left, Main Navigation right -->
								<div class="navbar-main-row">
									<div class="logo-container">
										<f:cObject typoscriptObjectPath="lib.logo" />
									</div>
									<nav class="main-nav-container">
										<f:cObject typoscriptObjectPath="lib.mainNavigation" />
									</nav>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<f:cObject typoscriptObjectPath="lib.navFlyoutContact" />
			<f:render partial="NavFlyoutSearch" />
			<!--TYPO3SEARCH_begin-->
			<v:content.render column="0" />
			<f:if condition="{showServices}">
				<f:render partial="ServicesHome" arguments="{text: textServices, optional: optionalService, optionalText: optionalServiceText, optionalImage: optionalServiceImage}" />
			</f:if>
			<v:content.render column="1" />
			<!--TYPO3SEARCH_end-->

			<f:render partial="GoTop" />
			<f:render partial="Footer" />
		</div>
		<f:cObject typoscriptObjectPath="lib.mobileNavigation" />

	</f:section>


</div>