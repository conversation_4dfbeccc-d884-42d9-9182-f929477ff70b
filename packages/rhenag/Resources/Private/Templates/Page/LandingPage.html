<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

<f:layout name="LandingPage" />

<f:section name="Configuration">
	<flux:form id="landing-page">

		<flux:field.input name="pageClass" default="some-css-class" />
		<flux:field.select name="theme" label="Theme" items="{
                           0:{0:'Haupt-Theme',1:'theme-default'},
                           1:{0:'Zweites-Theme',1:'theme-secondary'}
                           }" />

	</flux:form>
	<flux:grid>
		<!-- Edit this grid to change the "backend layout" structure -->
		<flux:grid.row>
			<flux:grid.column colPos="0" name="Top" label="Bereich oben">
				<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_landingpageteaser,rhenagcomponents_landingpageteaser2" />

			</flux:grid.column>
		</flux:grid.row>
		<flux:grid.row>
			<flux:grid.column colPos="1" name="Bottom" label="Bereich unten">
				<flux:form.variable name="allowedContentTypes" value="rhenag_section,rhenag_containerpromobox,rhenag_container,rhenag_containersplit,rhenag_container25,rhenag_container6633,rhenag_container3366,rhenagcomponents_contentspecialteaser" />

			</flux:grid.column>
		</flux:grid.row>
	</flux:grid>
</f:section>





<f:section name="Main">


	<div class="page">
		<f:cObject typoscriptObjectPath="lib.mobileHeader" />
		<a href="#top" id="top"></a>
		<f:render partial="BrowserWarning" />

		<div class="navbar active-is-open">
			<div class="fluid-container">
				<div class="row">
					<div class="col-12">
						<div class="navbar-flex-container">
							<!-- Top row: Meta-Nav only, right aligned -->
							<div class="navbar-meta-row">
								<div class="meta-nav-container">
									<f:cObject typoscriptObjectPath="lib.metaNavigation" />
								</div>
							</div>
							<!-- Bottom row: Logo left, Main Navigation right -->
							<div class="navbar-main-row">
								<div class="logo-container">
									<f:cObject typoscriptObjectPath="lib.logo" />
								</div>
								<nav class="main-nav-container">
									<f:cObject typoscriptObjectPath="lib.mainNavigation" />
								</nav>
							</div>
						</div>
					</div>
				</div>
			</div>
			<f:cObject typoscriptObjectPath="lib.extraNavigation" />
		</div>
		<f:cObject typoscriptObjectPath="lib.navFlyoutContact" />
		<f:render partial="NavFlyoutSearch" />
		<!--TYPO3SEARCH_begin-->
		<v:content.render column="0" as="topElements" render="false">
			<f:if condition="{topElements}">
				<f:then>
					<v:content.render column="0" />
				</f:then>
				<f:else>
					<div class="topSpacer"></div>
				</f:else>
			</f:if>
		</v:content.render>

		<v:content.render column="1" />
		<!--TYPO3SEARCH_end-->
		<f:render partial="GoTop" />
		<f:render partial="Footer" />
	</div>
	<f:cObject typoscriptObjectPath="lib.mobileNavigation" />
</f:section>


</html>