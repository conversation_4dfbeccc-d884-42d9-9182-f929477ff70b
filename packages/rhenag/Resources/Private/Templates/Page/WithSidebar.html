<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers">

	<f:layout name="PageWithSidebarLeft" />

	<f:section name="Configuration">
		<flux:form id="with-sidebar">

			<flux:field.input name="pageClass" default="some-css-class" />
			<flux:field.select name="theme" label="Theme" items="{
                           0:{0:'Haupt-Theme',1:'theme-default'},
                           1:{0:'Zweites-Theme',1:'theme-secondary'}
                           }" />

		</flux:form>
		<flux:grid>
			<!-- Edit this grid to change the "backend layout" structure -->
			<flux:grid.row>
				<flux:grid.column colPos="0" name="Top" colspan="4" label="Bereich oben">
					<flux:form.variable name="allowedContentTypes" value="rhenagslider_contentpagevideowithteasers,rhenagcomponents_contentpageteaser" />

				</flux:grid.column>
			</flux:grid.row>
			<flux:grid.row>
				<flux:grid.column colPos="1" name="Left" label="Bereich Sidebar">
					<flux:form.variable name="allowedContentTypes" value="rhenag_containersplit,rhenag_containersplit2,rhenag_container6633,rhenag_container3366,rhenagcomponents_textimage,rhenagcomponents_contentaccordion,rhenagcomponents_contentimagetextbox,rhenagcomponents_linkboxes,rhenagcomponents_contentinfobox,rhenagcomponents_downloadboxes,rhenagcomponents_icontext,list"
					/>

				</flux:grid.column>
				<flux:grid.column colPos="2" name="Right" colspan="3" label="Bereich Content">
					<flux:form.variable name="allowedContentTypes" value="rhenagaccordion,rhenag_containersplit,rhenag_containersplit2,rhenag_container6633,rhenag_container3366,rhenagcomponents_textimage,rhenagcomponents_contentaccordion,rhenagcomponents_contentimagetextbox,rhenagcomponents_linkboxes,rhenagcomponents_contentinfobox,rhenagcomponents_downloadboxes,rhenagcomponents_icontext,table,list"
					/>

				</flux:grid.column>
			</flux:grid.row>
			<style>
				[data-colpos="1"] .t3js-sortable-lang-0 {
					display: none;
				}
			</style>
		</flux:grid>

	</f:section>





	<f:section name="Main">

		<f:render partial="BrowserWarning" />

		<div class="navbar active-is-open">
			<div class="fluid-container">
				<div class="row">
					<div class="col-12">
						<div class="navbar-flex-container">
							<!-- Top row: Meta-Nav only, right aligned -->
							<div class="navbar-meta-row">
								<div class="meta-nav-container">
									<f:cObject typoscriptObjectPath="lib.metaNavigation" />
								</div>
							</div>
							<!-- Bottom row: Logo left, Main Navigation right -->
							<div class="navbar-main-row">
								<div class="logo-container">
									<f:cObject typoscriptObjectPath="lib.logo" />
								</div>
								<nav class="main-nav-container">
									<f:cObject typoscriptObjectPath="lib.mainNavigation" />
								</nav>
							</div>
						</div>
					</div>
				</div>
			</div>
			<f:cObject typoscriptObjectPath="lib.extraNavigation" />
		</div>
		<f:cObject typoscriptObjectPath="lib.navFlyoutContact" />

		<f:render partial="NavFlyoutSearch" />
		<f:cObject typoscriptObjectPath="lib.mobileNavigation" />

		<div class="page">
			<a href="#top" id="top"></a>
			<f:cObject typoscriptObjectPath="lib.mobileHeader" />
			<!--TYPO3SEARCH_begin-->
			<v:content.render column="0" as="topElements" render="false">
				<f:if condition="{topElements}">
					<f:then>
						<v:content.render column="0" />
					</f:then>
					<f:else>
						<div class="topSpacer"></div>
					</f:else>
				</f:if>
			</v:content.render>

			<div class="fluid-container">
				<div class="row">
					<div class="col-sidebar">
						<div id="submenuOffset"></div>
						<nav class="sidebar-nav-container">
							<f:cObject typoscriptObjectPath="lib.sidebarNavigation" />
							<v:content.render column="1" />
						</nav>
					</div>
					<div class="col-content">
						<v:content.render column="2" />
					</div>
				</div>
			</div>
			<!--TYPO3SEARCH_end-->

			<f:render partial="GoTop" />
			<f:render partial="Footer" />

		</div>


	</f:section>


</div>