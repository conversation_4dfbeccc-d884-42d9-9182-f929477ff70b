var elixir = require('laravel-elixir');
require('laravel-elixir-fonts');

var runTimestamp = Math.round(Date.now()/1000);
var fontName = 'customIcons';




elixir(function(mix) {

	///////
	//CREATE ICON FONT
	mix.fonts(['svgWatchFolder/**/*.svg'], 'public/fonts/', {
		font: {
				normalize: true,
				fontName: fontName, // required
				prependUnicode: false, // recommended option
				formats: ['ttf', 'eot', 'woff', 'woff2', 'svg'], // default, 'woff2' and 'svg' are available
				timestamp: runTimestamp // recommended to get consistent builds when watching files
			}

	});
	mix.sass('customIcons.scss');

	///////
	//FONTS
	// mix.copy('resources/assets/fonts/webfonts', 'public/fonts/webfonts');
	// mix.copy('resources/assets/fonts/font-awesome-4.5.0/fonts', 'public/fonts/font-awesome');
	mix.copy('resources/assets/fonts/black-tie/fonts/regular', 'public/fonts/black-tie/regular');
	mix.copy('resources/assets/fonts/black-tie/fonts/brands', 'public/fonts/black-tie/brands');


	////////
	//STYLES - SEP
	// mix.copy('bower_components/jquery.mb.ytplayer/dist/css', 'public/css');
	

	////////
	//STYLES 
	// mix.copy('bower_components/magnific-popup/src/css', 'resources/assets/sass/modules/magnific-popup'); //NEEDED ONLY to INITIALIZE
	mix.copy('bower_components/jquery-focuspoint/css', 'resources/assets/sass/modules/focuspoint');
	// mix.copy('bower_components/superslides/dist/stylesheets', 'resources/assets/sass/modules/superslides'); //NEEDED ONLY to INITIALIZE
	
	mix.sass('screen.scss');
	


	///////
	//jQuery & modernizr
	mix.copy('resources/assets/javascript/vendor/jquery-1.11.2.min.js', 'public/js');
	mix.copy('resources/assets/javascript/vendor/modernizr-2.8.3-respond-1.4.2.min.js', 'public/js');
	

	///////
	//LIBS
	// mix.copy('bower_components/masonry/dist/masonry.pkgd.min.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/magnific-popup/dist/jquery.magnific-popup.min.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/jquery-focuspoint/js/jquery.focuspoint.min.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/hammerjs/hammer.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/jquery-unveil/jquery.unveil.min.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/superslides/examples/javascripts/jquery.easing.1.3.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/superslides/examples/javascripts/jquery.animate-enhanced.min.js', 'resources/assets/javascript/libs');
	// mix.copy('bower_components/superslides/examples/javascripts/hammer.min.js', 'resources/assets/javascript/libs');
	// mix.copy('bower_components/superslides/dist/jquery.superslides.min.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/matchHeight/dist/jquery.matchHeight-min.js', 'resources/assets/javascript/libs');
	// mix.copy('bower_components/jquery-validation/dist/jquery.validate.min.js', 'resources/assets/javascript/libs');
	// mix.copy('bower_components/jquery-validation/src/localization/messages_de.js', 'resources/assets/javascript/libs');
	mix.copy('resources/assets/vendor/jquery-selectBox-master/jquery.selectBox.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/jQuery.mmenu/dist/js/jquery.mmenu.all.min.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/jQuery.mmenu/dist/3rdparty/addons/currentitem/jquery.mmenu.currentitem.min.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/picturefill/dist/picturefill.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/jquery.scrollTo/jquery.scrollTo.min.js', 'resources/assets/javascript/libs');
	// mix.copy('resources/assets/vendor/Responsive-Img-master/js/responsiveImg.js', 'resources/assets/javascript/libs');
	mix.copy('bower_components/stacktable.js/stacktable.js', 'resources/assets/javascript/libs');
	

	mix.scriptsIn('resources/assets/javascript/libs', 'public/js/libs.js');
	

	///////
	//Responsive Images PHP Handler ONLY ONCE AT THE BEGINNING!!!!!
	// mix.copy('resources/assets/vendor/Responsive-Img-master/js/responsiveImg.js.php', 'public/php');

	///////
	//LIBS-SEP
	// mix.copy('bower_components/jquery.mb.ytplayer/dist/jquery.mb.YTPlayer.min.js', 'public/js');
	//mix.copy('bower_components/jQuery.mmenu/dist/js/jquery.mmenu.all.min.js', 'public/js');
	//mix.copy('bower_components/jQuery.mmenu/dist/3rdparty/addons/currentitem/jquery.mmenu.currentitem.min.js', 'public/js');
	

	///////
	//MAIN
	mix.scriptsIn('resources/assets/javascript/module', 'public/js/main.js');



	
 

});
