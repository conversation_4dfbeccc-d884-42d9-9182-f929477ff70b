@charset "UTF-8";
/* ==========================================================================
   Normalize.scss settings
   ========================================================================== */
/**
 * Includes legacy browser support IE6/7
 *
 * Set to false if you want to drop support for IE6 and IE7
 */
/* Base
   ========================================================================== */
/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS and IE text size adjust after device orientation change,
 *    without disabling user zoom.
 * 3. Corrects text resizing oddly in IE 6/7 when body `font-size` is set using
 *  `em` units.
 */
html {
  font-family: sans-serif;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/**
 * Remove default margin.
 */
body {
  margin: 0;
}

/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

/**
 * 1. Correct `inline-block` display not defined in IE 6/7/8/9 and Firefox 3.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */
audio,
canvas,
progress,
video {
  display: inline-block;
  /* 1 */
  vertical-align: baseline;
  /* 2 */
}

/**
 * Prevents modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/10/11, Safari, and Firefox < 22.
 */
[hidden],
template {
  display: none;
}

/* Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * Improve readability of focused elements when they are also in an
 * active/hover state.
 */
a:active, a:hover {
  outline: 0;
}

/* Text-level semantics
   ========================================================================== */
/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted;
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */
b,
strong {
  font-weight: bold;
}

/**
 * Address styling not present in Safari and Chrome.
 */
dfn {
  font-style: italic;
}

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/**
 * Addresses styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000;
}

/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* Embedded content
   ========================================================================== */
/**
 * 1. Remove border when inside `a` element in IE 8/9/10.
 * 2. Improves image quality when scaled in IE 7.
 */
img {
  border: 0;
}

/**
 * Correct overflow not hidden in IE 9/10/11.
 */
svg:not(:root) {
  overflow: hidden;
}

/* Grouping content
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari.
 */
figure {
  margin: 1em 40px;
}

/**
 * Address differences between Firefox and other browsers.
 */
hr {
  box-sizing: content-box;
  height: 0;
}

/**
 * Contain overflow in all browsers.
 */
pre {
  overflow: auto;
}

/**
 * Address odd `em`-unit font size rendering in all browsers.
 * Correct font family set oddly in IE 6, Safari 4/5, and Chrome.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

/* Forms
   ========================================================================== */
/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */
/**
 * 1. Correct color not being inherited.
 *  Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 * 4. Improves appearance and consistency in all browsers.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  /* 1 */
  font: inherit;
  /* 2 */
  margin: 0;
  /* 3 */
}

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible;
}

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none;
}

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *  and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *  `input` and others.
 * 4. Removes inner spacing in IE 7 without affecting normal text inputs.
 *  Known issue: inner spacing remains in IE 6.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
}

/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}

/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
input {
  line-height: normal;
}

/**
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 *  Known issue: excess padding remains in IE 6.
 */
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome.
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  box-sizing: content-box;
  /* 2 */
}

/**
 * Remove inner padding and search cancel button in Safari and Chrome on OS X.
 * Safari (but not Chrome) clips the cancel button when the search input has
 * padding (and `textfield` appearance).
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 * 3. Corrects text not wrapping in Firefox 3.
 * 4. Corrects alignment displayed oddly in IE 6/7.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */
textarea {
  overflow: auto;
}

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold;
}

/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

/* -------------------------------------------------------------
  Sass CSS3 Mixins! The Cross-Browser CSS3 Sass Library
  By: Matthieu Aussaguel, http://www.mynameismatthieu.com, @matthieu_tweets

  List of CSS3 Sass Mixins File to be @imported and @included as you need

  The purpose of this library is to facilitate the use of CSS3 on different browsers avoiding HARD TO READ and NEVER
  ENDING css files

  note: All CSS3 Properties are being supported by Safari 5
  more info: http://www.findmebyip.com/litmus/#css3-properties

------------------------------------------------------------- */
/*!
 *  The BlackTie Font is commercial software. Please do not distribute.
 */
@font-face {
  font-family: 'BlackTie';
  src: url("../fonts/black-tie/regular/BlackTie-Regular-webfont.eot?v=1.0.0");
  src: url("../fonts/black-tie/regular/BlackTie-Regular-webfont.eot?#iefix&v=1.0.0") format("embedded-opentype"), url("../fonts/black-tie/regular/BlackTie-Regular-webfont.woff2?v=1.0.0") format("woff2"), url("../fonts/black-tie/regular/BlackTie-Regular-webfont.woff?v=1.0.0") format("woff"), url("../fonts/black-tie/regular/BlackTie-Regular-webfont.ttf?v=1.0.0") format("truetype"), url("../fonts/black-tie/regular/BlackTie-Regular-webfont.svg?v=1.0.0#black_tieregular") format("svg");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Font Awesome Brands';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("../fonts/black-tie/brands/fa-brands-400.woff2") format("woff2"), url("../fonts/black-tie/brands/fa-brands-400.ttf") format("truetype");
}

.bts, .btb, .btr, .btl, .fab {
  display: inline-block;
  font: normal normal normal 14px/1 "BlackTie";
  font-size: inherit;
  vertical-align: -14.2857142857%;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translate(0, 0);
}

.bts {
  font-weight: 900;
}

.btb {
  font-weight: 700;
}

.btl {
  font-weight: 200;
}

.fab {
  font-family: "Font Awesome Brands";
}

/* makes the font 25% smaller relative to the icon container */
.bt-sm {
  font-size: .7em;
  vertical-align: baseline;
}

/* makes the font 33% larger relative to the icon container */
.bt-lg {
  font-size: 1.3333333333em;
  line-height: 0.75em;
}

.bt-2x {
  font-size: 2em;
}

.bt-3x {
  font-size: 3em;
}

.bt-4x {
  font-size: 4em;
}

.bt-5x {
  font-size: 5em;
}

.bt-lg,
.bt-2x,
.bt-3x,
.bt-4x,
.bt-5x {
  vertical-align: -30%;
}

.bt-fw {
  width: 1.2857142857em;
  text-align: center;
}

.bt-ul {
  padding-left: 0;
  margin-left: 2.1428571429em;
  list-style-type: none;
}

.bt-ul > li {
  position: relative;
}

.bt-li {
  position: absolute;
  left: -2.1428571429em;
  width: 2.1428571429em;
  top: 0.1428571429em;
  text-align: center;
}

.bt-li.bt-lg {
  left: -2em;
}

.bt-border {
  padding: .2em;
  border: solid 0.08em #eee;
  border-radius: .1em;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.bts.pull-left, .btb.pull-left, .btr.pull-left, .btl.pull-left, .fab.pull-left {
  margin-right: .3em;
}

.bts.pull-right, .btb.pull-right, .btr.pull-right, .btl.pull-right, .fab.pull-right {
  margin-left: .3em;
}

.bt-spin {
  animation: bt-spin 2s infinite linear;
}

.bt-pulse {
  animation: bt-spin 1s infinite steps(8);
}

@keyframes bt-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

.bt-rotate-90 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
  transform: rotate(90deg);
}

.bt-rotate-180 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  transform: rotate(180deg);
}

.bt-rotate-270 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
  transform: rotate(270deg);
}

.bt-flip-horizontal {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0);
  transform: scale(-1, 1);
}

.bt-flip-vertical {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  transform: scale(1, -1);
}

:root .bt-rotate-90,
:root .bt-rotate-180,
:root .bt-rotate-270,
:root .bt-flip-horizontal,
:root .bt-flip-vertical {
  filter: none;
}

.bt-stack {
  position: relative;
  display: inline-block;
  width: 1.2857142857em;
  height: 1em;
  line-height: 1em;
  vertical-align: baseline;
}

.bt-stack-sm {
  position: absolute;
  top: 0;
  left: 0;
  line-height: inherit;
  font-size: .5em;
}

.bt-stack-1x, .bt-stack-sm {
  display: inline-block;
  width: 100%;
  text-align: center;
}

.bt-inverse {
  color: #fff;
}

/* Black Tie uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.bt-bars:before {
  content: "";
}

.bt-envelope:before {
  content: "";
}

.bt-search:before {
  content: "";
}

.bt-search-plus:before {
  content: "";
}

.bt-search-minus:before {
  content: "";
}

.bt-phone:before {
  content: "";
}

.bt-comment:before {
  content: "";
}

.bt-commenting:before {
  content: "";
}

.bt-comments:before {
  content: "";
}

.bt-rss:before {
  content: "";
}

.bt-times:before {
  content: "";
}

.bt-times-circle:before {
  content: "";
}

.bt-clock:before {
  content: "";
}

.bt-star:before {
  content: "";
}

.bt-star-half:before {
  content: "";
}

.bt-check:before {
  content: "";
}

.bt-check-circle:before {
  content: "";
}

.bt-check-square:before {
  content: "";
}

.bt-th:before {
  content: "";
}

.bt-th-large:before {
  content: "";
}

.bt-heart:before {
  content: "";
}

.bt-heart-half:before {
  content: "";
}

.bt-calendar:before {
  content: "";
}

.bt-shopping-cart:before {
  content: "";
}

.bt-plus:before {
  content: "";
}

.bt-plus-circle:before {
  content: "";
}

.bt-plus-square:before {
  content: "";
}

.bt-pen:before {
  content: "";
}

.bt-minus:before {
  content: "";
}

.bt-minus-circle:before {
  content: "";
}

.bt-minus-square:before {
  content: "";
}

.bt-pencil:before {
  content: "";
}

.bt-edit:before {
  content: "";
}

.bt-thumbs-up:before {
  content: "";
}

.bt-thumbs-down:before {
  content: "";
}

.bt-gear:before {
  content: "";
}

.bt-trash:before {
  content: "";
}

.bt-file:before {
  content: "";
}

.bt-info-circle:before {
  content: "";
}

.bt-label:before {
  content: "";
}

.bt-rocket:before {
  content: "";
}

.bt-book:before {
  content: "";
}

.bt-book-open:before {
  content: "";
}

.bt-notebook:before {
  content: "";
}

.bt-camera:before {
  content: "";
}

.bt-folder:before {
  content: "";
}

.bt-quote-left:before {
  content: "";
}

.bt-quote-right:before {
  content: "";
}

.bt-eye:before {
  content: "";
}

.bt-lock:before {
  content: "";
}

.bt-lock-open:before {
  content: "";
}

.bt-gift:before {
  content: "";
}

.bt-spinner-clock:before {
  content: "";
}

.bt-spinner:before {
  content: "";
}

.bt-wrench:before {
  content: "";
}

.bt-cloud:before {
  content: "";
}

.bt-cloud-upload:before {
  content: "";
}

.bt-cloud-download:before {
  content: "";
}

.bt-sync:before {
  content: "";
}

.bt-question-circle:before {
  content: "";
}

.bt-share:before {
  content: "";
}

.bt-briefcase:before {
  content: "";
}

.bt-money:before {
  content: "";
}

.bt-megaphone:before {
  content: "";
}

.bt-sign-in:before {
  content: "";
}

.bt-sign-out:before {
  content: "";
}

.bt-film:before {
  content: "";
}

.bt-trophy:before {
  content: "";
}

.bt-code:before {
  content: "";
}

.bt-light-bulb:before {
  content: "";
}

.bt-print:before {
  content: "";
}

.bt-fax:before {
  content: "";
}

.bt-video:before {
  content: "";
}

.bt-signal:before {
  content: "";
}

.bt-sitemap:before {
  content: "";
}

.bt-upload:before {
  content: "";
}

.bt-download:before {
  content: "";
}

.bt-key:before {
  content: "";
}

.bt-mug:before {
  content: "";
}

.bt-bookmark:before {
  content: "";
}

.bt-flag:before {
  content: "";
}

.bt-external-link:before {
  content: "";
}

.bt-smile:before {
  content: "";
}

.bt-frown:before {
  content: "";
}

.bt-meh:before {
  content: "";
}

.bt-magic:before {
  content: "";
}

.bt-bolt:before {
  content: "";
}

.bt-exclamation-triangle:before {
  content: "";
}

.bt-exclamation-circle:before {
  content: "";
}

.bt-flask:before {
  content: "";
}

.bt-music:before {
  content: "";
}

.bt-push-pin:before {
  content: "";
}

.bt-shield:before {
  content: "";
}

.bt-sort:before {
  content: "";
}

.bt-reply:before {
  content: "";
}

.bt-forward:before {
  content: "";
}

.bt-reply-all:before {
  content: "";
}

.bt-forward-all:before {
  content: "";
}

.bt-bell:before {
  content: "";
}

.bt-bell-off:before {
  content: "";
}

.bt-ban:before {
  content: "";
}

.bt-database:before {
  content: "";
}

.bt-hard-drive:before {
  content: "";
}

.bt-merge:before {
  content: "";
}

.bt-fork:before {
  content: "";
}

.bt-wifi:before {
  content: "";
}

.bt-paper-plane:before {
  content: "";
}

.bt-inbox:before {
  content: "";
}

.bt-fire:before {
  content: "";
}

.bt-play:before {
  content: "";
}

.bt-pause:before {
  content: "";
}

.bt-stop:before {
  content: "";
}

.bt-play-circle:before {
  content: "";
}

.bt-next:before {
  content: "";
}

.bt-previous:before {
  content: "";
}

.bt-repeat:before {
  content: "";
}

.bt-fast-forward:before {
  content: "";
}

.bt-fast-reverse:before {
  content: "";
}

.bt-volume:before {
  content: "";
}

.bt-volume-off:before {
  content: "";
}

.bt-volume-up:before {
  content: "";
}

.bt-volume-down:before {
  content: "";
}

.bt-maximize:before {
  content: "";
}

.bt-minimize:before {
  content: "";
}

.bt-closed-captions:before {
  content: "";
}

.bt-shuffle:before {
  content: "";
}

.bt-triangle:before {
  content: "";
}

.bt-square:before {
  content: "";
}

.bt-circle:before {
  content: "";
}

.bt-hexagon:before {
  content: "";
}

.bt-octagon:before {
  content: "";
}

.bt-angle-up:before {
  content: "";
}

.bt-angle-down:before {
  content: "";
}

.bt-angle-left:before {
  content: "";
}

.bt-angle-right:before {
  content: "";
}

.bt-angles-up:before {
  content: "";
}

.bt-angles-down:before {
  content: "";
}

.bt-angles-left:before {
  content: "";
}

.bt-angles-right:before {
  content: "";
}

.bt-arrow-up:before {
  content: "";
}

.bt-arrow-down:before {
  content: "";
}

.bt-arrow-left:before {
  content: "";
}

.bt-arrow-right:before {
  content: "";
}

.bt-bar-chart:before {
  content: "";
}

.bt-pie-chart:before {
  content: "";
}

.bt-circle-arrow-up:before {
  content: "";
}

.bt-circle-arrow-down:before {
  content: "";
}

.bt-circle-arrow-left:before {
  content: "";
}

.bt-circle-arrow-right:before {
  content: "";
}

.bt-caret-up:before {
  content: "";
}

.bt-caret-down:before {
  content: "";
}

.bt-caret-left:before {
  content: "";
}

.bt-caret-right:before {
  content: "";
}

.bt-long-arrow-up:before {
  content: "";
}

.bt-long-arrow-down:before {
  content: "";
}

.bt-long-arrow-left:before {
  content: "";
}

.bt-long-arrow-right:before {
  content: "";
}

.bt-Bold:before {
  content: "";
}

.bt-italic:before {
  content: "";
}

.bt-underline:before {
  content: "";
}

.bt-link:before {
  content: "";
}

.bt-paper-clip:before {
  content: "";
}

.bt-align-left:before {
  content: "";
}

.bt-align-center:before {
  content: "";
}

.bt-align-right:before {
  content: "";
}

.bt-align-justify:before {
  content: "";
}

.bt-cut:before {
  content: "";
}

.bt-copy:before {
  content: "";
}

.bt-paste:before {
  content: "";
}

.bt-photo:before {
  content: "";
}

.bt-table:before {
  content: "";
}

.bt-ulist:before {
  content: "";
}

.bt-olist:before {
  content: "";
}

.bt-indent:before {
  content: "";
}

.bt-outdent:before {
  content: "";
}

.bt-undo:before {
  content: "";
}

.bt-redo:before {
  content: "";
}

.bt-sup:before {
  content: "";
}

.bt-sub:before {
  content: "";
}

.bt-text-size:before {
  content: "";
}

.bt-text-color:before {
  content: "";
}

.bt-remove-formatting:before {
  content: "";
}

.bt-blockquote:before {
  content: "";
}

.bt-globe:before {
  content: "";
}

.bt-map:before {
  content: "";
}

.bt-map-arrow:before {
  content: "";
}

.bt-map-marker:before {
  content: "";
}

.bt-map-pin:before {
  content: "";
}

.bt-home:before {
  content: "";
}

.bt-building:before {
  content: "";
}

.bt-industry:before {
  content: "";
}

.bt-desktop:before {
  content: "";
}

.bt-laptop:before {
  content: "";
}

.bt-tablet:before {
  content: "";
}

.bt-mobile:before {
  content: "";
}

.bt-tv:before {
  content: "";
}

.bt-radio-checked:before {
  content: "";
}

.bt-radio-unchecked:before {
  content: "";
}

.bt-checkbox-checked:before {
  content: "";
}

.bt-checkbox-unchecked:before {
  content: "";
}

.bt-checkbox-intermediate:before {
  content: "";
}

.bt-user:before {
  content: "";
}

.bt-user-male:before {
  content: "";
}

.bt-user-female:before {
  content: "";
}

.bt-crown:before {
  content: "";
}

.bt-credit-card:before {
  content: "";
}

.bt-strikethrough:before {
  content: "";
}

.bt-eject:before {
  content: "";
}

.bt-ellipsis-h:before {
  content: "";
}

.bt-ellipsis-v:before {
  content: "";
}

.fab-facebook:before {
  content: "";
}

.fab-facebook-alt:before {
  content: "";
}

.fab-twitter:before {
  content: "";
}

.fab-linkedin:before {
  content: "";
}

.fab-linkedin-alt:before {
  content: "";
}

.fab-instagram:before {
  content: "";
}

.fab-github:before {
  content: "";
}

.fab-github-alt:before {
  content: "";
}

.fab-googleplus:before {
  content: "";
}

.fab-googleplus-alt:before {
  content: "";
}

.fab-pinterest:before {
  content: "";
}

.fab-pinterest-alt:before {
  content: "";
}

.fab-tumblr:before {
  content: "";
}

.fab-tumblr-alt:before {
  content: "";
}

.fab-bitcoin:before {
  content: "";
}

.fab-bitcoin-alt:before {
  content: "";
}

.fab-dropbox:before {
  content: "";
}

.fab-stackexchange:before {
  content: "";
}

.fab-stackoverflow:before {
  content: "";
}

.fab-flickr:before {
  content: "";
}

.fab-flickr-alt:before {
  content: "";
}

.fab-bitbucket:before {
  content: "";
}

.fab-html5:before {
  content: "";
}

.fab-css3:before {
  content: "";
}

.fab-apple:before {
  content: "";
}

.fab-windows:before {
  content: "";
}

.fab-android:before {
  content: "";
}

.fab-linux:before {
  content: "";
}

.fab-dribbble:before {
  content: "";
}

.fab-youtube:before {
  content: "";
}

.fab-skype:before {
  content: "";
}

.fab-foursquare:before {
  content: "";
}

.fab-trello:before {
  content: "";
}

.fab-maxcdn:before {
  content: "";
}

.fab-gittip:before,
.fab-gratipay:before {
  content: "";
}

.fab-vimeo:before {
  content: "";
}

.fab-vimeo-alt:before {
  content: "";
}

.fab-slack:before {
  content: "";
}

.fab-wordpress:before {
  content: "";
}

.fab-wordpress-alt:before {
  content: "";
}

.fab-openid:before {
  content: "";
}

.fab-yahoo:before {
  content: "";
}

.fab-yahoo-alt:before {
  content: "";
}

.fab-reddit:before {
  content: "";
}

.fab-google:before {
  content: "";
}

.fab-google-alt:before {
  content: "";
}

.fab-stumbleupon:before {
  content: "";
}

.fab-stumbleupon-alt:before {
  content: "";
}

.fab-delicious:before {
  content: "";
}

.fab-digg:before {
  content: "";
}

.fab-piedpiper:before {
  content: "";
}

.fab-piedpiper-alt:before {
  content: "";
}

.fab-drupal:before {
  content: "";
}

.fab-joomla:before {
  content: "";
}

.fab-behance:before {
  content: "";
}

.fab-steam:before {
  content: "";
}

.fab-steam-alt:before {
  content: "";
}

.fab-spotify:before {
  content: "";
}

.fab-deviantart:before {
  content: "";
}

.fab-soundcloud:before {
  content: "";
}

.fab-vine:before {
  content: "";
}

.fab-codepen:before {
  content: "";
}

.fab-jsfiddle:before {
  content: "";
}

.fab-rebel:before {
  content: "";
}

.fab-empire:before {
  content: "";
}

.fab-git:before {
  content: "";
}

.fab-hackernews:before {
  content: "";
}

.fab-hackernews-alt:before {
  content: "";
}

.fab-slideshare:before {
  content: "";
}

.fab-twitch:before {
  content: "";
}

.fab-yelp:before {
  content: "";
}

.fab-paypal:before {
  content: "";
}

.fab-google-wallet:before {
  content: "";
}

.fab-angellist:before {
  content: "";
}

.fab-cc-visa:before {
  content: "";
}

.fab-cc-mastercard:before {
  content: "";
}

.fab-cc-discover:before {
  content: "";
}

.fab-cc-amex:before {
  content: "";
}

.fab-cc-paypal:before {
  content: "";
}

.fab-cc-stripe:before {
  content: "";
}

.fab-lastfm:before {
  content: "";
}

.fab-whatsapp:before {
  content: "";
}

.fab-medium:before {
  content: "";
}

.fab-meanpath:before {
  content: "";
}

.fab-meanpath-alt:before {
  content: "";
}

.fab-pagelines:before {
  content: "";
}

.fab-ioxhost:before {
  content: "";
}

.fab-buysellads:before {
  content: "";
}

.fab-buysellads-alt:before {
  content: "";
}

.fab-connectdevelop:before {
  content: "";
}

.fab-dashcube:before {
  content: "";
}

.fab-forumbee:before {
  content: "";
}

.fab-leanpub:before {
  content: "";
}

.fab-sellsy:before {
  content: "";
}

.fab-shirtsinbulk:before {
  content: "";
}

.fab-simplybuilt:before {
  content: "";
}

.fab-skyatlas:before {
  content: "";
}

.fab-viacoin:before {
  content: "";
}

.fab-codiepie:before {
  content: "";
}

.fab-queue:before {
  content: "";
}

.fab-queue-alt:before {
  content: "";
}

.fab-fonticons:before {
  content: "";
}

.fab-fonticons-alt:before {
  content: "";
}

.fab-blacktie:before {
  content: "";
}

.fab-blacktie-alt:before {
  content: "";
}

.fab-xing:before {
  content: "";
}

.fab-vk:before {
  content: "";
}

.fab-weibo:before {
  content: "";
}

.fab-renren:before {
  content: "";
}

.fab-tencent-weibo:before {
  content: "";
}

.fab-qq:before {
  content: "";
}

.fab-wechat:before,
.fab-weixin:before {
  content: "";
}

/*
 * Colors
 */
/*
 * general Color Variables
 */
/*
 * Fonts
 */
/*
 * Transitions
 */
/*
 * Text Shadows
 */
/*
 * Grid
 */
/*
 * Breakpoints
 */
/*
 * zIndex
 */
/*
 * Navi
 */
/*
 * Teasers
 */
/*
 * Promoboxes
 */
.fluid-container {
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  max-width: 1400px;
}

.fluid-container:after {
  content: " ";
  display: block;
  clear: both;
}

@media (min-width: 2501px) {
  .fluid-container {
    max-width: 1600px;
  }
}

.row {
  margin-left: -1.1768447837%;
  margin-right: -1.1768447837%;
}

.row:after, .row:before {
  content: " ";
  /* Older browser do not support empty content */
  visibility: hidden;
  display: block;
  height: 0;
  clear: both;
}

.col {
  box-sizing: border-box;
}

.col-2 {
  width: 16.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.col-3 {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .col-3 {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .col-3 {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.col-4 {
  width: 33.3333333333%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .col-4 {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (max-width: 544px) {
  .col-4 {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.col-8 {
  width: 66.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .col-8 {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (max-width: 544px) {
  .col-8 {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.col-1-5 {
  width: 20%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.col-6 {
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .col-6 {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.col-search {
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-left: 50%;
}

@media (max-width: 767px) {
  .col-search {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.col-12 {
  width: 100%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.col-8-center {
  width: 66.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-left: 16.6666666667%;
}

@media (max-width: 960px) {
  .col-8-center {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    margin-left: 0%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.col-mid-center {
  width: 66.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-left: 16.6666666667%;
  text-align: center;
}

@media (max-width: 960px) {
  .col-mid-center {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    margin-left: 0%;
  }
}

.col-last {
  float: right;
}

.col-sidebar {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  min-height: 1px;
}

@media (max-width: 992px) {
  .col-sidebar {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    padding: 0 1rem;
  }
}

.col-content {
  width: 75%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 992px) {
  .col-content {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    padding: 0 1rem;
  }
}

/*
 * Section
 */
section.section {
  padding: 2.2rem 0;
  margin: 0;
}

section.section.light {
  background-color: #f8f8f8;
}

section.section.dark {
  background-color: #f2f2f2;
}

section.section.darker {
  background-color: #e5e5e5;
}

/*
 * clearfix
 */
.clearfix:after, .clearfix:before {
  content: " ";
  /* Older browser do not support empty content */
  visibility: hidden;
  display: block;
  height: 0;
  clear: both;
}

/*
 * squareBox
 */
.square-b {
  width: 16.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  height: 0;
  padding-top: 16.6666666667%;
  position: relative;
}

.square-b span {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

@media (max-width: 1250px) {
  .page {
    padding-top: 100px;
  }
}

@media (max-width: 767px) {
  .fluid-container.container-100 > .row > [class^="col"] {
    padding: 0 1rem;
  }
}

.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

/*
 *  Fonts
 */
@font-face {
  font-family: 'BlissPro-ExtraLight';
  src: url("../fonts/BlissPro/BlsPrW-XLt/blsprw-xlt.eot");
  src: url("../fonts/BlissPro/BlsPrW-XLt/blsprw-xlt.eot?#iefix") format("embedded-opentype"), url("../fonts/BlissPro/BlsPrW-XLt/blsprw-xlt.woff2") format("woff2"), url("../fonts/BlissPro/BlsPrW-XLt/blsprw-xlt.woff") format("woff"), url("../fonts/BlissPro/BlsPrW-XLt/blsprw-xlt.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'BlissPro-Light';
  src: url("../fonts/BlissPro/BlsPrW-Lt/blsprw-lt.eot");
  src: url("../fonts/BlissPro/BlsPrW-Lt/blsprw-lt.eot?#iefix") format("embedded-opentype"), url("../fonts/BlissPro/BlsPrW-Lt/blsprw-lt.woff2") format("woff2"), url("../fonts/BlissPro/BlsPrW-Lt/blsprw-lt.woff") format("woff"), url("../fonts/BlissPro/BlsPrW-Lt/blsprw-lt.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'BlissPro-Bold';
  src: url("../fonts/BlissPro/BlsPrW-Bd/blsprw-bd.eot");
  src: url("../fonts/BlissPro/BlsPrW-Bd/blsprw-bd.eot?#iefix") format("embedded-opentype"), url("../fonts/BlissPro/BlsPrW-Bd/blsprw-bd.woff2") format("woff2"), url("../fonts/BlissPro/BlsPrW-Bd/blsprw-bd.woff") format("woff"), url("../fonts/BlissPro/BlsPrW-Bd/blsprw-bd.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

/*
 *  Fluid Typography
 */
html {
  font-size: 16px;
  line-height: 1.5;
}

@media (min-width: 1400px) and (max-width: 2500px) {
  html {
    font-size: 18px;
    font-size: calc(16px + (18 - 16) * (100vw - 1400px) / (2500 - 1400));
    line-height: 1.6;
  }
}

@media (min-width: 2501px) {
  html {
    font-size: 18px;
    line-height: 1.6;
  }
}

code, kbd, pre, samp {
  display: block;
  background-color: #F9F9F9;
  border: 1px dashed #2F6FAB;
  color: black;
  line-height: 1.1em;
  padding: 1em;
  font-size: 0.6em;
  margin-bottom: 2rem;
}

body {
  font-family: "BlissPro-ExtraLight";
  font-weight: normal;
  color: #666666;
}

strong {
  font-family: "BlissPro-Bold";
  font-weight: normal;
}

.small {
  font-size: 0.8em;
}

.smaller {
  font-size: 0.6em;
}

.big {
  font-size: 1.2em;
}

.bigger {
  font-family: "BlissPro-Light";
  font-size: 1.4em;
}

.text-inside {
  padding-left: 1.2em;
}

.text-align-left {
  text-align: left;
}

.text-align-center {
  text-align: center;
}

p {
  margin-top: 0;
  margin-bottom: 0.75rem;
}

p.columnized {
  /* Chrome, Safari, Opera */
  /* Firefox */
  column-count: 2;
  /* Chrome, Safari, Opera */
  /* Firefox */
  column-gap: 40px;
}

@media (max-width: 767px) {
  p.columnized {
    /* Chrome, Safari, Opera */
    /* Firefox */
    column-count: 1;
  }
}

p.align-center {
  text-align: center;
}

a {
  text-decoration: underline;
  color: #666666;
}

a:hover, a:focus, a:active {
  text-decoration: none;
  color: #ea7322;
}

a.link-with-arrow-down {
  text-decoration: none;
  font-family: "BlissPro-Bold";
}

a.link-with-arrow-down:before {
  color: #ff8d2f;
  content: "\f099";
  font-family: BlackTie;
  font-weight: normal;
  font-size: 0.65em;
  margin-right: 0.65em;
}

a.link-with-arrow-down:hover, a.link-with-arrow-down:focus, a.link-with-arrow-down:active {
  text-decoration: none;
}

a.link-with-arrow-down:hover:before, a.link-with-arrow-down:focus:before, a.link-with-arrow-down:active:before {
  text-decoration: none;
}

a.link-download {
  text-decoration: none;
  font-family: "BlissPro-Bold";
}

a.link-download:before {
  color: #ff8d2f;
  content: "\f056";
  font-family: BlackTie;
  font-weight: normal;
  font-size: 0.65em;
  margin-right: 0.65em;
}

a.link-download:hover, a.link-download:focus, a.link-download:active {
  text-decoration: none;
}

a.link-download:hover:before, a.link-download:focus:before, a.link-download:active:before {
  text-decoration: none;
}

a.link-extern {
  text-decoration: none;
  font-family: "BlissPro-Bold";
}

a.link-extern:before {
  color: #ff8d2f;
  content: "\f05b";
  font-family: BlackTie;
  font-weight: normal;
  font-size: 0.65em;
  margin-right: 0.65em;
}

a.link-extern:hover, a.link-extern:focus, a.link-extern:active {
  text-decoration: none;
}

a.link-extern:hover:before, a.link-extern:focus:before, a.link-extern:active:before {
  text-decoration: none;
}

a.link-extern-2 {
  padding-right: 20px;
  text-decoration: none;
}

a.link-extern-2:after {
  content: "\f05b";
  font-family: BlackTie;
  font-weight: normal;
  font-size: 0.65em;
  margin-left: 0.58em;
}

a.link-extern-2:hover, a.link-extern-2:focus, a.link-extern-2:active {
  text-decoration: none;
}

a.link-extern-2:hover:after, a.link-extern-2:focus:after, a.link-extern-2:active:after {
  font-weight: 600;
}

a.link-normal {
  text-decoration: none;
  color: #666666;
}

a.link-normal:hover, a.link-normal:focus, a.link-normal:active {
  text-decoration: none;
  color: #ea7322;
}

a.link-highlighted {
  text-decoration: none;
  color: #ea7322;
}

a.link-highlighted:hover, a.link-highlighted:focus, a.link-highlighted:active {
  text-decoration: none;
  color: #c85a00;
}

a.link-some {
  text-decoration: none;
}

a.link-some:hover, a.link-some:focus, a.link-some:active {
  text-decoration: none;
}

a.link-some:hover:before, a.link-some:focus:before, a.link-some:active:before {
  font-weight: 600;
}

a.link-some:before {
  display: inline-block;
  width: 1em;
  margin-right: 0.6rem;
}

a.link-xing:before {
  content: "\f168";
  font-family: Font Awesome Brands;
  font-weight: normal;
  font-size: 1em;
}

a.link-facebook:before {
  content: "\f39e";
  font-family: Font Awesome Brands;
  font-weight: normal;
  font-size: 1em;
}

a.link-instagram:before {
  content: "\f16d";
  font-family: Font Awesome Brands;
  font-weight: normal;
  font-size: 1em;
}

a.link-youtube:before {
  content: "\f167";
  font-family: Font Awesome Brands;
  font-weight: normal;
  font-size: 1em;
}

a.link-linkedin:before {
  content: "\f004";
  font-family: Font Awesome Brands;
  font-weight: normal;
  font-size: 1em;
}

a.link-twitter:before {
  content: "\e61b";
  font-family: Font Awesome Brands;
  font-weight: normal;
  font-size: 1em;
}

a.link-callback {
  text-decoration: none;
  font-family: "BlissPro-Bold";
}

a.link-callback:before {
  color: #ff8d2f;
  content: "\f005";
  font-family: BlackTie;
  font-weight: normal;
  font-size: 0.65em;
  margin-right: 0.65em;
}

a.link-callback:hover, a.link-callback:focus, a.link-callback:active {
  text-decoration: none;
}

a.link-callback:hover:before, a.link-callback:focus:before, a.link-callback:active:before {
  text-decoration: none;
}

.link-with-arrow {
  text-decoration: none;
  font-family: "BlissPro-Bold";
}

.link-with-arrow:before {
  color: #ff8d2f;
  content: "\f09b";
  font-family: BlackTie;
  font-weight: normal;
  font-size: 0.65em;
  margin-right: 0.65em;
}

.link-with-arrow:hover, .link-with-arrow:focus, .link-with-arrow:active {
  text-decoration: none;
}

.link-with-arrow:hover:before, .link-with-arrow:focus:before, .link-with-arrow:active:before {
  text-decoration: none;
}

.img-caption {
  padding: 1rem;
  margin: 0;
  background: #e5e5e5;
}

.heroTitle {
  display: inline-block;
  color: #fafafa;
  background-color: #ff8d2f;
  padding: 0.3em 0.7em;
  font-size: 2.5rem;
  line-height: 1;
}

.NoBackground {
  display: inline-block;
  color: #fafafa;
  background-color: transparent;
  padding: 0.3em 0.7em;
  font-size: 1.6rem;
  line-height: 1;
}

.heroSubtitle {
  display: inline-block;
  color: #fafafa;
  background-color: #140043;
  padding: 0.5em 1em;
  font-size: 1.5rem;
  line-height: 1.1;
}

.heroSubtitleNoBackground {
  color: #666666;
  display: block;
  font-family: BlissPro-Bold;
  font-size: 0.5em;
  font-weight: normal;
  margin-top: 0.4rem;
}

.promobox-title {
  font-size: 2rem;
  margin-bottom: 1rem;
  margin-top: 0;
  line-height: 1.2;
  color: #140043;
}

.teaser-switch-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-family: "BlissPro-Light";
  margin-top: 0;
  line-height: 1.2;
  color: #140043;
  font-weight: normal;
}

.teaser-landingpage-title {
  font-family: "BlissPro-Light";
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
  margin-top: 0;
  line-height: 1.2;
  color: #140043;
  font-weight: normal;
}

.teaser-special-title {
  font-size: 2rem;
  margin-bottom: 1rem;
  margin-top: 0;
  line-height: 1.2;
  color: #140043;
}

.teaser-special-subtitle {
  font-family: "BlissPro-Light";
  font-size: 1.2rem;
  margin-bottom: 0.4rem;
  margin-top: 0;
  line-height: 1.2;
  color: #ff8d2f;
}

.h1,
.h1-align-center {
  font-family: "BlissPro-ExtraLight";
  font-size: 2.7rem;
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 2rem;
  font-weight: normal;
}

.h1-alternative,
.h1-alternative-align-center {
  font-family: "BlissPro-ExtraLight";
  font-size: 2.7rem;
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 2rem;
  font-weight: normal;
  color: #140043;
}

.h2,
.h2-align-center {
  font-family: "BlissPro-Light";
  font-size: 1.8rem;
  line-height: 1.2;
  margin-top: 4rem;
  margin-bottom: 1rem;
  font-weight: normal;
}

.h2-alternative,
.h2-alternative-align-center {
  font-family: "BlissPro-Light";
  font-size: 1.8rem;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: normal;
  color: #140043;
}

.h3,
.h3-align-center {
  font-family: "BlissPro-Light";
  font-size: 1.4rem;
  line-height: 1.1;
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: normal;
}

[class*="align-center"] {
  text-align: center;
}

.preline {
  font-family: "BlissPro-Light";
  font-size: 1rem;
  line-height: 1.1;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: normal;
}

ul {
  list-style: none;
  margin-top: 0;
  margin-bottom: 1rem;
  padding-left: 0.5rem;
}

ul li {
  position: relative;
  padding-left: 1.5rem;
}

ul li:before {
  content: "■";
  color: #140043;
  position: absolute;
  top: -2px;
  left: 0;
}

/*
 * Text Colors
 */
.text-color-primary {
  color: #ea7322;
}

.text-color-green {
  color: #7cbf12;
}

.text-color-blue {
  color: #587799;
}

/*
 * Header and Footer Title and Lists
 */
.col-title {
  border-bottom: 2px solid #ff8d2f;
  font-family: "BlissPro-Light";
  white-space: nowrap;
  color: #fafafa;
  padding-bottom: 6px;
  margin-bottom: 0.6rem;
}

.col-title > p {
  margin: 0;
}

ul.col-list {
  padding: 0;
  margin: 0;
  margin-top: 0;
  margin-bottom: 1rem;
  list-style: none;
}

ul.col-list li {
  padding-left: 0;
  padding-bottom: 0.3rem;
}

ul.col-list li:before {
  display: none;
}

ul.col-list li a {
  text-decoration: none;
}

ul.col-list li a:hover, ul.col-list li a:focus, ul.col-list li a:active {
  text-decoration: none;
}

.theme-secondary .h1-alternative {
  color: #ff8d2f;
  text-transform: uppercase;
}

.btn {
  display: inline-block;
  border: 0;
  padding: 0.4em 0.8em;
  line-height: 1;
  border-radius: 1em;
}

.btn-primary, .btn-secondary, .btn-warning {
  background-color: #ff8d2f;
  color: #fafafa;
  text-decoration: none;
  transition: all, 0.25s, ease-in-out;
  margin-top: 1rem;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active, .btn-warning:hover, .btn-warning:focus, .btn-warning:active {
  text-decoration: none;
  color: #fafafa;
  background-color: #c85a00;
  transition: all, 0.25s, ease-in-out;
}

.btn-secondary, .btn-warning {
  background-color: #140043;
}

.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active, .btn-warning:hover, .btn-warning:focus, .btn-warning:active {
  background-color: #3200a9;
}

.btn-lg {
  padding: 0.6em 0.8em;
  font-size: 1.3rem;
  line-height: 1.1;
  border-radius: 0.8em;
  font-family: "BlissPro-Light";
}

input[type="search"] {
  box-sizing: border-box;
}

/* fix webkit overflow sroll-x */
.mm-slideout {
  overflow-x: hidden;
}

/* fix top position */
.mm-slideout .teaser-home .teaser-canvas {
  margin-top: 0;
}

.navbar-mobile {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  height: auto;
  width: 100%;
  background: #fafafa;
  background: rgba(250, 250, 250, 0.9);
  z-index: 100;
  height: 100px;
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94) 0s;
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

.navbar-mobile.scrolled {
  top: -100px;
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

@media (max-width: 1250px) {
  .navbar-mobile {
    display: block;
  }
}

.navbar-mobile .logo-container {
  position: absolute;
  left: 1.1768447837%;
  top: 10px;
  max-width: 211px;
}

.navbar-mobile .mobileMenuButton {
  position: absolute;
  top: 0;
  right: 1.1768447837%;
  padding: 8px 12px;
  border: 1px solid #ff8d2f;
  border-radius: 3px;
  top: 50%;
  transform: translateY(-50%);
}

.navbar-mobile .mobileMenuButton span {
  display: block;
  width: 34px;
  height: 2px;
  border-radius: 15px;
  border: 2px solid #ff8d2f;
}

.navbar-mobile .mobileMenuButton span + span {
  margin-top: 5px;
}

.navbar-mobile .mobileMenuButton:hover, .navbar-mobile .mobileMenuButton:focus, .navbar-mobile .mobileMenuButton:active {
  background-color: #c85a00;
  border-color: #c85a00;
}

.navbar-mobile .mobileMenuButton:hover span, .navbar-mobile .mobileMenuButton:focus span, .navbar-mobile .mobileMenuButton:active span {
  border-color: #fafafa;
}

#mobileNav {
  display: none;
  background: #444;
  color: #fafafa;
  font-family: "BlissPro-Light";
  font-weight: normal;
  font-size: 1.3rem;
  line-height: 1.1;
  visibility: hidden;
}

@media (max-width: 1250px) {
  #mobileNav.mm-opened {
    display: block;
  }
}

#mobileNav.mm-menu {
  visibility: visible;
}

#mobileNav .mm-panel .mm-navbar {
  border-bottom: 1px solid #666666;
}

#mobileNav .mm-panel .mm-navbar .mm-title {
  color: #fafafa;
}

#mobileNav .mm-panel .mm-navbar a:before {
  border-color: #fafafa;
}

#mobileNav .mm-panel ul.mm-listview {
  font-size: inherit;
}

#mobileNav .mm-panel ul.mm-listview li {
  padding-left: 0;
}

#mobileNav .mm-panel ul.mm-listview li:before {
  display: none;
}

#mobileNav .mm-panel ul.mm-listview li:after {
  border-color: #666666;
  left: 0;
}

#mobileNav .mm-panel ul.mm-listview li a:before {
  border-color: #666666;
}

#mobileNav .mm-panel ul.mm-listview li a:after {
  border-color: #fafafa;
}

#mobileNav .mm-panel ul.mm-listview li:hover a, #mobileNav .mm-panel ul.mm-listview li:active a, #mobileNav .mm-panel ul.mm-listview li:focus a {
  color: #ff8d2f;
}

#mobileNav .mm-panel ul.mm-listview li.active a {
  color: #c85a00;
}

#mobileNav .mm-panel ul.mm-listview li.search-container {
  padding: 10px 10px 10px 20px;
}

#mobileNav .mm-panel ul.mm-listview li.search-container .search-query {
  width: 80%;
  border: 1px solid #666666;
  border-radius: 0px;
  padding: 4px 10px;
  text-transform: capitalize;
  color: #666666;
}

#mobileNav .mm-panel ul.mm-listview li.search-container .search-query:hover, #mobileNav .mm-panel ul.mm-listview li.search-container .search-query:focus, #mobileNav .mm-panel ul.mm-listview li.search-container .search-query:active {
  outline: none;
}

#mobileNav .mm-panel ul.mm-listview li.search-container button {
  border: none;
  background-color: transparent;
  position: absolute;
  top: 17px;
  right: 10px;
}

#mobileNav .mm-panel ul.mm-listview li.search-container button:hover, #mobileNav .mm-panel ul.mm-listview li.search-container button:focus, #mobileNav .mm-panel ul.mm-listview li.search-container button:active {
  outline: none;
}

#mobileNav .mm-panel ul.mm-listview li.meta-item {
  background-color: #515151;
}

/* Magnific Popup CSS */
.mfp-bg {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1042;
  overflow: hidden;
  position: fixed;
  background: #0b0b0b;
  opacity: 0.8;
}

.mfp-wrap {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1043;
  position: fixed;
  outline: none !important;
  -webkit-backface-visibility: hidden;
}

.mfp-container {
  text-align: center;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 0 8px;
  box-sizing: border-box;
}

.mfp-container:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.mfp-align-top .mfp-container:before {
  display: none;
}

.mfp-content {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0 auto;
  text-align: left;
  z-index: 1045;
}

.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
  width: 100%;
  cursor: auto;
}

.mfp-ajax-cur {
  cursor: progress;
}

.mfp-zoom-out-cur, .mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  cursor: zoom-out;
}

.mfp-zoom {
  cursor: pointer;
  cursor: zoom-in;
}

.mfp-auto-cursor .mfp-content {
  cursor: auto;
}

.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
  -webkit-user-select: none;
  user-select: none;
}

.mfp-loading.mfp-figure {
  display: none;
}

.mfp-hide {
  display: none !important;
}

.mfp-preloader {
  color: #CCC;
  position: absolute;
  top: 50%;
  width: auto;
  text-align: center;
  margin-top: -0.8em;
  left: 8px;
  right: 8px;
  z-index: 1044;
}

.mfp-preloader a {
  color: #CCC;
}

.mfp-preloader a:hover {
  color: #FFF;
}

.mfp-s-ready .mfp-preloader {
  display: none;
}

.mfp-s-error .mfp-content {
  display: none;
}

button.mfp-close, button.mfp-arrow {
  overflow: visible;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
  display: block;
  outline: none;
  padding: 0;
  z-index: 1046;
  box-shadow: none;
  touch-action: manipulation;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

.mfp-close {
  width: 44px;
  height: 44px;
  line-height: 44px;
  position: absolute;
  right: 0;
  top: 0;
  text-decoration: none;
  text-align: center;
  opacity: 0.65;
  padding: 0 0 18px 10px;
  color: #FFF;
  font-style: normal;
  font-size: 28px;
  font-family: Arial, Baskerville, monospace;
}

.mfp-close:hover, .mfp-close:focus {
  opacity: 1;
}

.mfp-close:active {
  top: 1px;
}

.mfp-close-btn-in .mfp-close {
  color: #333;
}

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
  color: #FFF;
  right: -6px;
  text-align: right;
  padding-right: 6px;
  width: 100%;
}

.mfp-counter {
  position: absolute;
  top: 0;
  right: 0;
  color: #CCC;
  font-size: 12px;
  line-height: 18px;
  white-space: nowrap;
}

.mfp-arrow {
  position: absolute;
  opacity: 0.65;
  margin: 0;
  top: 50%;
  margin-top: -55px;
  padding: 0;
  width: 90px;
  height: 110px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.mfp-arrow:active {
  margin-top: -54px;
}

.mfp-arrow:hover, .mfp-arrow:focus {
  opacity: 1;
}

.mfp-arrow:before, .mfp-arrow:after {
  content: '';
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  left: 0;
  top: 0;
  margin-top: 35px;
  margin-left: 35px;
  border: medium inset transparent;
}

.mfp-arrow:after {
  border-top-width: 13px;
  border-bottom-width: 13px;
  top: 8px;
}

.mfp-arrow:before {
  border-top-width: 21px;
  border-bottom-width: 21px;
  opacity: 0.7;
}

.mfp-arrow-left {
  left: 0;
}

.mfp-arrow-left:after {
  border-right: 17px solid #FFF;
  margin-left: 31px;
}

.mfp-arrow-left:before {
  margin-left: 25px;
  border-right: 27px solid #3F3F3F;
}

.mfp-arrow-right {
  right: 0;
}

.mfp-arrow-right:after {
  border-left: 17px solid #FFF;
  margin-left: 39px;
}

.mfp-arrow-right:before {
  border-left: 27px solid #3F3F3F;
}

.mfp-iframe-holder {
  padding-top: 40px;
  padding-bottom: 40px;
}

.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 900px;
}

.mfp-iframe-holder .mfp-close {
  top: -40px;
}

.mfp-iframe-scaler {
  width: 100%;
  height: 0;
  overflow: hidden;
  padding-top: 56.25%;
}

.mfp-iframe-scaler iframe {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: #000;
}

/* Main image in popup */
img.mfp-img {
  width: auto;
  max-width: 100%;
  height: auto;
  display: block;
  line-height: 0;
  box-sizing: border-box;
  padding: 40px 0 40px;
  margin: 0 auto;
}

/* The shadow behind the image */
.mfp-figure {
  line-height: 0;
}

.mfp-figure:after {
  content: '';
  position: absolute;
  left: 0;
  top: 40px;
  bottom: 40px;
  display: block;
  right: 0;
  width: auto;
  height: auto;
  z-index: -1;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: #444;
}

.mfp-figure small {
  color: #BDBDBD;
  display: block;
  font-size: 12px;
  line-height: 14px;
}

.mfp-figure figure {
  margin: 0;
}

.mfp-bottom-bar {
  margin-top: -36px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  cursor: auto;
}

.mfp-title {
  text-align: left;
  line-height: 18px;
  color: #F3F3F3;
  word-wrap: break-word;
  padding-right: 36px;
}

.mfp-image-holder .mfp-content {
  max-width: 100%;
}

.mfp-gallery .mfp-image-holder .mfp-figure {
  cursor: pointer;
}

@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {
  /**
       * Remove all paddings around the image on small screen
       */
  .mfp-img-mobile .mfp-image-holder {
    padding-left: 0;
    padding-right: 0;
  }
  .mfp-img-mobile img.mfp-img {
    padding: 0;
  }
  .mfp-img-mobile .mfp-figure:after {
    top: 0;
    bottom: 0;
  }
  .mfp-img-mobile .mfp-figure small {
    display: inline;
    margin-left: 5px;
  }
  .mfp-img-mobile .mfp-bottom-bar {
    background: rgba(0, 0, 0, 0.6);
    bottom: 0;
    margin: 0;
    top: auto;
    padding: 3px 5px;
    position: fixed;
    box-sizing: border-box;
  }
  .mfp-img-mobile .mfp-bottom-bar:empty {
    padding: 0;
  }
  .mfp-img-mobile .mfp-counter {
    right: 5px;
    top: 3px;
  }
  .mfp-img-mobile .mfp-close {
    top: 0;
    right: 0;
    width: 35px;
    height: 35px;
    line-height: 35px;
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    text-align: center;
    padding: 0;
  }
}

@media all and (max-width: 900px) {
  .mfp-arrow {
    transform: scale(0.75);
  }
  .mfp-arrow-left {
    transform-origin: 0;
  }
  .mfp-arrow-right {
    transform-origin: 100%;
  }
  .mfp-container {
    padding-left: 6px;
    padding-right: 6px;
  }
}

button.mfp-close {
  font-size: 31px;
}

.mfp-arrow {
  cursor: pointer;
  font-size: 50px;
  height: 100px;
  margin-top: -50px;
  z-index: 1045;
}

.mfp-arrow i {
  position: absolute;
  top: 50%;
  margin-top: -0.5em;
  color: #fafafa;
}

.mfp-arrow.mfp-arrow-left i {
  left: 22px;
}

.mfp-arrow.mfp-arrow-right i {
  right: 22px;
}

.mfp-arrow:before, .mfp-arrow:after {
  display: none;
}

img.mfp-img {
  padding: 0;
  margin-bottom: 40px;
}

.mfp-bottom-bar {
  margin: 0;
}

.mfp-bottom-bar .mfp-title {
  padding: 0;
  font-size: 16px;
  font-weight: normal;
  padding: 15px;
  text-align: center;
}

.mfp-bottom-bar .mfp-counter {
  display: none;
}

body.mfp-zoom-out-cur #logo {
  max-width: 70px;
}

/* 

====== Zoom effect ======

*/
.mfp-zoom-in {
  /* start state */
  /* animate in */
  /* animate out */
}

.mfp-zoom-in .mfp-with-anim {
  opacity: 0;
  transition: all 0.2s ease-in-out;
  transform: scale(0.8);
}

.mfp-zoom-in.mfp-bg {
  opacity: 0;
  transition: all 0.3s ease-out;
}

.mfp-zoom-in.mfp-ready .mfp-with-anim {
  opacity: 1;
  transform: scale(1);
}

.mfp-zoom-in.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-zoom-in.mfp-removing .mfp-with-anim {
  transform: scale(0.8);
  opacity: 0;
}

.mfp-zoom-in.mfp-removing.mfp-bg {
  opacity: 0;
}

/* 

====== Newspaper effect ======

*/
.mfp-newspaper {
  /* start state */
  /* animate in */
  /* animate out */
}

.mfp-newspaper .mfp-with-anim {
  opacity: 0;
  transition: all 0.5s;
  transform: scale(0) rotate(500deg);
}

.mfp-newspaper.mfp-bg {
  opacity: 0;
  transition: all 0.5s;
}

.mfp-newspaper.mfp-ready .mfp-with-anim {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

.mfp-newspaper.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-newspaper.mfp-removing .mfp-with-anim {
  transform: scale(0) rotate(500deg);
  opacity: 0;
}

.mfp-newspaper.mfp-removing.mfp-bg {
  opacity: 0;
}

/* 

====== Move-horizontal effect ======

*/
.mfp-move-horizontal {
  /* start state */
  /* animate in */
  /* animate out */
}

.mfp-move-horizontal .mfp-with-anim {
  opacity: 0;
  transition: all 0.3s;
  transform: translateX(-50px);
}

.mfp-move-horizontal.mfp-bg {
  opacity: 0;
  transition: all 0.3s;
}

.mfp-move-horizontal.mfp-ready .mfp-with-anim {
  opacity: 1;
  transform: translateX(0);
}

.mfp-move-horizontal.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-move-horizontal.mfp-removing .mfp-with-anim {
  transform: translateX(50px);
  opacity: 0;
}

.mfp-move-horizontal.mfp-removing.mfp-bg {
  opacity: 0;
}

/* 

====== Move-from-top effect ======

*/
.mfp-move-from-top {
  /* start state */
  /* animate in */
  /* animate out */
}

.mfp-move-from-top .mfp-content {
  vertical-align: top;
}

.mfp-move-from-top .mfp-with-anim {
  opacity: 0;
  transition: all 0.2s;
  transform: translateY(-100px);
}

.mfp-move-from-top.mfp-bg {
  opacity: 0;
  transition: all 0.2s;
}

.mfp-move-from-top.mfp-ready .mfp-with-anim {
  opacity: 1;
  transform: translateY(0);
}

.mfp-move-from-top.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-move-from-top.mfp-removing .mfp-with-anim {
  transform: translateY(-50px);
  opacity: 0;
}

.mfp-move-from-top.mfp-removing.mfp-bg {
  opacity: 0;
}

/* 

====== 3d unfold ======

*/
.mfp-3d-unfold {
  /* start state */
  /* animate in */
  /* animate out */
}

.mfp-3d-unfold .mfp-content {
  perspective: 2000px;
}

.mfp-3d-unfold .mfp-with-anim {
  opacity: 0;
  transition: all 0.3s ease-in-out;
  transform-style: preserve-3d;
  transform: rotateY(-60deg);
}

.mfp-3d-unfold.mfp-bg {
  opacity: 0;
  transition: all 0.5s;
}

.mfp-3d-unfold.mfp-ready .mfp-with-anim {
  opacity: 1;
  transform: rotateY(0deg);
}

.mfp-3d-unfold.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-3d-unfold.mfp-removing .mfp-with-anim {
  transform: rotateY(60deg);
  opacity: 0;
}

.mfp-3d-unfold.mfp-removing.mfp-bg {
  opacity: 0;
}

/* 

====== Zoom-out effect ======

*/
.mfp-zoom-out {
  /* start state */
  /* animate in */
  /* animate out */
}

.mfp-zoom-out .mfp-with-anim {
  opacity: 0;
  transition: all 0.3s ease-in-out;
  transform: scale(1.3);
}

.mfp-zoom-out.mfp-bg {
  opacity: 0;
  transition: all 0.3s ease-out;
}

.mfp-zoom-out.mfp-ready .mfp-with-anim {
  opacity: 1;
  transform: scale(1);
}

.mfp-zoom-out.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-zoom-out.mfp-removing .mfp-with-anim {
  transform: scale(1.3);
  opacity: 0;
}

.mfp-zoom-out.mfp-removing.mfp-bg {
  opacity: 0;
}

.image-popup-link {
  cursor: pointer;
}

.image-popup-link:hover, .image-popup-link:focus, .image-popup-link:active {
  outline: none;
}

.image-popup-link picture {
  position: relative;
}

.image-popup-link picture:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0);
  transition: all, 0.25s, ease-in-out;
}

.image-popup-link picture:after {
  content: "\f002";
  font-family: BlackTie;
  font-weight: 400;
  font-size: 1.5rem;
  line-height: 1.4;
  color: #fafafa;
  opacity: 0.5;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -30%);
  transition: all, 0.25s, ease-in-out;
}

.image-popup-link:hover picture:before, .image-popup-link:focus picture:before {
  background: rgba(0, 0, 0, 0.5);
  transition: all, 0.25s, ease-in-out;
}

.image-popup-link:hover picture:after, .image-popup-link:focus picture:after {
  opacity: 1;
  transform: translate(-50%, -50%);
  transition: all, 0.25s, ease-in-out;
}

.mfp-zoom-out-cur {
  cursor: inherit;
}

.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  cursor: pointer;
}

.mfp-image-holder .mfp-close, .mfp-iframe-holder .mfp-close {
  padding-right: 0;
  position: fixed;
  top: 40px;
  right: 40px;
}

.mfp-close:after {
  content: "\f00c";
  font-family: BlackTie;
  font-size: 1.5rem;
  line-height: 1.4;
  color: #f8f8f8;
  position: fixed;
  width: 2.25rem;
  height: 2.25rem;
  top: 40px;
  right: 40px;
  transition: all, 0.25s, ease-in-out;
}

.mfp-close:hover, .mfp-close:focus {
  color: #fafafa;
  transition: all, 0.25s, ease-in-out;
}

.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  /* ideally, transition speed should match zoom duration */
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
  opacity: 1;
}

.mfp-with-zoom.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container,
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}

/*
 * GLOBAL ACCORDION STYLES
 */
.accordion dd,
.accordion__panel {
  width: 100%;
  margin-left: 0;
}

.accordion {
  position: relative;
  margin-bottom: 2em;
}

.accordionTitle,
.accordion__Heading {
  display: block;
  text-decoration: none;
}

.accordionItem {
  height: auto;
  overflow: hidden;
  max-height: 100000px;
  transition: max-height 1s;
}

@media screen and (min-width: 48em) {
  .accordionItem {
    max-height: 100000px;
    transition: max-height 0.5s;
  }
}

.accordionItem.is-collapsed {
  max-height: 0;
}

.no-js .accordionItem.is-collapsed {
  max-height: auto;
}

.animateIn {
  animation: accordionIn 0.45s normal ease-in-out both 1;
}

.animateOut {
  animation: accordionOut 0.45s alternate ease-in-out both 1;
}

@keyframes accordionIn {
  0% {
    opacity: 0;
    transform: scale(0.9) rotateX(-60deg);
    transform-origin: 50% 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes accordionOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.9) rotateX(-60deg);
  }
}

.accordion-container {
  margin-bottom: 1.5rem;
}

.accordion {
  margin-bottom: 0;
}

.accordion-default dl {
  margin: 0;
}

.accordion-default dt {
  margin-top: 0.5rem;
}

.accordion-default .accordionTitle {
  text-align: center;
  padding: 0.5rem 0;
}

.accordion-default .accordion-content {
  text-align: center;
}

.accordion-default .accordion-content .accordion-content-wrapper {
  text-align: center;
}

.accordion-secondary dl {
  margin: 0;
}

.accordion-secondary dt {
  margin-top: 0.5rem;
}

.accordion-secondary .accordionTitle {
  background-color: #e5e5e5;
  position: relative;
  font-size: 1.2rem;
  line-height: 1.4;
  padding: 10px 65px 10px 15px;
}

.accordion-secondary .accordionTitle:after {
  content: "\f091";
  color: #ff8d2f;
  font-family: BlackTie;
  font-weight: normal;
  font-size: 1rem;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 30px;
  transition: all 0.4s;
  margin-top: -10px;
  transform: rotate(180deg);
}

.accordion-secondary .accordionTitle.is-collapsed:after {
  transform: rotate(0deg);
  margin-top: -10px;
  transition: all 0.4s;
}

.accordion-secondary .accordion-content-wrapper {
  padding: 30px 20px;
  background-color: #eeeeee;
}

/* !FOCUSED IMAGES */
/*-----------------------------------------*/
.focuspoint {
  position: relative;
  /*Any position but static should work*/
  overflow: hidden;
}

.focuspoint img {
  position: absolute;
  left: 0;
  top: 0;
  margin: 0;
  display: block;
  /* fill and maintain aspect ratio */
  width: auto;
  height: auto;
  min-width: 100%;
  min-height: 100%;
  max-height: none;
  max-width: none;
}

/* Dropdown control */
.selectBox-dropdown {
  width: 100% !important;
  position: relative;
  display: inline-block;
  cursor: default;
}

.selectBox-dropdown:focus,
.selectBox-dropdown:focus .selectBox-arrow {
  border-color: #666;
}

.selectBox-dropdown .selectBox-label {
  padding: 2px 8px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
}

.selectBox-dropdown .selectBox-arrow {
  position: absolute;
  top: 0;
  right: 0;
  width: 23px;
  height: 100%;
}

/* Dropdown menu */
.selectBox-dropdown-menu {
  position: absolute;
  z-index: 99999;
  max-height: 200px;
  min-height: 1em;
  border: solid 1px #666666;
  /* should be the same border width as .selectBox-dropdown */
  background: #fafafa;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

/* Inline control */
.selectBox-inline {
  min-width: 150px;
  outline: none;
  border: solid 1px #666666;
  background: #FFF;
  display: inline-block;
  overflow: auto;
}

.selectBox-inline:focus {
  border-color: #666;
}

/* Options */
.selectBox-options,
.selectBox-options LI,
.selectBox-options LI A {
  list-style: none;
  display: block;
  cursor: default;
  padding: 0;
  margin: 0;
}

.selectBox-options.selectBox-options-top {
  border-bottom: none;
  margin-top: 1px;
}

.selectBox-options.selectBox-options-bottom {
  border-top: none;
}

.selectBox-options LI A {
  line-height: 1.5;
  padding: 0 .5em;
  white-space: nowrap;
  overflow: hidden;
  background: 6px center no-repeat;
}

.selectBox-options LI.selectBox-hover A {
  background-color: #EEE;
}

.selectBox-options LI.selectBox-disabled A {
  color: #888;
  background-color: transparent;
}

.selectBox-options LI.selectBox-selected A {
  background-color: #C8DEF4;
}

.selectBox-options .selectBox-optgroup {
  color: #666;
  background: #EEE;
  font-weight: bold;
  line-height: 1.5;
  padding: 0 .3em;
  white-space: nowrap;
}

/* Disabled state */
.selectBox.selectBox-disabled {
  color: #888 !important;
}

.selectBox-dropdown.selectBox-disabled .selectBox-arrow {
  opacity: .5;
  filter: alpha(opacity=50);
}

.selectBox-inline.selectBox-disabled {
  color: #888 !important;
}

.selectBox-inline.selectBox-disabled .selectBox-options A {
  background-color: transparent !important;
}

.selectBox {
  border-bottom: 1px solid #666666;
}

.selectBox-dropdown .selectBox-arrow {
  top: 3px;
}

.selectBox-dropdown .selectBox-arrow:before {
  font-family: 'FontAwesome';
  position: absolute;
  top: 50%;
  right: 10px;
  background: none;
  font-size: 22px;
  margin-top: -10px;
  content: "\f078";
  font-size: 1em;
  line-height: 1;
}

/* ---------------------------------------------- /*
 * Mouse animate icon
/* ---------------------------------------------- */
.mouse-icon {
  position: relative;
  border: 2px solid #fff;
  border-radius: 11px;
  height: 45px;
  width: 30px;
  margin: 0 auto;
  display: block;
  z-index: 10;
}

.mouse-icon .wheel {
  -webkit-animation-name: drop;
  -webkit-animation-duration: 1s;
  -webkit-animation-timing-function: linear;
  -webkit-animation-delay: 0s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-play-state: running;
  animation-name: drop;
  animation-duration: 1s;
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-iteration-count: infinite;
  animation-play-state: running;
}

.mouse-icon .wheel {
  position: relative;
  border-radius: 10px;
  background: #fff;
  width: 4px;
  height: 10px;
  top: 4px;
  margin-left: auto;
  margin-right: auto;
}

@keyframes drop {
  0% {
    top: 5px;
    opacity: 0;
  }
  30% {
    top: 10px;
    opacity: 1;
  }
  100% {
    top: 25px;
    opacity: 0;
  }
}

html {
  margin-right: 0 !important;
}

body {
  margin: 0;
  overflow-x: hidden !important;
}

.col-sidebar {
  margin-bottom: 4rem;
}

.col-content {
  margin-bottom: 4rem;
}

.topSpacer {
  padding-top: 208px;
}

@media (max-width: 1250px) {
  .topSpacer {
    padding-top: 136px;
  }
}

/*
 Campaign Page
 styles for "Kampagnenseite", Nov 2019, mp 	
*/
.layout-campaign-page .page {
  padding-top: 0px;
}

.layout-campaign-page .campaign-header {
  text-align: center;
  position: fixed;
  display: block;
  height: 100px;
  width: 100%;
  z-index: 90000;
  background-color: #fff;
  opacity: .85;
  padding: 10px;
}

.layout-campaign-page .campaign-header a {
  display: inline-block;
}

.layout-campaign-page .fluid-container.container-promobox {
  margin-top: 2rem;
}

.layout-campaign-page .CampaignContact .fluid-container.container-100 .content-wrapper {
  margin: auto 25%;
}

.layout-campaign-page .text-align-left,
.layout-campaign-page .text-image .indent {
  margin: auto 25%;
}

.layout-campaign-page ul li:before {
  color: #666;
}

.layout-campaign-page .layout-campaign-page footer {
  padding: 1.2em 1rem;
  text-align: center;
}

.navbar {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  height: auto;
  width: 100%;
  background: #fafafa;
  background: #fafafa;
  z-index: 1000;
  filter: alpha(opacity=85);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
  -webkit-opacity: 0.85;
  -khtml-opacity: 0.85;
  -moz-opacity: 0.85;
  -ms-opacity: 0.85;
  -o-opacity: 0.85;
  opacity: 0.85;
  border-bottom: 1px solid #d7d7d7;
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s;
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

@media (max-width: 1250px) {
  .navbar {
    display: none;
  }
}

.navbar #navi-contact {
  position: fixed;
  top: -100px;
  right: 10%;
  background-color: #ff8d2f;
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

.navbar #navi-contact a {
  color: #fafafa;
}

.navbar #navi-contact a i {
  color: #fafafa;
}

.navbar #navi-contact:hover, .navbar #navi-contact:focus, .navbar #navi-contact:active {
  background-color: #c85a00;
}

.navbar.scrolled {
  top: -190px; /* Angepasst für Meta-Navigation + Extra-Sub-Nav */
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

.navbar.scrolled #navi-contact {
  top: 0;
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94) 1s;
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

.navbar.scrolled.active-is-open.scrolled {
  top: -190px;
}

.navbar .fluid-container {
  position: relative;
}

/* Legacy logo container - overridden by flexbox layout */
.navbar .fluid-container .logo-container {
  position: static;
  left: auto;
  top: auto;
  max-width: 211px;
}

.navbar .fluid-container .main-nav-container {
  text-align: center;
  margin-left: auto;
}

.navbar .fluid-container .main-nav-container ul.main-nav {
  margin: 0;
  padding: 0;
  font-size: 20px;
  line-height: 1.2;
  z-index: 100;
  list-style: none;
}

.navbar .fluid-container .main-nav-container ul.main-nav li {
  display: inline-block;
  font-size: 20px;
  line-height: 1.2;
  color: #140043;
  font-family: "BlissPro-Light";
  z-index: 100;
  padding-left: 0;
  /*
					 * Second Level Menu
					 */
}

@media (min-width: 1171px) and (max-width: 1330px) {
  .navbar .fluid-container .main-nav-container ul.main-nav li {
    /* font-size: 78%; */
  }
}

.navbar .fluid-container .main-nav-container ul.main-nav li:before {
  display: none;
}

.navbar .fluid-container .main-nav-container ul.main-nav li a {
  color: #140043;
  text-decoration: none;
  padding-left: 15px;
  padding-right: 15px;
  font-family: "BlissPro-Light";
  transition: all, 0.25s, ease-in-out;
}

.navbar .fluid-container .main-nav-container ul.main-nav li a:hover {
  color: #ff8d2f;
  transition: all, 0.25s, ease-in-out;
}

.navbar .fluid-container .main-nav-container ul.main-nav li.open a {
  color: #ff8d2f;
}

.navbar .fluid-container .main-nav-container ul.main-nav li.open .navbar-dropdown-container.navbar-dropdown-dropped:before {
  opacity: 1;
}

.navbar .fluid-container .main-nav-container ul.main-nav li.home a i {
  color: #140043;
  font-size: 26px;
  transition: all, 0.25s, ease-in-out;
}

.navbar .fluid-container .main-nav-container ul.main-nav li.home a:hover i {
  color: #ff8d2f;
  transition: all, 0.25s, ease-in-out;
}

.navbar .fluid-container .main-nav-container ul.main-nav li.active > a {
  font-family: "BlissPro-Bold";
  color: #ff8d2f;
}

.navbar .fluid-container .main-nav-container ul.main-nav li.active.has-sub > a:after {
  bottom: -46px;
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  left: 50%;
  margin-left: -10px;
  width: 20px;
  height: 20px;
  transform: rotate(-45deg);
  box-shadow: 1px -2px 0px #ccc;
  z-index: 85;
  background: #f0f0f0;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container {
  background: #666666;
  background: rgba(102, 102, 102, 0.8);
  background: #ff8d2f;
  position: absolute;
  width: 100%;
  min-width: 250px;
  top: 41px;
  left: 50%;
  z-index: 90;
  transform: translateX(-50%);
  transition: all 0.5s ease-out 0.25s;
  opacity: 0;
  height: 0;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container:before {
  opacity: 0;
  top: -14px;
  content: "";
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 14px solid #ff8d2f;
  position: absolute;
  left: 50%;
  margin-left: -10px;
  z-index: 95;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container.navbar-dropdown-dropped {
  transition: all 0.5s ease-out 0.25s;
  opacity: 1;
  height: auto;
  box-shadow: 0px 0px 20px 1px rgba(0, 0, 0, 0.3);
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container.navbar-dropdown-dropped ul.sub-nav.open {
  display: block;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container ul.sub-nav {
  display: none;
  margin: 0;
  padding: 0;
  text-align: left;
  line-height: 0;
  padding: 20px 0;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container ul.sub-nav li {
  color: #fafafa;
  display: block;
  font-family: "BlissPro-Light";
  font-size: 18px;
  line-height: 1.2;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container ul.sub-nav li a {
  color: #fafafa;
  padding: 4px 15px;
  text-decoration: none;
  font-family: "BlissPro-Light";
  font-size: 18px;
  line-height: 1.2;
  display: block;
  position: relative;
  transition: all, 0.25s, ease-in-out;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container ul.sub-nav li a:after {
  display: none;
}

.navbar .fluid-container .main-nav-container ul.main-nav li .navbar-dropdown-container ul.sub-nav li a:hover {
  color: #140043;
  transition: all, 0.25s, ease-in-out;
}

/* Flexbox Navigation Layout */
.navbar .fluid-container .navbar-flex-container {
  display: flex;
  flex-direction: column;
  padding: 0 0 1rem 0;
}

/* Meta navigation row - only meta nav, right aligned */
.navbar .fluid-container .navbar-meta-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
  background: #FAFAFA;
  padding: 8px 0;
}

/* Main navigation row - logo left, main nav right */
.navbar .fluid-container .navbar-main-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container {
  flex: 0 0 auto;
  text-align: right;
}

.navbar .fluid-container .navbar-main-row .logo-container {
  flex: 0 0 auto;
}

.navbar .fluid-container .navbar-main-row .main-nav-container {
  flex: 0 0 auto;
}

/* Logo styles */
.navbar .fluid-container .navbar-main-row .logo-container img {
  max-height: 60px;
  width: auto;
}

/* Meta navigation positioning */
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 0px;
  border-bottom: 2px solid #ff8d2f;
}

@media (max-width: 992px) {
  .navbar .fluid-container .navbar-meta-row {
    display: none;
  }

  .navbar .fluid-container .navbar-main-row {
    justify-content: center;
  }
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 0px;
  border-bottom: 2px solid #ff8d2f;
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li {
  display: inline-block;
  text-align: left;
  font-size: 16px;
  line-height: 1.2;
  padding-left: 0;
  padding: 8px 15px;
  color: #140043;
  font-family: "BlissPro-Light";
  width: auto;
  min-width: auto;
  transition: all, 0.25s, ease-in-out;
  position: relative;
}

/* Letztes Meta-Navigation Element hat gleichen rechten Abstand wie Hauptnavigation */
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li:last-child {
  padding-right: 15px;
}

@media (min-width: 1171px) and (max-width: 1330px) {
  .navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li {
    /* font-size: 12px; */
  }
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.newsletter {
  width: 78px;
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li:before {
  display: none;
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li a {
  text-decoration: none;
  color: #140043;
  font-family: "BlissPro-Light";
  display: block;
  position: relative;
}

/* Entfernt: Pseudo-Element für Unterstreichung - wird durch text-decoration ersetzt */

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li a i {
  color: #ff8d2f;
  font-size: 16px;
  line-height: 1.2;
  margin-right: 8px;
  margin-bottom: 0;
  display: inline-block;
  vertical-align: middle;
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li a[data-action="flyout"]:after {
  /* opacity: 0;
  bottom: -35px;
  content: "";
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 14px solid #515050;
  position: absolute;
  left: 50%;
  margin-left: -10px; */
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li a.active[data-action="flyout"]:after {
  opacity: 1;
  bottom: -15px;
  transition: all 0.4s ease-out 1s;
}

/* Einfacher Hover-Effekt: text-decoration underline für alle Meta-Navigation Elemente */
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li:hover,
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li:focus {
  text-decoration: underline;
}

.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li:hover a,
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li:focus a {
  text-decoration: underline;
}

/* Rhenag Element verwendet jetzt die gleichen Hover-Effekte wie alle anderen Meta-Navigation Elemente */

/* Spezifisch für das rhenag i:before Element - verhindert Icon-Wechsel beim Hover */
.theme-secondary .meta-nav .rhenag:hover i:before,
.theme-secondary .meta-nav .rhenag:focus i:before,
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.rhenag:hover i:before,
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.rhenag:focus i:before {
  background: url("../images/svg/icon-rhenag-energie-orange.svg") no-repeat center center !important;
  /* background-size: 100% !important; */
  background-image: url("../images/svg/icon-rhenag-energie-orange.svg") !important;
}

.nav-flyout {
  font-size: 18px;
  line-height: 1.2;
  background-color: #666666;
  color: #fafafa;
  position: fixed;
  top: -1050px;
  left: 0;
  width: 100%;
  padding-top: 2rem;
  padding-bottom: 2rem;
  z-index: 1110;
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

@media (max-width: 1250px) {
  .nav-flyout {
    display: none;
  }
}

.nav-flyout > .fluid-container > .row {
  margin: 0;
}

.nav-flyout.out, .nav-flyout.out-long {
  top: 188px; /* Angepasst für Meta-Navigation + Extra-Sub-Nav */
  transition: top 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* easeOutQuad */
}

.nav-flyout.out.scrolled, .nav-flyout.out-long.scrolled {
  top: 175px; /* Angepasst für Meta-Navigation + Extra-Sub-Nav */
}

.nav-flyout.out-long {
  top: 188px; /* Angepasst für Meta-Navigation + Extra-Sub-Nav */
}

.nav-flyout .arrow-up {
  position: absolute;
  right: 0;
  top: -2rem;
  margin-top: -14px;
  z-index: 100;
}

.nav-flyout a {
  color: #fafafa;
  text-decoration: none;
}

.nav-flyout a:hover, .nav-flyout a:focus, .nav-flyout a:active {
  color: #ff8d2f;
  text-decoration: none;
}

.nav-flyout a.link-highlighted {
  color: #ff8d2f;
}

.nav-flyout a.link-highlighted:hover, .nav-flyout a.link-highlighted:focus, .nav-flyout a.link-highlighted:active {
  color: #c85a00;
  text-decoration: none;
}

.nav-flyout .box-special {
  background-color: #fafafa;
  color: #666666;
  padding: 1rem;
  margin-bottom: 1rem;
}

.nav-flyout .box-special a.link-with-arrow {
  color: #666666;
}

.nav-flyout .box-special a.link-with-arrow:hover, .nav-flyout .box-special a.link-with-arrow:active, .nav-flyout .box-special a.link-with-arrow:focus {
  color: #ea7322;
}

.nav-flyout .box-special p:last-child {
  margin-bottom: 0;
}

.nav-flyout#flyout-search {
  text-align: left;
  line-height: 1;
}

.nav-flyout#flyout-search .row {
  margin-bottom: 0;
}

.nav-flyout#flyout-search .searchTitle {
  display: inline-block;
}

.nav-flyout#flyout-search form {
  display: inline-block;
  width: 100%;
}

.nav-flyout#flyout-search form input {
  margin-right: 15px;
  min-width: 83%;
  padding: 8px;
  color: #fff;
  border-radius: 0;
  background-color: transparent;
  border: none;
  border-bottom: 1px solid #a3a3a3;
}

.nav-flyout#flyout-search form input:focus {
  outline: none;
}

.nav-flyout#flyout-search form .submit-container {
  width: 50px;
  height: 50px;
  display: inline-block;
  float: right;
}

.nav-flyout#flyout-search form .submit-container button {
  background: transparent;
  border: none;
  padding: 0;
  margin-top: 11px;
  font-size: 1.4rem;
  outline: none;
  transition: all 0.2s ease-out;
}

.nav-flyout#flyout-search form .submit-container button i {
  color: #ff8d2f;
}

.nav-flyout#flyout-search form .submit-container button:hover, .nav-flyout#flyout-search form .submit-container button:focus, .nav-flyout#flyout-search form .submit-container button:active {
  margin-top: 11px;
  -khtml-transform: scale(1.6);
  transform: scale(1.6);
  transition: all 0.2s ease-out;
}

.extra-nav-container .extra-nav {
  padding: 0;
  margin: 0;
}

.extra-nav-container .extra-nav > li {
  display: none;
  padding: 0;
}

.extra-nav-container .extra-nav > li .extra-navbar-dropdown-container {
  display: none;
}

.extra-nav-container .extra-nav > li.active {
  display: block;
}

.extra-nav-container .extra-nav > li.active:before {
  display: none;
}

.extra-nav-container .extra-nav > li.active > a {
  display: none;
}

.extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container {
  background: #f0f0f0;
  display: block;
  box-shadow: 0px -1px 0px #ccc;
  padding: 10px 0;
}

.extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container .extra-sub-nav {
  list-style: none;
  margin: 0;
  padding: 0;
  text-align: center;
}

.extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container .extra-sub-nav li {
  display: inline-block;
}

.extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container .extra-sub-nav li a {
  text-decoration: none;
  color: #140043;
  font-family: "BlissPro-Light";
  font-size: 20px;
  transition: all, 0.25s, ease-in-out;
}

.extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container .extra-sub-nav li a:hover, .extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container .extra-sub-nav li a:focus {
  transition: all, 0.25s, ease-in-out;
  text-decoration: none;
  color: #ff8d2f;
  font-family: "BlissPro-Light";
  font-size: 20px;
}

.extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container .extra-sub-nav li.active a {
  font-family: "BlissPro-Bold";
  color: #ff8d2f;
}

.extra-nav-container .extra-nav > li.active .extra-navbar-dropdown-container .extra-sub-nav li:before {
  display: none;
}

.theme-default .navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.login {
  display: inline-block;
}

.theme-default .navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.rhenag {
  display: none;
}

.theme-secondary .navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.login {
  display: none;
}

.theme-secondary .navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.rhenag {
  display: inline-block;
}

.theme-secondary .meta-nav .rhenag i:before {
  background: url("../images/svg/icon-rhenag-energie-orange.svg") no-repeat center center;
  background-size: 100%;
  content: ' ';
  width: 30px;
  height: 19px !important;
  display: block;
}

/* Hover-Effekt für rhenag Element entfernt */

/* Spezifische Height-Regel für rhenag Icon */
.navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.rhenag i:before,
.theme-secondary .navbar .fluid-container .navbar-meta-row .meta-nav-container ul.meta-nav li.rhenag i:before {
  height: 19px !important;
}

.sidebar-nav-container.fixed {
  position: fixed;
}

.sidebar-nav-container.absoluteBottom {
  position: absolute;
}

.sidebar-nav-container .sub-menu {
  margin: 0;
  padding: 0;
  font-family: "BlissPro-Light";
  padding: 1rem 2rem;
  background-color: #fff;
}

.sidebar-nav-container .sub-menu > :before {
  display: none;
}

@media (max-width: 992px) {
  .sidebar-nav-container .sub-menu {
    margin-bottom: 0rem;
  }
}

.sidebar-nav-container .sub-menu li {
  padding: 0;
  padding: 0.6rem 0;
}

@media (max-width: 992px) {
  .sidebar-nav-container .sub-menu li {
    display: inline-block;
    padding-right: 20px;
  }
  .sidebar-nav-container .sub-menu li:last-child {
    padding-right: 0;
  }
}

.sidebar-nav-container .sub-menu li a {
  text-decoration: none;
  font-size: 20px;
  line-height: 1.4;
  transition: all, 0.25s, ease-in-out;
}

.sidebar-nav-container .sub-menu li a:hover, .sidebar-nav-container .sub-menu li a:focus, .sidebar-nav-container .sub-menu li a:active {
  transition: all, 0.25s, ease-in-out;
  color: #ff8d2f;
}

.sidebar-nav-container .sub-menu li.active a {
  color: #ff8d2f;
  display: block;
  padding-left: 20px;
  transition: all, 0.25s, ease-in-out;
}

.sidebar-nav-container .sub-menu li.active a:before {
  color: #ff8d2f;
  content: "\f09b";
  font-family: BlackTie;
  font-weight: normal;
  font-size: 0.65em;
  margin-right: 0.65em;
  position: absolute;
  left: 0;
  top: 16px;
  transition: all, 0.25s, ease-in-out;
}

/*
 * Conainers
 */
/*
 * GoTop
 */
.scroll-up {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  display: none;
  width: 60px;
  height: 60px;
}

.scroll-up a {
  width: 60px;
  height: 60px;
  background-color: #f8f8f8;
  border-radius: 100% 100%;
}

.scroll-up a:after {
  content: "\f090";
  font-family: BlackTie;
  font-weight: 200;
  position: absolute;
  left: 50%;
  top: 50%;
  tranform: translate(-50% -50%);
}

/*
 * Arrows
 */
.arrow-up {
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 14px solid #666666;
  border-bottom: 14px solid red;
}

/*
 * Cicles
 */
/*
 * Icons
 */
.icon:before {
  line-height: normal;
}

/*
 * Text with Image
 */
.imageTextBox {
  padding: 1rem 0px;
  background-color: #f2f2f2;
  margin-bottom: 2rem;
}

.imageTextBox .col-left {
  width: 40%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .imageTextBox .col-left {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.imageTextBox .col-left .picture-mobile {
  display: none !important;
}

@media (max-width: 767px) {
  .imageTextBox .col-left .picture-mobile {
    display: block !important;
  }
}

.imageTextBox .col-left .focuspoint {
  height: 100%;
}

@media (max-width: 767px) {
  .imageTextBox .col-left .focuspoint {
    display: none !important;
    max-height: 400px;
  }
  .imageTextBox .col-left .focuspoint img {
    display: block;
    max-width: 100%;
    max-height: 100% !important;
    height: auto;
    position: relative;
    top: 0 !important;
    left: 0 !important;
  }
}

.imageTextBox .col-right {
  width: 60%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  float: right;
}

@media (max-width: 767px) {
  .imageTextBox .col-right {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

/*
 * Icon with Text
 */
.iconText-wrapper {
  text-align: center;
}

.iconText-wrapper .iconText-container-header {
  margin-bottom: 1rem;
}

.iconText-wrapper .iconText-container-panels {
  display: table;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  padding-left: 0;
  padding-right: 0;
}

@media (max-width: 992px) {
  .iconText-wrapper .iconText-container-panels {
    display: block;
  }
}

@media (max-width: 767px) {
  .iconText-wrapper .iconText-container-panels {
    display: block;
  }
}

.iconText-wrapper .iconText-container-panels .iconText-element {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  float: none;
  display: inline-block;
  vertical-align: top;
}

@media (max-width: 992px) {
  .iconText-wrapper .iconText-container-panels .iconText-element {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (max-width: 767px) {
  .iconText-wrapper .iconText-container-panels .iconText-element {
    width: 100%;
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    display: block;
    float: none;
  }
}

.iconText-wrapper .iconText-container-panels .iconText-element > div {
  padding: 1.5rem 1rem;
}

.iconText-wrapper .iconText-container-panels .iconText-element > div .svg-icon {
  margin-bottom: 0.5rem;
}

.iconText-wrapper .iconText-container-panels .iconText-element > div .iconText-header {
  margin-bottom: 1rem;
}

/*
 * text-image 
 */
.text-image picture.image-left, .text-image div.image-left {
  float: left;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
  z-index: 100;
}

.text-image picture.image-right, .text-image div.image-right {
  float: right;
  margin-left: 1rem;
  margin-bottom: 0.5rem;
  z-index: 100;
}

.text-image picture.image-left-alone, .text-image div.image-left-alone {
  float: left;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}

.text-image picture.image-right-alone, .text-image div.image-right-alone {
  float: right;
  margin-left: 1rem;
  margin-bottom: 0.5rem;
}

.text-image picture.image-top, .text-image div.image-top {
  float: none;
  margin-bottom: 0.5rem;
}

.text-image picture.image-top-center, .text-image div.image-top-center {
  float: none;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0.5rem;
  text-align: center;
  width: 100%;
  display: block;
}

.text-image picture.image-top-center img, .text-image div.image-top-center img {
  display: inline-block;
}

.text-image picture.image-bottom, .text-image div.image-bottom {
  float: none;
  margin-top: 1rem;
}

.text-image picture.image-bottom-center, .text-image div.image-bottom-center {
  float: none;
  margin-left: auto;
  margin-right: auto;
  margin-top: 0.5rem;
  text-align: center;
  width: 100%;
  display: block;
}

.text-image picture.image-bottom-center img, .text-image div.image-bottom-center img {
  display: inline-block;
}

.text-image picture.img-size-sm, .text-image div.img-size-sm {
  max-width: 25%;
}

.text-image picture.img-size-md, .text-image div.img-size-md {
  max-width: 50%;
}

.text-image picture.img-size-full, .text-image div.img-size-full {
  max-width: 100%;
}

@media (max-width: 767px) {
  .text-image picture, .text-image div {
    width: 100%;
    display: block;
    max-width: 100% !important;
    height: auto;
    float: none !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .text-image picture img, .text-image div img {
    width: 100%;
    display: block;
    max-width: 100%;
    height: auto;
    margin: 0;
    float: none;
  }
}

.text-image .text-with-image-left-alone.text-with-img-size-sm {
  margin-left: 27%;
}

@media (max-width: 767px) {
  .text-image .text-with-image-left-alone.text-with-img-size-sm {
    margin-left: 0;
  }
}

.text-image .text-with-image-left-alone.text-with-img-size-md {
  margin-left: 52%;
}

@media (max-width: 767px) {
  .text-image .text-with-image-left-alone.text-with-img-size-md {
    margin-left: 0;
  }
}

@media (max-width: 767px) {
  .text-image .text-with-image-left-alone {
    margin-left: 0 !important;
  }
}

.text-image .text-with-image-right-alone.text-with-img-size-sm {
  margin-right: 27%;
}

@media (max-width: 767px) {
  .text-image .text-with-image-right-alone.text-with-img-size-sm {
    margin-right: 0;
  }
}

.text-image .text-with-image-right-alone.text-with-img-size-md {
  margin-right: 52%;
}

@media (max-width: 767px) {
  .text-image .text-with-image-right-alone.text-with-img-size-md {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  .text-image .text-with-image-right-alone {
    margin-right: 0 !important;
  }
}

/*
 * Cookie Consent
 */
.cc_container {
  background: #666666 !important;
}

.cc_container a, .cc_container a:visited {
  color: #ff8d2f !important;
}

.cc_container a:hover, .cc_container a:focus, .cc_container a:visited:hover, .cc_container a:visited:focus {
  color: #c85a00 !important;
}

.cc_container .cc_btn, .cc_container .cc_btn:visited {
  background-color: #ff8d2f !important;
  color: #fff !important;
}

.cc_container .cc_btn:hover, .cc_container .cc_btn:focus, .cc_container .cc_btn:visited:hover, .cc_container .cc_btn:visited:focus {
  color: #fff !important;
  background-color: #c85a00 !important;
}

body {
  background-color: #f8f8f8;
}

* {
  box-sizing: border-box;
}

div {
  position: relative;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.clearfix {
  display: inline-block;
  clear: both;
}

/* start commented backslash hack \*/
* html .clearfix {
  height: 1%;
}

.clearfix {
  display: block;
}

/* close commented backslash hack */
footer {
  font-size: 18px;
  line-height: 1.2;
  color: #fafafa;
  z-index: 0;
  background: url("../images/svg/rhenag-pattern.svg") no-repeat 105% bottom #4d4c4c;
  position: relative;
  padding: 4em 1rem;
}

footer a {
  color: #fafafa;
  text-decoration: none;
}

footer a:hover, footer a:focus, footer a:active {
  text-decoration: none;
}

footer a.link-normal {
  color: #fafafa;
}

footer a.link-highlight:hover, footer a.link-highlight:focus, footer a.link-highlight:active {
  color: #c85a00;
}

footer .fluid-container .row .col-footer {
  width: 16.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  position: relative;
  z-index: 10;
}

@media (max-width: 1250px) {
  footer .fluid-container .row .col-footer {
    width: 33.3333333333%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (max-width: 767px) {
  footer .fluid-container .row .col-footer {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (max-width: 480px) {
  footer .fluid-container .row .col-footer {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

footer .fluid-container .row .col-footer .col-list {
  min-height: 180px;
}

.theme-secondary footer {
  background-color: #140043;
}

.theme-secondary footer .footer-icon {
  background-color: #392b5b;
}

.teaser-home {
  margin-bottom: 4rem;
}

.teaser-home .teaser-canvas {
  min-height: 720px;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas {
    min-height: 280px;
    margin-top: 100px;
  }
}

.teaser-home .teaser-canvas .teaser-item {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 720px;
  opacity: 0;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
  overflow: hidden;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas .teaser-item {
    min-height: 280px;
  }
}

.teaser-home .teaser-canvas .teaser-item.active {
  opacity: 1;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
}

.teaser-home .teaser-canvas .teaser-item.text-position-right .heroTitle {
  position: absolute;
  bottom: 370px;
  right: 100px;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 140px;
    right: 30px;
    font-size: 1.2rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 570px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 470px;
  }
}

.teaser-home .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
  position: absolute;
  top: 350px;
  right: 50px;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 140px;
    right: 10px;
    font-size: 0.8rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 150px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 250px;
  }
}

.teaser-home .teaser-canvas .teaser-item.text-position-left .heroTitle {
  position: absolute;
  bottom: 370px;
  left: 50px;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 140px;
    left: 30px;
    font-size: 1.2rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 570px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 470px;
  }
}

.teaser-home .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
  position: absolute;
  top: 350px;
  left: 100px;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 140px;
    left: 10px;
    font-size: 0.8rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 150px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 250px;
  }
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video {
  position: absolute;
  top: 0;
  left: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: block;
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video::-webkit-media-controls {
  display: none;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas .teaser-item.teaser-video > video {
    display: none;
  }
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-center-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-top-left {
  top: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-top-right {
  top: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-center-left {
  top: 50%;
  left: 0;
  transform: translate(0%, -50%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-center-right {
  top: 50%;
  left: auto;
  right: 0;
  transform: translate(0%, -50%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-bottom-left {
  top: auto;
  bottom: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-bottom-right {
  top: auto;
  bottom: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-top-center {
  top: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > video.position-bottom-center {
  top: auto;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-home .teaser-canvas .teaser-item.teaser-video > picture {
  display: none;
}

@media (max-width: 960px) {
  .teaser-home .teaser-canvas .teaser-item.teaser-video > picture {
    display: block;
  }
}

.teaser-home .teaser-canvas .teaser-progressBar {
  width: 100%;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #fafafa;
  z-index: 1;
}

.teaser-home .teaser-canvas .teaser-progressBar span {
  background-color: #ff8d2f;
  width: 0%;
  display: block;
  height: 4px;
}

.teaser-home .teaser-bullets {
  display: none;
}

@media (max-width: 960px) {
  .teaser-home .teaser-bullets {
    display: block;
    position: absolute;
    top: -45px;
    left: 0;
    width: 100%;
  }
}

.teaser-home .teaser-bullets ul {
  padding: 0;
  margin: 0;
  list-style: none;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
}

.teaser-home .teaser-bullets ul li {
  display: inline-block;
  padding: 0;
  border-radius: 100%;
  border: 1px solid #fafafa;
  width: 8px;
  height: 8px;
}

.teaser-home .teaser-bullets ul li.active {
  background-color: #fafafa;
}

.teaser-home .teaser-bullets ul li:before {
  display: none;
}

.teaser-home .teaser-controller {
  text-align: center;
  margin-top: -150px;
}

@media (max-width: 960px) {
  .teaser-home .teaser-controller {
    margin-top: 0;
  }
}

.teaser-home .teaser-controller .teaser-switch-box-container {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

@media (max-width: 960px) {
  .teaser-home .teaser-controller .teaser-switch-box-container {
    display: block;
  }
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box-hidden {
  display: none;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  float: none;
  display: inline-table;
  display: flex;
  cursor: pointer;
}

@media (max-width: 960px) {
  .teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box {
    width: 100%;
    display: block;
    float: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    opacity: 0;
  }
  .teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.active {
    opacity: 1;
    z-index: 10;
  }
  .teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box:last-child {
    clear: both;
  }
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .svg-icon {
  background-color: #b40000;
  background-image: -webkit-gradient(left, right);
  background-image: linear-gradient(#b40000, #b40000);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#b40000', endColorStr='#b40000');
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link {
  background-color: #b40000;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link:hover, .teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link:focus, .teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link:active {
  background-color: #ff1b1b;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box > div {
  background: #fafafa;
  background: rgba(250, 250, 250, 0.7);
  padding: 1.5rem 1rem 4rem;
  -ms-box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  -o-box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  transition: all 1s ease-in;
  height: 100%;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box > div:before {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fafafa;
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: all 0.5s ease-out;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box > div .svg-icon {
  margin-bottom: 1rem;
  z-index: 5;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box > div p {
  line-height: 1.4rem;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box > div .cta-link {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div {
  background: #fafafa;
  background: #fafafa;
  transition: all 1s ease-out;
}

.teaser-home .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div:before {
  top: -15px;
  opacity: 1;
  transition: all 0.5s ease-out 1.5s;
}

.teaser-home.teaser-home-with-video {
  margin-bottom: 6rem;
}

.teaser-home.teaser-home-with-video .teaser-canvas {
  min-height: 720px;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas {
    min-height: 0;
    margin-top: 0;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item {
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
  overflow: hidden;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item {
    min-height: 280px;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.active {
  opacity: 1;
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroTitle {
  position: absolute;
  bottom: 370px;
  right: 100px;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 140px;
    right: 30px;
    font-size: 1.2rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 570px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 470px;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
  position: absolute;
  top: 350px;
  right: 50px;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 140px;
    right: 10px;
    font-size: 0.8rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 150px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 250px;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroTitle {
  position: absolute;
  bottom: 370px;
  left: 50px;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 140px;
    left: 30px;
    font-size: 1.2rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 570px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 470px;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
  position: absolute;
  top: 350px;
  left: 100px;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 140px;
    left: 10px;
    font-size: 0.8rem;
  }
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 150px;
  }
}

@media (min-width: 1171px) and (max-width: 1300px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 250px;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video {
  position: absolute;
  top: 0;
  left: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: block;
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video::-webkit-media-controls {
  display: none;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video {
    display: none;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-center-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-top-left {
  top: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-top-right {
  top: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-center-left {
  top: 50%;
  left: 0;
  transform: translate(0%, -50%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-center-right {
  top: 50%;
  left: auto;
  right: 0;
  transform: translate(0%, -50%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-bottom-left {
  top: auto;
  bottom: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-bottom-right {
  top: auto;
  bottom: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-top-center {
  top: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > video.position-bottom-center {
  top: auto;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > picture {
  display: none;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-canvas .teaser-item.teaser-video > picture {
    display: block;
  }
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-progressBar {
  width: 100%;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #fafafa;
  z-index: 1;
}

.teaser-home.teaser-home-with-video .teaser-canvas .teaser-progressBar span {
  background-color: #ff8d2f;
  width: 0%;
  display: block;
  height: 4px;
}

.teaser-home.teaser-home-with-video .teaser-bullets {
  display: none;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-bullets {
    display: block;
    position: absolute;
    top: -45px;
    left: 0;
    width: 100%;
  }
}

.teaser-home.teaser-home-with-video .teaser-bullets ul {
  padding: 0;
  margin: 0;
  list-style: none;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
}

.teaser-home.teaser-home-with-video .teaser-bullets ul li {
  display: inline-block;
  padding: 0;
  border-radius: 100%;
  border: 1px solid #fafafa;
  width: 8px;
  height: 8px;
}

.teaser-home.teaser-home-with-video .teaser-bullets ul li.active {
  background-color: #fafafa;
}

.teaser-home.teaser-home-with-video .teaser-bullets ul li:before {
  display: none;
}

.teaser-home.teaser-home-with-video .teaser-controller {
  text-align: center;
  margin-top: -150px;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-controller {
    margin-top: 0 !important;
  }
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container {
    display: block;
  }
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box-hidden {
  display: none;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  float: none;
  display: inline-table;
  cursor: pointer;
}

@media (max-width: 960px) {
  .teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box {
    width: 100%;
    display: block;
    float: none;
    position: relative;
    top: 0;
    left: 0;
    z-index: 9;
    opacity: 1;
  }
  .teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.active {
    opacity: 1;
    z-index: 10;
  }
  .teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box:last-child {
    clear: both;
  }
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .svg-icon {
  background-color: #b40000;
  background-image: -webkit-gradient(left, right);
  background-image: linear-gradient(#b40000, #b40000);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#b40000', endColorStr='#b40000');
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link {
  background-color: #b40000;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link:hover, .teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link:focus, .teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.teaser-switch-box-highlight > div .cta-link:active {
  background-color: #ff1b1b;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box > div {
  background: #fff;
  padding: 1.5rem 1rem 4rem;
  -ms-box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  -o-box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  transition: all 1s ease-in;
  height: 100%;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box > div:before {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fafafa;
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: all 0.5s ease-out;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box > div .svg-icon {
  margin-bottom: 1rem;
  z-index: 5;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box > div p {
  line-height: 1.4rem;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box > div .cta-link {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div {
  background: #fafafa;
  background: #fafafa;
  transition: all 1s ease-out;
}

.teaser-home.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div:before {
  top: -15px;
  opacity: 1;
  transition: all 0.5s ease-out 1.5s;
}

.teaser-home-with-video video {
  opacity: 0;
}

.teaser-home-with-video .teaser-home-video {
  max-height: 80vh !important;
  overflow: hidden;
}

.teaser-home-with-video .teaser-home-video .heroWrapper {
  position: absolute;
  top: 10rem;
  right: 6rem;
  opacity: 0;
  display: inline-block;
  text-align: right;
}

@media (min-width: 961px) and (max-width: 1170px) {
  .teaser-home-with-video .teaser-home-video .heroWrapper {
    top: 1rem;
    right: 1rem;
  }
}

@media (max-width: 960px) {
  .teaser-home-with-video .teaser-home-video .heroWrapper {
    top: 1rem;
    right: 1rem;
  }
  .teaser-home-with-video .teaser-home-video .heroWrapper .heroTitle {
    font-size: 1rem;
  }
  .teaser-home-with-video .teaser-home-video .heroWrapper .heroSubtitle {
    font-size: 0.9rem;
  }
}

.teaser-home-with-video .teaser-controller {
  margin-top: -110px;
}

.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box > div,
.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div {
  background: #fff;
}

.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div:before {
  display: none;
}

.teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box {
  display: block;
}

@media (max-width: 960px) {
  .teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box {
    margin-bottom: 1rem;
  }
}

@media (max-width: 960px) {
  .teaser-home-with-video .teaser-controller .teaser-switch-box-container {
    height: auto !important;
  }
}

@media (max-width: 960px) {
  .teaser-home-with-video .teaser-controller .teaser-switch-box-container .teaser-switch-box {
    opacity: 1;
  }
}

.theme-secondary .teaser-controller .teaser-switch-box-container .teaser-switch-box .btn.btn-primary {
  background-color: #140043;
}

.theme-secondary .teaser-controller .teaser-switch-box-container .teaser-switch-box .btn.btn-primary:hover, .theme-secondary .teaser-controller .teaser-switch-box-container .teaser-switch-box .btn.btn-primary:focus, .theme-secondary .teaser-controller .teaser-switch-box-container .teaser-switch-box .btn.btn-primary:active {
  background-color: #3200a9;
}

.teaser-landingpage {
  position: relative;
  min-height: 940px;
  margin-bottom: 4rem;
}

.teaser-landingpage .move-down {
  display: none;
}

.teaser-landingpage.minHeight {
  height: auto;
}

.teaser-landingpage.minHeight .move-down {
  display: block;
  position: absolute;
  bottom: 60px;
  left: 50%;
  margin-left: -20px;
  z-index: 9;
}

.teaser-landingpage.minHeight .teaser-canvas {
  min-height: 520px;
  max-height: 520px;
  height: 520px;
}

.teaser-landingpage.minHeight .teaser-canvas .teaser-item {
  min-height: 520px;
  max-height: 520px;
  height: 520px;
}

.teaser-landingpage.minHeight .teaser-canvas .teaser-item .heroTitle {
  bottom: 170px;
}

.teaser-landingpage.minHeight .teaser-info {
  position: relative;
  left: auto;
  top: auto;
  background: #fafafa;
  background: rgba(250, 250, 250, 0);
}

.teaser-landingpage.minHeight .teaser-info .fluid-container .row {
  margin-bottom: 0;
}

.teaser-landingpage .teaser-canvas {
  min-height: 940px;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas {
    min-height: 280px;
  }
}

.teaser-landingpage .teaser-canvas .teaser-item {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 940px;
  opacity: 0;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
  overflow: hidden;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas .teaser-item {
    min-height: 280px;
  }
}

.teaser-landingpage .teaser-canvas .teaser-item.active {
  opacity: 1;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
}

.teaser-landingpage .teaser-canvas .teaser-item.text-position-right .heroTitle {
  position: absolute;
  bottom: 590px;
  right: 100px;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 140px !important;
    right: 30px;
    font-size: 1.2rem;
  }
}

.teaser-landingpage .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
  position: absolute;
  top: 350px;
  right: 50px;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 140px;
    right: 10px;
    font-size: 0.8rem;
  }
}

.teaser-landingpage .teaser-canvas .teaser-item.text-position-left .heroTitle {
  position: absolute;
  bottom: 590px;
  left: 50px;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 140px;
    left: 30px;
    font-size: 1.2rem;
  }
}

.teaser-landingpage .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
  position: absolute;
  top: 350px;
  left: 100px;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 140px;
    left: 10px;
    font-size: 0.8rem;
  }
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video {
  position: absolute;
  top: 0;
  left: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: block;
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video::-webkit-media-controls {
  display: none;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video {
    display: none;
  }
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-center-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-top-left {
  top: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-top-right {
  top: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-center-left {
  top: 50%;
  left: 0;
  transform: translate(0%, -50%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-center-right {
  top: 50%;
  left: auto;
  right: 0;
  transform: translate(0%, -50%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-bottom-left {
  top: auto;
  bottom: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-bottom-right {
  top: auto;
  bottom: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-top-center {
  top: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video.position-bottom-center {
  top: auto;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-landingpage .teaser-canvas .teaser-item.teaser-video > picture {
  display: none;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-canvas .teaser-item.teaser-video > picture {
    display: block;
  }
}

.teaser-landingpage .teaser-canvas .teaser-progressBar {
  width: 100%;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #fafafa;
  z-index: 1;
}

.teaser-landingpage .teaser-canvas .teaser-progressBar span {
  background-color: #ff8d2f;
  width: 0%;
  display: block;
  height: 4px;
}

.teaser-landingpage .teaser-bullets {
  display: none;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-bullets {
    display: block;
    position: absolute;
    top: -45px;
    left: 0;
    width: 100%;
  }
}

.teaser-landingpage .teaser-bullets ul {
  padding: 0;
  margin: 0;
  list-style: none;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
}

.teaser-landingpage .teaser-bullets ul li {
  display: inline-block;
  padding: 0;
  border-radius: 100%;
  border: 1px solid #fafafa;
  width: 8px;
  height: 8px;
}

.teaser-landingpage .teaser-bullets ul li.active {
  background-color: #fafafa;
}

.teaser-landingpage .teaser-bullets ul li:before {
  display: none;
}

.teaser-landingpage .teaser-info {
  text-align: center;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fafafa;
  background: rgba(250, 250, 250, 0.8);
  padding-top: 0rem;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-info {
    margin-top: 0;
    position: relative;
    left: auto;
    top: auto;
    background: #fafafa;
    background: rgba(250, 250, 250, 0);
  }
}

.teaser-landingpage .teaser-info .h2 {
  margin-top: 2rem;
}

.teaser-landingpage .teaser-info .teaser-info-box-container {
  display: table;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-info .teaser-info-box-container {
    display: block;
  }
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  float: none;
  display: table-cell;
  vertical-align: top;
  display: flex-item;
}

@media (max-width: 1000px) {
  .teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box {
    width: 100%;
    display: block;
    float: none;
    z-index: 9;
  }
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box > div {
  padding: 1.5rem 1rem;
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box > div a {
  text-decoration: none;
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box > div a:hover, .teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box > div a:focus {
  text-decoration: none;
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box > div a:hover .teaser-landingpage-text, .teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box > div a:focus .teaser-landingpage-text {
  color: #666666;
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box > div .svg-icon {
  margin-bottom: 0.5rem;
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box.active > div {
  background: #fafafa;
  background: #fafafa;
}

.teaser-landingpage .teaser-info .teaser-info-box-container .teaser-info-box.active > div:before {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fafafa;
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
}

.touch .teaser-landingpage .teaser-canvas .teaser-item.teaser-video > video {
  display: none;
}

.touch .teaser-landingpage .teaser-canvas .teaser-item.teaser-video > picture {
  display: block;
}

.teaser-landingpage2 {
  margin-bottom: 4rem;
}

.teaser-landingpage2 .teaser-canvas {
  min-height: 720px;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas {
    min-height: 280px;
    margin-top: 100px;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-item {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 720px;
  opacity: 0;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
  overflow: hidden;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas .teaser-item {
    min-height: 280px;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-item.active {
  opacity: 1;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
}

.teaser-landingpage2 .teaser-canvas .teaser-item.text-position-right .heroTitle {
  position: absolute;
  bottom: 370px;
  right: 100px;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 140px;
    right: 30px;
    font-size: 1.2rem;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
  position: absolute;
  top: 350px;
  right: 50px;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 140px;
    right: 10px;
    font-size: 0.8rem;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-item.text-position-left .heroTitle {
  position: absolute;
  bottom: 370px;
  left: 50px;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 140px;
    left: 30px;
    font-size: 1.2rem;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
  position: absolute;
  top: 350px;
  left: 100px;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 140px;
    left: 10px;
    font-size: 0.8rem;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video {
  position: absolute;
  top: 0;
  left: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: block;
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video::-webkit-media-controls {
  display: none;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video {
    display: none;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-center-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-top-left {
  top: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-top-right {
  top: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-center-left {
  top: 50%;
  left: 0;
  transform: translate(0%, -50%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-center-right {
  top: 50%;
  left: auto;
  right: 0;
  transform: translate(0%, -50%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-bottom-left {
  top: auto;
  bottom: 0;
  left: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-bottom-right {
  top: auto;
  bottom: 0;
  left: auto;
  right: 0;
  transform: translate(0%, 0%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-top-center {
  top: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > video.position-bottom-center {
  top: auto;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0%);
}

.teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > picture {
  display: none;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-canvas .teaser-item.teaser-video > picture {
    display: block;
  }
}

.teaser-landingpage2 .teaser-canvas .teaser-progressBar {
  width: 100%;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #fafafa;
  z-index: 1;
}

.teaser-landingpage2 .teaser-canvas .teaser-progressBar span {
  background-color: #ff8d2f;
  width: 0%;
  display: block;
  height: 4px;
}

.teaser-landingpage2 .teaser-bullets {
  display: none;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-bullets {
    display: block;
    position: absolute;
    top: -45px;
    left: 0;
    width: 100%;
  }
}

.teaser-landingpage2 .teaser-bullets ul {
  padding: 0;
  margin: 0;
  list-style: none;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
}

.teaser-landingpage2 .teaser-bullets ul li {
  display: inline-block;
  padding: 0;
  border-radius: 100%;
  border: 1px solid #fafafa;
  width: 8px;
  height: 8px;
}

.teaser-landingpage2 .teaser-bullets ul li.active {
  background-color: #fafafa;
}

.teaser-landingpage2 .teaser-bullets ul li:before {
  display: none;
}

.teaser-landingpage2 .teaser-controller {
  text-align: center;
  margin-top: -150px;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-controller {
    margin-top: 0;
  }
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-controller .teaser-switch-box-container {
    display: block;
  }
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  float: none;
  display: flex-item;
  cursor: pointer;
}

@media (max-width: 960px) {
  .teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box {
    width: 100%;
    display: block;
    float: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    opacity: 0;
  }
  .teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box.active {
    opacity: 1;
    z-index: 10;
  }
  .teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box:last-child {
    clear: both;
  }
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box > div {
  background: #fafafa;
  background: rgba(250, 250, 250, 0.7);
  padding: 1.5rem 1rem 3rem;
  -ms-box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  -o-box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.3);
  transition: all 1s ease-in;
  height: 100%;
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box > div:before {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fafafa;
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: all 0.5s ease-out;
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box > div .svg-icon {
  margin-bottom: 1rem;
  z-index: 5;
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box > div .cta-link {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div {
  background: #fafafa;
  background: #fafafa;
  transition: all 1s ease-out;
}

.teaser-landingpage2 .teaser-controller .teaser-switch-box-container .teaser-switch-box.active > div:before {
  top: -15px;
  opacity: 1;
  transition: all 0.5s ease-out 1.5s;
}

.teaser-special {
  min-height: 468px;
}

@media (max-width: 1023px) {
  .teaser-special {
    min-height: 0;
  }
}

@media (max-width: 1023px) {
  .teaser-special picture img {
    filter: alpha(opacity=15);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=15)";
    -webkit-opacity: 0.15;
    -khtml-opacity: 0.15;
    -moz-opacity: 0.15;
    -ms-opacity: 0.15;
    -o-opacity: 0.15;
    opacity: 0.15;
  }
}

.teaser-special .fluid-container {
  min-height: 468px;
}

@media (max-width: 1023px) {
  .teaser-special .fluid-container {
    min-height: 0;
    height: auto;
  }
}

.teaser-special .fluid-container .row {
  min-height: 468px;
  margin-bottom: 0;
}

@media (max-width: 1023px) {
  .teaser-special .fluid-container .row {
    min-height: 0;
    margin-bottom: 1.1768447837%;
  }
}

.teaser-special .fluid-container .row .teaser-content {
  width: 33.3333333333%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-left: 41.6666666667%;
  display: block;
  display: flex;
  align-items: center;
  min-height: 468px;
}

@media (max-width: 1700px) {
  .teaser-special .fluid-container .row .teaser-content {
    width: 41.6666666667%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    margin-left: 58.3333333333%;
  }
}

@media (max-width: 1023px) {
  .teaser-special .fluid-container .row .teaser-content {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    margin-left: 0%;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (max-width: 1023px) {
  .teaser-special .fluid-container .row .teaser-content {
    min-height: 0;
    justify-content: center;
    text-align: center;
  }
  .teaser-special .fluid-container .row .teaser-content > div {
    padding: 20px 0;
  }
}

.teaser-special .fluid-container .row .teaser-content .btn-cta {
  display: none;
}

@media (max-width: 1700px) {
  .teaser-special .fluid-container .row .teaser-content .btn-cta {
    display: inline-block;
  }
}

.teaser-special .fluid-container .row .teaser-cta {
  width: 25%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 468px;
}

@media (max-width: 1023px) {
  .teaser-special .fluid-container .row .teaser-cta {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (max-width: 1023px) {
  .teaser-special .fluid-container .row .teaser-cta {
    min-height: 0;
    margin-bottom: 1.1768447837%;
  }
}

@media (max-width: 1700px) {
  .teaser-special .fluid-container .row .teaser-cta {
    display: none;
  }
}

.teaser-special.teaser-special-2 .fluid-container .row .teaser-content {
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-left: 0%;
}

.teaser-special.teaser-special-2 .fluid-container .row .teaser-content .heroTitle {
  margin-bottom: 0;
}

.teaser-special.teaser-special-2 .fluid-container .row .teaser-content .heroSubtitle {
  margin-left: 2rem;
}

@media (max-width: 1700px) {
  .teaser-special.teaser-special-2 .fluid-container .row .teaser-content {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    margin-left: 0%;
  }
}

@media (max-width: 1023px) {
  .teaser-special.teaser-special-2 .fluid-container .row .teaser-content {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    margin-left: 0%;
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .teaser-special.teaser-special-2 .fluid-container .row .teaser-content .heroSubtitle {
    margin-left: 0;
  }
}

.teaser-special.teaser-special-2 .fluid-container .row .teaser-cta {
  display: block;
  display: flex;
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  text-align: right;
}

@media (max-width: 1023px) {
  .teaser-special.teaser-special-2 .fluid-container .row .teaser-cta {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    text-align: center;
    display: flex;
  }
}

.teaser-sidebar {
  margin-bottom: 4rem;
}

.teaser-sidebar .teaser-canvas {
  min-height: 468px;
}

@media (max-width: 960px) {
  .teaser-sidebar .teaser-canvas {
    min-height: 280px;
  }
}

.teaser-sidebar .teaser-canvas .teaser-item {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 468px;
  opacity: 0;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
  overflow: hidden;
}

@media (max-width: 960px) {
  .teaser-sidebar .teaser-canvas .teaser-item {
    min-height: 280px;
  }
}

.teaser-sidebar .teaser-canvas .teaser-item.active {
  opacity: 1;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
}

.teaser-contentpage {
  margin-bottom: 4rem;
}

.teaser-contentpage .teaser-canvas {
  min-height: 640px;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas {
    min-height: 280px;
  }
}

.teaser-contentpage .teaser-canvas .teaser-item {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 468px;
  opacity: 0;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
  overflow: hidden;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas .teaser-item {
    min-height: 280px;
    position: relative;
  }
}

.teaser-contentpage .teaser-canvas .teaser-item.active {
  opacity: 1;
  transition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
}

.teaser-contentpage .teaser-canvas .teaser-item .fluid-container {
  min-height: 640px;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas .teaser-item .fluid-container {
    min-height: 280px;
    margin-top: 100px;
  }
}

.teaser-contentpage .teaser-canvas .teaser-item.text-position-right .heroTitle {
  position: absolute;
  bottom: 118px;
  right: 100px;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas .teaser-item.text-position-right .heroTitle {
    bottom: 140px;
    right: 30px;
    font-size: 1.2rem;
  }
}

.teaser-contentpage .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
  position: absolute;
  top: 350px;
  right: 50px;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas .teaser-item.text-position-right .heroSubtitle {
    top: 140px;
    right: 10px;
    font-size: 0.8rem;
  }
}

.teaser-contentpage .teaser-canvas .teaser-item.text-position-left .heroTitle {
  position: absolute;
  bottom: 80px;
  left: 50px;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas .teaser-item.text-position-left .heroTitle {
    bottom: 140px;
    left: 30px;
    font-size: 1.2rem;
  }
}

.teaser-contentpage .teaser-canvas .teaser-item.text-position-left .heroTitleSubline {
  position: absolute;
  top: 350px;
  left: 100px;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas .teaser-item.text-position-left .heroTitleSubline {
    top: 140px;
    left: 10px;
    font-size: 0.8rem;
  }
}

.teaser-contentpage .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
  position: absolute;
  top: 350px;
  left: 100px;
}

@media (max-width: 960px) {
  .teaser-contentpage .teaser-canvas .teaser-item.text-position-left .heroSubtitle {
    top: 140px;
    left: 10px;
    font-size: 0.8rem;
  }
}

.svg-icon {
  zoom: 110%;
  border-radius: 100%;
  background-color: #f68b37;
  background-image: -webkit-gradient(left, right);
  background-image: linear-gradient(#f68b37, #e97322);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#f68b37', endColorStr='#e97322');
  display: inline-block;
  width: 100px;
  height: 100px;
}

.svg-icon svg {
  position: relative;
}

.svg-icon svg .cls-1 {
  fill: #fafafa;
}

.svg-icon svg .icon {
  fill: #fafafa;
}

.svg-icon.svg-icon-sm {
  zoom: 60%;
}

.svg-icon.svg-icon-green {
  background-color: #2de0ac;
  background-image: -webkit-gradient(left, right);
  background-image: linear-gradient(#2de0ac, #09ce95);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#2de0ac', endColorStr='#09ce95');
}

.svg-icon.svg-icon-blue {
  background-color: #68aeff;
  background-image: -webkit-gradient(left, right);
  background-image: linear-gradient(#68aeff, #489dff);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#68aeff', endColorStr='#489dff');
}

@media (max-width: 992px) {
  .ce-gallery {
    width: 100%;
  }
}

/* Anpassungen von kleinen bis groeßeren Geraeten */
@media (min-width: 640px) {
  .ce-gallery .ce-column {
    margin: 0;
    /* Abstand zwischen Bildern */
    padding: 0 5px;
    box-sizing: border-box;
    float: left;
  }
  /* Kein Abstand beim ersten und letzten Bild */
  .ce-gallery .ce-column:first-child {
    padding-left: 0;
  }
  .ce-gallery .ce-column:last-child {
    margin-right: 0;
  }
  /* Fluid Image Tags */
  .ce-gallery img,
  .ce-gallery picture {
    width: 100%;
    height: auto;
  }
  /* Spaltenbreiten je nach eingestellten Columns */
  .ce-gallery[data-ce-columns="2"] .ce-column {
    width: 50%;
  }
  .ce-gallery[data-ce-columns="3"] .ce-column {
    width: 33%;
  }
  .ce-gallery[data-ce-columns="4"] .ce-column {
    width: 25%;
  }
  .ce-gallery[data-ce-columns="5"] .ce-column {
    width: 20%;
  }
}

/* Anpassungen fuer kleine Geraete */
@media (max-width: 640px) {
  /* Ein Bild pro Zeile */
  .ce-gallery .ce-column {
    margin: 0 0 10px;
    width: 100%;
    box-sizing: border-box;
  }
  /* Fluid Image Tags */
  .ce-gallery img,
  .ce-gallery picture {
    width: 100%;
    height: auto;
  }
}

footer .ce-gallery img,
footer .ce-gallery picture {
  max-width: 120px;
}

.promobox-double {
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-bottom: 2rem;
}

@media (max-width: 992px) {
  .promobox-double {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (min-width: 993px) and (max-width: 1300px) {
  .promobox-double {
    width: 66.6666666667%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.promobox-double .promobox .promobox-top {
  background-color: #fff;
  overflow: hidden;
}

.promobox-double .promobox .promobox-top .promobox-left {
  width: 50%;
  height: 100%;
  display: inline-block;
}

@media (min-width: 768px) and (max-width: 992px) {
  .promobox-double .promobox .promobox-top .promobox-left {
    min-height: 250px;
  }
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-top .promobox-left {
    width: 100%;
  }
}

.promobox-double .promobox .promobox-top .promobox-left > div {
  padding: 15px;
}

.promobox-double .promobox .promobox-top .promobox-right {
  width: 50%;
  height: 100%;
  display: inline-block;
  float: right;
}

@media (min-width: 768px) and (max-width: 992px) {
  .promobox-double .promobox .promobox-top .promobox-right {
    min-height: 250px;
  }
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-top .promobox-right {
    width: 100%;
    display: none;
  }
}

.promobox-double .promobox .promobox-top .promobox-right.mobile {
  display: none;
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-top .promobox-right.mobile {
    display: block;
  }
}

.promobox-double .promobox .promobox-top .promobox-right .promobox-image {
  height: 100%;
  min-height: 330px;
}

@media (max-width: 992px) {
  .promobox-double .promobox .promobox-top .promobox-right .promobox-image {
    min-height: 250px;
  }
}

.promobox-double .promobox .promobox-top .promobox-right .promobox-image:before {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fff;
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-top .promobox-right .promobox-image:before {
    left: 50%;
    top: auto;
    bottom: -20px;
    transform: translateX(-50%);
  }
}

.promobox-double .promobox .promobox-spacer {
  height: 1rem;
}

.promobox-double .promobox .promobox-bottom {
  background-color: #fff;
  margin-top: 1rem;
}

@media (min-width: 768px) and (max-width: 992px) {
  .promobox-double .promobox .promobox-bottom {
    margin-top: 2rem;
  }
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-bottom {
    margin-top: 2rem;
  }
}

.promobox-double .promobox .promobox-bottom .promobox-left {
  width: 50%;
  height: 100%;
  display: inline-block;
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-bottom .promobox-left {
    width: 100%;
    float: none;
  }
}

.promobox-double .promobox .promobox-bottom .promobox-left .promobox-image {
  height: 100%;
  min-height: 330px;
  margin-bottom: -7px;
}

@media (max-width: 992px) {
  .promobox-double .promobox .promobox-bottom .promobox-left .promobox-image {
    min-height: 250px;
  }
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-bottom .promobox-left .promobox-image {
    width: 100%;
  }
}

.promobox-double .promobox .promobox-bottom .promobox-left .promobox-image:after {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fff;
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-bottom .promobox-left .promobox-image:after {
    left: 50%;
    top: auto;
    bottom: -20px;
    transform: translateX(-50%);
  }
}

.promobox-double .promobox .promobox-bottom .promobox-right {
  width: 50%;
  float: right;
  display: inline-block;
}

@media (max-width: 767px) {
  .promobox-double .promobox .promobox-bottom .promobox-right {
    width: 100%;
    float: none;
  }
}

.promobox-double .promobox .promobox-bottom .promobox-right > div {
  padding: 15px;
}

.promobox-single {
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-bottom: 2rem;
}

@media (max-width: 992px) {
  .promobox-single {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (min-width: 993px) and (max-width: 1300px) {
  .promobox-single {
    width: 33.3333333333%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.promobox-single .promobox {
  background-color: #fff;
}

.promobox-single .promobox .promobox-image {
  min-height: 330px;
}

@media (max-width: 992px) {
  .promobox-single .promobox .promobox-image {
    min-height: 250px;
  }
}

.promobox-single .promobox .promobox-image:after {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fff;
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.promobox-single .promobox .promobox-spacer {
  height: 0;
}

@media (max-width: 992px) {
  .promobox-single .promobox .promobox-bottom {
    padding-bottom: 0rem;
  }
}

.promobox-single .promobox .promobox-bottom .promobox-info-container {
  padding: 0rem 1rem 1rem 1rem;
  margin-top: 1rem;
}

.promobox-wide {
  width: 100%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-bottom: 2rem;
}

.promobox-wide .promobox {
  background-color: #fff;
}

.promobox-wide .promobox .promobox-left {
  width: 67%;
  height: 100%;
  display: inline-block;
}

@media (min-width: 768px) and (max-width: 992px) {
  .promobox-wide .promobox .promobox-left {
    width: 50%;
  }
}

@media (max-width: 767px) {
  .promobox-wide .promobox .promobox-left {
    width: 100%;
  }
}

.promobox-wide .promobox .promobox-left .promobox-image {
  min-height: 330px;
  height: 100%;
  margin-bottom: -7px;
}

@media (max-width: 992px) {
  .promobox-wide .promobox .promobox-left .promobox-image {
    min-height: 250px;
  }
}

.promobox-wide .promobox .promobox-left .promobox-image:after {
  content: "";
  width: 35px;
  height: 35px;
  border-radius: 100%;
  background-color: #fff;
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

@media (max-width: 767px) {
  .promobox-wide .promobox .promobox-left .promobox-image:after {
    top: auto;
    left: 50%;
    bottom: -20px;
    transform: translateX(-50%);
  }
}

.promobox-wide .promobox .promobox-right {
  width: 33%;
  height: 100%;
  display: inline-block;
  float: right;
}

@media (min-width: 768px) and (max-width: 992px) {
  .promobox-wide .promobox .promobox-right {
    width: 50%;
  }
}

@media (max-width: 767px) {
  .promobox-wide .promobox .promobox-right {
    width: 100%;
    float: none;
  }
}

.promobox-wide .promobox .promobox-right > div {
  padding: 15px;
}

table {
  margin-bottom: 1.5rem;
}

table thead td, table thead th {
  font-family: "BlissPro-Light";
  font-weight: normal;
  vertical-align: top;
}

table td {
  padding: 1.25rem;
  vertical-align: top;
}

table.table-theme-boxed thead td, table.table-theme-boxed thead th {
  font-family: "BlissPro-Light";
  font-weight: normal;
  color: #140043;
  background: #e5e5e5;
  padding: 1.25rem;
  font-size: 1.25rem;
  line-height: 1;
}

table.table-theme-boxed tbody tr td {
  background: #eeeeee;
  padding: 1.25rem;
}

table.table-theme-boxed tbody tr:nth-child(2n) td {
  background: #f8f8f8;
}

table.table-theme-light thead td, table.table-theme-light thead th {
  font-family: "BlissPro-Light";
  font-weight: normal;
  background: #eeeeee;
  border-bottom: 1px solid #ff8d2f;
  padding: 1.25rem;
  font-size: 1.25rem;
  line-height: 1;
  color: #140043;
}

table.table-theme-light tbody td {
  border-bottom: 1px solid #ff8d2f;
  padding: 1.25rem;
  background: #f8ede4;
}

table.table-theme-light tbody td:first-child {
  background: transparent;
}

table.table-prices thead td, table.table-prices thead th {
  text-align: right;
}

table.table-prices thead td:first-child, table.table-prices thead th:first-child {
  text-align: left;
}

table.table-prices tbody td {
  text-align: right;
}

table.table-prices tbody td:first-child {
  text-align: left;
}

table.table-prices.table-align-left thead td, table.table-prices.table-align-left thead th {
  text-align: left;
}

table.table-prices.table-align-left thead td:first-child, table.table-prices.table-align-left thead th:first-child {
  text-align: left;
}

table.table-prices.table-align-left tbody td {
  text-align: left;
}

table.table-prices.table-align-left tbody td:first-child {
  text-align: left;
}

.tx-indexedsearch-browsebox ul.browsebox {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.tx-indexedsearch-browsebox ul.browsebox li {
  display: inline-block;
  padding-right: 12px;
  padding-left: 0;
}

.tx-indexedsearch-browsebox ul.browsebox li:before {
  display: none;
}

.tx-indexedsearch-res table {
  margin: 0;
}

.tx-indexedsearch-res table td {
  padding: 0;
}

#tx_indexedsearch .input-append.searchfield input.tx-indexedsearch-searchbox-sword {
  padding-right: 50px;
  width: 100%;
  display: inline-block;
  padding: 0.5rem 1rem;
  border: none;
}

#tx_indexedsearch .input-append.searchfield button {
  position: absolute;
  top: 50%;
  right: 1rem;
  border: none;
  background: transparent;
  transform: translateY(-50%);
}

#tx_indexedsearch .input-append.searchfield button:hover {
  outline: none;
}

#tx_indexedsearch .input-append.searchfield button:hover i {
  color: #ff8d2f;
}

.tx-vnc-testimonials {
  margin-left: -1.7652671756%;
  margin-right: -1.7652671756%;
}

.tx-vnc-testimonials .testimonial {
  margin-bottom: 3rem;
  width: 50%;
  float: left;
  padding-left: 1.7652671756%;
  padding-right: 1.7652671756%;
}

.tx-vnc-testimonials .testimonial.even {
  width: 50%;
  float: right;
  padding-left: 1.7652671756%;
  padding-right: 1.7652671756%;
}

.tx-vnc-testimonials .testimonial .testimonial-image {
  z-index: 10;
  float: left;
  width: 25%;
  position: relative;
  border-radius: 100%;
  max-width: 150px;
}

.tx-vnc-testimonials .testimonial .testimonial-image:before {
  content: "";
  display: block;
  padding-top: 100%;
}

.tx-vnc-testimonials .testimonial .testimonial-text {
  text-align: center;
  width: 75%;
  float: left;
}

.tx-vnc-testimonials .testimonial .testimonial-text .testimonial-quotation {
  color: #ff8d2f;
  font-style: italic;
}

@media (max-width: 767px) {
  .tx-vnc-testimonials .testimonial {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
  .tx-vnc-testimonials .testimonial.even {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

.link-boxes.link-boxes-2 {
  margin-left: -1.7652671756%;
  margin-right: -1.7652671756%;
}

.link-boxes.link-boxes-3 {
  margin-left: -1.5691263783%;
  margin-right: -1.5691263783%;
}

.link-boxes.link-boxes-4 {
  margin-left: -1.7652671756%;
  margin-right: -1.7652671756%;
}

.link-boxes.link-boxes-5 {
  margin-left: -1.4122137405%;
  margin-right: -1.4122137405%;
}

.link-boxes .link-box {
  margin-bottom: 3rem;
}

.link-boxes.link-boxes-2 .link-box {
  width: 50%;
  float: left;
  padding-left: 1.7652671756%;
  padding-right: 1.7652671756%;
}

@media (max-width: 992px) {
  .link-boxes.link-boxes-2 .link-box {
    width: 50%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

@media (max-width: 767px) {
  .link-boxes.link-boxes-2 .link-box {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

.link-boxes.link-boxes-3 .link-box {
  width: 33.3333333333%;
  float: left;
  padding-left: 1.5691263783%;
  padding-right: 1.5691263783%;
}

@media (max-width: 992px) {
  .link-boxes.link-boxes-3 .link-box {
    width: 50%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

@media (max-width: 767px) {
  .link-boxes.link-boxes-3 .link-box {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

.link-boxes.link-boxes-4 .link-box {
  width: 25%;
  float: left;
  padding-left: 1.7652671756%;
  padding-right: 1.7652671756%;
}

@media (max-width: 992px) {
  .link-boxes.link-boxes-4 .link-box {
    width: 50%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

@media (max-width: 767px) {
  .link-boxes.link-boxes-4 .link-box {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

.link-boxes.link-boxes-5 .link-box {
  width: 20%;
  float: left;
  padding-left: 1.4122137405%;
  padding-right: 1.4122137405%;
}

@media (max-width: 992px) {
  .link-boxes.link-boxes-5 .link-box {
    width: 50%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

@media (max-width: 767px) {
  .link-boxes.link-boxes-5 .link-box {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

.link-boxes .link-box > div {
  height: 100%;
  background: #fafafa;
}

.link-boxes .link-box .link-box-header {
  background: #666666;
  color: #fafafa;
  padding: 1rem 1rem 1rem 3.4rem;
  font-size: 1.1em;
}

.link-boxes .link-box .link-box-header .svg-icon {
  position: absolute;
  left: 1rem;
  width: 1.875rem;
  height: 1.875rem;
}

.link-boxes .link-box .tarif-box-header {
  background: #ff8d2f;
  color: #fafafa;
  padding: 1rem;
  font-size: 1.1em;
  text-align: center;
  line-height: 1.2rem;
  min-height: 76px;
}

.link-boxes .link-box .tarif-box-image {
  width: 100%;
  height: auto;
}

.link-boxes .link-box .link-box-body {
  padding: 1rem;
}

.info-box {
  padding: 1rem;
  margin-bottom: 3rem;
  background: #eeeeee;
}

.info-box.info-box-green {
  background: #e1ece8;
}

@media (max-width: 767px) {
  .info-box.info-box-left {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

@media (max-width: 767px) {
  .info-box.info-box-right {
    width: 100%;
    float: right;
    padding-left: 1.7652671756%;
    padding-right: 1.7652671756%;
  }
}

.info-box .icon-text {
  padding: 1rem 1rem 1rem 2.1rem;
}

.info-box .icon-text .svg-icon {
  position: absolute;
  left: 0;
  width: 1.875rem;
  height: 1.875rem;
}

.info-box h3 {
  color: #140043;
}

.info-box b, .info-box strong {
  color: #666666;
}

.info-box-green h3 {
  color: #56796d;
}

.info-box-green b, .info-box-green strong {
  color: #56796d;
}

.services {
  width: 66.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-left: 16.6666666667%;
}

.services .accordion {
  width: 100%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.services .service {
  width: 33.3333333333%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  height: 250px;
  perspective: 1000px;
  transform-style: preserve-3d;
}

.services .service:hover .back {
  transform: rotateY(0deg);
}

.services .service:hover .front {
  transform: rotateY(180deg);
}

.services .service .flipper {
  transition: 0.6s;
  transform-style: preserve-3d;
  position: relative;
}

.services .service .front, .services .service .back {
  backface-visibility: hidden;
  transition: 0.6s;
  transform-style: preserve-3d;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  height: 230px;
}

.services .service .front {
  z-index: 2;
  transform: rotateY(0deg);
  background: #ff8d2f;
  color: #fafafa;
}

.services .service .back {
  transform: rotateY(-180deg);
  border: 1px solid #ff8d2f;
}

.services .service .service-additional {
  background: #e5e5e5;
  height: 230px;
  text-align: center;
  display: block;
}

.services .service svg {
  height: 100px;
}

.tx-powermail {
  /** Progressbar Option **/
}

.tx-powermail .btn-group .btn {
  margin-right: 0.5em;
  margin-bottom: 1em;
  outline: none;
}

.tx-powermail .powermail_fieldset {
  border: 0;
  padding: 0;
  margin: 0;
}

.tx-powermail .powermail_fieldset .powermail_legend {
  display: none;
}

.tx-powermail .form-pages {
  /* margin-left: -$gutterPercent; */
  /* margin-right: -$gutterPercent; */
  width: 75%;
}

@media (max-width: 992px) {
  .tx-powermail .form-pages {
    width: 100%;
  }
}

.tx-powermail .form-pages .form-page .form-field {
  width: 100%;
  float: right;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.tx-powermail .form-pages .form-page.page-full {
  margin-left: -1.1768447837%;
  margin-right: -1.1768447837%;
}

.tx-powermail .form-pages .form-page.page-full .form-field.field-small {
  width: 33.3333333333%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-full .form-field.field-small {
    width: 100%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.tx-powermail .form-pages .form-page.page-full .form-field.field-medium {
  width: 66.6666666667%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-full .form-field.field-medium {
    width: 100%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.tx-powermail .form-pages .form-page.page-full .form-field.field-full {
  width: 100%;
  float: right;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.tx-powermail .form-pages .form-page.page-half, .tx-powermail .form-pages .form-page.page-half-last {
  margin-left: -1.1768447837%;
  margin-right: -1.1768447837%;
  width: 50%;
  float: left;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-half, .tx-powermail .form-pages .form-page.page-half-last {
    width: 100%;
    float: left;
  }
}

.tx-powermail .form-pages .form-page.page-half .form-field, .tx-powermail .form-pages .form-page.page-half-last .form-field {
  width: 100%;
  float: right;
  padding-left: 2.3536895674%;
  padding-right: 2.3536895674%;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-half .form-field, .tx-powermail .form-pages .form-page.page-half-last .form-field {
    width: 100%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.tx-powermail .form-pages .form-page.page-half .form-field.field-small, .tx-powermail .form-pages .form-page.page-half-last .form-field.field-small {
  width: 33.3333333333%;
  float: left;
  padding-left: 2.3536895674%;
  padding-right: 2.3536895674%;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-half .form-field.field-small, .tx-powermail .form-pages .form-page.page-half-last .form-field.field-small {
    width: 100%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.tx-powermail .form-pages .form-page.page-half .form-field.field-medium, .tx-powermail .form-pages .form-page.page-half-last .form-field.field-medium {
  width: 66.6666666667%;
  float: left;
  padding-left: 2.3536895674%;
  padding-right: 2.3536895674%;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-half .form-field.field-medium, .tx-powermail .form-pages .form-page.page-half-last .form-field.field-medium {
    width: 100%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.tx-powermail .form-pages .form-page.page-half .form-field.field-full, .tx-powermail .form-pages .form-page.page-half-last .form-field.field-full {
  width: 100%;
  float: right;
  padding-left: 2.3536895674%;
  padding-right: 2.3536895674%;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-half .form-field.field-full, .tx-powermail .form-pages .form-page.page-half-last .form-field.field-full {
    width: 100%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.tx-powermail .form-pages .form-page.page-half-last {
  width: 50%;
  float: right;
}

@media (max-width: 544px) {
  .tx-powermail .form-pages .form-page.page-half-last {
    width: 100%;
    float: left;
  }
}

.tx-powermail input[type="text"], .tx-powermail input[type="password"], .tx-powermail input[type="email"], .tx-powermail input[type="number"], .tx-powermail input[type="tel"], .tx-powermail textarea, .tx-powermail select {
  width: 100%;
  font-size: 1rem;
  padding: 0.4rem;
  outline: none !important;
  border: 1px solid #a3a3a3;
  height: 2.2rem;
}

.tx-powermail input[type="text"].parsley-error, .tx-powermail input[type="password"].parsley-error, .tx-powermail input[type="email"].parsley-error, .tx-powermail input[type="number"].parsley-error, .tx-powermail input[type="tel"].parsley-error, .tx-powermail textarea.parsley-error, .tx-powermail select.parsley-error {
  border-color: #ff0000;
  outline: none;
}

.tx-powermail textarea {
  height: 10rem;
}

.tx-powermail .parsley-errors-list {
  color: #ff0000;
  font-size: 0.8rem;
  padding-left: 0;
  position: absolute;
  right: 0;
}

.tx-powermail .parsley-errors-list li {
  list-style-type: none;
  padding-left: 0;
}

.tx-powermail .parsley-errors-list li:before {
  display: none;
}

.tx-powermail .powermail_label {
  font-size: 0.9rem;
  line-height: 1.5rem;
}

.tx-powermail .powermail_field {
  margin-bottom: 1.5rem;
}

.tx-powermail .progressbar .powermail_field_error_container_marker {
  display: none;
}

.tx-powermail .progressbar__icon {
  width: 64px;
  height: 64px;
  margin-bottom: 1.875rem;
  position: relative;
}

.tx-powermail .progressbar__icon svg {
  width: 135px;
  position: absolute;
  top: -30px;
  left: -34px;
}

.tx-powermail .progressbar__icon path {
  fill: #666666 !important;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar__icon {
    width: 100%;
  }
  .tx-powermail .progressbar__icon svg {
    left: 0;
    right: 0;
    margin: 0 auto;
  }
}

.tx-powermail .progressbar__radio {
  cursor: pointer;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar__radio {
    margin-right: 0;
    max-width: 80%;
  }
}

.tx-powermail .progressbar__radio label {
  cursor: pointer;
}

.tx-powermail .progressbar__radio:hover .progressbar__radioBox {
  border-color: #ff8d2f;
  color: #ff8d2f;
}

.tx-powermail .progressbar input[type="submit"] {
  position: relative;
  top: 1rem;
}

.tx-powermail .progressbar fieldset {
  margin-top: 3rem;
  width: 50%;
  /** Poll Styling */
}

@media (max-width: 992px) {
  .tx-powermail .progressbar fieldset {
    width: 100%;
  }
}

.tx-powermail .progressbar fieldset .powermail_tab_navigation {
  top: 1rem;
  margin-bottom: 3rem;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_ .powermail_field, .tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .powermail_field {
  margin-top: 1.875rem;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar fieldset .powermail_fieldwrap_type_ .powermail_field, .tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .powermail_field {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_ label, .tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll label {
  font-size: 22px;
  font-family: "BlissPro-Light";
  position: relative;
  color: #666666;
  line-height: 35px;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .powermail_field {
  margin-top: 1.875rem;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .powermail_field {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    align-items: center !important;
    flex-direction: column !important;
  }
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .powermail_field {
  display: flex;
  flex-direction: inherit;
  align-items: flex-start;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .progressbar__radio {
  display: block;
  margin-right: 2rem;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .progressbar__radio:hover .progressbar__radioBox {
  border-color: #ff8d2f;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .progressbar__radio:hover .progressbar__label {
  color: #ff8d2f;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .progressbar__radio label {
  cursor: pointer;
  text-align: center;
  padding: 0 1rem;
  display: block;
  max-width: 200px;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .progressbar__label {
  transition: all .5s ease-in-out;
  line-height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll .progressbar__radioBox {
  border-radius: 0.5rem;
  border: 1px solid #666666;
  min-height: 40px;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  z-index: 0;
  transition: all .5s ease-in-out;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll input:checked + .progressbar__radioBox {
  border-color: #ff8d2f;
  background: #ff8d2f;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  z-index: -1;
  border-radius: 0.5rem;
}

.tx-powermail .progressbar fieldset .powermail_fieldwrap_type_poll input:checked ~ .progressbar__label {
  color: #fafafa !important;
}

.tx-powermail .progressbar fieldset .powermail_tab_navigation a {
  margin-top: 0;
}

.tx-powermail .progressbar fieldset .powermail_tab_navigation a:last-child {
  margin-left: 2rem;
}

.tx-powermail .progressbar fieldset .powermail_tab_navigation a:first-child {
  margin-left: 0;
}

.tx-powermail .progressbar fieldset .powermail_tab_navigation a:first-child:only-child:not(.btn-primary) {
  position: relative;
  top: -2rem;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar fieldset .powermail_tab_navigation a:last-child {
    float: right;
  }
}

.tx-powermail .progressbar__label {
  transition: all .5s ease-in-out;
  line-height: 24px;
  min-height: 40px;
}

.tx-powermail .progressbar .poll__modal {
  position: absolute;
  top: 0;
  right: -25px;
  height: 20px;
  width: 20px;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar .poll__modal {
    top: unset;
    bottom: 3px;
  }
}

.tx-powermail .progressbar .poll__modal__button {
  width: 20px;
  height: 20px;
  background: #ff8d2f;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  border-radius: 16px;
  color: #fafafa;
  font-family: "BlissPro-Bold";
  position: absolute;
  cursor: pointer;
}

.tx-powermail .progressbar .poll__modal__content {
  display: none;
  background-color: #666666;
  color: #fafafa;
  font-size: 70%;
  position: absolute;
  width: 200px;
  left: 35px;
  top: 50%;
  transform: translateY(-52%);
  z-index: 1;
  padding: 1rem;
  line-height: 24px;
}

.tx-powermail .progressbar .poll__modal__content:before {
  content: '';
  width: 18px;
  height: 18px;
  background-color: #666666;
  transform: rotate(45deg) translateY(-52%);
  position: absolute;
  left: -15px;
  top: 50%;
  z-index: -1;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar .poll__modal__content {
    left: unset;
    right: 35px;
  }
  .tx-powermail .progressbar .poll__modal__content:before {
    left: unset;
    right: 5px;
  }
  .tx-powermail .progressbar .poll__modal__content--right {
    left: 35px;
  }
  .tx-powermail .progressbar .poll__modal__content--right:before {
    right: unset;
    left: -15px;
  }
}

.tx-powermail .progressbar .radio label {
  font-size: 16px !important;
  font-size: 16px !important;
}

@media (max-width: 992px) {
  .tx-powermail .progressbar .radio label {
    max-width: 100%;
    min-height: 40px;
    margin-right: 0;
  }
}

.tx-powermail .progressbar #bar {
  height: 0.5rem;
  width: 100%;
  background-color: #e5e5e5;
  position: relative;
}

.tx-powermail .progressbar #bar .progress {
  position: absolute;
  content: '';
  width: 0;
  height: 0.5rem;
  background-color: #ff8d2f;
  left: 0;
  top: 0;
}

.news-list-view .article {
  margin-left: -1.1768447837%;
  margin-right: -1.1768447837%;
  margin-bottom: 2rem;
}

.news-list-view .article .news-header {
  width: 75%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  margin-bottom: 1rem;
}

.news-list-view .article .news-img-wrap {
  width: 25%;
  float: right;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
  background: none;
  margin: 0;
}

.news-list-view .article .news-img-wrap a {
  padding: 0;
  border: 0;
}

.news-list-view .article .teaser-text {
  margin-left: 0.75rem;
}

.news-list-view .article .news-list-date {
  padding-right: 0.5rem;
  margin-right: 0.5rem;
  border-right: 1px solid #ddd;
}

.news-list-view .article .page-navigation ul li {
  float: left;
  display: block;
  padding: 0 2px;
  background: none;
}

.news-list-view .article .page-navigation ul li::before {
  content: " ";
}

@media (max-width: 767px) {
  .news-list-view .article .news-header {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .news-list-view .article .news-img-wrap {
    width: 33.3333333333%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .news-list-view .article .teaser-text {
    width: 66.6666666667%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

@media (max-width: 544px) {
  .news-list-view .article .news-header {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .news-list-view .article .news-img-wrap {
    width: 100%;
    float: right;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .news-list-view .article .teaser-text {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.news.news-single .article .news-img-wrap {
  float: right;
}

.news.news-single .article .news-img-wrap .news-img-caption {
  padding: 1rem;
  margin: 0;
}

.news-categories {
  margin-left: 0;
  padding-left: 0;
  margin-bottom: 2rem;
}

.news-categories li {
  display: inline-block;
}

@media (max-width: 544px) {
  .news-categories li {
    padding-bottom: 1rem;
  }
}

.news-categories li:before {
  display: none;
}

.news-categories li:first-child {
  margin-left: 0;
  padding-left: 0;
}

@media (max-width: 544px) {
  .news-categories li:first-child {
    padding-left: 1.5rem;
  }
}

.news-categories li a {
  text-decoration: none;
  background: #fafafa;
  border-radius: 0.5rem;
  padding: 0.25rem 0.75rem;
  font-family: "BlissPro-Bold";
}

.news-categories li a.active {
  color: #ea7322;
}

.news-categories li a.active:after {
  content: "\f012";
  font-family: "BlackTie";
  font-size: 0.75rem;
}

.news-single .news-list-date {
  padding-right: 0.5rem;
  margin-right: 0.5rem;
  border-right: 1px solid #ddd;
}

.news-single .news-img-caption {
  background: #e5e5e5;
}

.news-single .article .news-img-wrap img {
  display: block;
}

.news-single .news-related-wrap {
  margin-top: 2rem;
}

.news-single .news-related-wrap h3 {
  margin-bottom: 0.8rem;
}

.news-single .article .teaser-text {
  font-size: inherit;
  font-family: "BlissPro-Bold";
  color: #666666;
}

.home-news {
  background: #fff;
}

.home-news .home-news-item {
  padding: 1rem !important;
}

.home-news .home-news-item .home-news-header {
  margin-top: 0;
}

.home-news.news-3 .home-news-item {
  width: 33.3333333333%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.home-news.news-3 .home-news-item.last {
  width: 33.3333333333%;
  float: right;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .home-news.news-3 .home-news-item {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .home-news.news-3 .home-news-item.last {
    display: none;
  }
}

@media (max-width: 544px) {
  .home-news.news-3 .home-news-item {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .home-news.news-3 .home-news-item.last {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    display: block;
  }
}

.home-news.news-2 .home-news-item {
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.home-news.news-2 .home-news-item.last {
  width: 50%;
  float: right;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .home-news.news-2 .home-news-item {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .home-news.news-2 .home-news-item.last {
    display: none;
  }
}

@media (max-width: 544px) {
  .home-news.news-2 .home-news-item {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .home-news.news-2 .home-news-item.last {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    display: block;
  }
}

ul.events-list li {
  padding-left: 0;
  margin-bottom: 1.25rem;
  line-height: 1.25rem;
}

ul.events-list li:before {
  display: none;
}

ul.events-list li a {
  text-decoration: none;
}

ul.events-list li a .event-date, ul.events-list li a .event-title, ul.events-list li a .event-city {
  display: inline-block;
}

ul.events-list li a .event-date {
  width: 130px;
  position: relative;
}

ul.events-list li a .event-date:after {
  content: "\00B7";
  position: absolute;
  right: 5px;
}

ul.events-list li a .event-date.date-only {
  width: 80px;
}

ul.events-list li a .event-city {
  width: 130px;
  position: relative;
}

ul.events-list li a .event-title {
  width: calc(100% - 280px);
  position: relative;
  font-weight: bold;
}

ul.events-list li a .event-title:after {
  content: "\00B7";
  position: absolute;
  right: 5px;
}

ul.events-list li a .event-bookedout {
  background-color: #ff8d2f;
  padding: 3px 5px;
  color: #fafafa;
  border-radius: 5px;
  font-size: 12px;
  margin-left: 10px;
}

@media (max-width: 767px) {
  ul.events-list li a .event-city {
    width: auto;
  }
  ul.events-list li a .event-title {
    width: 100%;
  }
  ul.events-list li a .event-title:after {
    display: none;
  }
}

.home-events {
  background: #666666;
}

.home-events.events-2 .home-events-item {
  width: 50%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.home-events.events-2 .home-events-item.last {
  width: 50%;
  float: right;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .home-events.events-2 .home-events-item {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .home-events.events-2 .home-events-item.last {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    display: none;
  }
}

@media (max-width: 544px) {
  .home-events.events-2 .home-events-item.last {
    display: block;
  }
}

.home-events.events-3 .home-events-item {
  width: 33.3333333333%;
  float: left;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

.home-events.events-3 .home-events-item.last {
  width: 33.3333333333%;
  float: right;
  padding-left: 1.1768447837%;
  padding-right: 1.1768447837%;
}

@media (max-width: 767px) {
  .home-events.events-3 .home-events-item {
    width: 50%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .home-events.events-3 .home-events-item.last {
    display: none;
  }
}

@media (max-width: 544px) {
  .home-events.events-3 .home-events-item {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
  .home-events.events-3 .home-events-item.last {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    display: block;
  }
}

.home-events .home-events-item {
  padding: 1rem !important;
  color: #fafafa;
}

.home-events .home-events-item .event-header {
  color: #ff8d2f;
}

.home-events .home-events-item .link-with-arrow {
  color: #fafafa;
}

.events-navigation {
  margin-top: 2rem;
  background-color: #fff;
  padding: 1rem;
}

.contact-item {
  margin-bottom: 2rem;
  display:flex;
}

.contact-item .contact-image {
  text-align: center;
  width: 150px;
  height: 150px;
  max-width: 150px;
  overflow: hidden;
  border-radius: 50%;
  float: left;
}

/* Responsive Anpassung für 4-Spalten-Layout - nur Container-Größe, Focuspoint bleibt intakt */
@media (max-width: 1400px) {
  .contact-item .contact-image {
    width: 120px;
    height: 120px;
    max-width: 120px;
  }
}

@media (max-width: 1200px) {
  .contact-item .contact-image {
    width: 100px;
    height: 100px;
    max-width: 100px;
  }
}

.contact-item .contact-info {
  float: left;
}

.contact-item .contact-info > div {
  padding-left: 10px;
}

.contact-item .contact-info > div p {
  margin-bottom: 0.5rem;
}

@media (max-width: 544px) {
  .contact-item .contact-image {
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
    float: none;
    margin: 0 auto 0.75rem;
  }
  .contact-item .contact-image img {
    display: inline-block;
  }
  .contact-item .contact-info {
    text-align: center;
    width: 100%;
    float: left;
    padding-left: 1.1768447837%;
    padding-right: 1.1768447837%;
  }
}

.video-js .vjs-big-play-button .vjs-icon-placeholder:before, .video-js .vjs-modal-dialog, .vjs-button > .vjs-icon-placeholder:before, .vjs-modal-dialog .vjs-modal-dialog-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-js .vjs-big-play-button .vjs-icon-placeholder:before, .vjs-button > .vjs-icon-placeholder:before {
  text-align: center;
}

@font-face {
  font-family: VideoJS;
  src: url(../font/2.1.0/VideoJS.eot?#iefix) format("eot");
}

@font-face {
  font-family: VideoJS;
  src: url(data:application/font-woff;charset=utf-8;base64,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) format("woff"), url(data:application/x-font-ttf;charset=utf-8;base64,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) format("truetype");
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-play {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-big-play-button .vjs-icon-placeholder:before, .video-js .vjs-play-control .vjs-icon-placeholder {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-play:before {
  content: "\f101";
}

.video-js .vjs-big-play-button .vjs-icon-placeholder:before, .video-js .vjs-play-control .vjs-icon-placeholder:before {
  content: "\f101";
}

.vjs-icon-play-circle {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-play-circle:before {
  content: "\f102";
}

.video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder, .vjs-icon-pause {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder:before, .vjs-icon-pause:before {
  content: "\f103";
}

.video-js .vjs-mute-control.vjs-vol-0 .vjs-icon-placeholder, .vjs-icon-volume-mute {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-mute-control.vjs-vol-0 .vjs-icon-placeholder:before, .vjs-icon-volume-mute:before {
  content: "\f104";
}

.video-js .vjs-mute-control.vjs-vol-1 .vjs-icon-placeholder, .vjs-icon-volume-low {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-mute-control.vjs-vol-1 .vjs-icon-placeholder:before, .vjs-icon-volume-low:before {
  content: "\f105";
}

.video-js .vjs-mute-control.vjs-vol-2 .vjs-icon-placeholder, .vjs-icon-volume-mid {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-mute-control.vjs-vol-2 .vjs-icon-placeholder:before, .vjs-icon-volume-mid:before {
  content: "\f106";
}

.video-js .vjs-mute-control .vjs-icon-placeholder, .vjs-icon-volume-high {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-mute-control .vjs-icon-placeholder:before, .vjs-icon-volume-high:before {
  content: "\f107";
}

.video-js .vjs-fullscreen-control .vjs-icon-placeholder, .vjs-icon-fullscreen-enter {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-fullscreen-control .vjs-icon-placeholder:before, .vjs-icon-fullscreen-enter:before {
  content: "\f108";
}

.video-js.vjs-fullscreen .vjs-fullscreen-control .vjs-icon-placeholder, .vjs-icon-fullscreen-exit {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js.vjs-fullscreen .vjs-fullscreen-control .vjs-icon-placeholder:before, .vjs-icon-fullscreen-exit:before {
  content: "\f109";
}

.vjs-icon-square {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-square:before {
  content: "\f10a";
}

.vjs-icon-spinner {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-spinner:before {
  content: "\f10b";
}

.vjs-icon-subtitles {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-subs-caps-button .vjs-icon-placeholder, .video-js .vjs-subtitles-button .vjs-icon-placeholder {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js.video-js:lang(en-AU) .vjs-subs-caps-button .vjs-icon-placeholder, .video-js.video-js:lang(en-GB) .vjs-subs-caps-button .vjs-icon-placeholder, .video-js.video-js:lang(en-IE) .vjs-subs-caps-button .vjs-icon-placeholder, .video-js.video-js:lang(en-NZ) .vjs-subs-caps-button .vjs-icon-placeholder {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-subtitles:before {
  content: "\f10c";
}

.video-js .vjs-subs-caps-button .vjs-icon-placeholder:before, .video-js .vjs-subtitles-button .vjs-icon-placeholder:before {
  content: "\f10c";
}

.video-js.video-js:lang(en-AU) .vjs-subs-caps-button .vjs-icon-placeholder:before, .video-js.video-js:lang(en-GB) .vjs-subs-caps-button .vjs-icon-placeholder:before, .video-js.video-js:lang(en-IE) .vjs-subs-caps-button .vjs-icon-placeholder:before, .video-js.video-js:lang(en-NZ) .vjs-subs-caps-button .vjs-icon-placeholder:before {
  content: "\f10c";
}

.vjs-icon-captions {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-captions-button .vjs-icon-placeholder, .video-js:lang(en) .vjs-subs-caps-button .vjs-icon-placeholder, .video-js:lang(fr-CA) .vjs-subs-caps-button .vjs-icon-placeholder {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-captions:before {
  content: "\f10d";
}

.video-js .vjs-captions-button .vjs-icon-placeholder:before, .video-js:lang(en) .vjs-subs-caps-button .vjs-icon-placeholder:before, .video-js:lang(fr-CA) .vjs-subs-caps-button .vjs-icon-placeholder:before {
  content: "\f10d";
}

.video-js .vjs-chapters-button .vjs-icon-placeholder, .vjs-icon-chapters {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-chapters-button .vjs-icon-placeholder:before, .vjs-icon-chapters:before {
  content: "\f10e";
}

.vjs-icon-share {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-share:before {
  content: "\f10f";
}

.vjs-icon-cog {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-cog:before {
  content: "\f110";
}

.vjs-icon-circle {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-play-progress, .video-js .vjs-volume-level {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-circle:before {
  content: "\f111";
}

.video-js .vjs-play-progress:before, .video-js .vjs-volume-level:before {
  content: "\f111";
}

.vjs-icon-circle-outline {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-circle-outline:before {
  content: "\f112";
}

.vjs-icon-circle-inner-circle {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-circle-inner-circle:before {
  content: "\f113";
}

.vjs-icon-hd {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-hd:before {
  content: "\f114";
}

.video-js .vjs-control.vjs-close-button .vjs-icon-placeholder, .vjs-icon-cancel {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-control.vjs-close-button .vjs-icon-placeholder:before, .vjs-icon-cancel:before {
  content: "\f115";
}

.video-js .vjs-play-control.vjs-ended .vjs-icon-placeholder, .vjs-icon-replay {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-play-control.vjs-ended .vjs-icon-placeholder:before, .vjs-icon-replay:before {
  content: "\f116";
}

.vjs-icon-facebook {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-facebook:before {
  content: "\f117";
}

.vjs-icon-gplus {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-gplus:before {
  content: "\f118";
}

.vjs-icon-linkedin {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-linkedin:before {
  content: "\f119";
}

.vjs-icon-twitter {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-twitter:before {
  content: "\f11a";
}

.vjs-icon-tumblr {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-tumblr:before {
  content: "\f11b";
}

.vjs-icon-pinterest {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-pinterest:before {
  content: "\f11c";
}

.video-js .vjs-descriptions-button .vjs-icon-placeholder, .vjs-icon-audio-description {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-descriptions-button .vjs-icon-placeholder:before, .vjs-icon-audio-description:before {
  content: "\f11d";
}

.video-js .vjs-audio-button .vjs-icon-placeholder, .vjs-icon-audio {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.video-js .vjs-audio-button .vjs-icon-placeholder:before, .vjs-icon-audio:before {
  content: "\f11e";
}

.vjs-icon-next-item {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-next-item:before {
  content: "\f11f";
}

.vjs-icon-previous-item {
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
}

.vjs-icon-previous-item:before {
  content: "\f120";
}

.video-js {
  display: block;
  vertical-align: top;
  box-sizing: border-box;
  color: #fff;
  background-color: #fff;
  position: relative;
  padding: 0;
  font-size: 10px;
  line-height: 1;
  font-weight: 400;
  font-style: normal;
  font-family: Arial, Helvetica, sans-serif;
  word-break: initial;
}

.video-js:-moz-full-screen {
  position: absolute;
}

.video-js:-webkit-full-screen {
  width: 100% !important;
  height: 100% !important;
}

.video-js[tabindex="-1"] {
  outline: 0;
}

.video-js * {
  box-sizing: inherit;
}

.video-js :after, .video-js :before {
  box-sizing: inherit;
}

.video-js ul {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  list-style-position: outside;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.video-js.vjs-16-9, .video-js.vjs-4-3, .video-js.vjs-fluid {
  width: 100%;
  max-width: 100%;
  height: 0;
}

.video-js.vjs-16-9 {
  padding-top: 56.25%;
}

.video-js.vjs-4-3 {
  padding-top: 75%;
}

.video-js.vjs-fill {
  width: 100%;
  height: 100%;
}

.video-js .vjs-tech {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

body.vjs-full-window {
  padding: 0;
  margin: 0;
  height: 100%;
  overflow-y: auto;
}

.vjs-full-window .video-js.vjs-fullscreen {
  position: fixed;
  overflow: hidden;
  z-index: 1000;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
}

.video-js.vjs-fullscreen {
  width: 100% !important;
  height: 100% !important;
  padding-top: 0 !important;
}

.video-js.vjs-fullscreen.vjs-user-inactive {
  cursor: none;
}

.vjs-hidden {
  display: none !important;
}

.vjs-disabled {
  opacity: .5;
  cursor: default;
}

.video-js .vjs-offscreen {
  height: 1px;
  left: -9999px;
  position: absolute;
  top: 0;
  width: 1px;
}

.vjs-lock-showing {
  display: block !important;
  opacity: 1;
  visibility: visible;
}

.vjs-no-js {
  padding: 20px;
  color: #fff;
  background-color: #000;
  font-size: 18px;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
  width: 300px;
  height: 150px;
  margin: 0 auto;
}

.vjs-no-js a {
  color: #66a8cc;
}

.vjs-no-js a:visited {
  color: #66a8cc;
}

.video-js .vjs-big-play-button {
  font-size: 3em;
  line-height: 1.5em;
  height: 1.5em;
  width: 3em;
  display: block;
  position: absolute;
  top: 50%;
  margin-top: -.75em;
  left: 50%;
  margin-left: -1.5em;
  padding: 0;
  cursor: pointer;
  opacity: 1;
  border: .06666em solid #fff;
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
  border-radius: .3em;
  transition: all .4s;
}

.vjs-big-play-centered .vjs-big-play-button {
  top: 50%;
  left: 50%;
  margin-top: -.75em;
  margin-left: -1.5em;
}

.video-js .vjs-big-play-button:focus, .video-js:hover .vjs-big-play-button {
  border-color: #fff;
  background-color: #73859f;
  background-color: rgba(115, 133, 159, 0.5);
  transition: all 0s;
}

.vjs-controls-disabled .vjs-big-play-button, .vjs-error .vjs-big-play-button, .vjs-has-started .vjs-big-play-button, .vjs-using-native-controls .vjs-big-play-button {
  display: none;
}

.vjs-has-started.vjs-paused.vjs-show-big-play-button-on-pause .vjs-big-play-button {
  display: block;
}

.video-js button {
  background: 0 0;
  border: none;
  color: inherit;
  display: inline-block;
  overflow: visible;
  font-size: inherit;
  line-height: inherit;
  text-transform: none;
  text-decoration: none;
  transition: none;
  -webkit-appearance: none;
  appearance: none;
}

.vjs-control .vjs-button {
  width: 100%;
  height: 100%;
}

.video-js .vjs-control.vjs-close-button {
  cursor: pointer;
  height: 3em;
  position: absolute;
  right: 0;
  top: .5em;
  z-index: 2;
}

.video-js .vjs-modal-dialog {
  background: rgba(0, 0, 0, 0.8);
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8), rgba(255, 255, 255, 0));
  overflow: auto;
  box-sizing: content-box;
}

.video-js .vjs-modal-dialog > * {
  box-sizing: border-box;
}

.vjs-modal-dialog .vjs-modal-dialog-content {
  font-size: 1.2em;
  line-height: 1.5;
  padding: 20px 24px;
  z-index: 1;
}

.vjs-menu-button {
  cursor: pointer;
}

.vjs-menu-button.vjs-disabled {
  cursor: default;
}

.vjs-workinghover .vjs-menu-button.vjs-disabled:hover .vjs-menu {
  display: none;
}

.vjs-menu .vjs-menu-content {
  display: block;
  padding: 0;
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
  overflow: auto;
  box-sizing: content-box;
}

.vjs-menu .vjs-menu-content > * {
  box-sizing: border-box;
}

.vjs-scrubbing .vjs-menu-button:hover .vjs-menu {
  display: none;
}

.vjs-menu li {
  list-style: none;
  margin: 0;
  padding: .2em 0;
  line-height: 1.4em;
  font-size: 1.2em;
  text-align: center;
  text-transform: lowercase;
}

.vjs-menu li.vjs-menu-item:focus, .vjs-menu li.vjs-menu-item:hover {
  background-color: #73859f;
  background-color: rgba(115, 133, 159, 0.5);
}

.vjs-menu li.vjs-selected {
  background-color: #fff;
  color: #2b333f;
}

.vjs-menu li.vjs-selected:focus, .vjs-menu li.vjs-selected:hover {
  background-color: #fff;
  color: #2b333f;
}

.vjs-menu li.vjs-menu-title {
  text-align: center;
  text-transform: uppercase;
  font-size: 1em;
  line-height: 2em;
  padding: 0;
  margin: 0 0 .3em 0;
  font-weight: 700;
  cursor: default;
}

.vjs-menu-button-popup .vjs-menu {
  display: none;
  position: absolute;
  bottom: 0;
  width: 10em;
  right: 0;
  height: 0;
  margin-bottom: 1.5em;
  border-top-color: rgba(43, 51, 63, 0.7);
}

.vjs-menu-button-popup .vjs-menu .vjs-menu-content {
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
  position: absolute;
  width: 100%;
  bottom: 1.5em;
  max-height: 15em;
}

.vjs-menu-button-popup .vjs-menu.vjs-lock-showing, .vjs-workinghover .vjs-menu-button-popup:hover .vjs-menu {
  display: block;
}

.video-js .vjs-menu-button-inline {
  transition: all .4s;
  overflow: hidden;
}

.video-js .vjs-menu-button-inline:before {
  width: 2.222222222em;
}

.video-js .vjs-menu-button-inline.vjs-slider-active, .video-js .vjs-menu-button-inline:focus, .video-js .vjs-menu-button-inline:hover {
  width: 12em;
}

.video-js.vjs-no-flex .vjs-menu-button-inline {
  width: 12em;
}

.vjs-menu-button-inline .vjs-menu {
  opacity: 0;
  height: 100%;
  width: auto;
  position: absolute;
  left: 4em;
  top: 0;
  padding: 0;
  margin: 0;
  transition: all .4s;
}

.vjs-menu-button-inline.vjs-slider-active .vjs-menu, .vjs-menu-button-inline:focus .vjs-menu, .vjs-menu-button-inline:hover .vjs-menu {
  display: block;
  opacity: 1;
}

.vjs-no-flex .vjs-menu-button-inline .vjs-menu {
  display: block;
  opacity: 1;
  position: relative;
  width: auto;
}

.vjs-no-flex .vjs-menu-button-inline.vjs-slider-active .vjs-menu, .vjs-no-flex .vjs-menu-button-inline:focus .vjs-menu, .vjs-no-flex .vjs-menu-button-inline:hover .vjs-menu {
  width: auto;
}

.vjs-menu-button-inline .vjs-menu-content {
  width: auto;
  height: 100%;
  margin: 0;
  overflow: hidden;
}

.video-js .vjs-control-bar {
  display: none;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3em;
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
}

.vjs-has-started .vjs-control-bar {
  display: flex;
  visibility: visible;
  opacity: 1;
  transition: visibility .1s, opacity .1s;
}

.vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar {
  visibility: visible;
  opacity: 0;
  transition: visibility 1s, opacity 1s;
}

.vjs-controls-disabled .vjs-control-bar, .vjs-error .vjs-control-bar, .vjs-using-native-controls .vjs-control-bar {
  display: none !important;
}

.vjs-audio.vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar {
  opacity: 1;
  visibility: visible;
}

.vjs-has-started.vjs-no-flex .vjs-control-bar {
  display: table;
}

.video-js .vjs-control {
  position: relative;
  text-align: center;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 4em;
  flex: none;
}

.vjs-button > .vjs-icon-placeholder:before {
  font-size: 1.8em;
  line-height: 1.67;
}

.video-js .vjs-control:focus, .video-js .vjs-control:focus:before, .video-js .vjs-control:hover:before {
  text-shadow: 0 0 1em #fff;
}

.video-js .vjs-control-text {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.vjs-no-flex .vjs-control {
  display: table-cell;
  vertical-align: middle;
}

.video-js .vjs-custom-control-spacer {
  display: none;
}

.video-js .vjs-progress-control {
  cursor: pointer;
  flex: auto;
  display: flex;
  align-items: center;
  min-width: 4em;
}

.video-js .vjs-progress-control.disabled {
  cursor: default;
}

.vjs-live .vjs-progress-control {
  display: none;
}

.vjs-no-flex .vjs-progress-control {
  width: auto;
}

.video-js .vjs-progress-holder {
  flex: auto;
  transition: all .2s;
  height: .3em;
}

.video-js .vjs-progress-control .vjs-progress-holder {
  margin: 0 10px;
}

.video-js .vjs-progress-control:hover .vjs-progress-holder {
  font-size: 1.666666666666666666em;
}

.video-js .vjs-progress-control:hover .vjs-progress-holder.disabled {
  font-size: 1em;
}

.video-js .vjs-progress-holder .vjs-play-progress {
  position: absolute;
  display: block;
  height: 100%;
  margin: 0;
  padding: 0;
  width: 0;
  left: 0;
  top: 0;
}

.video-js .vjs-progress-holder .vjs-load-progress {
  position: absolute;
  display: block;
  height: 100%;
  margin: 0;
  padding: 0;
  width: 0;
  left: 0;
  top: 0;
}

.video-js .vjs-progress-holder .vjs-load-progress div {
  position: absolute;
  display: block;
  height: 100%;
  margin: 0;
  padding: 0;
  width: 0;
  left: 0;
  top: 0;
}

.video-js .vjs-play-progress {
  background-color: #fff;
}

.video-js .vjs-play-progress:before {
  font-size: .9em;
  position: absolute;
  right: -.5em;
  top: -.333333333333333em;
  z-index: 1;
}

.video-js .vjs-load-progress {
  background: #bfc7d3;
  background: rgba(115, 133, 159, 0.5);
}

.video-js .vjs-load-progress div {
  background: #fff;
  background: rgba(115, 133, 159, 0.75);
}

.video-js .vjs-time-tooltip {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: .3em;
  color: #000;
  float: right;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1em;
  padding: 6px 8px 8px 8px;
  pointer-events: none;
  position: relative;
  top: -3.4em;
  visibility: hidden;
  z-index: 1;
}

.video-js .vjs-progress-holder:focus .vjs-time-tooltip {
  display: none;
}

.video-js .vjs-progress-control:hover .vjs-progress-holder:focus .vjs-time-tooltip, .video-js .vjs-progress-control:hover .vjs-time-tooltip {
  display: block;
  font-size: .6em;
  visibility: visible;
}

.video-js .vjs-progress-control.disabled:hover .vjs-time-tooltip {
  font-size: 1em;
}

.video-js .vjs-progress-control .vjs-mouse-display {
  display: none;
  position: absolute;
  width: 1px;
  height: 100%;
  background-color: #000;
  z-index: 1;
}

.vjs-no-flex .vjs-progress-control .vjs-mouse-display {
  z-index: 0;
}

.video-js .vjs-progress-control:hover .vjs-mouse-display {
  display: block;
}

.video-js.vjs-user-inactive .vjs-progress-control .vjs-mouse-display {
  visibility: hidden;
  opacity: 0;
  transition: visibility 1s, opacity 1s;
}

.video-js.vjs-user-inactive.vjs-no-flex .vjs-progress-control .vjs-mouse-display {
  display: none;
}

.vjs-mouse-display .vjs-time-tooltip {
  color: #fff;
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.8);
}

.video-js .vjs-slider {
  position: relative;
  cursor: pointer;
  padding: 0;
  margin: 0 .45em 0 .45em;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  background-color: #73859f;
  background-color: rgba(115, 133, 159, 0.5);
}

.video-js .vjs-slider.disabled {
  cursor: default;
}

.video-js .vjs-slider:focus {
  text-shadow: 0 0 1em #fff;
  box-shadow: 0 0 1em #fff;
}

.video-js .vjs-mute-control {
  cursor: pointer;
  flex: none;
  padding-left: 2em;
  padding-right: 2em;
  padding-bottom: 3em;
}

.video-js .vjs-volume-control {
  cursor: pointer;
  margin-right: 1em;
  display: flex;
}

.video-js .vjs-volume-control.vjs-volume-horizontal {
  width: 5em;
}

.video-js .vjs-volume-panel .vjs-volume-control {
  visibility: visible;
  opacity: 0;
  width: 1px;
  height: 1px;
  margin-left: -1px;
}

.video-js .vjs-volume-panel {
  transition: width 1s;
}

.video-js .vjs-volume-panel:active .vjs-volume-control, .video-js .vjs-volume-panel:focus .vjs-volume-control, .video-js .vjs-volume-panel:hover .vjs-volume-control {
  visibility: visible;
  opacity: 1;
  position: relative;
  transition: visibility .1s, opacity .1s, height .1s, width .1s, left 0s, top 0s;
}

.video-js .vjs-volume-panel .vjs-volume-control:active, .video-js .vjs-volume-panel .vjs-volume-control:hover {
  visibility: visible;
  opacity: 1;
  position: relative;
  transition: visibility .1s, opacity .1s, height .1s, width .1s, left 0s, top 0s;
}

.video-js .vjs-volume-panel .vjs-mute-control:hover ~ .vjs-volume-control, .video-js .vjs-volume-panel .vjs-volume-control.vjs-slider-active {
  visibility: visible;
  opacity: 1;
  position: relative;
  transition: visibility .1s, opacity .1s, height .1s, width .1s, left 0s, top 0s;
}

.video-js .vjs-volume-panel:active .vjs-volume-control.vjs-volume-horizontal, .video-js .vjs-volume-panel:focus .vjs-volume-control.vjs-volume-horizontal, .video-js .vjs-volume-panel:hover .vjs-volume-control.vjs-volume-horizontal {
  width: 5em;
  height: 3em;
}

.video-js .vjs-volume-panel .vjs-volume-control:active.vjs-volume-horizontal, .video-js .vjs-volume-panel .vjs-volume-control:hover.vjs-volume-horizontal {
  width: 5em;
  height: 3em;
}

.video-js .vjs-volume-panel .vjs-mute-control:hover ~ .vjs-volume-control.vjs-volume-horizontal, .video-js .vjs-volume-panel .vjs-volume-control.vjs-slider-active.vjs-volume-horizontal {
  width: 5em;
  height: 3em;
}

.video-js .vjs-volume-panel.vjs-volume-panel-horizontal.vjs-slider-active, .video-js .vjs-volume-panel.vjs-volume-panel-horizontal:active, .video-js .vjs-volume-panel.vjs-volume-panel-horizontal:hover {
  width: 9em;
  transition: width .1s;
}

.video-js .vjs-volume-panel .vjs-volume-control.vjs-volume-vertical {
  height: 8em;
  width: 3em;
  left: -3.5em;
  transition: visibility 1s, opacity 1s, height 1s 1s, width 1s 1s, left 1s 1s, top 1s 1s;
}

.video-js .vjs-volume-panel .vjs-volume-control.vjs-volume-horizontal {
  transition: visibility 1s, opacity 1s, height 1s 1s, width 1s, left 1s 1s, top 1s 1s;
}

.video-js.vjs-no-flex .vjs-volume-panel .vjs-volume-control.vjs-volume-horizontal {
  width: 5em;
  height: 3em;
  visibility: visible;
  opacity: 1;
  position: relative;
  transition: none;
}

.video-js.vjs-no-flex .vjs-volume-control.vjs-volume-vertical, .video-js.vjs-no-flex .vjs-volume-panel .vjs-volume-control.vjs-volume-vertical {
  position: absolute;
  bottom: 3em;
  left: .5em;
}

.video-js .vjs-volume-panel {
  display: flex;
}

.video-js .vjs-volume-bar {
  margin: 1.35em .45em;
}

.vjs-volume-bar.vjs-slider-horizontal {
  width: 5em;
  height: .3em;
}

.vjs-volume-bar.vjs-slider-vertical {
  width: .3em;
  height: 5em;
  margin: 1.35em auto;
}

.video-js .vjs-volume-level {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #fff;
}

.video-js .vjs-volume-level:before {
  position: absolute;
  font-size: .9em;
}

.vjs-slider-vertical .vjs-volume-level {
  width: .3em;
}

.vjs-slider-vertical .vjs-volume-level:before {
  top: -.5em;
  left: -.3em;
}

.vjs-slider-horizontal .vjs-volume-level {
  height: .3em;
}

.vjs-slider-horizontal .vjs-volume-level:before {
  top: -.3em;
  right: -.5em;
}

.video-js .vjs-volume-panel.vjs-volume-panel-vertical {
  width: 4em;
}

.vjs-volume-bar.vjs-slider-vertical .vjs-volume-level {
  height: 100%;
}

.vjs-volume-bar.vjs-slider-horizontal .vjs-volume-level {
  width: 100%;
}

.video-js .vjs-volume-vertical {
  width: 3em;
  height: 8em;
  bottom: 8em;
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
}

.video-js .vjs-volume-horizontal .vjs-menu {
  left: -2em;
}

.vjs-poster {
  display: inline-block;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: cover;
  background-color: #fff;
  cursor: pointer;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 100%;
}

.vjs-poster img {
  display: block;
  vertical-align: middle;
  margin: 0 auto;
  max-height: 100%;
  padding: 0;
  width: 100%;
}

.vjs-has-started .vjs-poster {
  display: none;
}

.vjs-audio.vjs-has-started .vjs-poster {
  display: block;
}

.vjs-using-native-controls .vjs-poster {
  display: none;
}

.video-js .vjs-live-control {
  display: flex;
  align-items: flex-start;
  flex: auto;
  font-size: 1em;
  line-height: 3em;
}

.vjs-no-flex .vjs-live-control {
  display: table-cell;
  width: auto;
  text-align: left;
}

.video-js .vjs-time-control {
  flex: none;
  font-size: 1em;
  line-height: 3em;
  min-width: 2em;
  width: auto;
  padding-left: 1em;
  padding-right: 1em;
}

.video-js .vjs-current-time, .vjs-live .vjs-time-control {
  display: none;
}

.vjs-no-flex .vjs-current-time {
  display: none;
}

.vjs-no-flex .vjs-remaining-time.vjs-time-control.vjs-control {
  width: 0 !important;
  white-space: nowrap;
}

.video-js .vjs-duration, .vjs-no-flex .vjs-duration {
  display: none;
}

.vjs-time-divider {
  display: none;
  line-height: 3em;
}

.vjs-live .vjs-time-divider {
  display: none;
}

.video-js .vjs-play-control .vjs-icon-placeholder {
  cursor: pointer;
  flex: none;
}

.vjs-text-track-display {
  position: absolute;
  bottom: 3em;
  left: 0;
  right: 0;
  top: 0;
  pointer-events: none;
}

.video-js.vjs-user-inactive.vjs-playing .vjs-text-track-display {
  bottom: 1em;
}

.video-js .vjs-text-track {
  font-size: 1.4em;
  text-align: center;
  margin-bottom: .1em;
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.5);
}

.vjs-subtitles {
  color: #fff;
}

.vjs-captions {
  color: #fc6;
}

.vjs-tt-cue {
  display: block;
}

video::-webkit-media-text-track-display {
  transform: translateY(-3em);
}

.video-js.vjs-user-inactive.vjs-playing video::-webkit-media-text-track-display {
  transform: translateY(-1.5em);
}

.video-js .vjs-fullscreen-control {
  cursor: pointer;
  flex: none;
}

.vjs-playback-rate > .vjs-menu-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.vjs-playback-rate .vjs-playback-rate-value {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  font-size: 1.5em;
  line-height: 2;
  text-align: center;
}

.vjs-playback-rate .vjs-menu {
  width: 4em;
  left: 0;
}

.vjs-error .vjs-error-display .vjs-modal-dialog-content {
  font-size: 1.4em;
  text-align: center;
}

.vjs-error .vjs-error-display:before {
  color: #fff;
  content: 'X';
  font-family: Arial, Helvetica, sans-serif;
  font-size: 4em;
  left: 0;
  line-height: 1;
  margin-top: -.5em;
  position: absolute;
  text-shadow: .05em .05em .1em #000;
  text-align: center;
  top: 50%;
  vertical-align: middle;
  width: 100%;
}

.vjs-loading-spinner {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -25px 0 0 -25px;
  opacity: .85;
  text-align: left;
  border: 6px solid rgba(43, 51, 63, 0.7);
  box-sizing: border-box;
  background-clip: padding-box;
  width: 50px;
  height: 50px;
  border-radius: 25px;
  visibility: hidden;
}

.vjs-seeking .vjs-loading-spinner, .vjs-waiting .vjs-loading-spinner {
  display: block;
  animation: 0s linear .3s forwards vjs-spinner-show;
}

.vjs-loading-spinner:after, .vjs-loading-spinner:before {
  content: "";
  position: absolute;
  margin: -6px;
  box-sizing: inherit;
  width: inherit;
  height: inherit;
  border-radius: inherit;
  opacity: 1;
  border: inherit;
  border-color: transparent;
  border-top-color: #fff;
}

.vjs-seeking .vjs-loading-spinner:after, .vjs-seeking .vjs-loading-spinner:before {
  animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8) infinite, vjs-spinner-fade 1.1s linear infinite;
}

.vjs-waiting .vjs-loading-spinner:after, .vjs-waiting .vjs-loading-spinner:before {
  animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8) infinite, vjs-spinner-fade 1.1s linear infinite;
}

.vjs-seeking .vjs-loading-spinner:before, .vjs-waiting .vjs-loading-spinner:before {
  border-top-color: #fff;
}

.vjs-seeking .vjs-loading-spinner:after, .vjs-waiting .vjs-loading-spinner:after {
  border-top-color: #fff;
  animation-delay: .44s;
}

@keyframes vjs-spinner-show {
  to {
    visibility: visible;
  }
}

@keyframes vjs-spinner-spin {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes vjs-spinner-fade {
  0% {
    border-top-color: #73859f;
  }
  20% {
    border-top-color: #73859f;
  }
  35% {
    border-top-color: #fff;
  }
  60% {
    border-top-color: #73859f;
  }
  100% {
    border-top-color: #73859f;
  }
}

.vjs-chapters-button .vjs-menu ul {
  width: 24em;
}

.video-js .vjs-subs-caps-button + .vjs-menu .vjs-captions-menu-item .vjs-menu-item-text .vjs-icon-placeholder {
  position: absolute;
}

.video-js .vjs-subs-caps-button + .vjs-menu .vjs-captions-menu-item .vjs-menu-item-text .vjs-icon-placeholder:before {
  font-family: VideoJS;
  content: "\f10d";
  font-size: 1.5em;
  line-height: inherit;
}

.video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-custom-control-spacer {
  flex: auto;
}

.video-js.vjs-layout-tiny:not(.vjs-fullscreen).vjs-no-flex .vjs-custom-control-spacer {
  width: auto;
}

.video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-audio-button, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-captions-button, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-chapters-button, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-current-time, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-descriptions-button, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-duration, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-mute-control, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-playback-rate, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-progress-control, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-remaining-time, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-subtitles-button, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-time-divider, .video-js.vjs-layout-tiny:not(.vjs-fullscreen) .vjs-volume-control {
  display: none;
}

.video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-audio-button, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-captions-button, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-chapters-button, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-current-time, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-descriptions-button, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-duration, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-mute-control, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-playback-rate, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-remaining-time, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-subtitles-button, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-time-divider, .video-js.vjs-layout-x-small:not(.vjs-fullscreen) .vjs-volume-control {
  display: none;
}

.video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-captions-button, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-chapters-button, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-current-time, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-descriptions-button, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-duration, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-mute-control, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-playback-rate, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-remaining-time, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-subtitles-button .vjs-audio-button, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-time-divider, .video-js.vjs-layout-small:not(.vjs-fullscreen) .vjs-volume-control {
  display: none;
}

.vjs-modal-dialog.vjs-text-track-settings {
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.75);
  color: #fff;
  height: 70%;
}

.vjs-text-track-settings .vjs-modal-dialog-content {
  display: table;
}

.vjs-text-track-settings .vjs-track-settings-colors, .vjs-text-track-settings .vjs-track-settings-font {
  display: table-cell;
}

.vjs-text-track-settings .vjs-track-settings-controls {
  display: table-cell;
  text-align: right;
  vertical-align: bottom;
}

.vjs-text-track-settings fieldset {
  margin: 5px;
  padding: 3px;
  border: none;
}

.vjs-text-track-settings fieldset span {
  display: inline-block;
  margin-left: 5px;
}

.vjs-text-track-settings legend {
  color: #fff;
  margin: 0 0 5px 0;
}

.vjs-text-track-settings .vjs-label {
  position: absolute;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
  display: block;
  margin: 0 0 5px 0;
  padding: 0;
  border: 0;
  height: 1px;
  width: 1px;
  overflow: hidden;
}

.vjs-track-settings-controls button {
  background-color: #fff;
  background-image: linear-gradient(-180deg, #fff 88%, #73859f 100%);
  color: #2b333f;
  cursor: pointer;
  border-radius: 2px;
}

.vjs-track-settings-controls button:active, .vjs-track-settings-controls button:focus {
  outline-style: solid;
  outline-width: medium;
  background-image: linear-gradient(0deg, #fff 88%, #73859f 100%);
}

.vjs-track-settings-controls button:hover {
  color: rgba(43, 51, 63, 0.75);
}

.vjs-track-settings-controls .vjs-default-button {
  margin-right: 1em;
}

@media print {
  .video-js > :not(.vjs-tech):not(.vjs-poster) {
    visibility: hidden;
  }
}

@media \0screen {
  .vjs-user-inactive.vjs-playing .vjs-control-bar :before {
    content: "";
  }
}

@media \0screen {
  .vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar {
    visibility: hidden;
  }
}

.vjs-resize-manager {
  display: none;
}

.vjs-marker {
  position: absolute;
  left: 0;
  bottom: 0;
  opacity: 1;
  height: 100%;
  transition: opacity .2s ease;
  -webkit-transition: opacity .2s ease;
  -moz-transition: opacity .2s ease;
  z-index: 100;
}

.vjs-marker:hover {
  cursor: pointer;
  transform: scale(1.3, 1.3);
}

.vjs-tip {
  visibility: hidden;
  display: block;
  opacity: .8;
  padding: 5px;
  font-size: 10px;
  position: absolute;
  bottom: 14px;
  z-index: 100000;
}

.vjs-tip .vjs-tip-arrow {
  background: url(data:image/gif;base64,R0lGODlhCQAJAIABAAAAAAAAACH5BAEAAAEALAAAAAAJAAkAAAIRjAOnwIrcDJxvwkplPtchVQAAOw==) no-repeat top left;
  bottom: 0;
  left: 50%;
  margin-left: -4px;
  background-position: bottom left;
  position: absolute;
  width: 9px;
  height: 5px;
}

.vjs-tip .vjs-tip-inner {
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  padding: 5px 8px 4px 8px;
  background-color: #000;
  color: #fff;
  max-width: 200px;
  text-align: center;
}

.vjs-break-overlay {
  visibility: hidden;
  position: absolute;
  z-index: 100000;
  top: 0;
}

.vjs-break-overlay .vjs-break-overlay-text {
  padding: 9px;
  text-align: center;
}

.table__date {
  width: 100%;
}

.table__date .table__wrap {
  font-size: 16px;
  display: flex;
  flex-direction: row;
}

@media (max-width: 767px) {
  .table__date .table__wrap {
    flex-wrap: wrap;
  }
}

.table__date .table__wrap .table__left {
  width: 70%;
}

@media (max-width: 767px) {
  .table__date .table__wrap .table__left {
    width: 100%;
  }
}

.table__date .table__wrap .table__left--modules {
  padding-top: 1.5rem;
}

.table__date .table__wrap .table__left--modules .seminar-list-flexlist {
  display: flex;
  gap: 1.5rem;
}

@media (max-width: 767px) {
  .table__date .table__wrap .table__left--modules .seminar-list-flexlist {
    flex-direction: column;
  }
}

.table__date .table__wrap .table__left--modules .seminar-list-flexlist .event-module--flexitem {
  min-width: 20%;
  font-size: 16px;
}

.table__date .table__wrap .table__right {
  width: 30%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@media (max-width: 767px) {
  .table__date .table__wrap .table__right {
    width: 100%;
  }
}

.table__date .table__wrap .table__right a {
  align-self: flex-end;
}

@media (max-width: 767px) {
  .table__date .table__wrap .table__right a {
    align-self: center;
  }
}

.tx-rhenag-events .accordion .event-listitem {
  border-bottom: 2px dotted #dadada;
}

.tx-rhenag-events .accordion .event-listitem > a {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tx-rhenag-events .accordion .event-listitem-item {
  padding-bottom: 1.25rem;
}

.tx-rhenag-events .accordion .event-listitem-item h5 {
  margin: 0;
}

.tx-rhenag-events .accordion .event-listitem-item p {
  margin-bottom: 0;
}

.tx-rhenag-events .accordion .event-listitem .newSeminarBubble {
  padding: 0px 2px 0px 4px;
}

.tx-rhenag-events .accordion .event-listitem .newSeminarBubble.new {
  background-color: #7cbf12;
}

.tx-rhenag-events .accordion .event-listitem .newSeminarBubble.online {
  background-color: #313131;
}

.seminar-form-flexlist {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

@media (max-width: 767px) {
  .seminar-form-flexlist {
    flex-direction: column;
  }
}

.seminar-form-flexlist .seminar-list-flexlist .seminar-list-flexitem:first-child p {
  font-weight: bold;
}

.seminar-form-flexlist .seminar-list-flexlist .seminar-list-flexitem p {
  margin-bottom: 0;
}

/*# sourceMappingURL=screen.css.map */

.powermail_field .suggest-items {
  position: absolute;
  top: 100%;
  z-index: 10;
  width: 100%;
  padding: .5rem 0;
  display: none;
  max-height: 240px;
  overflow-y: auto;
  background: white;
  box-shadow: 0px 10px 20px 1px rgba(0, 0, 0, 0.2);
}
.powermail_field .suggest-items:empty {
  display: none;
}
.powermail_field .suggest-items li {
  /* margin-block-end: .5rem; */
  padding: 0;
}
.powermail_field .suggest-items li::before {
  content: none;
}
.powermail_field .suggest-items li a {
  display: block;
  padding: .25rem .5rem;
  text-decoration: none;
}
.powermail_field .suggest-items li a b {
  font-family: "BlissPro-Light";
}
.powermail_field .suggest-items li:hover a {
  background-color: #ff8d2f;
  color: #fff;
}
.powermail_field:has(.suggest-items):focus-within .suggest-items:not(:empty) {
  display: block;
}
