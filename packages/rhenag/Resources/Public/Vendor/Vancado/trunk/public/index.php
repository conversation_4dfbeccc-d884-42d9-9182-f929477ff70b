<h1>Templates</h1>
<ul>
	<li><a href="demo/templates/startseite.php">Home</a></li>
	<li><a href="demo/templates/landingpage.php">Landingpage</a></li>
	<li><a href="demo/templates/with-sidebar.php">With Sidebar</a></li>
	<li>toDo:</li>
	<li><a href="demo/templates/404.php">404</a></li>
</ul>

<h1>Components</h1>
<ul>
	<li><a href="demo/components/grid.php">Grid</a></li>
	<li><a href="demo/components/type.php">Type</a></li>
	<li><a href="demo/components/icons.php">Icons</a></li>
	<li><a href="demo/components/home-teaser.php"><PERSON></a></li>
	<li><a href="demo/components/landingpage-teaser.php">Landingpage Teaser</a></li>
	<li><a href="demo/components/promoboxes.php">Promoboxes</a></li>
	<li><a href="demo/components/special-teaser.php">Special Teaser</a></li>
	<li><a href="demo/components/special-teaser-2.php">Special Teaser 2</a></li>
	<li><a href="demo/components/accordions.php">Accordions</a></li>
	<li><a href="demo/components/image-text-box.php">Image-Text-Box</a></li>
	<li><a href="demo/components/tables.php">Tables</a></li>
</ul>

<h1>Layouts</h1>
<ul>
	<li><a href="demo/layouts/startseite.php">Home</a></li>
	<li><a href="demo/layouts/landingpage.php">Landingpage</a></li>
	<li><a href="demo/layouts/with-sidebar.php">With sidebar</a></li>
	<li>toDo:</li>
	<li><a href="demo/layouts/404.php">404</a></li>
</ul>
<h1>Themes</h1>
<ul>
	<li><a href="demo/themes/theme-default.php">Default Theme</a></li>
	<li><a href="demo/themes/theme-secondary.php">Secondary Theme</a></li>
</ul>


<h1>General Infos</h1>
<?php
echo nl2br(
'- LAYOUTS
------------------------------

Haupt-layout Klasse (.layout)

- Layout Home (.layout-home)
- Layout Landingpage (.layout-landingpage)
toDo:
- Layout with Sidebar Left (.layout-with-sidebar-left)


- THEMES
------------------------------
Themes (class in Body Tag)
- theme-default
- theme-secondary



- COMPONENT
------------------------------
- Container für Satzspiegel Elemente
	-> < div class="fluid-container">< div class="row">< /div>< /div>
- Section Container mit Theming
	-> < section class="section light">< /section>
	-> < section class="section dark">< /section>
	-> < section class="section darker">< /section>
- TeaserHome
	+ Images and Videos
	+ .text-position-right / .text-position-left
	+ .teaser-switch-box-highlight
- TeaserLandingpage
	+ .text-position-right / .text-position-left
- TeaserSpecial
- TeaserSpecial2
- Promoboxes
	+ single
	+ double
- ImageTextElement
- Accordions
	+.accordion-default
	+.accordion-secondary
- box-special for the flyout navi
	

toDo:
- custom select dropdowns (jquery selectBox) ?
- OnOff toggler (apple-alike) ?
- FlipBoxes ?

');
?>



