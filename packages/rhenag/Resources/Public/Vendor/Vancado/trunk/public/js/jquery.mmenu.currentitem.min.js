/*	
 * jQuery mmenu currentItem addon
 *
 * Copyright (c) Anthemis
 */
!function(t){var n="mmenu",e="currentItem";t[n].addons[e]={setup:function(){var i=this,o=this.opts[e];if("boolean"==typeof o&&(o={find:o}),"object"!=typeof o&&(o={}),o=this.opts[e]=t.extend(!0,{},t[n].defaults[e],o),o.find){var f=function(t){t=t.split("?")[0].split("#")[0];var n=i.$menu.find('a[href="'+t+'"], a[href="'+t+'/"]');n.length?i.setSelected(n.parent(),!0):(t=t.split("/").slice(0,-1),t.length&&f(t.join("/")))};f(window.location.href)}},add:function(){},clickAnchor:function(t,n){}},t[n].defaults[e]={find:!1}}(jQuery);