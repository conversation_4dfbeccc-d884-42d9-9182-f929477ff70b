//uses classList, setAttribute, and querySelectorAll
//if you want this to work in IE8/9 youll need to polyfill these
(function(){
	var d = document,
	accordionToggles = d.querySelectorAll('.js-accordionTrigger'),
	setAria,
	setAccordionAria,
	switchAccordion,
	touchSupported = ('ontouchstart' in window),
	pointerSupported = ('pointerdown' in window);

	skipClickDelay = function(e){
		e.preventDefault();
		e.target.click();
	}

		setAriaAttr = function(el, ariaType, newProperty){
		el.setAttribute(ariaType, newProperty);
	};
	setAccordionAria = function(el1, el2, expanded){
		switch(expanded) {
			case "true":
				setAriaAttr(el1, 'aria-expanded', 'true');
				setAriaAttr(el2, 'aria-hidden', 'false');
				break;
			case "false":
				setAriaAttr(el1, 'aria-expanded', 'false');
				setAriaAttr(el2, 'aria-hidden', 'true');
				break;
			default:
				break;
		}
	};

	//function
	switchAccordion = function(e) {
		e.preventDefault();		
		var target = e.target.getAttribute('data-target');
		if (target) {
			var thisAnswer = document.getElementById(target);
		} else {
			var thisAnswer = e.target.parentNode.nextElementSibling;	
		}

		var thisQuestion = e.target;
		if(thisAnswer.classList.contains('is-collapsed')) {
			setAccordionAria(thisQuestion, thisAnswer, 'true');
		} else {
			setAccordionAria(thisQuestion, thisAnswer, 'false');
		}

		thisQuestion.classList.toggle('is-collapsed');
		thisQuestion.classList.toggle('is-expanded');
	
		thisAnswer.classList.toggle('is-collapsed');
		thisAnswer.classList.toggle('is-expanded');
		thisAnswer.classList.toggle('animateIn');
	};
	for (var i=0,len=accordionToggles.length; i<len; i++) {
		//if(touchSupported) {
		//	accordionToggles[i].addEventListener('touchstart', skipClickDelay, false);
		//}
		//if(pointerSupported){
		//	accordionToggles[i].addEventListener('pointerdown', skipClickDelay, false);
		//}
		accordionToggles[i].addEventListener('click', switchAccordion, false);
	}
})();
$(document).ready(function(){
	initialize_focuspoint();
	picturefill();

});

$(window).load(function(){
	initialize_focuspoint();
});

// $(window).load(function(){
// 	// initialize_focuspoint();
// 	clearTimeout(window.loadFinishedFocuspoint);
//     window.loadFinishedFocuspoint = setTimeout(function(){
// 		initialize_focuspoint();
//     }, 500);
// });


function initialize_focuspoint(){
	$('.focuspoint').focusPoint({
		throttleDuration: 100 //re-focus images at most once every 100ms.
	});
}





$(document).ready(function(){

	$('.scrollTo').click(function(e) {
		e.preventDefault();
		$.scrollTo( $(this).data('target'), 750, {easing:'swing'} );
	    return false;
	});

});

$(window).load(function(){
	//remove etracker
	$('[src^="https://www.etracker.de"]').hide();
});

$(window).resize(function() {
    clearTimeout(window.resizedFinishedFunctions);
    window.resizedFinishedFunctions = setTimeout(function(){

    }, 250);
});

$('.launch-chat').click(function(e) {
	e.preventDefault();
	if (!$('#eva_chatWindow').is(":visible")) {	
		$('.eva_bubble_Chat').first().click();
	}
});

$(document).ready(function(){

	$('.cookie-settings').on('click', function(e) {
		e.preventDefault();
		UC_UI.showSecondLayer();
	});

});
/*
 * Google Analytics OptOut
 */



var gaProperty = '***********-2';

// Disable tracking if the opt-out cookie exists.
var disableStr = 'ga-disable-' + gaProperty;
if (document.cookie.indexOf(disableStr + '=true') > -1) {
	window[disableStr] = true;
}

// Opt-out function
function gaOptout() {
	document.cookie = disableStr + '=true; expires=Thu, 31 Dec 2099 23:59:59 UTC; path=/';
	window[disableStr] = true;
	alert('Die Erfassung durch Google Analytics wird nun verhindert!');
}

$( document ).on( "click", "#optout", function(e) {
	e.preventDefault();
	gaOptout();
});
$(document).ready(function() {
  // initializeGalleries();
  initializeMagnificPopup();
});

function initializeMagnificPopup(){
	$('.image-popup-link').magnificPopup({
		type:'image',
		closeBtnInside: false,
		showCloseBtn: false,
		mainClass: 'mfp-with-zoom', // this class is for CSS animation below
		zoom: {
		   enabled: true, // By default it's false, so don't forget to enable it

		   duration: 300, // duration of the effect, in milliseconds
		   easing: 'ease-in-out', // CSS transition easing function

		   // The "opener" function should return the element from which popup will be zoomed in
		   // and to which popup will be scaled down
		   // By defailt it looks for an image tag:
		   opener: function(openerElement) {
		     // openerElement is the element on which popup was initialized, in this case its <a> tag
		     // you don't need to add "opener" option if this code matches your needs, it's defailt one.
		     return openerElement.is('img') ? openerElement : openerElement.find('img');
		   }
		 }
	});
}

/*
function initializeGalleries(){
	$('.gallery-container').each(function() { // the containers for all your galleries
	  
			  var $container = $(this);
			  var $imageLinks = $container.find('.gallery-item');
			 
			  var items = [];
			  $imageLinks.each(function() {
			    var $item = $(this);
			    var type = 'image';
			    if ($item.hasClass('gallery-youtube')) {
			      type = 'iframe';
			    }
			    var magItem = {
			      src: $item.attr('href'),
			      type: type
			    };
			    magItem.title = $item.data('title');
			    items.push(magItem);
			    });
			 
			  $imageLinks.magnificPopup({
			    mainClass: 'mfp-with-anim',
			    items: items,
			    closeBtnInside: false,
			    enableEscapeKey: true,
			    showCloseBtn: true,
			    removalDelay: 300,
			    titleSrc: 'title', // Attribute of the target element that contains caption for the slide.
			    closeMarkup: '<button title="%title%" type="button" class="mfp-close"><i class="btl bt-times"></i></button>',
			    gallery:{
			        enabled:true,
			        arrowMarkup: '<span title="%title%" class="mfp-arrow mfp-arrow-%dir%"><i class="btl bt-angle-%dir% mfp-prevent-close"></i></span>'
			    },
			    type: 'image',
			    callbacks: {
			      beforeOpen: function() {
			      	this.st.image.markup = this.st.image.markup.replace('mfp-figure', 'mfp-figure mfp-with-anim');
			      	this.st.mainClass = this.st.el.attr('data-effect');

			        var index = $imageLinks.index(this.st.el);
			        if (-1 !== index) {
			          this.goTo(index);
			        }
			      }
			    }
			  });
	 
	 });


}
*/
var breakpointSmallMin = 750; // min-width: 768px  =  $width > 750px
var breakpointMediumMax = 974; // max-width: 992px  =  $width < 974px

$(function() {
    	// equalPromoboxesHeights();
    $('.link-box').matchHeight();
    $('.link-box-header').matchHeight();
    $('.teaser-info-box').matchHeight();
    $('.equal-height').matchHeight();
    $('.promobox-top').matchHeight();
    $('.promobox-bottom').matchHeight();
    $('.top-equal').matchHeight();
    $('.iconText-element').matchHeight();
    $('.teaser-landingpage-text').matchHeight();
   // $('.layout-campaign-page .promobox-single').matchHeight();

    doublePromoboxesHeights();
    widePromoboxesHeights();
    imageTextBoxCaptionHeights();
});


$(window).load(function() {
	$('.equal-height').matchHeight();
	$('.promobox-top').matchHeight();
	$('.promobox-bottom').matchHeight();
	$('.top-equal').matchHeight();
    $('.iconText-element').matchHeight();
  //  $('.layout-campaign-page .promobox-single').matchHeight();
    doublePromoboxesHeights();
	widePromoboxesHeights();
	imageTextBoxCaptionHeights();
});

$(window).resize(function() {
    clearTimeout(window.resizedFinishedMatchHeight);
    window.resizedFinishedMatchHeight = setTimeout(function(){
    	// equalPromoboxesHeights();
    	doublePromoboxesHeights();
    	widePromoboxesHeights();
    	$('.link-box').matchHeight();
    	$('.equal-height').matchHeight();
    	$('.promobox-top').matchHeight();
    	$('.promobox-bottom').matchHeight();
    	$('.top-equal').matchHeight();
    	imageTextBoxCaptionHeights();
    }, 500);

});

function imageTextBoxCaptionHeights(){
	if( $('.imageTextBox').length > 0 ){
		if( $(window).innerWidth() > breakpointSmallMin ){
			$('.imageTextBox').each(function(){
				if( $(this).find('.img-caption').length > 0 ){
					var totalHeight = $('.col-right').height();
					var captionHeight = $('.img-caption').height();
					var paddings = 16 + 16;
					var pictureHeight = totalHeight - captionHeight - paddings;
					$(this).find('.focuspoint').height(pictureHeight);
				}
			});
		}else{
			$('.imageTextBox').each(function(){
				$(this).find('.focuspoint').height('auto');
			});
		}
	}
}


function doublePromoboxesHeights(){
	if( $(window).innerWidth() > breakpointSmallMin ){
		$('.promobox-bottom').each(function(){
			var ph = $(this).attr('style');
			var pbh = $(this).height();
			var plh = $(this).children('.promobox-left.bottom-equal').height();
			var prh = $(this).children('.promobox-right.bottom-equal').height();

			if(ph == ''){
				if(plh>prh){
					$(this).children('.bottom-equal').height(plh);
				}else{
					$(this).children('.bottom-equal').height(prh);
				}
			}else{
				$(this).children('.bottom-equal').height(pbh);
			}
		});
	}else{
		$('.bottom-equal').height('auto');
	}
	
}

function widePromoboxesHeights(){
	if( $(window).innerWidth() > breakpointSmallMin ){
		$('.promobox-wide').each(function(){
			var plh = $(this).children().children('.promobox-left').height();
			var prh = $(this).children().children('.promobox-right').height();

			if(plh>prh){
				$(this).children().children('.left-right-equal').height(plh);
			}else{
				$(this).children().children('.left-right-equal').height(prh);
			}
		});
	}else{
		$(this).children().children('.left-right-equal').height('auto');
	}
}
/*
function equalPromoboxesHeights(){
	if( $(window).innerWidth() > breakpointSmallMin && $(window).innerWidth() < breakpointMediumMax ){
		$('.promobox-top').each(function(){
			var plh = $(this).children('.promobox-left.top-equal').height();
			var prh = $(this).children('.top-equal').height();

			if(plh>prh){
				$(this).children('.promobox-right.top-equal').height(plh);
			}else{
				$(this).children('.promobox-left.top-equal').height(prh);
			}
	});
		$('.promobox-bottom').each(function(){
			var plh = $(this).children('.promobox-left.bottom-equal').height();
			var prh = $(this).children('.promobox-right.bottom-equal').height();

			if(plh>prh){
				$(this).children('.promobox-right.bottom-equal').height(plh);
			}else{
				$(this).children('.promobox-left.bottom-equal').height(prh);
			}
		});
	}else{
		// $('.top-equal').height('auto');
		// $('.bottom-equal').height('auto');

		$('.equal-height').matchHeight();
    	$('.promobox-top').matchHeight();
    	$('.promobox-bottom').matchHeight();
	}
}
*/


$(document).ready(function() {
  $("#mobileNav").mmenu({
  	navbar : {
  		title: 'Menü'
  	},
  	currentItem : {
  		find : true
  	}
  });
});







$('document').ready(function(){


	/*
	 * Dropdown Menu L2 with delay
	 */
	var timer;
	var timer2;
	var timeout2;

	$(".main-nav > li ").on("mouseover", function() {
	  clearTimeout(timer);
	  $this = $(this);
	  if(  $('.main-nav > li.open') == $this ){
	  }

	  if( $('.main-nav').hasClass('dropped') ){
	  	timeout2 = 0;
	  }else{
	  	timeout2 = 1000;
	  }
	 $('.main-nav > li').removeClass('open');
	  if(  $this.children('.navbar-dropdown-container').length > 0 && !$this.hasClass('active')){
		  $this.addClass('open');
	  	  	$('.main-nav').addClass('dropped');
		  timer2 = setTimeout(function(){
		  	$('.navbar-dropdown-container').addClass('navbar-dropdown-dropped');
		  	$('.navbar-dropdown-container ul.sub-nav').removeClass('open');
		  	$this.children('.navbar-dropdown-container').children('ul').addClass('open');
		   }, timeout2);
	  }else{
	  	  $('.main-nav').removeClass('dropped');
	  	  $('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
	  	  $('.main-nav > li').removeClass('open');
	  	  $('.navbar-dropdown-container ul.sub-nav').removeClass('open');
	  }
	}).on("mouseleave", function() {
		clearTimeout(timer2);
	  timer = setTimeout(function(){
	  	$('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
	  	$('.main-nav > li').removeClass('open');
	  	$('.navbar-dropdown-container ul.sub-nav').removeClass('open');
	  }, 600);
	});


	$(".extra-sub-nav li").on("mouseover", function() {
		clearTimeout(timer);
		clearTimeout(timer2);
	  	 $('.main-nav').removeClass('dropped');
		$('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
		$('.main-nav > li').removeClass('open');
		$('.navbar-dropdown-container ul.sub-nav').removeClass('open');
	}).on("mouseleave", function() {
	});


	/*
	 * Dropdown Flyoutmenu toggler
	 */
	$('[data-action="flyout"]').on('click', function(e){ 
		e.preventDefault();


		if( $(this).hasClass('active') ){//close
			if( $(this).closest('.navbar').hasClass('active-is-open') ){
				var addClass = 'out-long';
			}else{
				var addClass = 'out';
			}

			$('.nav-flyout.out').removeClass('out')
			$('.nav-flyout.out-long').removeClass('out-long');
			$('[data-action="flyout"].active').removeClass('active');
			var target = $(this).data('target');
			$(target).removeClass(addClass);

		}else{//open

			if( $(this).closest('.navbar').hasClass('active-is-open') ){
				var addClass = 'out-long';
			}else{
				var addClass = 'out';
			}

			// activeIsOpenHide();

			$('.nav-flyout.out').removeClass('out')
			$('.nav-flyout.out-long').removeClass('out-long');
			$('[data-action="flyout"].active').removeClass('active');
			$(this).toggleClass('active');
			var target = $(this).data('target');
			$(target).addClass(addClass);
			// activeIsOpenHide();
		}

	});


});


/* Catch the srcoll movement*/
/* show/hide mainnav*/
$(function(){
    var _top = $(window).scrollTop();
    var _direction;

    $(window).scroll(function(){
        var _cur_top = $(window).scrollTop();
        if(_top < _cur_top && _cur_top!=0 )
        {
            _direction = 'down';

            // activeIsOpenHide(); 

            $('.navbar').addClass('scrolled');
            $('.navbar-mobile').addClass('scrolled');
            $('.navbar-dropdown-container ul.sub-nav').removeClass('open');
            $('[data-action="flyout"]').removeClass('active');
            $('.nav-flyout').removeClass('out').removeClass('out-long');
            $('.nav-flyout').addClass('scrolled');


        }
        else
        {
            _direction = 'up';


            $('.navbar').removeClass('scrolled');
            $('.navbar-mobile').removeClass('scrolled');
            $('.nav-flyout').removeClass('scrolled');
            $('.nav-flyout').removeClass('out');

            setTimeout(function(){
            	if(_cur_top == 0){
            		// activeIsOpenShow();
            	}
            },1000);
        }


        if(_cur_top > 0 ){
        	$('.scroll-up').fadeIn();
        }else{
        	$('.scroll-up').fadeOut();
        }

        _top = _cur_top;
    });

    $(document).click(function(){
	    $('[data-action="flyout"]').removeClass('active');
        $('.nav-flyout').removeClass('out');
        // activeIsOpenShow();
	});

	$(".navbar").click(function(e){
	    e.stopPropagation();
	});
	$(".nav-flyout").click(function(e){
	    e.stopPropagation();
	});


});

var pos = $(window).scrollTop();
$(function(){
    fixedSubNavi();

    $(window).scroll(function(){
    	pos = $(window).scrollTop();
    	fixedSubNavi();
    });

});
$(window).resize(function(){
   fixedSubNavi();
});

function fixedSubNavi(){
	var subNavOffset = 10;
	if( $('.sidebar-nav-container').length > 0 ){

		if( pos + $('.navbar').height() > $('footer').offset().top - 250  && $(window).width() > 992 ){
			$('.sidebar-nav-container').removeClass('fixed');
			$('.sidebar-nav-container').addClass('absoluteBottom');
			$('.sidebar-nav-container').css('width', $('.col-sidebar').width() );
			$('.sidebar-nav-container').css('height', $('.sub-menu').height() + 32 );
			$('.sidebar-nav-container').css('left','16px');
			$('.sidebar-nav-container').css('top', $('.col-content').height() - $('.sub-menu').height() - 32 );

		}else if( pos + $('.navbar').height() + subNavOffset  > $('#submenuOffset').offset().top  && $(window).width() > 992 ){
			$('.sidebar-nav-container').removeClass('absoluteBottom');
			$('.sidebar-nav-container').addClass('fixed');
			$('.sidebar-nav-container').css('width', $('.col-sidebar').width() );
			$('.sidebar-nav-container').css('left',$('.logo-container').offset().left);
			$('.sidebar-nav-container').css('top',$('.navbar').height() + subNavOffset);
		}else{
			$('.sidebar-nav-container').removeClass('fixed');
			$('.sidebar-nav-container').removeClass('absoluteBottom');
			$('.sidebar-nav-container').css('width','auto');
		}
	}
}

/*
..in progress
*/

/*
$('document').ready(function(){
	if( $('.navbar').hasClass('active-is-open') ){
		activeIsOpenShow();
	}

});

function activeIsOpenShow(){
	if( $('.navbar').hasClass('active-is-open') ){
		$('.navbar.active-is-open').find('.main-nav > li.active').not('.home').addClass('open');
		$('.main-nav > li.active').not('.home').find('.navbar-dropdown-container').addClass('navbar-dropdown-dropped');
		$('.navbar-dropdown-dropped > ul.sub-nav').addClass('open');
	}
}
function activeIsOpenHide(){
	if( $('.navbar').hasClass('active-is-open') ){
		$('.navbar.active-is-open').find('.main-nav > li.active').not('.home').removeClass('open');
		$('.main-nav > li.active').not('.home').find('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
		$('.navbar-dropdown-dropped > ul.sub-nav').removeClass('open');
	}
}
*/


	/*
	$(".responsiveImg").responsiveImg({
		srcAttribute: "data-src",
		breakpoints:{
	         "__CACHE__small":360,
		    "__CACHE__medium":780,
		    "__CACHE__large":900
	    },
	    pathToPHP : baseUrl+"php",
	    createNewImages : true,
	    jpegQuality : 90

	});
	*/

$(document).ready(function(){
	$('[name="tx_indexedsearch[submit_button]"]').on('click',function(e){
		if( $(this).closest('form').find('input.search-query').val() == '' ){
			e.preventDefault();
			$(this).closest('form').find('input.search-query').select();
		}
	});

});

$(document).ready(function(){
	// $('select').selectBox();
});
$('.table').stacktable();
 $(function() {
      if($('#slides').length>0){
        var $slides = $('#slides');
        Hammer($slides[0]).on("swipeleft", function(e) {
          $slides.data('superslides').animate('next');
        });
        Hammer($slides[0]).on("swiperight", function(e) {
          $slides.data('superslides').animate('prev');
        });
        $slides.superslides({
          hashchange: false,
          play: 9000,
          animation: 'fade',
          animation_speed: 'normal'
        });
        
      }
});



$('#slides').on('animated.slides', function () {
  initialize_focuspoint();
});
if( $('.teaser-home').length > 0 ){

		var timer = null;
		var duration = 6000;//in miliseconds
		var durationProgressBar = duration - 200;//in miliseconds
		var timeout = 30000;//in miliseconds
		var timeoutProgressBar = timeout - 200;//in miliseconds
		var current = $('.teaser-switch-box.active').data('item');
		var countTeasersHome = $('.teaser-switch-box').length;
		var isOperationInProgress = false;


		$(document).ready(function(){
			$('.teaser-switch-box-hidden').off('click').on('click',function(e){
				e.stopPropagation();
				stopTeaserHome();
				var selectedItem = $(this).data('item');
				playSlide(selectedItem);
				if(!isOperationInProgress){
					setTimeout(function(e){
						playTeaserHome(e);
						isOperationInProgress = false;
					},timeout);
				}
				isOperationInProgress = true;
			});	
		});


		$(document).ready(function(e){
			playTeaserHome(e);
		});
		
		$(window).load(function(e){
			// setTeaserHomeHeight();
			positionTeaserHomeControllerMobile();
		});

		$(window).resize(function() {
		    // setTeaserHomeHeight();

		    clearTimeout(window.resizedFinishedteaserHome);
		    window.resizedFinishedteaserHome = setTimeout(function(){
				positionTeaserHomeControllerMobile();
		    	
		    }, 250);
		});

		function setTeaserHomeHeight(){
			var wh = $(window).height();
			$('.teaser-home').height(wh);
		}



		function playTeaserHome(e){
			startProgressBar();
			timer = setInterval(function(){
				if(current<(countTeasersHome-1)){
					current++;
				}else{
					current = 0;
				}
				playSlide(current);
				startProgressBar();
			},duration);
		}

		function stopTeaserHome(){
			clearInterval(timer);
			stopProgressBar();
		}

		function positionTeaserHomeControllerMobile(){
			if( $(window).width() < 801 ){
				var ch = $('.teaser-switch-box').height();
				$('.teaser-switch-box-container').height(ch);
			}
		}

		function playTeaserHomeNext(){
			if(current<(countTeasersHome-1)){
				current++;
			}else{
				current = 0;
			}
			$('.teaser-switch-box-hidden[data-item="'+current+'"]').trigger('click');
		}

		function playTeaserHomePrevious(){
			if( (current - 1) < 0){
				current = countTeasersHome - 1;
			}else{
				current--;
			}
			$('.teaser-switch-box-hidden[data-item="'+current+'"]').trigger('click');
		}

		function playSlide(slideId){
			if( $('.teaser-video video').length > 0 ){
				$('.teaser-video video').get(0).pause();
			}

			$('.teaser-switch-box').removeClass('active');
			$('.teaser-switch-box-hidden').removeClass('active');
			$('.teaser-bullets li').removeClass('active');
			$('.teaser-switch-box.equal-height').animate({
				'margin-top': 0
			}, 800);
			$('.teaser-switch-box[data-item="'+slideId+'"]').addClass('active');
			$('.teaser-switch-box-hidden[data-item="'+slideId+'"]').addClass('active');

			$('.teaser-item').removeClass('active');
			$('.teaser-item[data-item="'+slideId+'"]').addClass('active');
			$('.teaser-bullets li[data-item="'+slideId+'"]').addClass('active');
			if ( $('.teaser-item[data-item="'+slideId+'"]').hasClass('teaser-video') ){
				if( $(window).width() > 960 ){
					$('#video'+slideId).get(0).play();
				}

			}
			current = $('.teaser-switch-box.active').data('item');
			$('.teaser-switch-box.equal-height.active').animate({
				'margin-top': -110
			}, 800);
		}

		function startProgressBar(){
			$(".teaser-progressBar").fadeIn(10);
			$(".teaser-progressBar > span").each(function() {
			  $(this)
			    .stop()
			    .width('0px')
			    .animate({
			      width: $('.teaser-progressBar').width()
			    }, {duration: durationProgressBar,easing:'linear'});
			});
		}
		function stopProgressBar(){
			$(".teaser-progressBar > span").each(function() {
				$(this)
				.stop()
				.width('100%')
			    .animate({
			      width: '0px',
			    }, {duration: timeoutProgressBar,easing:'linear'});
			});	
		}




		if( $('#teaser-home').length > 0 ){

			// get a reference to an element
			var stage = document.getElementById('teaser-home');

			// create a manager for that element
			var mc = new Hammer.Manager(stage,{
				touchAction: 'pan-x',
				touchAction: 'pan-y'
			});

			// create a recognizer
			var Swipe = new Hammer.Swipe();

			// add the recognizer
			mc.add(Swipe);

			// subscribe to events
			mc.on('swipeleft', function(e) {
			    // do something cool
			    playTeaserHomeNext();
			});
			mc.on('swiperight', function(e) {
			    // do something cool
			    playTeaserHomePrevious();
			});
		}


}



if( $('.teaser-landingpage2').length > 0 ){
	$(document).ready(function(){
		if( $('#video').length > 0 ){
			$('#video').get(0).play();
		}
	});
}

if( $('.teaser-landingpage').length > 0 ){

		// var breakpointSmallMin = 750; // min-width: 768px  =  $width > 750px
		var breakpointSmallMin = 983; // min-width: 1000px  =  $width > 983px
		// var teaserLandingpageMinHeight = 940;
		var teaserLandingpageMinHeight = 520;
		var teaserLandingpageMinHeightStop = 940;
		var teaserLandingpageMaxHeight = 1080;
		var teaserLandingpageMinHeightMobile = 280;
		var teaserLandingpageTextHorizontalPosition = 350;
		var teaserLandingpageTextHorizontalPositionMobile = 140;


		$(document).ready(function(){
			setTeaserLandingpageHeight();
			if( $('#video').length > 0 ){
				$('#video').get(0).play();
			}
		});


		$(window).load(function(e){
			setTeaserLandingpageHeight();
		});

		$(window).resize(function() {
		    setTeaserLandingpageHeight();

		    clearTimeout(window.resizedFinishedTeaserLandingpage);
		    window.resizedFinishedTeaserLandingpage = setTimeout(function(){
		    	
		    }, 250);
		});

		function setTeaserLandingpageHeight(){
			if( $(window).innerWidth()>breakpointSmallMin ){
				var wh = $(window).height();
				var heroHeight = wh;

				if( wh >  teaserLandingpageMaxHeight ){
					heroHeight = teaserLandingpageMaxHeight;
				}
				if( wh <  teaserLandingpageMinHeight ){
					heroHeight = 'auto';
					$('.teaser-landingpage').addClass('minHeight');
				}else{
					$('.teaser-landingpage').removeClass('minHeight');
				}

				$('.teaser-landingpage').height(heroHeight);
				$('.teaser-landingpage .teaser-canvas').height(heroHeight);
				$('.teaser-landingpage .teaser-item').height(heroHeight);

				var heroTitlePos;
				if( wh <  teaserLandingpageMinHeight){
					heroTitlePos = '170px';
				}else if( wh <  teaserLandingpageMinHeightStop){
					heroTitlePos = '590px';
				}else if( wh >=  teaserLandingpageMinHeight && wh <=  teaserLandingpageMaxHeight){
					heroTitlePos = wh - teaserLandingpageTextHorizontalPosition;
				}else if( wh >  teaserLandingpageMaxHeight){
					heroTitlePos = teaserLandingpageMaxHeight - teaserLandingpageTextHorizontalPosition;
				}else{
					heroTitlePos = teaserLandingpageMinHeight - teaserLandingpageTextHorizontalPosition;
				}
				$('.teaser-landingpage .heroTitle').css('bottom',heroTitlePos);
				
			}else{
				$('.teaser-landingpage').removeClass('minHeight');
				$('.teaser-landingpage').height('auto');
				$('.teaser-landingpage .teaser-canvas').height('auto');
				$('.teaser-landingpage .teaser-item').height('auto');
			}
		}



}


$(document).ready(function() {
	  $('.lazy').unveil(200, function() {
		  $(this).load(function() {
		  });
	  });
});
if($('.default-form').length > 0){


	//ON Page Scripts
	//
	//more Documentation on:
	// http://jqueryvalidation.org/documentation/
	//
	var validator = $(".default-form").validate({
	  success: "valid",
	  rules: {
	    // simple rule, converted to {required:true}
	    name: "required",
	    // compound rule
	    title: { 
	    		 selectcheck: true //funktioniert nur wenn selectBox nicht angeschaltet ist.
	    },
	    field1: {
	      required: true,
	      email: true
	    },
	    field2: {
	      required: true
	    },
	  }
	});


	jQuery.validator.addMethod('selectcheck', function (value) {
        return (value != '0');
    }, "Bitte wählen");


}
var videoFile = document.getElementById("rehnag-start-video");
if (videoFile) {
	var options = {
		controls: false,
		liveui: false,
		resizeManager: false,
		languages: {
			de: de
		},
		html5: {
			//nativeControlsForTouch: true,
			hls: {
				withCredentials: false,
				preload: true,
				bandwidth: 928117100
			}
		}
	};


	var player = videojs('rehnag-start-video', options);
	player.httpSourceSelector();
	player.qualityLevels();
	var timer = 0;
	var teaserLength, teasers;

	player.on('timeupdate', function(event) {
		if(parseInt(this.currentTime(), 10) > 0) {
			$('.vjs-tech').fadeIn();
		}
		var currentTimer;
		currentTimer = Math.round(parseInt(this.currentTime(), 10));
		if (timer != currentTimer) {
			timer = currentTimer;
			var $elements = $('[data-type="teaser"][data-timestamp="'+timer+'"]:not(.teaser-switch-box-highlight)');
			var $titleElements = $('[data-type="heroTitle"][data-timestamp="'+timer+'"]');
			if ($elements.length) {
				animate($elements, $titleElements);
			}
		}
	});

	function animate($teaserElements, $titleElements) {
		if( $(window).innerWidth() > 640){
			animateTeaser($teaserElements);
		}
		animateTitle($titleElements);
	}

	function animateTeaser($teaserElements) {
		$('[data-type="teaser"]:not(.teaser-switch-box-highlight)').animate(
			{
				'margin-top': 0
			},
			800
		);
		$teaserElements.stop().animate({
			'margin-top': -110
		}, 800);
	}

	function animateTitle($titleElements) {
		$('[data-type="heroTitle"]').css('z-index', 10).fadeOut(800);
		$titleElements.stop().css('z-index', 5).fadeIn(800);
	}

}

//# sourceMappingURL=main.js.map


/* SWRH-52 */
$(document).ready(function () {
	console.log('SRWH-52 Test');
	
	const rawEntries = [
		"1&1 Energy GmbH",
		"17er Oberlandenergie GmbH",
		"24/7 Energie & Kommunikation GmbH",
		"24/7 Strom GmbH",
		"A&A Stromallianz Solutions GmbH",
		"acon Energie Management GmbH",
		"Adolf Roth GmbH & Co. KG",
		"AggerEnergie GmbH",
		"Ahrtal-Werke GmbH",
		"ALBSTADTWERKE GmbH",
		"Albwerk GmbH & Co. KG",
		"Alpiq Energie Deutschland GmbH",
		"alz strom vertriebs GmbH",
		"AMB R. Adolf - C. Kämpf Mineralöle, Schmierstoffe und Transport GmbH",
		"Ammer Loisach Energie GmbH",
		"Amtswerke Eggebek",
		"Aplus Energy GmbH",
		"Apoldaer Wasser GmbH",
		"APW-Energie GmbH",
		"Arktis Energy GmbH",
		"Aschaffenburger Versorgungs-GmbH (AVG)",
		"Audax Energie GmbH",
		"August Riemeier Mineralöle und Transporte GmbH & Co. KG",
		"August Schmäling",
		"AVA Rohstoffe GmbH",
		"Avalon Trust GmbH",
		"AVIA Steingass Mineralöle GmbH",
		"AVU Aktiengesellschaft für Versorgungs-Unternehmen",
		"aWATTar Deutschland GmbH",
		"AWEG Abwasserentsorgungsgesellschaft Emster mbH & Co. KG",
		"B.R.A.S.S.T. Bau GmbH",
		"Bad Honnef AG",
		"BarMalGas GmbH",
		"Bayer Industry Services GmbH",
		"Bayernwerk Regio Energie GmbH",
		"BayWa Ökoenergie GmbH",
		"BayWA r.e. Bürgerstrom GmbH",
		"BayWa r.e. Energy Trading GmbH",
		"bbsw Energie GmbH",
		"BCS Wärme GmbH",
		"Becker Energie GbR",
		"Beegy GmbH",
		"Beer Energien GmbH & Co. KG",
		"BEG Energietechnik GmbH",
		"BeGreen.Energy GmbH",
		"Behrenswerth Energieservice GmbH",
		"BELKAW GmbH",
		"Benergie-Service GmbH",
		"Berg Mineralöl  GmbH",
		"Berliner Energieagentur GmbH",
		"Berliner Stadtwerke GmbH",
		"Bernburger Mineralölvertrieb Lühmann GmbH & Co. KG",
		"BES - Badische Energie-Servicegesellschaft mbH",
		"Betriebsgesellschaft Wasser und Abwasser mbH Sömmerda",
		"BEV Bayrische Energieversorgungsgesellschaft mbH",
		"BEW Bergische Energie- und Wasser-GmbH",
		"BH Energy GmbH",
		"BHM Berliner Energiehandel GmbH",
		"BIGGE ENERGIE GmbH & Co. KG",
		"Bischoff, Vliex & Schöngen, Pfennings GmbH & Co. KG",
		"BK Badische-Kraftwerk GmbH & Co. KG",
		"BKW Bad Wildunger Kraftwagenverkehrs- und Wasserversorgungsgesellschaft mbH",
		"BMG Mineralölhandel GmbH",
		"Boie GmbH & Co. KG",
		"Bonn-Netz GmbH",
		"bpure GmbH",
		"BRASST Energiedienstleistungsgesellschaft mbH",
		"BRAWAG GmbH Wasser- und Abwassergesellschaft Brandenburg an der Havel",
		"Bremer Energiehaus- Genossenschaft eG",
		"Brillant Energie GmbH",
		"BRS Beteiligungsgesellschaft Bonn/Rhein-Sieg mbH",
		"BSE Strom und Erdgas GmbH",
		"BTB - Energieversorgungsgesellschaft mbH",
		"BTB -Blockheizkraftwerks,Träger- und Betreibergesellschaft mbH Berlin",
		"Burgenland Energie GmbH",
		"Bürger speichern Energie eG",
		"BürgerEnergie Nord eG",
		"BürgerEnergie Thüringen Sachsen eG",
		"Bürger-Energie-Genossenschaft-Freisinger Land eG",
		"BürgerGrünStrom GmbH & Co. KG",
		"Bürgerwerke eG",
		"C4 Energie Vertriebs GmbH & Co. KG",
		"Campus Energie GmbH & Co. KG",
		"Campus Ludwigslust GmbH & Co.KG",
		"Carboon Zero Germany",
		"CB Energie GmbH",
		"CF Flex Power GmbH",
		"Change! Energy GmbH",
		"Cismarer Naturstrom GmbH & Co. KG",
		"CoCloud GmbH",
		"COHAUS München GmbH",
		"Consulta AG",
		"Contigo Energie AG",
		"Coppen GmbH",
		"cowelio GmbH",
		"CrämerSchmäling GmbH",
		"cts Service GmbH",
		"Dalkia Energie Sevice GmbH",
		"DBI Gas- und Umwelttechnik GmbH",
		"dean Handelsgesellschaft mbH",
		"DEC Deutsches EnergieContor GmbH & Co. KG",
		"DEH Deutsche Energiehandels GmbH",
		"DERAWA Zweckverband Delitzsch-Rackwitzer Wasserversorgung",
		"Dessauer Versorgungs- und Verkehrsgesellschaft mbH",
		"Dessauer Wasser und Abwasser GmbH",
		"Deutsche Energie Versorgung GmbH & Co. KG",
		"DEVG Deutsche Energie Versorgung GmbH & Co. KG",
		"Die Energieagenten Versorgungs GmbH",
		"die energievorsorger GmbH",
		"DIG Deutsche Industrie Gas GmbH",
		"Donau-Wasserkraft AG",
		"Dortmunder Energie- und Wasserversorgung GmbH",
		"DREWAG",
		"Dritte WRB GmbH & Co. KG",
		"Duisburger Versorgungs- und Verkehrsgesellschaft mbH",
		"DVGW Deutscher Verein des Gas- und Wasserfaches eV",
		"E WIE EINFACH Strom & Gas GmbH",
		"E.ON Energie Deutschland GmbH",
		"E.ON Energy Solutions GmbH",
		"e.optimum AG",
		"e.optimum home GmbH",
		"E.T. GmbH & Co. KG",
		"E.VITA GmbH",
		"e.wa riss GmbH & Co. KG",
		"e:veen Energie eG",
		"EAG Energie Abrechnungs- und Service GmbH",
		"EAM Energie GmbH",
		"easyOptimize GmbH",
		"EBERwerk GmbH & Co. KG",
		"Eckhardt GmbH",
		"ECN Energie GmbH",
		"EcoPowerPlus GmbH",
		"EDi Energie-Direkt Hohenlohe GmbH",
		"eeMobility GmbH",
		"Efficiencity GmbH",
		"eforna e.K.",
		"eg factory GmbH",
		"EG Tachterting-Feichten eG",
		"EGF EnergieGesellschaft Frankenberg mbH",
		"Eggers Energy GmbH",
		"Egge-Wasserwerke GmbH",
		"EGR Energiegesellschaft Riesa GmbH",
		"EGT Energievertrieb GmbH",
		"Ehinger Energie Stromvertrieb GmbH & Co. Kg",
		"Eifelville GmbH",
		"Eigenbetrieb Müritz-Elde-Wasser (MEWA)",
		"Eigenbetrieb Wasser & Abwasser Trossingen",
		"Eigenbetrieb Wasserwerk im Amt Itzstedt",
		"EINHORN-ENERGIE GmbH & Co. KG",
		"EINHUNDERT Energie GmbH",
		"eins energie in sachsen GmbH & Co. KG",
		"Eisenacher Versorgungs-Betriebe GmbH",
		"ELE Verteilnetz GmbH",
		"ElectroFleet Energy GmbH",
		"Elektrizitäts- und Wasserversorgungsgenossenschaft Vagen eG",
		"Elektrizitätsgenossenschaft Dirmstein eG",
		"Elektrizitätsgenossenschaft für Wittmund eG",
		"Elektrizitätsversorgung Berlin ElVeBe GmbH",
		"Elektrizitätsversorgung Werther GmbH",
		"Elektrizitätswerk der Ortsgemeinde Gerolsheim",
		"Elektrizitätswerk Goldbach-Hösbach GmbH & Co. KG",
		"Elektrizitätswerk Hindelang eG",
		"Elektrizitätswerk Mittelbaden AG & Co. KG",
		"Elektrizitätswerk Müller",
		"Elektrizitätswerk Tegernsee Vertriebs- und Service-KG",
		"Elektrizitätswerke Düsseldorf AG",
		"Elektrizitätswerke Reutte GmbH & Co.KG",
		"Elektrizitätswerke Schönau Energie GmbH",
		"Elektrizitätswerke Schönau Vertriebs GmbH",
		"EMB Energie Mark Brandenburg",
		"Emil Energie GmbH",
		"EMIXO GmbH",
		"Emscher Lippe Energie GmbH",
		"EnBW Ostwürttemberg DonauRies AG",
		"enedi",
		"enercity AG",
		"Enerco Systems GmbH & Co. KG",
		"Energie 360 GmbH & Co.KG",
		"Energie Calw GmbH",
		"Energie für Immoblien GmbH",
		"Energie Marburg Biednkopf GmbH &. Co.KG",
		"Energie Sachsenheim GmbH & Co. KG",
		"Energie Schwaben GmbH",
		"Energie Südbayern GmbH",
		"Energie- u. Wasserversorgung Bonn/Rhein-Sieg GmbH",
		"Energie- und Medienversorgung Schwarza GmbH",
		"Energie und Versorgung Butzbach GmbH",
		"Energie und Wasser Potsdam GmbH",
		"Energie- und Wasserversorgung Bitz GmbH",
		"Energie- und Wasserversorgung Bonn/Rhein-Sieg GmbH",
		"Energie- und Wasserversorgung Bruchsal GmbH",
		"Energie- und Wasserversorgung Rheine GmbH",
		"Energie Vertrieb Deutschland EVD GmbH",
		"Energie Vorpommern GmbH",
		"Energie Waldeck-Frankenberg GmbH",
		"ENergie Wasser NIederrhein GmbH",
		"Energiefeld Geel GmbH & Co. KG",
		"Energiegenossenschaft Darmstadt eG",
		"Energiegenossenschaft Lehrte - Sehnde",
		"Energiegenossenschaft Nordwest eG",
		"Energiegenossenschaft Rhein-Ruhr eG",
		"energieGUT GmbH",
		"Energiehandel Süd GmbH & Co. KG",
		"Energiehaus Deutschland B2B GmbH",
		"EnergieHof Schmalfeld UG (haftungsbeschränkt) & Co. KG",
		"ENERGIEN in REGIONEN Passau GmbH & Co. KG",
		"ENERGIEN in REGIONEN Rottal-Inn GmbH & Co. KG",
		"Energienetze Mittelrhein GmbH & Co. KG",
		"EnergieRevolte GmbH",
		"Energie-Rhein-Sieg GmbH",
		"EnergieSaarLorLux AG",
		"Energie-Service Dienstleistungsgesellschaft mbH",
		"EnergieSüdwest AG",
		"Energieversorgung Apolda GmbH",
		"Energieversorgung Bad Bentheim GmbH & Co. KG",
		"Energieversorgung Beckum GmbH & Co. KG",
		"Energieversorgung Burghausen GmbH",
		"Energieversorgung Emsbüren GmbH",
		"Energieversorgung Filstal GmbH & Co. KG",
		"Energieversorgung Greiz GmbH",
		"Energieversorgung Guben GmbH",
		"Energieversorgung Klettgau-Rheintal GmbH & Co. KG",
		"Energieversorgung Leverkusen GmbH & Co. KG",
		"Energieversorgung Limburg GmbH",
		"Energieversorgung Main-Spessart GmbH",
		"Energieversorgung Marienberg GmbH",
		"Energieversorgung Mittelrhein AG",
		"Energieversorgung Oberhausen AG",
		"Energieversorgung Rodau GmbH",
		"Energieversorgung Rudolstadt GmbH",
		"Energieversorgung Rüsselsheim GmbH",
		"Energieversorgung Schmalkalden GmbH",
		"Energieversorgung Sehnde GmbH",
		"Energieversorgung Selb-Marktredwitz GmbH",
		"Energieversorgung Titisee-Neustadt GmbH",
		"Energieversorgung Wenzenbach GmbH",
		"Energieversorgungs- und Servicegesellschaft Friedenau mbH Stuttgart",
		"Energievertrieb Babelsberg GmbH",
		"Energiewerke Waldbröl GmbH",
		"energis GmbH",
		"Energora GmbH",
		"Energy Market Solutions GmbH",
		"Energy2day GmbH",
		"energy4u GmbH & Co. KG",
		"energycoop eG",
		"EnergyLink Direkt GmbH & Co. KG",
		"Enervatis Energieversorgungsgesellschaft mbH",
		"enervolution GmbH",
		"eness GmbH",
		"e-netz Südhessen AG",
		"enewa GmbH",
		"engel Energie GmbH",
		"ENGIE Deutschland GmbH",
		"ENNI Energie und Umwelt Niederrhein GmbH",
		"enno-energie GmbH",
		"Enovos Power GmbH",
		"Enpal Energy",
		"EnPS Energie Pfalz-Saar GmbH",
		"ENSTROGA AG",
		"Ensys AG",
		"Ensys Solution GmbH",
		"ENTEGA Plus GmbH",
		"EnVersum GmbH",
		"envia Mitteldeutsche Energie AG",
		"EnviTec Energy GmbH & Co. KG",
		"EnviTec Stromkontor GmbH & Co. KG",
		"envitra Energie GmbH",
		"enwag energie- und wassergesellschaft mbH",
		"EnWds Energie Weil der Stadt GmbH & Co. KG",
		"enwor - energie und wasser vor ort GmbH",
		"eprimo GmbH",
		"e-punkt GmbH",
		"eq strom GmbH & Co. KG",
		"Erdgas Südwest GmbH",
		"e-regio GmbH & Co. KG",
		"Erenja AG & Co. KG",
		"Erhard Bürk-Kauffmann GmbH",
		"Erwin Steigleiter GmbH",
		"Erzgebirge Trinkwasser GmbH ETW",
		"e-shelter facility services GmbH",
		"ESWE Versorgungs AG",
		"E-two-energy GmbH",
		"Eurogate Technical Services GmbH",
		"EVD Energieversorgung Deutschland GmbH",
		"EVD Energieversorgung Dormagen GmbH",
		"EVM Energieversorgung Michelfeld GmbH",
		"EVO Vertrieb GmbH",
		"EVU Weilerbach",
		"EW Eichsfeldgas GmbH",
		"E-Werk Meckenheim/Pfalz",
		"e-werk Sachsenwald GmbH",
		"EWF Energy GmbH",
		"EWR AG",
		"EWR GmbH",
		"EWV Energie- und Wasser-Versorgung GmbH",
		"EWZ Energiewerke Zeulenroda GmbH",
		"ExtraEnergie GmbH",
		"ExtraGrün GmbH",
		"Fair Trade Power Deutschland GmbH",
		"FairEnergie GmbH",
		"fairster.Energie gGmbH",
		"Färber Gas GmbH",
		"Fernwärmegesellschaft Noll mbH",
		"Fernwasserversorgung Elbaue-Ostharz GmbH",
		"Fernwasserversorgung Franken",
		"Fernwasserversorgung Oberfranken",
		"Fernwasserversorgung Südthüringen",
		"Feuchter Gemeindewerke GmbH",
		"Fey Energie GmbH & Co. KG",
		"Filderstadtwerke (Eigenbetrieb der Stadt Filderstadt)",
		"First Utility GmbH",
		"FirstCon GmbH",
		"Förderverein Neue Produktion eV",
		"FORTAS Energie Gas GmbH",
		"Frankfurt Energy Holding GmbH",
		"Freitaler Stadtwerke GmbH",
		"Friedel Energie & Service eK",
		"Friedrich Mineralölhandel GmbH",
		"friesenenergie GmbH",
		"Fromholz, Brennstoff- u. Mineralölvertriebs GmbH",
		"Frühmesser Mineralölhandelsgesellschaft mbh & Co. KG",
		"FSG Energy GmbH & Co. KG",
		"FT Energiepartner GmbH",
		"fünfwerke GmbH & Co. KG",
		"Fuxx - Die Sparenergie GmbH",
		"GAG Gasversorgung Ahrensburg GmbH",
		"GAG Servicegesellschaft mbH",
		"Gallier Energie GmbH",
		"Gas und Strom Mittelrhein GmbH",
		"Gas- und Wasserversorgung Höxter GmbH & Co. KG",
		"GASAG AG",
		"Gasversorgung Westerwald GmbH",
		"Gedea Schwarzwaldkraft Service GmbH",
		"Geiger GmbH",
		"GELSENWASSER AG",
		"Gemeinde-Elektrizitäts- und Wasserwerk",
		"GemeindeStrom Wadgassen GmbH",
		"Gemeindewasserwerk Beckingen",
		"Gemeindewasserwerk Nonnweiler",
		"Gemeindewasserwerk Perl",
		"Gemeindewasserwerk Riegelsberg",
		"Gemeindewasserwerk Schmelz",
		"Gemeindewasserwerk Waldfeucht",
		"Gemeindewasserwerk Weiskirchen",
		"Gemeindewerk Seegebiet Mansfelder Land GmbH",
		"Gemeindewerk Sörup GmbH",
		"Gemeindewerke Ammerbuch GmbH",
		"Gemeindewerke Bad Sassendorf GmbH & Co. KG",
		"Gemeindewerke Bad Zwischenahn für Wasser und Abwasser",
		"Gemeindewerke Baiersbronn",
		"Gemeindewerke Bobenheim-Roxheim GmbH",
		"Gemeindewerke Budenheim AöR",
		"Gemeindewerke Everswinkel GmbH",
		"Gemeindewerke Grefrath GmbH",
		"Gemeindewerke Großkotzenburg",
		"Gemeindewerke Halstenbek ",
		"Gemeindewerke Herxheim",
		"Gemeindewerke Hohenwestedt GmbH",
		"Gemeindewerke Hünxe GmbH",
		"Gemeindewerke Malente GmbH",
		"Gemeindewerke Murnau-Eigenbetrieb der Markt Murnau",
		"Gemeindewerke Oberhaching GmbH",
		"Gemeindewerke Peiner Land GmbH & Co. KG",
		"Gemeindewerke Ruppichteroth GmbH",
		"Gemeindewerke Schutterwald",
		"Gemeindewerke Schwarzenbruck GmbH",
		"Gemeindewerke St. Michel-Energie GmbH",
		"Gemeindewerke Stockelsdorf GmbH",
		"Gemeindewerke Taufkirchen (Vils) GmbH & Co. KG",
		"Gemeindewerke Umkirch GmbH",
		"Gemeindewerke Wadgassen GmbH",
		"Gemeindewerke Waldbronn Wasserversorgung",
		"Gemeindewerke Wedemark GmbH",
		"Gemeindewerke Wendelstein Kommunalunternehmen",
		"Gemeindliche Werke Hengersberg - Eigenbetrieb des Marktes Hengersberg",
		"Genek Gesellschaft für Energieeinkauf mbH & Co. KG",
		"Genie Energie Versorgungsgesellschaft mbH",
		"GENO Energie GmbH",
		"Georg Oest Mineralölwerk GmbH & Co. KG",
		"Gesellschaft für Energieversorgung Ostalb mbH",
		"Gesellschaft für regionale Teilhabe und Klimaschutz mbH",
		"Gesellschaft zur Energieversorgung der kirchlichen und sozialen Einrichtungen GmbH",
		"Gespring Wasser Schmalkalden",
		"GETEC ENERGIE GmbH",
		"GETEC Energy Management GmbH",
		"GETEC net GmbH",
		"Getigy GmbH",
		"GeWAP Gesellschaft für Wasserver- und Abwasserentsorgung Peitz mbH",
		"Gewerbepark Nürnberg-Feucht Versorgungs- und Abwasserentsorgungs GmbH",
		"GEWI GmbH",
		"GGEW Bergstraße AG",
		"Glotec Germany GmbH",
		"goldgas GmbH",
		"Göttinger Versorgungs- und Verkehrsbetriebe GmbH",
		"GP JOULE Connect GmbH & Co. KG",
		"GP JOULE Plus GmbH",
		"GRAVO energy",
		"Green Planet Energy eG",
		"Greenline - alternative energien GmbH",
		"GreenStone Energy GmbH",
		"grün.power GmbH",
		"grünES GmbH",
		"GrünHausEnergie GmbH",
		"GrünStrom Kirchberg GmbH",
		"Grünstromwerk GmbH",
		"GrünWatt GmbH",
		"Grünwelt Wärmestrom GmbH",
		"Gruppen-Gas- und Elektrizitätswerk Bergstraße GGEW AG ",
		"Gruppenwasserwerke Bornheim",
		"GSD Naturenergie GmbH",
		"GVG Rhein-Erft GmbH",
		"H. +  R. Bellersheim GmbH",
		"Haas GmbH",
		"Halberstadtwerke GmbH",
		"Hamburger Energiewerke GmbH",
		"Hamburger Wasserwerke GmbH",
		"Hanwha Q CELLS GmbH",
		"Harzer Zinkoxide GmbH",
		"HaseEnergie GmbH",
		"Hausstrom GmbH",
		"HBL Nord-Energie GmbH",
		"Heimatenergiewerke GmbH",
		"Heinrich Fip GmbH & Co. KG",
		"Heinrich Klöcker GmbH & Co. KG",
		"Heizöl Sistig GmbH",
		"Helten, Alfred",
		"Hermann Bantleon GmbH",
		"Hermann Hansen, Einzelunternehmer",
		"Hertener Stadtwerke GmbH",
		"Herzberger Wasser- und Abwasserzweckverband (HWAZ)",
		"Hessenwasser GmbH & Co. KG",
		"HGH Service u. Abrechnungen GmbH",
		"HHB Agrarenergie GmbH & Co. KG",
		"HH-EL Energie Hanse GmbH",
		"HHLP-Energy GmbH",
		"HochsauerlandEnergie GmbH",
		"Hochsauerlandwasser GmbH",
		"homee GmbH",
		"HORIZEN GmbH",
		"Hospital LogiServe GmbH",
		"Hoyer GmbH",
		"HSG Hanseatische Strom- und Gasversorgungsgesellschaft mbH",
		"Hycube Technologies GmbH",
		"IBAENERGIE GmbH",
		"IBC GreenElements GmbH",
		"illwerke vkw Deutschland GmbH",
		"In(n) Energie GmbH",
		"Innostrom GmbH",
		"Innowatio GmbH",
		"inteligy GmbH",
		"Interconnector GmbH",
		"Janssen Mineralöle GmbH & Co. KG",
		"Johannesstift Diakonie Services GmbH",
		"JuraStrom GmbH",
		"Jürgen Dorst GmbH",
		"Just Energy Deutschland GmbH",
		"Kaltenhof gGmbH",
		"KBG Homberg eG",
		"Keep – Kommunale Eisenberger Energiepartner GmbH",
		"Keslar GmbH Energiehandel",
		"KEV Energie GmbH",
		"KEW Karwendel Energie und Wasser GmbH",
		"Kleiner 2 Grad GmbH",
		"KlickEnergie GmbH & Co. KG",
		"Knauber Erdgas GmbH",
		"KO Kosten Optimierte Energie GmbH",
		"Komm Energie",
		"Kommunalunternehmen Stadtwerke Pfaffenhofen a. d. Ilm",
		"Köthen Energie GmbH",
		"Kraftwerk Köhlgartenwiese GmbH",
		"Kreiswasserwerk Heinsberg GmbH",
		"Kreiswasserwerk Neuwied Eigenbetrieb des Landkreises Neuwied",
		"Kreiswerke Bautzen Wasserversorgung GmbH",
		"Kreiswerke Cham Wasserversorgung",
		"Kreiswerke Cochem-Zell Wasserversorgung",
		"Kreiswerke Grevenbroich GmbH",
		"Kreiswerke Main-Kinzig GmbH",
		"KWG Energie GmbH",
		"kws Kommunal-Wasserversorgung Saar GmbH",
		"KWW GmbH - Kommunales Wasserwerk",
		"L. Ilzhöfers Nachfolger Inh. Walch KG",
		"Landstrom GmbH & Co. KG",
		"LB Heizstrom Nord GmbH",
		"LB Heizstrom Süd GmbH",
		"LCG Energy GmbH",
		"LE Energy Solutions GmbH",
		"Leine Energie GmbH",
		"Leipziger Kommunale Energieeffizienz GmbH",
		"lekker Energie GmbH",
		"Lensahner Wasserbetriebe A.ö.R.",
		"Lenz Energie AG",
		"Leu Energie GmbH & Co. KG",
		"Lewak greenprojekts GmbH",
		"LFE GmbH",
		"LGE Energy Berlin GmbH",
		"LichtBlick SE",
		"Lightcore Energy Deutschland GmbH",
		"LogoEnergie GmbH",
		"LPR Energy GmbH",
		"LSW Energie GmbH & Co. KG",
		"Lumenaza GmbH",
		"Lütgens Management",
		"Luxtrio UG (haftungsbeschränkt)",
		"LWE Landwerke Eifel Vertriebs GmbH",
		"M4 Energy eG",
		"Maier & Korduletsch Energie GmbH",
		"MAINGAU Energie GmbH",
		"mainnetz GmbH",
		"Maintal-Werke GmbH",
		"Mainzer Netze GmbH",
		"Mainzer Stadtwerke  Vertrieb und Service GmbH",
		"Mainzer Wärme GmbH",
		"Mann Naturenergie GmbH & Co. KG",
		"Maschinenringe Deutschland GmbH",
		"Max Energy GmbH",
		"Med 360° Service GmbH",
		"medl GmbH",
		"MEGA Monheimer Elektrizitäts- und Gasversorgung GmbH",
		"mein-strom-direkt GmbH",
		"meistro Energie GmbH",
		"METANK Gas+Strom GmbH",
		"Meyer Erneuerbare Energien GmbH",
		"MIDEWA Wasserversorgungsgesellschaft in Mitteldeutschland mbH",
		"Mindener Stadtwerke GmbH",
		"Mindener Wasser GmbH",
		"Mineralöl Harrer GmbH",
		"Mittelmärkische Wasser- und Abwasser GmbH",
		"mivolta GmbH",
		"Mobilität & Energie me-Süd GmbH",
		"Mobilität & Energie Mitte-West GmbH & Co. KG",
		"MÖHRLE energy GmbH",
		"MONTANA Energieversorgung GmbH & Co. KG",
		"Mundt GmbH Hannover",
		"MWEnergy GmbH",
		"MyEnergy UG (haftungsbeschränkt)",
		"NaCon GmbH",
		"NaturEnergie+ Deutschland GmbH",
		"NatürlicheEnergie EMH GmbH",
		"Naturstrom Rheinland-Pfalz GmbH",
		"naturstrom vor Ort GmbH",
		"NaturStrom XL GmbH",
		"NaturStromHandel GmbH",
		"Naturwerke GmbH",
		"Neander Energie GmbH",
		"Neckermann Strom GmbH",
		"Netze Duisburg GmbH",
		"Netzgesellschaft Düsseldorf mbH",
		"Netzgesellschaft Gütersloh mbH",
		"NEW AG",
		"NEW Schwalm-Nette GmbH",
		"Next Kraftwerke GmbH",
		"Nexus Energie GmbH",
		"Nexus Energy Deutschland GmbH",
		"NGBT Energie GmbH",
		"Nienburg Energie GmbH",
		"Niers-Energie GmbH",
		"Nimex Energie",
		"node.energy GmbH",
		"Norbert Neuburger   ",
		"Nord Stadtwerke GmbH",
		"Nordgröön Energie GmbH & Co. KG",
		"Nordlicht Power GbR",
		"NordostWerke GmbH",
		"nowenergy GmbH",
		"NRN Energie GmbH",
		"nvb Nordhorner Versorgungsbetriebe GmbH",
		"NWG Power GmbH",
		"Oberbillig Energie GmbH",
		"Obereichsfeldischer Wasserleitungsverband",
		"Oberhessengas Netz GmbH",
		"Oberhessische Versorgungsbetriebe AG",
		"Octopus Energy Germany",
		"ÖfA Ökostrom für Alle GmbH",
		"Offenburger Wasserversorgung GmbH",
		"Ohra Energie GmbH",
		"Optimization engineers GmbH",
		"Osthavelländische Trinkwasserversorgung und Abwasserbehandlung GmbH",
		"OVE GmbH & Co. KG",
		"OVO Energy Germany GmbH",
		"paketsparer GmbH",
		"PANELY GmbH",
		"PassatEnergie GmbH",
		"Peter & Krebs Mineralölhandels GmbH",
		"Pfalzwerke AG",
		"PLAN-B NET ZERO ENERGY GmbH",
		"Poggelann Naturenergie GmbH & Co. KG",
		"Poitzmann & Geißel Vertriebs GmbH",
		"Polarstern GmbH",
		"Präg Strom & Gas GmbH & Co. KG",
		"Primaenergy Gmbh",
		"Primastrom GmbH",
		"Primavolt GmbH",
		"Prio Services GmbH",
		"PRO-E Preußisch Oldendorfer Energieversorgung GmbH",
		"proefa GmbH",
		"ProEngeno GmbH & Co. KG",
		"Progas GmbH & Co. KG",
		"PROKON Regenerative Energien GmbH",
		"Propan Rheingas GmbH & Co. KG",
		"prosumergy GmbH",
		"PST Europe Sales GmbH",
		"PV Energy Solutions",
		"Q1 Energie AG",
		"Qsol net GmbH",
		"QUADRA Energy GmbH",
		"Queichtal Energie Offenbach GmbH & Co. KG",
		"QUEST Energy GmbH",
		"RABOT CHARGE GmbH",
		"Raiffeisen Energie GmbH & Co. KG",
		"Raiffeisen Waren GmbH",
		"Rakelbusch & harting Energie GmbH & Co. KG",
		"Rakelbusch Heizölhandel GmbH",
		"Rationelle Energie Süd GmbH",
		"Regiogrön GmbH & Co. KG",
		"RegioGrünStrom GmbH & Co. KG",
		"Regionah Energie GmbH",
		"Regionaler Zweckverband Wasserversorgung Bereich Lugau-Glauchau",
		"Regionalwerk Bodensee GmbH & Co. KG",
		"Regionalwerk Chiemgau-Rupertiwinkel gKU",
		"Regionalwerk Würmtal GmbH & Co. KG",
		"regionalwerke GmbH & Co. KG",
		"Regionetz GmbH",
		"RegioPlus Strom- und Gashandel GmbH",
		"Reibert energie GmbH",
		"Reitter Wasserkraftanlagen GmbH & Co. KG",
		"Remstalwerk GmbH & Co. KG",
		"renergie GmbH",
		"Renergiewerke Buttenwiesen GmbH",
		"REWAG Regensburger Energie- und Wasserversorgung AG & Co. KG",
		"Rhegio Natur GmbH",
		"Rhein Energie AG",
		"RheinEnergie Express GmbH",
		"Rheingauwasser GmbH",
		"Rheinhessen-Energie GmbH",
		"Rheinhessische Energie- und Wasserversorgungs- GmbH",
		"Rheinische Elektrizitäts- und  Gasversorgungsgesellschaft mbH",
		"Rhein-Sieg Netz",
		"RheinWerke GmbH",
		"rhenag Rheinische Energie AG",
		"RhönEnergieFulda GmbH ",
		"Richtwerk Energie GmbH iGr.",
		"Rödl GmbH",
		"Rolf Kopsicker GmbH",
		"Rommel Energie GmbH",
		"RUB Energy GmbH & Co. KG",
		"RWE Renewables GmbH",
		"RWW Rheinisch-Westfälische Wasserwerksgesellschaft mbH",
		"SachsenEnergie AG",
		"Saergas GmbH & Co. KG",
		"Sauberenergie",
		"Schillhorn Strom und Gas GmbH",
		"SCHLESWAG Abwasser GmbH",
		"Schröder Gas GmbH & Co. KG",
		"Schuster & Sohn KG",
		"schwarzwald energy GmbH",
		"SD Energie GmbH",
		"SE SAUBER ENERGIE GmbH & Co. KG",
		"SE Sustainable Exergy GmbH",
		"sem GmbH",
		"SENEC GmbH",
		"servenergy GmbH",
		"SES SolarEigenStrom GmbH",
		"SGB Energie GmbH",
		"Shell Energy Retail GmbH",
		"Siegener Versorgungsbetriebe GmbH",
		"SKIBATRON Mess-und Abrechnungssysteme GmbH",
		"SOLARIMO GmbH",
		"sonnen eServices Deutschland GmbH",
		"sonnen eServices GmbH",
		"SONNENEXT energy GmbH",
		"SOWAG Süd-Oberlausitzer Wasserversorgungs- und Abwasserentsorgungsgesellschaft mbH",
		"Sparkasse Siegen",
		"SpotmyEnergy GmbH",
		"SpreeGas Gesellschaft für Gasversorgung und Energiedienstleistung mbH",
		"Spremberger Wasser- und Abwasserzweckverband",
		"Stadt- und Überlandwerke GmbH Lübben",
		"Stadt- und Überlandwerke GmbH Luckau-Lübbenau",
		"Stadtbetrieb Bornheim",
		"StadtEnergie GmbH",
		"Städtische Werke AG",
		"Städtische Werke Magdeburg GmbH & Co. KG",
		"Städtische Werke Spremberg (Lausitz) GmbH",
		"Städtisches Wasserwerk Eschweiler GmbH",
		"Städtisches Wasserwerk Kaufbeuren",
		"Städtisches Wasserwerk Kreuztal",
		"Städtisches Wasserwerk Leutkirch im Allgäu",
		"Städtisches Wasserwerk Simbach",
		"Stadtwerk am See GmbH & Co. KG",
		"Stadtwerk Haßfurt GmbH",
		"Stadtwerk Rheda-Wiedenbrück GmbH & Co. KG",
		"Stadtwerk Tauberfranken GmbH",
		"Stadtwerk Verl GmbH",
		"Stadtwerke - Strom Plauen GmbH & Co. KG",
		"Stadtwerke Ahaus GmbH",
		"Stadtwerke Altdorf GmbH",
		"Stadtwerke Amberg Versorgungs GmbH",
		"Stadtwerke Andernach GmbH",
		"Stadtwerke Arnsberg Vertriebs und Energiedienstleistungs GmbH",
		"Stadtwerke Aue GmbH",
		"Stadtwerke Augsburg Energie GmbH",
		"Stadtwerke Backnang GmbH",
		"Stadtwerke Bad Brückenau GmbH",
		"Stadtwerke Bad Dürkheim GmbH",
		"Stadtwerke Bad Friedrichshall",
		"Stadtwerke Bad Homburg v. d. Höhe",
		"Stadtwerke Bad Kreuznach",
		"Stadtwerke Bad Langensalza GmbH",
		"Stadtwerke Bad Nauheim GmbH",
		"Stadtwerke Bad Tölz GmbH",
		"Stadtwerke Bad Vilbel GmbH",
		"Stadtwerke Balve GmbH",
		"Stadtwerke Barmstedt Xtra GmbH",
		"Stadtwerke Barsinghausen GmbH",
		"Stadtwerke Barth",
		"Stadtwerke Bayreuth Energie und Wasser GmbH",
		"Stadtwerke Bergheim GmbH",
		"Stadtwerke Bielefeld GmbH",
		"Stadtwerke Blaustein",
		"Stadtwerke Böblingen GmbH",
		"Stadtwerke Bochum Holding GmbH",
		"Stadtwerke Bogen GmbH",
		"Stadtwerke Böhmetal GmbH",
		"Stadtwerke Bramsche GmbH",
		"Stadtwerke Brilon Energie GmbH",
		"Stadtwerke Brühl GmbH",
		"Stadtwerke Brunsbüttel GmbH",
		"Stadtwerke Burg GmbH",
		"Stadtwerke Burgdorf",
		"Stadtwerke Castrop-Rauxel GmbH",
		"Stadtwerke Celle GmbH",
		"Stadtwerke Coesfeld GmbH",
		"Stadtwerke Cottbus GmbH",
		"Stadtwerke Dachau",
		"Stadtwerke Dachau (Eigenbetrieb der Stadt Dachau)",
		"Stadtwerke Deggendorf GmbH",
		"Stadtwerke Delmenhorst GmbH",
		"Stadtwerke Diez GmbH",
		"Stadtwerke Dinslaken GmbH",
		"Stadtwerke Ditzingen GmbH & Co. KG",
		"Stadtwerke Dorfen GmbH",
		"Stadtwerke Dreieich GmbH",
		"Stadtwerke Duisburg AG",
		"Stadtwerke Dülmen GmbH",
		"Stadtwerke Düren GmbH",
		"Stadtwerke Düsseldorf AG",
		"Stadtwerke Eberbach GmbH",
		"Stadtwerke Einbeck GmbH",
		"Stadtwerke Eisenberg Energie GmbH",
		"Stadtwerke Eisenhüttenstadt GmbH",
		"Stadtwerke Elm-Lappwald GmbH",
		"Stadtwerke Emsdetten",
		"Stadtwerke Energie Verbund SEV GmbH",
		"Stadtwerke Eppingen GmbH & Co. KG",
		"Stadtwerke Erft GmbH",
		"Stadtwerke Erkrath GmbH",
		"Stadtwerke Eschwege GmbH",
		"Stadtwerke Espelkamp Anstalt öffentlichen Rechts",
		"Stadtwerke EVB Huntetal GmbH",
		"Stadtwerke Finsterwalde GmbH",
		"Stadtwerke Forchheim",
		"Stadtwerke Forst GmbH",
		"Stadtwerke Frankenthal GmbH",
		"Stadtwerke Frankfurt (Oder) GmbH",
		"Stadtwerke Freiberg a.N. Vertriebs-GmbH",
		"Stadtwerke Freudenstadt GmbH & Co. KG",
		"Stadtwerke Garbsen GmbH",
		"Stadtwerke Geldern GmbH",
		"Stadtwerke Gelnhausen GmbH",
		"Stadtwerke Georgsmarienhütte GmbH",
		"Stadtwerke Gescher GmbH",
		"Stadtwerke Gießen AG",
		"Stadtwerke Gifhorn GmbH",
		"Stadtwerke Gotha GmbH",
		"Stadtwerke Göttingen AG",
		"Stadtwerke Greifswald GmbH",
		"Stadtwerke Greven GmbH",
		"Stadtwerke Grevesmühlen GmbH",
		"Stadtwerke Grimma GmbH",
		"Stadtwerke Gronau GmbH",
		"Stadtwerke Groß-Gerau Versorgungs GmbH",
		"Stadtwerke Grünstadt GmbH",
		"Stadtwerke Güstrow GmbH",
		"Stadtwerke Gütersloh GmbH",
		"Stadtwerke Haan GmbH",
		"Stadtwerke Haldensleben",
		"Stadtwerke Haltern am See GmbH",
		"Stadtwerke Hammelburg GmbH",
		"Stadtwerke Harsewinkel GmbH",
		"Stadtwerke Hattingen GmbH",
		"Stadtwerke Hechingen",
		"Stadtwerke Heidelberg Energie GmbH",
		"Stadtwerke Heiligenhafen Eigenbetrieb der Stadt Heiligenhafen",
		"Stadtwerke Heiligenhaus GmbH",
		"Stadtwerke Hennef ",
		"Stadtwerke Hennigsdorf",
		"Stadtwerke Herborn GmbH",
		"Stadtwerke Herford GmbH",
		"Stadtwerke Herrenberg",
		"Stadtwerke Hilden GmbH",
		"Stadtwerke Hollfeld, Eigenbetrieb",
		"Stadtwerke Holzminden GmbH",
		"Stadtwerke Homburg GmbH",
		"Stadtwerke Hünfeld GmbH",
		"Stadtwerke Hürth AöR",
		"Stadtwerke Iserlohn GmbH",
		"Stadtwerke Kaarst GmbH",
		"Stadtwerke Kaiserslautern GmbH",
		"Stadtwerke Kalkar GmbH & Co. KG",
		"Stadtwerke Kamp-Lintfort GmbH",
		"Stadtwerke Karlsruhe Netzservice GmbH",
		"Stadtwerke Kempen GmbH",
		"Stadtwerke Kerpen GmbH & Co. KG",
		"Stadtwerke Kiel AG",
		"Stadtwerke Konstanz GmbH",
		"Stadtwerke Korschenbroich GmbH",
		"Stadtwerke Kulmbach",
		"Stadtwerke Külsheim GmbH",
		"Stadtwerke Lage GmbH",
		"Stadtwerke Landsberg KU",
		"Stadtwerke Langen GmbH",
		"Stadtwerke Langenfeld GmbH",
		"Stadtwerke Lauterbach GmbH",
		"Stadtwerke Lebach GmbH",
		"Stadtwerke Lehrte GmbH",
		"Stadtwerke Leichlingen GmbH",
		"Stadtwerke Lemgo GmbH",
		"Stadtwerke Lingen GmbH",
		"Stadtwerke Lippstadt GmbH",
		"Stadtwerke Löbau GmbH",
		"Stadtwerke Löhne Energie & mehr GmbH",
		"Stadtwerke Loitz GmbH",
		"Stadtwerke Ludwigsburg GmbH",
		"Stadtwerke Lünen GmbH",
		"Stadtwerke Malchow",
		"Stadtwerke Marburg",
		"Stadtwerke Marburg GmbH",
		"Stadtwerke Mechernich",
		"Stadtwerke Meckenheim",
		"Stadtwerke Meinerzhagen GmbH",
		"Stadtwerke Metzingen",
		"Stadtwerke Metzingen Eigenbetrieb der Stadt Metzingen",
		"Stadtwerke Mosbach GmbH",
		"Stadtwerke Mühlhausen GmbH",
		"Stadtwerke Mühlheim am Main GmbH",
		"Stadtwerke Müllheim Staufen GmbH",
		"Stadtwerke Munster-Bispingen GmbH",
		"Stadtwerke Neckarsulm",
		"Stadtwerke Nettetal GmbH",
		"Stadtwerke Neuenhaus GmbH",
		"Stadtwerke Neuffen AG",
		"Stadtwerke Neu-Isenburg GmbH",
		"Stadtwerke Neuruppin GmbH",
		"Stadtwerke Neuss Energie und Wasser GmbH",
		"Stadtwerke Neustadt a.Rbge.GmbH",
		"Stadtwerke Neustadt an der Orla GmbH",
		"Stadtwerke Oberkirch GmbH",
		"Stadtwerke Oberursel (Taunus) GmbH",
		"Stadtwerke Ochtrup",
		"Stadtwerke Oerlinghausen GmbH",
		"Stadtwerke Öhringen GmbH",
		"Stadtwerke Olching GmbH",
		"Stadtwerke Oldenburg in Holstein GmbH",
		"Stadtwerke Oranienburg GmbH",
		"Stadtwerke Ostmünsterland GmbH & Co. KG",
		"Stadtwerke Overath Energie GmbH",
		"Stadtwerke Paderborn GmbH",
		"Stadtwerke Pasewalk GmbH",
		"Stadtwerke Passau GmbH",
		"Stadtwerke Pirna Energie GmbH",
		"Stadtwerke Plattling Eigenbetrieb der Stadt Plattling",
		"Stadtwerke Plön Versorgungs GmbH",
		"Stadtwerke Porta Westfalica GmbH",
		"Stadtwerke Pulheim GmbH",
		"Stadtwerke Radevormwald GmbH",
		"Stadtwerke Radolfzell GmbH",
		"Stadtwerke Ramstein-Miesenbach GmbH",
		"Stadtwerke Ratingen GmbH",
		"Stadtwerke Recklinghausen",
		"Stadtwerke Rees GmbH",
		"Stadtwerke Ribnitz Damgarten GmbH",
		"Stadtwerke Riesa GmbH",
		"Stadtwerke Rietberg-Langenberg GmbH",
		"Stadtwerke Rödental",
		"Stadtwerke Rodgau Energie GmbH",
		"Stadtwerke Rösrath Energie GmbH",
		"Stadtwerke Rostock AG",
		"Stadtwerke Saalfeld GmbH",
		"Stadtwerke Saarlouis GmbH",
		"Stadtwerke Sankt Augustin GmbH",
		"Stadtwerke Schloß Holte-Stukenbrock GmbH",
		"Stadtwerke Schmalkalden GmbH",
		"Stadtwerke Schneverdingen GmbH",
		"Stadtwerke Schönebeck GmbH",
		"Stadtwerke Schüttorf Emsbüren",
		"Stadtwerke Schwerin GmbH (SWS)",
		"Stadtwerke Senftenberg GmbH",
		"Stadtwerke Service Meerbusch Willich GmbH & Co. KG",
		"Stadtwerke Soest GmbH",
		"Stadtwerke Soltau GmbH & Co. KG",
		"Stadtwerke Springe GmbH",
		"Stadtwerke Stadtoldendorf GmbH",
		"Stadtwerke Steinfurt GmbH",
		"Stadtwerke Straubing Strom und Gas GmbH",
		"Stadtwerke Stuttgart Vertriebsgesellschaft mbH",
		"Stadtwerke Suhl/Zella-Mehlis GmbH",
		"Stadtwerke Tecklenburger Land Energie GmbH",
		"Stadtwerke Teterow GmbH",
		"Stadtwerke Thale GmbH",
		"Stadtwerke Torgelow GmbH",
		"Stadtwerke Troisdorf GmbH",
		"Stadtwerke Tübingen GmbH",
		"Stadtwerke Unna GmbH",
		"Stadtwerke Velbert GmbH",
		"Stadtwerke Velten GmbH",
		"Stadtwerke Viernheim GmbH",
		"Stadtwerke Voerde GmbH",
		"Stadtwerke Waldkirch",
		"Stadtwerke Walldorf GmbH",
		"Stadtwerke Waltrop GmbH & Co. KG",
		"Stadtwerke Waren GmbH",
		"Stadtwerke Warendorf GmbH",
		"Stadtwerke Wedel GmbH",
		"Stadtwerke Weilburg GmbH",
		"Stadtwerke Weilheim i.OB Energie GmbH - SWE",
		"Stadtwerke Weinstadt Eigenbetrieb der Stadt Weinstadt",
		"Stadtwerke Wernigerode GmbH",
		"Stadtwerke Wesel GmbH",
		"Stadtwerke Weserbergland GmbH",
		"Stadtwerke Wesseling GmbH",
		"Stadtwerke Westmünsterland Energiekooperation GmbH & Co. KG",
		"Stadtwerke Willich GmbH",
		"Stadtwerke Winnenden GmbH",
		"Stadtwerke Wismar GmbH",
		"Stadtwerke Wissen GmbH",
		"Stadtwerke Witten GmbH",
		"Stadtwerke Wolfenbüttel GmbH",
		"Stadtwerke Wülfrath GmbH",
		"Stadtwerke Wunstorf GmbH",
		"Stadtwerkenergie Ostwestfalen-Lippe GmbH",
		"Statkraft Markets GmbH",
		"Steil Energie GmbH",
		"Strogon GmbH",
		"STROMIX GmbH",
		"Stromversorgung Angermünde GmbH",
		"Stromvertrieb Backnang GmbH & Co. KG",
		"Süd-Oberlausitzer Wasserversorgungs- und Abwasserentsorgungsgesellschaft mbH",
		"SüdWasser GmbH",
		"Sunvigo Energy I GmbH",
		"Sunvigo GmbH",
		"Süwag Energie AG",
		"Süwag Vertrieb AG & Co. KG",
		"SVS-Versorgungsbetriebe GmbH",
		"SW SÜDSTROM Wasserkraftwerke GmbH und Co. KG",
		"SWB Stadtwerke Balingen",
		"swb Vertrieb Bremen GmbH",
		"swb Vertrieb Bremerhaven GmbH & Co. KG",
		"SWE Energie GmbH",
		"switch Energievertriebsgesellschaft m.b.H., Zweigniederlassung Essen",
		"SWK Energie GmbH",
		"SWM Sächsische Wassermüller GmbH",
		"SWM Versorgungs GmbH",
		"SWN Stadtwerke Neustadt GmbH",
		"SWP Stadtwerke Pforzheim GmbH & Co. KG",
		"SWR Energie Service Bau GmbH",
		"SWS Energie GmbH",
		"SWTE Kommunal GmbH & Co.KG",
		"SWV Regional GmbH",
		"SWW Wunsiedel GmbH",
		"SYNVIA energy",
		"Systemstrom GmbH",
		"T.W.O. Technische Werke Osning GmbH",
		"team energie GmbH & Co. KG",
		"Tebbe Mineralölhandel GmbH & Co. KG",
		"Technische Werke Schussental GmbH & Co. KG",
		"Teutoburger Energie Netzwerk eG",
		"The Mobility House GmbH",
		"Thiele GmbH & Co. KG",
		"Thüga Energie GmbH",
		"Thüringer Fernwasserversorgung",
		"ThüWa ThüringenWasser GmbH",
		"Tibber Deutschland GmbH",
		"Tilly Hedrich GmbH & Co. KG",
		"TIWAG-Tiroler Wasserkraft AG",
		"Trink- und Abwasserverband (TAV) Bourtanger Moor",
		"Trink- und Abwasserverband Bad Bentheim, Schüttorf, Salzbergen und Emsbüren",
		"Trink- und Abwasserverband Börde",
		"Trink- und Abwasser Verband Eisenach-Erbstromtal",
		"Trink- und Abwasserzweckverband Uecker-Randow, Süd-Ost",
		"Trinkwasserverband Stader Land",
		"Trinkwasserverband Verden",
		"Trinkwasserzweckverband Neiße-Schöps",
		"Trinkwasserzweckverband Oberes Leinetal",
		"Trinkwasserzweckverband Pfeifholz",
		"TWH - Technische Werke Herbrechtingen GmbH",
		"TWL Energie Deutschland GmbH",
		"UNIEN GmbH",
		"Unser E GmbH",
		"Urbania Gesellschaft für Energievetrieb und -handel mbH",
		"Vattenfall Europe New Energy Services GmbH",
		"Vattenfall Europe Wärme AG",
		"Vattenfall Real Estate Energy Sales GmbH",
		"Vattenfall Wasserkraft GmbH",
		"VentusVentures Energy GmbH",
		"Verband für Abwasserbeseitigung und Hochwasserschutz Baunatal-Schauenburg",
		"Verbandsgemeinde Bernkastel-Kues Wasserwerk",
		"Verbandswasserwerk Bad Langensalza Abwasserzweckverband Mittlere Unstrut",
		"Verbandswasserwerk Langenfeld-Monheim GmbH & Co. KG",
		"Vereinigte Gas- und Wasserversorgung GmbH",
		"Vereinigte Stadtwerke GmbH",
		"Vereinigte Wertach-Elektrizitätswerke GmbH",
		"Versorgungs- Betriebe Elbe GmbH",
		"Vester GmbH",
		"Vestische Energie GmbH",
		"Villa Dr. Bauer",
		"vivi-power GmbH",
		"Volkswagen Group Charging GmbH",
		"Voltaro GmbH",
		"Voltego GmbH",
		"Vonovia Energie Service GmbH",
		"voxenergie GmbH",
		"Vraend GmbH",
		"w3energie GmbH",
		"Walter Fritz Deutsche Gesellschaft für Energieversorgung mbH",
		"Wärmeversorgung Würselen GmbH",
		"Warnow-Wasser- und Abwasserverband (WWAV)",
		"Wasser Abwasser Betriebsgesellschaft Coswig mbH",
		"Wasser Nord GmbH & Co. KG",
		"Wasser und Abwasser GmbH -Boddenland",
		"Wasser und Abwasser-Verband Bad Salzungen",
		"Wasser- und Abwasserverband Osterholz KöR",
		"Wasser- und Abwasserverband Wesermünde-Nord",
		"Wasser- und Abwasserwerk Eigenbetrieb der Verbandsgemeinde Mendig",
		"Wasser- und Abwasserzweckverband Calau (WAC)",
		"Wasser- und Abwasser-Zweckverband Niedergrafschaft",
		"Wasser- und Abwasserzweckverband Parchim-Lübz",
		"Wasser- und Abwasserzweckverband Seelow",
		"Wasserbeschaffungsverband Dänischer Wohld",
		"Wasserbeschaffungsverband des Amtes Hartum",
		"Wasserbeschaffungsverband Eiderstedt",
		"Wasserbeschaffungsverband Fehmarn",
		"Wasserbeschaffungsverband Föhr",
		"Wasserbeschaffungsverband Harburg",
		"Wasserbeschaffungsverband Haseldorfer Marsch",
		"Wasserbeschaffungsverband Mittelangeln",
		"Wasserbeschaffungsverband Mittelschwansen",
		"Wasserbeschaffungsverband Nordschwansen",
		"Wasserbeschaffungsverband Riedgruppe-Ost",
		"Wasserbeschaffungsverband Stormarnsche Schweiz",
		"Wasserbeschaffungsverband Sude-Schaale",
		"Wasserbeschaffungsverband Übersee",
		"Wasserbeschaffungsverband Usingen",
		"Wasserbeschaffungsverband Wakendorf I Amtsverwaltung Trave-Land",
		"Wasserbeschaffungsverband Wang",
		"Wasserbeschaffungsverband Wenden",
		"Wasserbeschaffungsverband Wesseling-Hersel",
		"Wassergenossenschaft Eiringhausen GmbH",
		"Wasserkraftwerk Birgitt Kraus",
		"Wasserkraftwerk Brunnwiesental GbR",
		"Wasserkraftwerk Dahlhausen GbR",
		"Wasserkraftwerk Großweil GmbH",
		"Wasserkraftwerk Herrenmühle Weißenfels",
		"Wasserkraftwerke Eichhofen",
		"Wasserkraftwerke Kommanditgesellschaft",
		"Wasserleitungszweckverband Gau Süd",
		"Wassernutzungsanlage GbR Gutach",
		"WASSERRIED GmbH & Co. KG",
		"Wasserver- und Abwasserentsorgungsbetrieb Stolzenau",
		"Wasserver- und Abwasserentsorgungs-Zweckverband Region Ludwigsfelde (WARL)",
		"Wasserver- und -entsorgungsbetrieb der Gemeinde Rellingen",
		"Wasserverband Aabach-Talsperre",
		"Wasserverband Bersenbrück",
		"Wasserverband Bremervörde",
		"Wasserverband Dannenberg-Hitzacker kAöR",
		"Wasserverband Eifel-Rur",
		"Wasserverband Gruppenwasserwerk Fritzlar-Homberg (Efze)",
		"Wasserverband Hümmling",
		"Wasserverband Ithbörde/Weserbergland (WVIW)",
		"Wasserverband Kinzig K.d.ö.R.",
		"Wasserverband Krempermarsch",
		"Wasserverband Lausitz",
		"Wasserverband Lausitz Betriebsführungs GmbH",
		"Wasserverband Lingener Land",
		"Wasserverband Nord",
		"Wasserverband Nordangeln",
		"Wasserverband Norderdithmarschen",
		"Wasserverband Oleftal",
		"Wasserverband Strausberg-Erkner",
		"Wasserverband Süderdithmarschen",
		"Wasserverband Treene",
		"Wasserverband Unteres Störgebiet",
		"Wasserverband Weddel-Lehre",
		"Wasserverband Wesermünde",
		"Wasserverband Wingst",
		"Wasserverband Wittlage",
		"Wasser-Verband-Wendland",
		"Wasserverbund Niederrhein GmbH",
		"Wasserversorgung Bad Orb GmbH",
		"Wasserversorgung Bayerischer Wald KöR",
		"Wasserversorgung Beckum GmbH",
		"Wasserversorgung Bischofswerda GmbH",
		"Wasserversorgung Brockwitz-Rödern GmbH",
		"Wasserversorgung der Gemeinde Dossenheim",
		"Wasserversorgung Eifelkreis Bitburg-Prüm",
		"Wasserversorgung Erding GmbH & Co. KG",
		"Wasserversorgung Fürth/Odenwald",
		"Wasserversorgung Holzwickede",
		"Wasserversorgung Oberstdorf GmbH",
		"Wasserversorgung Ostsaar GmbH",
		"Wasserversorgung Rheinhessen-Pfalz GmbH",
		"Wasserversorgung Riesa/Großenhain GmbH",
		"Wasserversorgung Ruhstorfer Gruppe",
		"Wasserversorgung Schönwald",
		"Wasserversorgung Steinbach (Taunus) GmbH",
		"Wasserversorgung Sulinger Land",
		"Wasserversorgung Syker Vorgeest GmbH",
		"Wasserversorgung und Stadtentwässerung Radebeul GmbH",
		"Wasserversorgung Weißeritzgruppe GmbH",
		"Wasserversorgungs- und Abwasserbehandlungswerke Zeulenroda",
		"Wasserversorgungs- und Abwasserentsorgungs- gesellschaft Schwerin mbH & Co. KG",
		"Wasserversorgungsgemeinschaft Escheburg w.V.",
		"Wasserversorgungs-GmbH Sankt Augustin",
		"Wasserversorgungsverband Kreis St. Wendel",
		"Wasserversorgungsverband Land Hadeln",
		"Wasserversorgungsverband Moormerland-Uplengen-Hesel-Jümme",
		"Wasserversorgungsverband Overledingen",
		"Wasserversorgungsverband Rheiderland",
		"Wasserversorgungsverband Rotenburg Land",
		"Wasserversorgungsverband Tecklenburger Land",
		"Wasserversorgungs-Zweckverband Maifeld-Eifel",
		"Wasserversorgungszweckverband Perlenbach",
		"Wasserversorgungszweckverband Weimar",
		"Wasserwerk Concordia Kreuzau GmbH",
		"Wasserwerk der Gemeinde Blankenheim - Gemeindewerke für Wasser und Abwasser",
		"Wasserwerk der Gemeinde Grünwald",
		"Wasserwerk der Stadt Barntrup",
		"Wasserwerk der Stadt Kappeln",
		"Wasserwerk der Stadt Ladenburg",
		"Wasserwerk der Stadt Melle",
		"Wasserwerk der Stadt Rahden",
		"Wasserwerk Gerauer Land KöR",
		"Wasserwerk Kellinghusen",
		"Wasserwerk Koblenz/Weißenthurm GmbH",
		"Wasserwerk Leopoldshöhe",
		"Wasserwerk Mettlach",
		"Wasserwerk Oberschleißheim",
		"Wasserwerk Oerbke",
		"Wasserwerk Vechta",
		"Wasserwerk Willich GmbH",
		"Wasserwerk Zweckverband Seebachgebiet",
		"Wasserwerke Dinslaken GmbH",
		"Wasserwerke Westfalen GmbH",
		"Wasserwerke Wittenhorst",
		"Wasserwerke Zwickau GmbH",
		"Wasserwerkszweckverband Bous/Schwalbach-Püttlingen-Saarwellingen",
		"Wasserzweckverband Freiberg",
		"Wasserzweckverband im Landkreis Birkenfeld",
		"WasserZweckVerband Malchin Stavenhagen",
		"Wasserzweckverband Nalbach",
		"Wasserzweckverband Rottenburger Gruppe KöR",
		"Wasserzweckverband Strelitz",
		"Wasserzweckverband Warndt",
		"Wasserzweckverband Weihergruppe",
		"Watzmann Natur Energie GmbH",
		"wbm Wirtschaftsbetriebe Meerbusch GmbH",
		"WBV Wasserbeschaffungsverband Thomasberg",
		"WBV Wasserbeschaffungsverband Wiehengebirge",
		"wechselstrom wechselgas GmbH",
		"WEG Weidener Energie GmbH",
		"Weihermann Mineralölhandel GmbH",
		"Weiler Wärme eG",
		"Weinmayr Energie GmbH",
		"Weiß Energie GmbH",
		"WEP Wärme-, Energie- und Prozesstechnik GmbH",
		"Werraenergie GmbH",
		"Werra-Strom GmbH",
		"Werra-Wasserkraftwerk Gloria Gerlach",
		"WES energy GmbH",
		"WeShareEnergy GmbH",
		"Westbridge Energy GmbH",
		"WESTFALICA GmbH",
		"Westhof Energie GmbH & Co. KG",
		"Westsächsische Abwasserentsorgungs- und Dienstleistungsgesellschaft mbH",
		"WEV Westdeutsche Energieversorgung GmbH",
		"Wirtschaftsbetriebe der Stadt Norden GmbH",
		"Wohnpark Stufenlos Energieerzeugungs- u. Vertriebs GmbH & Co. KG",
		"Wohnungsbaugenossenschaft Lünen eG",
		"WSE Energiedienstleistungen GmbH",
		"WSW Energie & Wasser AG",
		"WSW Netz GmbH",
		"Würmtal-Zweckverband für Wasserversorgung und Abwasserbeseitigung",
		"WVR Wasserversorgung Rheinhessen-Pfalz GmbH",
		"WWS Wasserwerk Saarwellingen GmbH",
		"WWW Wasserwerk Wadern GmbH",
		"XL Energie GmbH",
		"Yeti Energie AG",
		"Yippie GmbH",
		"Zander Brennstoffe & Service KG",
		"Zehrer & Petersen GmbH & Co. KG",
		"ZVO Energie GmbH",
		"Zweckverband Abwasser Schlematal (ZAST)",
		"Zweckverband Abwasserentsorgung Rheinhessen",
		"Zweckverband Bodensee-Wasserversorgung",
		"Zweckverband Eislinger Wasserversorgungsgruppe",
		"Zweckverband Fernwasser Südsachsen",
		"Zweckverband Fernwasserversorgung Sdier",
		"Zweckverband Fernwasserversorgung Spessartgruppe",
		"Zweckverband Filderwasserversorgung",
		"Zweckverband für Wasserversorgung Friedelsheimer Gruppe",
		"Zweckverband für Wasserversorgung Germersheimer Südgruppe KöR",
		"Zweckverband für Wasserversorgung Pfälzische Mittelrheingruppe",
		"Zweckverband für Wasserversorgung und Abwasserbeseitigung",
		"Zweckverband für Wasserversorgung und Abwasserentsorgung Eberswalde",
		"Zweckverband Gehrenberg-Wasserversorgung",
		"Zweckverband Gruppenwasserwerk Dieburg Wasserwerk Hergershausen",
		"Zweckverband Gruppenwasserwerk Florenberg",
		"Zweckverband Landeswasserversorgung",
		"Zweckverband Mittelhessische Wasserwerke",
		"Zweckverband Trinkwasserversorgung Mühlhausen und Unstruttal",
		"Zweckverband Trinkwasserversorgung und Abwasserbeseitigung Eisenberg",
		"Zweckverband Wasser und Abwasser Lobensteiner Oberland",
		"Zweckverband Wasser und Abwasser Suhl Mittlerer Rennsteig",
		"Zweckverband Wasser und Abwasser Vogtland",
		"Zweckverband Wasserverband Nordhannover KöR",
		"Zweckverband Wasserversorgung der Stadt- und Landgemeinden des Kreises Neunkirchen",
		"Zweckverband Wasserversorgung Drei Harden",
		"Zweckverband Wasserversorgung Eifel-Mosel",
		"Zweckverband Wasserversorgung Fränkischer Wirtschaftsraum",
		"Zweckverband Wasserversorgung Germersheimer Nordgruppe",
		"Zweckverband Wasserversorgung Hallertau",
		"Zweckverband Wasserversorgung Handwerksgruppe",
		"Zweckverband Wasserversorgung Isar-Vils",
		"Zweckverband Wasserversorgung Kaltenkirchen Henstedt-Ulzburg",
		"Zweckverband Wasserversorgung Kleine Kinzig",
		"Zweckverband Wasserversorgung Nordostwürttemberg",
		"Zweckverband Wasserversorgung Oberes Kollbachtal (WOK)",
		"Zweckverband Wasserversorgung Stadt und Kreis Offenbach",
		"Zweckverband Wasserversorgung Trollmühle",
		"Zweckverband Wasserversorgung und Abwasserbehandlung Rügen",
		"Zweckverband Wasserversorgung und Abwasserbeseitigung für Städte und Gemeinden des Landkreises Saalfeld-Rudolstadt",
		"Zweckverband Wasserversorgung und Abwasserentsorgung Fürstenwalde und Umland",
		"Zweckverband Wasserversorgung und Abwasserentsorgung Ostharz",
		"Zweckverband Wasserversorgungsgruppe Mühlbach",
		"Zweckverband Wasserversorgungsgruppe Oberes Elsenztal",
		"Zweckverband zur Wasserversorgung der Ampergruppe",
		"Zweckverband zur Wasserversorgung der Jura-Schwarzach-Thalach-Gruppe",
		"Zweckverband zur Wasserversorgung der Reckenberg-Gruppe (RBG)",
		"Zweckverband zur Wasserversorgung der Rhön-Maintal-Gruppe",
		"Zweckverband zur Wasserversorgung der Steinwaldgruppe",
		"Zweckverband zur Wasserversorgung der Surgruppe",
		"Zweckverband zur Wasserversorgung der Zeil-Ebelsbach-Gruppe",
		"Zweckverband zur Wasserversorgung Dillenberggruppe",
		"Zweckverband zur Wasserversorgung Gennach-Hühnerbach-Gruppe",
		"Zweckverband zur Wasserversorgung Moosrain",
		"Zweite Energieversorgungsvorratsgesellschaft mbH"
	].sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()) );

	const el = document.querySelector('.powermail_fieldwrap_type_input.powermail_fieldwrap_unternehmen');
	const mount = el.querySelector('.powermail_field');
	const list = document.createElement('ul');
	mount.appendChild(list);
	list.className = 'suggest-items';

	const input = list.closest('.powermail_field').querySelector('input[type=text]');

	function populateList() {
		const listItems = rawEntries.reduce((current, next) => {
			const needle = input.value.toLowerCase();
			const hay = next.toLowerCase();
			const reg = new RegExp(input.value.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1"), "ig");

			return hay.includes(needle)
				&& next !== input.value
				|| input.value === ''
					? needle && hay.startsWith(needle)
						? `<li><a role="button">${next.replace(reg, (term) => `<b>${term}</b>`)}</a></li>${current}`
						: `${current}<li><a role="button">${next.replace(reg, (term) => `<b>${term}</b>`)}</a></li>`
					: current	
		}, []);
		// listItems.sort((a, b) => a.startsWith(input.value) ? -1 : 1);
		list.innerHTML = listItems;
	};

	populateList();
	// document.body.append(list);

	if (input) {
		list.addEventListener('mousedown', event => {
			if (event.target.closest('a')) {
				input.value = event.target.textContent;
				input.dispatchEvent(new Event('input', { bubbles: true }));
			}
			// console.log('mousedown', input, input.value );
		});

		input.addEventListener('input', event => {
			// console.log('sdgsdgsdf', event.target.value);
			populateList();
		});
	}
});
