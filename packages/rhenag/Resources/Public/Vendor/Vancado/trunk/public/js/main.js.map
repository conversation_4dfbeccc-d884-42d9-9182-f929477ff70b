{"version": 3, "sources": ["accordeon.js", "focuspoint.js", "functions.js", "google-analytics.js", "magnific-popup.js", "matchHeight.js", "mmenu.js", "navbar.js", "responsive-images.js", "search.js", "selectBox.js", "stacktables.js", "superslides.js", "teaser-home.js", "teaser-landingpage.js", "unveil.js", "validation.js", "video-start.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACpCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AClFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACTA;AACA;AACA;ACFA;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC9LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACvFA;AACA;AACA;AACA;AACA;AACA;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "main.js", "sourcesContent": ["//uses classList, setAttribute, and querySelectorAll\r\n//if you want this to work in IE8/9 youll need to polyfill these\r\n(function(){\r\n\tvar d = document,\r\n\taccordionToggles = d.querySelectorAll('.js-accordionTrigger'),\r\n\tsetAria,\r\n\tsetAccordionAria,\r\n\tswitchAccordion,\r\n\ttouchSupported = ('ontouchstart' in window),\r\n\tpointerSupported = ('pointerdown' in window);\r\n\r\n\tskipClickDelay = function(e){\r\n\t\te.preventDefault();\r\n\t\te.target.click();\r\n\t}\r\n\r\n\t\tsetAriaAttr = function(el, ariaType, newProperty){\r\n\t\tel.setAttribute(ariaType, newProperty);\r\n\t};\r\n\tsetAccordionAria = function(el1, el2, expanded){\r\n\t\tswitch(expanded) {\r\n\t\t\tcase \"true\":\r\n\t\t\t\tsetAriaAttr(el1, 'aria-expanded', 'true');\r\n\t\t\t\tsetAriaAttr(el2, 'aria-hidden', 'false');\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"false\":\r\n\t\t\t\tsetAriaAttr(el1, 'aria-expanded', 'false');\r\n\t\t\t\tsetAriaAttr(el2, 'aria-hidden', 'true');\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t};\r\n\r\n\t//function\r\n\tswitchAccordion = function(e) {\r\n\t\te.preventDefault();\t\t\r\n\t\tvar target = e.target.getAttribute('data-target');\r\n\t\tif (target) {\r\n\t\t\tvar thisAnswer = document.getElementById(target);\r\n\t\t} else {\r\n\t\t\tvar thisAnswer = e.target.parentNode.nextElementSibling;\t\r\n\t\t}\r\n\r\n\t\tvar thisQuestion = e.target;\r\n\t\tif(thisAnswer.classList.contains('is-collapsed')) {\r\n\t\t\tsetAccordionAria(thisQuestion, thisAnswer, 'true');\r\n\t\t} else {\r\n\t\t\tsetAccordionAria(thisQuestion, thisAnswer, 'false');\r\n\t\t}\r\n\r\n\t\tthisQuestion.classList.toggle('is-collapsed');\r\n\t\tthisQuestion.classList.toggle('is-expanded');\r\n\t\r\n\t\tthisAnswer.classList.toggle('is-collapsed');\r\n\t\tthisAnswer.classList.toggle('is-expanded');\r\n\t\tthisAnswer.classList.toggle('animateIn');\r\n\t};\r\n\tfor (var i=0,len=accordionToggles.length; i<len; i++) {\r\n\t\t//if(touchSupported) {\r\n\t\t//\taccordionToggles[i].addEventListener('touchstart', skipClickDelay, false);\r\n\t\t//}\r\n\t\t//if(pointerSupported){\r\n\t\t//\taccordionToggles[i].addEventListener('pointerdown', skipClickDelay, false);\r\n\t\t//}\r\n\t\taccordionToggles[i].addEventListener('click', switchAccordion, false);\r\n\t}\r\n})();", "$(document).ready(function(){\r\n\tinitialize_focuspoint();\r\n\tpicturefill();\r\n\r\n});\r\n\r\n$(window).load(function(){\r\n\tinitialize_focuspoint();\r\n});\r\n\r\n// $(window).load(function(){\r\n// \t// initialize_focuspoint();\r\n// \tclearTimeout(window.loadFinishedFocuspoint);\r\n//     window.loadFinishedFocuspoint = setTimeout(function(){\r\n// \t\tinitialize_focuspoint();\r\n//     }, 500);\r\n// });\r\n\r\n\r\nfunction initialize_focuspoint(){\r\n\t$('.focuspoint').focusPoint({\r\n\t\tthrottleDuration: 100 //re-focus images at most once every 100ms.\r\n\t});\r\n}\r\n\r\n\r\n\r\n\r\n", "$(document).ready(function(){\r\n\r\n\t$('.scrollTo').click(function(e) {\r\n\t\te.preventDefault();\r\n\t\t$.scrollTo( $(this).data('target'), 750, {easing:'swing'} );\r\n\t    return false;\r\n\t});\r\n\r\n});\r\n\r\n$(window).load(function(){\r\n\t//remove etracker\r\n\t$('[src^=\"https://www.etracker.de\"]').hide();\r\n});\r\n\r\n$(window).resize(function() {\r\n    clearTimeout(window.resizedFinishedFunctions);\r\n    window.resizedFinishedFunctions = setTimeout(function(){\r\n\r\n    }, 250);\r\n});\r\n\r\n$('.launch-chat').click(function(e) {\r\n\te.preventDefault();\r\n\tif (!$('#eva_chatWindow').is(\":visible\")) {\t\r\n\t\t$('.eva_bubble_Chat').first().click();\r\n\t}\r\n});\r\n\r\n$(document).ready(function(){\r\n\r\n\t$('.cookie-settings').on('click', function(e) {\r\n\t\te.preventDefault();\r\n\t\tUC_UI.showSecondLayer();\r\n\t});\r\n\r\n});", "/*\r\n * Google Analytics OptOut\r\n */\r\n\r\n\r\n\r\nvar gaProperty = '***********-2';\r\n\r\n// Disable tracking if the opt-out cookie exists.\r\nvar disableStr = 'ga-disable-' + gaProperty;\r\nif (document.cookie.indexOf(disableStr + '=true') > -1) {\r\n\twindow[disableStr] = true;\r\n}\r\n\r\n// Opt-out function\r\nfunction gaOptout() {\r\n\tdocument.cookie = disableStr + '=true; expires=Thu, 31 Dec 2099 23:59:59 UTC; path=/';\r\n\twindow[disableStr] = true;\r\n\talert('Die Erfassung durch Google Analytics wird nun verhindert!');\r\n}\r\n\r\n$( document ).on( \"click\", \"#optout\", function(e) {\r\n\te.preventDefault();\r\n\tgaOptout();\r\n});", "$(document).ready(function() {\r\n  // initializeGalleries();\r\n  initializeMagnificPopup();\r\n});\r\n\r\nfunction initializeMagnificPopup(){\r\n\t$('.image-popup-link').magnificPopup({\r\n\t\ttype:'image',\r\n\t\tcloseBtnInside: false,\r\n\t\tshowCloseBtn: false,\r\n\t\tmainClass: 'mfp-with-zoom', // this class is for CSS animation below\r\n\t\tzoom: {\r\n\t\t   enabled: true, // By default it's false, so don't forget to enable it\r\n\r\n\t\t   duration: 300, // duration of the effect, in milliseconds\r\n\t\t   easing: 'ease-in-out', // CSS transition easing function\r\n\r\n\t\t   // The \"opener\" function should return the element from which popup will be zoomed in\r\n\t\t   // and to which popup will be scaled down\r\n\t\t   // By defailt it looks for an image tag:\r\n\t\t   opener: function(openerElement) {\r\n\t\t     // openerElement is the element on which popup was initialized, in this case its <a> tag\r\n\t\t     // you don't need to add \"opener\" option if this code matches your needs, it's defailt one.\r\n\t\t     return openerElement.is('img') ? openerElement : openerElement.find('img');\r\n\t\t   }\r\n\t\t }\r\n\t});\r\n}\r\n\r\n/*\r\nfunction initializeGalleries(){\r\n\t$('.gallery-container').each(function() { // the containers for all your galleries\r\n\t  \r\n\t\t\t  var $container = $(this);\r\n\t\t\t  var $imageLinks = $container.find('.gallery-item');\r\n\t\t\t \r\n\t\t\t  var items = [];\r\n\t\t\t  $imageLinks.each(function() {\r\n\t\t\t    var $item = $(this);\r\n\t\t\t    var type = 'image';\r\n\t\t\t    if ($item.hasClass('gallery-youtube')) {\r\n\t\t\t      type = 'iframe';\r\n\t\t\t    }\r\n\t\t\t    var magItem = {\r\n\t\t\t      src: $item.attr('href'),\r\n\t\t\t      type: type\r\n\t\t\t    };\r\n\t\t\t    magItem.title = $item.data('title');\r\n\t\t\t    items.push(magItem);\r\n\t\t\t    });\r\n\t\t\t \r\n\t\t\t  $imageLinks.magnificPopup({\r\n\t\t\t    mainClass: 'mfp-with-anim',\r\n\t\t\t    items: items,\r\n\t\t\t    closeBtnInside: false,\r\n\t\t\t    enableEscapeKey: true,\r\n\t\t\t    showCloseBtn: true,\r\n\t\t\t    removalDelay: 300,\r\n\t\t\t    titleSrc: 'title', // Attribute of the target element that contains caption for the slide.\r\n\t\t\t    closeMarkup: '<button title=\"%title%\" type=\"button\" class=\"mfp-close\"><i class=\"btl bt-times\"></i></button>',\r\n\t\t\t    gallery:{\r\n\t\t\t        enabled:true,\r\n\t\t\t        arrowMarkup: '<span title=\"%title%\" class=\"mfp-arrow mfp-arrow-%dir%\"><i class=\"btl bt-angle-%dir% mfp-prevent-close\"></i></span>'\r\n\t\t\t    },\r\n\t\t\t    type: 'image',\r\n\t\t\t    callbacks: {\r\n\t\t\t      beforeOpen: function() {\r\n\t\t\t      \tthis.st.image.markup = this.st.image.markup.replace('mfp-figure', 'mfp-figure mfp-with-anim');\r\n\t\t\t      \tthis.st.mainClass = this.st.el.attr('data-effect');\r\n\r\n\t\t\t        var index = $imageLinks.index(this.st.el);\r\n\t\t\t        if (-1 !== index) {\r\n\t\t\t          this.goTo(index);\r\n\t\t\t        }\r\n\t\t\t      }\r\n\t\t\t    }\r\n\t\t\t  });\r\n\t \r\n\t });\r\n\r\n\r\n}\r\n*/", "var breakpointSmallMin = 750; // min-width: 768px  =  $width > 750px\r\nvar breakpointMediumMax = 974; // max-width: 992px  =  $width < 974px\r\n\r\n$(function() {\r\n    \t// equalPromoboxesHeights();\r\n    $('.link-box').matchHeight();\r\n    $('.link-box-header').matchHeight();\r\n    $('.teaser-info-box').matchHeight();\r\n    $('.equal-height').matchHeight();\r\n    $('.promobox-top').matchHeight();\r\n    $('.promobox-bottom').matchHeight();\r\n    $('.top-equal').matchHeight();\r\n    $('.iconText-element').matchHeight();\r\n    $('.teaser-landingpage-text').matchHeight();\r\n   // $('.layout-campaign-page .promobox-single').matchHeight();\r\n\r\n    doublePromoboxesHeights();\r\n    widePromoboxesHeights();\r\n    imageTextBoxCaptionHeights();\r\n});\r\n\r\n\r\n$(window).load(function() {\r\n\t$('.equal-height').matchHeight();\r\n\t$('.promobox-top').matchHeight();\r\n\t$('.promobox-bottom').matchHeight();\r\n\t$('.top-equal').matchHeight();\r\n    $('.iconText-element').matchHeight();\r\n  //  $('.layout-campaign-page .promobox-single').matchHeight();\r\n    doublePromoboxesHeights();\r\n\twidePromoboxesHeights();\r\n\timageTextBoxCaptionHeights();\r\n});\r\n\r\n$(window).resize(function() {\r\n    clearTimeout(window.resizedFinishedMatchHeight);\r\n    window.resizedFinishedMatchHeight = setTimeout(function(){\r\n    \t// equalPromoboxesHeights();\r\n    \tdoublePromoboxesHeights();\r\n    \twidePromoboxesHeights();\r\n    \t$('.link-box').matchHeight();\r\n    \t$('.equal-height').matchHeight();\r\n    \t$('.promobox-top').matchHeight();\r\n    \t$('.promobox-bottom').matchHeight();\r\n    \t$('.top-equal').matchHeight();\r\n    \timageTextBoxCaptionHeights();\r\n    }, 500);\r\n\r\n});\r\n\r\nfunction imageTextBoxCaptionHeights(){\r\n\tif( $('.imageTextBox').length > 0 ){\r\n\t\tif( $(window).innerWidth() > breakpointSmallMin ){\r\n\t\t\t$('.imageTextBox').each(function(){\r\n\t\t\t\tif( $(this).find('.img-caption').length > 0 ){\r\n\t\t\t\t\tvar totalHeight = $('.col-right').height();\r\n\t\t\t\t\tvar captionHeight = $('.img-caption').height();\r\n\t\t\t\t\tvar paddings = 16 + 16;\r\n\t\t\t\t\tvar pictureHeight = totalHeight - captionHeight - paddings;\r\n\t\t\t\t\t$(this).find('.focuspoint').height(pictureHeight);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}else{\r\n\t\t\t$('.imageTextBox').each(function(){\r\n\t\t\t\t$(this).find('.focuspoint').height('auto');\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\nfunction doublePromoboxesHeights(){\r\n\tif( $(window).innerWidth() > breakpointSmallMin ){\r\n\t\t$('.promobox-bottom').each(function(){\r\n\t\t\tvar ph = $(this).attr('style');\r\n\t\t\tvar pbh = $(this).height();\r\n\t\t\tvar plh = $(this).children('.promobox-left.bottom-equal').height();\r\n\t\t\tvar prh = $(this).children('.promobox-right.bottom-equal').height();\r\n\r\n\t\t\tif(ph == ''){\r\n\t\t\t\tif(plh>prh){\r\n\t\t\t\t\t$(this).children('.bottom-equal').height(plh);\r\n\t\t\t\t}else{\r\n\t\t\t\t\t$(this).children('.bottom-equal').height(prh);\r\n\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\t$(this).children('.bottom-equal').height(pbh);\r\n\t\t\t}\r\n\t\t});\r\n\t}else{\r\n\t\t$('.bottom-equal').height('auto');\r\n\t}\r\n\t\r\n}\r\n\r\nfunction widePromoboxesHeights(){\r\n\tif( $(window).innerWidth() > breakpointSmallMin ){\r\n\t\t$('.promobox-wide').each(function(){\r\n\t\t\tvar plh = $(this).children().children('.promobox-left').height();\r\n\t\t\tvar prh = $(this).children().children('.promobox-right').height();\r\n\r\n\t\t\tif(plh>prh){\r\n\t\t\t\t$(this).children().children('.left-right-equal').height(plh);\r\n\t\t\t}else{\r\n\t\t\t\t$(this).children().children('.left-right-equal').height(prh);\r\n\t\t\t}\r\n\t\t});\r\n\t}else{\r\n\t\t$(this).children().children('.left-right-equal').height('auto');\r\n\t}\r\n}\r\n/*\r\nfunction equalPromoboxesHeights(){\r\n\tif( $(window).innerWidth() > breakpointSmallMin && $(window).innerWidth() < breakpointMediumMax ){\r\n\t\t$('.promobox-top').each(function(){\r\n\t\t\tvar plh = $(this).children('.promobox-left.top-equal').height();\r\n\t\t\tvar prh = $(this).children('.top-equal').height();\r\n\r\n\t\t\tif(plh>prh){\r\n\t\t\t\t$(this).children('.promobox-right.top-equal').height(plh);\r\n\t\t\t}else{\r\n\t\t\t\t$(this).children('.promobox-left.top-equal').height(prh);\r\n\t\t\t}\r\n\t});\r\n\t\t$('.promobox-bottom').each(function(){\r\n\t\t\tvar plh = $(this).children('.promobox-left.bottom-equal').height();\r\n\t\t\tvar prh = $(this).children('.promobox-right.bottom-equal').height();\r\n\r\n\t\t\tif(plh>prh){\r\n\t\t\t\t$(this).children('.promobox-right.bottom-equal').height(plh);\r\n\t\t\t}else{\r\n\t\t\t\t$(this).children('.promobox-left.bottom-equal').height(prh);\r\n\t\t\t}\r\n\t\t});\r\n\t}else{\r\n\t\t// $('.top-equal').height('auto');\r\n\t\t// $('.bottom-equal').height('auto');\r\n\r\n\t\t$('.equal-height').matchHeight();\r\n    \t$('.promobox-top').matchHeight();\r\n    \t$('.promobox-bottom').matchHeight();\r\n\t}\r\n}\r\n*/\r\n", "\r\n$(document).ready(function() {\r\n  $(\"#mobileNav\").mmenu({\r\n  \tnavbar : {\r\n  \t\ttitle: '<PERSON><PERSON>'\r\n  \t},\r\n  \tcurrentItem : {\r\n  \t\tfind : true\r\n  \t}\r\n  });\r\n});\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "$('document').ready(function(){\r\n\r\n\r\n\t/*\r\n\t * Dropdown Menu L2 with delay\r\n\t */\r\n\tvar timer;\r\n\tvar timer2;\r\n\tvar timeout2;\r\n\r\n\t$(\".main-nav > li \").on(\"mouseover\", function() {\r\n\t  clearTimeout(timer);\r\n\t  $this = $(this);\r\n\t  if(  $('.main-nav > li.open') == $this ){\r\n\t  }\r\n\r\n\t  if( $('.main-nav').hasClass('dropped') ){\r\n\t  \ttimeout2 = 0;\r\n\t  }else{\r\n\t  \ttimeout2 = 1000;\r\n\t  }\r\n\t $('.main-nav > li').removeClass('open');\r\n\t  if(  $this.children('.navbar-dropdown-container').length > 0 && !$this.hasClass('active')){\r\n\t\t  $this.addClass('open');\r\n\t  \t  \t$('.main-nav').addClass('dropped');\r\n\t\t  timer2 = setTimeout(function(){\r\n\t\t  \t$('.navbar-dropdown-container').addClass('navbar-dropdown-dropped');\r\n\t\t  \t$('.navbar-dropdown-container ul.sub-nav').removeClass('open');\r\n\t\t  \t$this.children('.navbar-dropdown-container').children('ul').addClass('open');\r\n\t\t   }, timeout2);\r\n\t  }else{\r\n\t  \t  $('.main-nav').removeClass('dropped');\r\n\t  \t  $('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');\r\n\t  \t  $('.main-nav > li').removeClass('open');\r\n\t  \t  $('.navbar-dropdown-container ul.sub-nav').removeClass('open');\r\n\t  }\r\n\t}).on(\"mouseleave\", function() {\r\n\t\tclearTimeout(timer2);\r\n\t  timer = setTimeout(function(){\r\n\t  \t$('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');\r\n\t  \t$('.main-nav > li').removeClass('open');\r\n\t  \t$('.navbar-dropdown-container ul.sub-nav').removeClass('open');\r\n\t  }, 600);\r\n\t});\r\n\r\n\r\n\t$(\".extra-sub-nav li\").on(\"mouseover\", function() {\r\n\t\tclearTimeout(timer);\r\n\t\tclearTimeout(timer2);\r\n\t  \t $('.main-nav').removeClass('dropped');\r\n\t\t$('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');\r\n\t\t$('.main-nav > li').removeClass('open');\r\n\t\t$('.navbar-dropdown-container ul.sub-nav').removeClass('open');\r\n\t}).on(\"mouseleave\", function() {\r\n\t});\r\n\r\n\r\n\t/*\r\n\t * Dropdown Flyoutmenu toggler\r\n\t */\r\n\t$('[data-action=\"flyout\"]').on('click', function(e){ \r\n\t\te.preventDefault();\r\n\r\n\r\n\t\tif( $(this).hasClass('active') ){//close\r\n\t\t\tif( $(this).closest('.navbar').hasClass('active-is-open') ){\r\n\t\t\t\tvar addClass = 'out-long';\r\n\t\t\t}else{\r\n\t\t\t\tvar addClass = 'out';\r\n\t\t\t}\r\n\r\n\t\t\t$('.nav-flyout.out').removeClass('out')\r\n\t\t\t$('.nav-flyout.out-long').removeClass('out-long');\r\n\t\t\t$('[data-action=\"flyout\"].active').removeClass('active');\r\n\t\t\tvar target = $(this).data('target');\r\n\t\t\t$(target).removeClass(addClass);\r\n\r\n\t\t}else{//open\r\n\r\n\t\t\tif( $(this).closest('.navbar').hasClass('active-is-open') ){\r\n\t\t\t\tvar addClass = 'out-long';\r\n\t\t\t}else{\r\n\t\t\t\tvar addClass = 'out';\r\n\t\t\t}\r\n\r\n\t\t\t// activeIsOpenHide();\r\n\r\n\t\t\t$('.nav-flyout.out').removeClass('out')\r\n\t\t\t$('.nav-flyout.out-long').removeClass('out-long');\r\n\t\t\t$('[data-action=\"flyout\"].active').removeClass('active');\r\n\t\t\t$(this).toggleClass('active');\r\n\t\t\tvar target = $(this).data('target');\r\n\t\t\t$(target).addClass(addClass);\r\n\t\t\t// activeIsOpenHide();\r\n\t\t}\r\n\r\n\t});\r\n\r\n\r\n});\r\n\r\n\r\n/* Catch the srcoll movement*/\r\n/* show/hide mainnav*/\r\n$(function(){\r\n    var _top = $(window).scrollTop();\r\n    var _direction;\r\n\r\n    $(window).scroll(function(){\r\n        var _cur_top = $(window).scrollTop();\r\n        if(_top < _cur_top && _cur_top!=0 )\r\n        {\r\n            _direction = 'down';\r\n\r\n            // activeIsOpenHide(); \r\n\r\n            $('.navbar').addClass('scrolled');\r\n            $('.navbar-mobile').addClass('scrolled');\r\n            $('.navbar-dropdown-container ul.sub-nav').removeClass('open');\r\n            $('[data-action=\"flyout\"]').removeClass('active');\r\n            $('.nav-flyout').removeClass('out').removeClass('out-long');\r\n            $('.nav-flyout').addClass('scrolled');\r\n\r\n\r\n        }\r\n        else\r\n        {\r\n            _direction = 'up';\r\n\r\n\r\n            $('.navbar').removeClass('scrolled');\r\n            $('.navbar-mobile').removeClass('scrolled');\r\n            $('.nav-flyout').removeClass('scrolled');\r\n            $('.nav-flyout').removeClass('out');\r\n\r\n            setTimeout(function(){\r\n            \tif(_cur_top == 0){\r\n            \t\t// activeIsOpenShow();\r\n            \t}\r\n            },1000);\r\n        }\r\n\r\n\r\n        if(_cur_top > 0 ){\r\n        \t$('.scroll-up').fadeIn();\r\n        }else{\r\n        \t$('.scroll-up').fadeOut();\r\n        }\r\n\r\n        _top = _cur_top;\r\n    });\r\n\r\n    $(document).click(function(){\r\n\t    $('[data-action=\"flyout\"]').removeClass('active');\r\n        $('.nav-flyout').removeClass('out');\r\n        // activeIsOpenShow();\r\n\t});\r\n\r\n\t$(\".navbar\").click(function(e){\r\n\t    e.stopPropagation();\r\n\t});\r\n\t$(\".nav-flyout\").click(function(e){\r\n\t    e.stopPropagation();\r\n\t});\r\n\r\n\r\n});\r\n\r\nvar pos = $(window).scrollTop();\r\n$(function(){\r\n    fixedSubNavi();\r\n\r\n    $(window).scroll(function(){\r\n    \tpos = $(window).scrollTop();\r\n    \tfixedSubNavi();\r\n    });\r\n\r\n});\r\n$(window).resize(function(){\r\n   fixedSubNavi();\r\n});\r\n\r\nfunction fixedSubNavi(){\r\n\tvar subNavOffset = 10;\r\n\tif( $('.sidebar-nav-container').length > 0 ){\r\n\r\n\t\tif( pos + $('.navbar').height() > $('footer').offset().top - 250  && $(window).width() > 992 ){\r\n\t\t\t$('.sidebar-nav-container').removeClass('fixed');\r\n\t\t\t$('.sidebar-nav-container').addClass('absoluteBottom');\r\n\t\t\t$('.sidebar-nav-container').css('width', $('.col-sidebar').width() );\r\n\t\t\t$('.sidebar-nav-container').css('height', $('.sub-menu').height() + 32 );\r\n\t\t\t$('.sidebar-nav-container').css('left','16px');\r\n\t\t\t$('.sidebar-nav-container').css('top', $('.col-content').height() - $('.sub-menu').height() - 32 );\r\n\r\n\t\t}else if( pos + $('.navbar').height() + subNavOffset  > $('#submenuOffset').offset().top  && $(window).width() > 992 ){\r\n\t\t\t$('.sidebar-nav-container').removeClass('absoluteBottom');\r\n\t\t\t$('.sidebar-nav-container').addClass('fixed');\r\n\t\t\t$('.sidebar-nav-container').css('width', $('.col-sidebar').width() );\r\n\t\t\t$('.sidebar-nav-container').css('left',$('.logo-container').offset().left);\r\n\t\t\t$('.sidebar-nav-container').css('top',$('.navbar').height() + subNavOffset);\r\n\t\t}else{\r\n\t\t\t$('.sidebar-nav-container').removeClass('fixed');\r\n\t\t\t$('.sidebar-nav-container').removeClass('absoluteBottom');\r\n\t\t\t$('.sidebar-nav-container').css('width','auto');\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/*\r\n..in progress\r\n*/\r\n\r\n/*\r\n$('document').ready(function(){\r\n\tif( $('.navbar').hasClass('active-is-open') ){\r\n\t\tactiveIsOpenShow();\r\n\t}\r\n\r\n});\r\n\r\nfunction activeIsOpenShow(){\r\n\tif( $('.navbar').hasClass('active-is-open') ){\r\n\t\t$('.navbar.active-is-open').find('.main-nav > li.active').not('.home').addClass('open');\r\n\t\t$('.main-nav > li.active').not('.home').find('.navbar-dropdown-container').addClass('navbar-dropdown-dropped');\r\n\t\t$('.navbar-dropdown-dropped > ul.sub-nav').addClass('open');\r\n\t}\r\n}\r\nfunction activeIsOpenHide(){\r\n\tif( $('.navbar').hasClass('active-is-open') ){\r\n\t\t$('.navbar.active-is-open').find('.main-nav > li.active').not('.home').removeClass('open');\r\n\t\t$('.main-nav > li.active').not('.home').find('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');\r\n\t\t$('.navbar-dropdown-dropped > ul.sub-nav').removeClass('open');\r\n\t}\r\n}\r\n*/\r\n\r\n", "\t/*\r\n\t$(\".responsiveImg\").responsiveImg({\r\n\t\tsrcAttribute: \"data-src\",\r\n\t\tbreakpoints:{\r\n\t         \"__CACHE__small\":360,\r\n\t\t    \"__CACHE__medium\":780,\r\n\t\t    \"__CACHE__large\":900\r\n\t    },\r\n\t    pathToPHP : baseUrl+\"php\",\r\n\t    createNewImages : true,\r\n\t    jpegQuality : 90\r\n\r\n\t});\r\n\t*/\r\n", "$(document).ready(function(){\r\n\t$('[name=\"tx_indexedsearch[submit_button]\"]').on('click',function(e){\r\n\t\tif( $(this).closest('form').find('input.search-query').val() == '' ){\r\n\t\t\te.preventDefault();\r\n\t\t\t$(this).closest('form').find('input.search-query').select();\r\n\t\t}\r\n\t});\r\n\r\n});\r\n", "$(document).ready(function(){\r\n\t// $('select').selectBox();\r\n});", "$('.table').stacktable();", " $(function() {\r\n      if($('#slides').length>0){\r\n        var $slides = $('#slides');\r\n        Hammer($slides[0]).on(\"swipeleft\", function(e) {\r\n          $slides.data('superslides').animate('next');\r\n        });\r\n        Hammer($slides[0]).on(\"swiperight\", function(e) {\r\n          $slides.data('superslides').animate('prev');\r\n        });\r\n        $slides.superslides({\r\n          hashchange: false,\r\n          play: 9000,\r\n          animation: 'fade',\r\n          animation_speed: 'normal'\r\n        });\r\n        \r\n      }\r\n});\r\n\r\n\r\n\r\n$('#slides').on('animated.slides', function () {\r\n  initialize_focuspoint();\r\n});", "if( $('.teaser-home').length > 0 ){\r\n\r\n\t\tvar timer = null;\r\n\t\tvar duration = 12000;//in miliseconds\r\n\t\tvar durationProgressBar = duration - 400;//in miliseconds\r\n\t\tvar timeout = 60000;//in miliseconds\r\n\t\tvar timeoutProgressBar = timeout - 400;//in miliseconds\r\n\t\tvar current = $('.teaser-switch-box.active').data('item');\r\n\t\tvar countTeasersHome = $('.teaser-switch-box').length;\r\n\t\tvar isOperationInProgress = false;\r\n\r\n\r\n\t\t$(document).ready(function(){\r\n\t\t\t// setTeaserHomeHeight();\r\n\r\n\t\t\t$('.teaser-switch-box').on('click',function(e){\r\n\t\t\t\te.stopPropagation();\r\n\t\t\t\t// e.preventDefault();\r\n\t\t\t\tvar button = $(this).find('.cta-link');\r\n\t\t\t\tconsole.log(button);\r\n\t\t\t\t// button.trigger('click');\r\n\t\t\t\twindow.location.href = $(button).attr('href');\r\n\t\t\t});\r\n\r\n\r\n\r\n\t\t\t$('.teaser-switch-box-hidden').off('click').on('click',function(e){\r\n\t\t\t\te.stopPropagation();\r\n\t\t\t\tstopTeaserHome();\r\n\t\t\t\tvar selectedItem = $(this).data('item');\r\n\t\t\t\tplaySlide(selectedItem);\r\n\t\t\t\tif(!isOperationInProgress){\r\n\t\t\t\t\tsetTimeout(function(e){\r\n\t\t\t\t\t\tplayTeaserHome(e);\r\n\t\t\t\t\t\tisOperationInProgress = false;\r\n\t\t\t\t\t},timeout);\r\n\t\t\t\t}\r\n\t\t\t\tisOperationInProgress = true;\r\n\t\t\t});\t\r\n\r\n\r\n\t\t});\r\n\r\n\r\n\t\t$(document).ready(function(e){\r\n\t\t\tplayTeaserHome(e);\r\n\t\t});\r\n\t\t\r\n\t\t$(window).load(function(e){\r\n\t\t\t// setTeaserHomeHeight();\r\n\t\t\tpositionTeaserHomeControllerMobile();\r\n\t\t});\r\n\r\n\t\t$(window).resize(function() {\r\n\t\t    // setTeaserHomeHeight();\r\n\r\n\t\t    clearTimeout(window.resizedFinishedteaserHome);\r\n\t\t    window.resizedFinishedteaserHome = setTimeout(function(){\r\n\t\t\t\tpositionTeaserHomeControllerMobile();\r\n\t\t    \t\r\n\t\t    }, 250);\r\n\t\t});\r\n\r\n\t\tfunction setTeaserHomeHeight(){\r\n\t\t\tvar wh = $(window).height();\r\n\t\t\t$('.teaser-home').height(wh);\r\n\t\t}\r\n\r\n\r\n\r\n\t\tfunction playTeaserHome(e){\r\n\t\t\tstartProgressBar();\r\n\t\t\ttimer = setInterval(function(){\r\n\t\t\t\tif(current<(countTeasersHome-1)){\r\n\t\t\t\t\tcurrent++;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tcurrent = 0;\r\n\t\t\t\t}\r\n\t\t\t\tplaySlide(current);\r\n\t\t\t\tstartProgressBar();\r\n\t\t\t},duration);\r\n\t\t}\r\n\r\n\t\tfunction stopTeaserHome(){\r\n\t\t\tclearInterval(timer);\r\n\t\t\tstopProgressBar();\r\n\t\t}\r\n\r\n\t\tfunction positionTeaserHomeControllerMobile(){\r\n\t\t\tif( $(window).width() < 801 ){\r\n\t\t\t\tvar ch = $('.teaser-switch-box').height();\r\n\t\t\t\t$('.teaser-switch-box-container').height(ch);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfunction playTeaserHomeNext(){\r\n\t\t\tif(current<(countTeasersHome-1)){\r\n\t\t\t\tcurrent++;\r\n\t\t\t}else{\r\n\t\t\t\tcurrent = 0;\r\n\t\t\t}\r\n\t\t\t$('.teaser-switch-box-hidden[data-item=\"'+current+'\"]').trigger('click');\r\n\t\t}\r\n\r\n\t\tfunction playTeaserHomePrevious(){\r\n\t\t\tif( (current - 1) < 0){\r\n\t\t\t\tcurrent = countTeasersHome - 1;\r\n\t\t\t}else{\r\n\t\t\t\tcurrent--;\r\n\t\t\t}\r\n\t\t\t$('.teaser-switch-box-hidden[data-item=\"'+current+'\"]').trigger('click');\r\n\t\t}\r\n\r\n\t\tfunction playSlide(slideId){\r\n\t\t\tif( $('.teaser-video video').length > 0 ){\r\n\t\t\t\t$('.teaser-video video').get(0).pause();\r\n\t\t\t}\r\n\t\t\t$('.teaser-switch-box').removeClass('active');\r\n\t\t\t$('.teaser-switch-box-hidden').removeClass('active');\r\n\t\t\t$('.teaser-bullets li').removeClass('active');\r\n\t\t\t$('.teaser-switch-box[data-item=\"'+slideId+'\"]').addClass('active');\r\n\t\t\t$('.teaser-switch-box-hidden[data-item=\"'+slideId+'\"]').addClass('active');\r\n\t\t\t$('.teaser-item').removeClass('active');\r\n\t\t\t$('.teaser-item[data-item=\"'+slideId+'\"]').addClass('active');\r\n\t\t\t$('.teaser-bullets li[data-item=\"'+slideId+'\"]').addClass('active');\r\n\t\t\tif ( $('.teaser-item[data-item=\"'+slideId+'\"]').hasClass('teaser-video') ){\r\n\t\t\t\tif( $(window).width() > 960 ){\r\n\t\t\t\t\t$('#video'+slideId).get(0).play();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tcurrent = $('.teaser-switch-box.active').data('item');\r\n\t\t}\r\n\r\n\t\tfunction startProgressBar(){\r\n\t\t\t$(\".teaser-progressBar\").fadeIn(10);\r\n\t\t\t$(\".teaser-progressBar > span\").each(function() {\r\n\t\t\t  $(this)\r\n\t\t\t    .stop()\r\n\t\t\t    .width('0px')\r\n\t\t\t    .animate({\r\n\t\t\t      width: $('.teaser-progressBar').width()\r\n\t\t\t    }, {duration: durationProgressBar,easing:'linear'});\r\n\t\t\t});\r\n\t\t}\r\n\t\tfunction stopProgressBar(){\r\n\t\t\t$(\".teaser-progressBar > span\").each(function() {\r\n\t\t\t\t$(this)\r\n\t\t\t\t.stop()\r\n\t\t\t\t.width('100%')\r\n\t\t\t    .animate({\r\n\t\t\t      width: '0px',\r\n\t\t\t    }, {duration: timeoutProgressBar,easing:'linear'});\r\n\t\t\t});\t\r\n\t\t}\r\n\r\n\r\n\r\n\r\n\t\tif( $('#teaser-home').length > 0 ){\r\n\r\n\t\t\t// get a reference to an element\r\n\t\t\tvar stage = document.getElementById('teaser-home');\r\n\r\n\t\t\t// create a manager for that element\r\n\t\t\tvar mc = new Hammer.Manager(stage,{\r\n\t\t\t\ttouchAction: 'pan-x',\r\n\t\t\t\ttouchAction: 'pan-y'\r\n\t\t\t});\r\n\r\n\t\t\t// create a recognizer\r\n\t\t\tvar Swipe = new Hammer.Swipe();\r\n\r\n\t\t\t// add the recognizer\r\n\t\t\tmc.add(Swipe);\r\n\r\n\t\t\t// subscribe to events\r\n\t\t\tmc.on('swipeleft', function(e) {\r\n\t\t\t    // do something cool\r\n\t\t\t    playTeaserHomeNext();\r\n\t\t\t});\r\n\t\t\tmc.on('swiperight', function(e) {\r\n\t\t\t    // do something cool\r\n\t\t\t    playTeaserHomePrevious();\r\n\t\t\t});\r\n\t\t}\r\n\r\n\r\n}\r\n\r\n\r\n", "if( $('.teaser-landingpage2').length > 0 ){\r\n\t$(document).ready(function(){\r\n\t\tif( $('#video').length > 0 ){\r\n\t\t\t$('#video').get(0).play();\r\n\t\t}\r\n\t});\r\n}\r\n\r\nif( $('.teaser-landingpage').length > 0 ){\r\n\r\n\t\t// var breakpointSmallMin = 750; // min-width: 768px  =  $width > 750px\r\n\t\tvar breakpointSmallMin = 983; // min-width: 1000px  =  $width > 983px\r\n\t\t// var teaserLandingpageMinHeight = 940;\r\n\t\tvar teaserLandingpageMinHeight = 520;\r\n\t\tvar teaserLandingpageMinHeightStop = 940;\r\n\t\tvar teaserLandingpageMaxHeight = 1080;\r\n\t\tvar teaserLandingpageMinHeightMobile = 280;\r\n\t\tvar teaserLandingpageTextHorizontalPosition = 350;\r\n\t\tvar teaserLandingpageTextHorizontalPositionMobile = 140;\r\n\r\n\r\n\t\t$(document).ready(function(){\r\n\t\t\tsetTeaserLandingpageHeight();\r\n\t\t\tif( $('#video').length > 0 ){\r\n\t\t\t\t$('#video').get(0).play();\r\n\t\t\t}\r\n\t\t});\r\n\r\n\r\n\t\t$(window).load(function(e){\r\n\t\t\tsetTeaserLandingpageHeight();\r\n\t\t});\r\n\r\n\t\t$(window).resize(function() {\r\n\t\t    setTeaserLandingpageHeight();\r\n\r\n\t\t    clearTimeout(window.resizedFinishedTeaserLandingpage);\r\n\t\t    window.resizedFinishedTeaserLandingpage = setTimeout(function(){\r\n\t\t    \t\r\n\t\t    }, 250);\r\n\t\t});\r\n\r\n\t\tfunction setTeaserLandingpageHeight(){\r\n\t\t\tif( $(window).innerWidth()>breakpointSmallMin ){\r\n\t\t\t\tvar wh = $(window).height();\r\n\t\t\t\tvar heroHeight = wh;\r\n\r\n\t\t\t\tif( wh >  teaserLandingpageMaxHeight ){\r\n\t\t\t\t\theroHeight = teaserLandingpageMaxHeight;\r\n\t\t\t\t}\r\n\t\t\t\tif( wh <  teaserLandingpageMinHeight ){\r\n\t\t\t\t\theroHeight = 'auto';\r\n\t\t\t\t\t$('.teaser-landingpage').addClass('minHeight');\r\n\t\t\t\t}else{\r\n\t\t\t\t\t$('.teaser-landingpage').removeClass('minHeight');\r\n\t\t\t\t}\r\n\r\n\t\t\t\t$('.teaser-landingpage').height(heroHeight);\r\n\t\t\t\t$('.teaser-landingpage .teaser-canvas').height(heroHeight);\r\n\t\t\t\t$('.teaser-landingpage .teaser-item').height(heroHeight);\r\n\r\n\t\t\t\tvar heroTitlePos;\r\n\t\t\t\tif( wh <  teaserLandingpageMinHeight){\r\n\t\t\t\t\theroTitlePos = '170px';\r\n\t\t\t\t}else if( wh <  teaserLandingpageMinHeightStop){\r\n\t\t\t\t\theroTitlePos = '590px';\r\n\t\t\t\t}else if( wh >=  teaserLandingpageMinHeight && wh <=  teaserLandingpageMaxHeight){\r\n\t\t\t\t\theroTitlePos = wh - teaserLandingpageTextHorizontalPosition;\r\n\t\t\t\t}else if( wh >  teaserLandingpageMaxHeight){\r\n\t\t\t\t\theroTitlePos = teaserLandingpageMaxHeight - teaserLandingpageTextHorizontalPosition;\r\n\t\t\t\t}else{\r\n\t\t\t\t\theroTitlePos = teaserLandingpageMinHeight - teaserLandingpageTextHorizontalPosition;\r\n\t\t\t\t}\r\n\t\t\t\t$('.teaser-landingpage .heroTitle').css('bottom',heroTitlePos);\r\n\t\t\t\t\r\n\t\t\t}else{\r\n\t\t\t\t$('.teaser-landingpage').removeClass('minHeight');\r\n\t\t\t\t$('.teaser-landingpage').height('auto');\r\n\t\t\t\t$('.teaser-landingpage .teaser-canvas').height('auto');\r\n\t\t\t\t$('.teaser-landingpage .teaser-item').height('auto');\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n}\r\n\r\n", "$(document).ready(function() {\r\n\t  $('.lazy').unveil(200, function() {\r\n\t\t  $(this).load(function() {\r\n\t\t  });\r\n\t  });\r\n});", "if($('.default-form').length > 0){\r\n\r\n\r\n\t//ON Page Scripts\r\n\t//\r\n\t//more Documentation on:\r\n\t// http://jqueryvalidation.org/documentation/\r\n\t//\r\n\tvar validator = $(\".default-form\").validate({\r\n\t  success: \"valid\",\r\n\t  rules: {\r\n\t    // simple rule, converted to {required:true}\r\n\t    name: \"required\",\r\n\t    // compound rule\r\n\t    title: { \r\n\t    \t\t selectcheck: true //funktioniert nur wenn selectBox nicht angeschaltet ist.\r\n\t    },\r\n\t    field1: {\r\n\t      required: true,\r\n\t      email: true\r\n\t    },\r\n\t    field2: {\r\n\t      required: true\r\n\t    },\r\n\t  }\r\n\t});\r\n\r\n\r\n\tjQuery.validator.addMethod('selectcheck', function (value) {\r\n        return (value != '0');\r\n    }, \"Bitte wählen\");\r\n\r\n\r\n}", "var videoFile = document.getElementById(\"rehnag-start-video\");\r\nif (videoFile) {\r\n\tvar options = {\r\n\t\tcontrols: false,\r\n\t\tliveui: false,\r\n\t\tresizeManager: false,\r\n\t\tlanguages: {\r\n\t\t\tde: de\r\n\t\t},\r\n\t\thtml5: {\r\n\t\t\t//nativeControlsForTouch: true,\r\n\t\t\thls: {\r\n\t\t\t\twithCredentials: false,\r\n\t\t\t\tpreload: true,\r\n\t\t\t\tbandwidth: 928117100\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tvar player = videojs('rehnag-start-video', options);\r\n\tplayer.httpSourceSelector();\r\n\tplayer.qualityLevels();\r\n\tvar timer = 0;\r\n\tvar teaserLength, teasers;\r\n\r\n\tplayer.on('timeupdate', function(event) {\r\n\t\tif(parseInt(this.currentTime(), 10) > 0) {\r\n\t\t\t$('.vjs-tech').fadeIn();\r\n\t\t}\r\n\t\tvar currentTimer;\r\n\t\tcurrentTimer = Math.round(parseInt(this.currentTime(), 10));\r\n\t\tif (timer != currentTimer) {\r\n\t\t\ttimer = currentTimer;\r\n\t\t\tvar $elements = $('[data-type=\"teaser\"][data-timestamp=\"'+timer+'\"]:not(.teaser-switch-box-highlight)');\r\n\t\t\tvar $titleElements = $('[data-type=\"heroTitle\"][data-timestamp=\"'+timer+'\"]');\r\n\t\t\tif ($elements.length) {\r\n\t\t\t\tanimate($elements, $titleElements);\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n\r\n\tfunction animate($teaserElements, $titleElements) {\r\n\t\tif( $(window).innerWidth() > 640){\r\n\t\t\tanimateTeaser($teaserElements);\r\n\t\t}\r\n\t\tanimateTitle($titleElements);\r\n\t}\r\n\r\n\tfunction animateTeaser($teaserElements) {\r\n\t\t$('[data-type=\"teaser\"]:not(.teaser-switch-box-highlight)').animate(\r\n\t\t\t{\r\n\t\t\t\t'margin-top': 0\r\n\t\t\t},\r\n\t\t\t800\r\n\t\t);\r\n\t\t$teaserElements.stop().animate({\r\n\t\t\t'margin-top': -110\r\n\t\t}, 800);\r\n\t}\r\n\r\n\tfunction animateTitle($titleElements) {\r\n\t\t$('[data-type=\"heroTitle\"]').css('z-index', 10).fadeOut(800);\r\n\t\t$titleElements.stop().css('z-index', 5).fadeIn(800);\r\n\t}\r\n\r\n}\r\n\r\n"]}