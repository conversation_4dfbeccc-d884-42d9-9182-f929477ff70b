var width, height, baseRatio;
var inputHeight = $('#powermail_field_height_abmessungen');
var inputWidth = $('#powermail_field_width_abmessungen');

$(document).ready(function () {

    $('.BoxContainer .box').on('click', function () {
        $('.BoxContainer .box').each(function () {
            $(this).removeClass('active');
        })
        $(this).addClass('active');
    });
    // rename step buttons from < > to words
    renameStepButtons();
    advertisementDimension();

    inputHeight.on('keyup', function () {
        height = parseInt($(this).val(), 10);
        changeInputVal(inputWidth, height, baseRatio);
    });

    inputWidth.on('keyup', function () {
        width = parseInt($(this).val(), 10);
        changeInputVal(inputHeight, width, 1 / baseRatio);
    });


});


function renameStepButtons() {
    $('.powermail_tab_navigation .btn-primary.pull-right').html('Weiter');
    $('.powermail_fieldwrap .btn-warning').html('Zurück');
    return true;
}

function advertisementDimension() {
    $('.powermail_fieldwrap_3anzeigenform input').on('click', function () {
        if ($(this).is(':checked')) {
            inputWidth.removeAttr('readonly').css('border-color', '#a3a3a3');
            inputHeight.removeAttr('readonly').css('border-color', '#a3a3a3');

            width = parseInt($(this).parent().data('width'), 10);

            if (isNaN(width)) {
                width = 1;
            }

            height = parseInt($(this).parent().data('height'), 10);
            if (isNaN(height)) {
                height = 1;
            }

            inputWidth.val(width);
            inputHeight.val(height);
            baseRatio = width / height;
        }

    });
}

function changeInputVal(source, value, ratio) {
    if (isNaN(value)) {
        value = 1;
    }
    value = Math.round(value * ratio);
    source.val(value);
    source.attr('readonly', 'readonly').css('border-color', '#ff8d2f');
}