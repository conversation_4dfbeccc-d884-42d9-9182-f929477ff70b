$(document).ready(function(){

	$('.scrollTo').click(function(e) {
		e.preventDefault();
		$.scrollTo( $(this).data('target'), 750, {easing:'swing'} );
	    return false;
	});

});

$(window).load(function(){
	//remove etracker
	$('[src^="https://www.etracker.de"]').hide();
});

$(window).resize(function() {
    clearTimeout(window.resizedFinishedFunctions);
    window.resizedFinishedFunctions = setTimeout(function(){

    }, 250);
});

$('.launch-chat').click(function(e) {
	e.preventDefault();
	if (!$('#eva_chatWindow').is(":visible")) {	
		$('.eva_bubble_Chat').first().click();
	}
});

$(document).ready(function(){

	$('.cookie-settings').on('click', function(e) {
		e.preventDefault();
		UC_UI.showSecondLayer();
	});

});