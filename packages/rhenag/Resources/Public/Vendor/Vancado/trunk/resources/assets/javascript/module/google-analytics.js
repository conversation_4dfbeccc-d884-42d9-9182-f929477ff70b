/*
 * Google Analytics OptOut
 */



var gaProperty = 'UA-68439485-2';

// Disable tracking if the opt-out cookie exists.
var disableStr = 'ga-disable-' + gaProperty;
if (document.cookie.indexOf(disableStr + '=true') > -1) {
	window[disableStr] = true;
}

// Opt-out function
function gaOptout() {
	document.cookie = disableStr + '=true; expires=Thu, 31 Dec 2099 23:59:59 UTC; path=/';
	window[disableStr] = true;
	alert('Die Erfassung durch Google Analytics wird nun verhindert!');
}

$( document ).on( "click", "#optout", function(e) {
	e.preventDefault();
	gaOptout();
});