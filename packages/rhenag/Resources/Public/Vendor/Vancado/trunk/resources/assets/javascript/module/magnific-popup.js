$(document).ready(function() {
  // initializeGalleries();
  initializeMagnificPopup();
});

function initializeMagnificPopup(){
	$('.image-popup-link').magnificPopup({
		type:'image',
		closeBtnInside: false,
		showCloseBtn: false,
		mainClass: 'mfp-with-zoom', // this class is for CSS animation below
		zoom: {
		   enabled: true, // By default it's false, so don't forget to enable it

		   duration: 300, // duration of the effect, in milliseconds
		   easing: 'ease-in-out', // CSS transition easing function

		   // The "opener" function should return the element from which popup will be zoomed in
		   // and to which popup will be scaled down
		   // By defailt it looks for an image tag:
		   opener: function(openerElement) {
		     // openerElement is the element on which popup was initialized, in this case its <a> tag
		     // you don't need to add "opener" option if this code matches your needs, it's defailt one.
		     return openerElement.is('img') ? openerElement : openerElement.find('img');
		   }
		 }
	});
}

/*
function initializeGalleries(){
	$('.gallery-container').each(function() { // the containers for all your galleries
	  
			  var $container = $(this);
			  var $imageLinks = $container.find('.gallery-item');
			 
			  var items = [];
			  $imageLinks.each(function() {
			    var $item = $(this);
			    var type = 'image';
			    if ($item.hasClass('gallery-youtube')) {
			      type = 'iframe';
			    }
			    var magItem = {
			      src: $item.attr('href'),
			      type: type
			    };
			    magItem.title = $item.data('title');
			    items.push(magItem);
			    });
			 
			  $imageLinks.magnificPopup({
			    mainClass: 'mfp-with-anim',
			    items: items,
			    closeBtnInside: false,
			    enableEscapeKey: true,
			    showCloseBtn: true,
			    removalDelay: 300,
			    titleSrc: 'title', // Attribute of the target element that contains caption for the slide.
			    closeMarkup: '<button title="%title%" type="button" class="mfp-close"><i class="btl bt-times"></i></button>',
			    gallery:{
			        enabled:true,
			        arrowMarkup: '<span title="%title%" class="mfp-arrow mfp-arrow-%dir%"><i class="btl bt-angle-%dir% mfp-prevent-close"></i></span>'
			    },
			    type: 'image',
			    callbacks: {
			      beforeOpen: function() {
			      	this.st.image.markup = this.st.image.markup.replace('mfp-figure', 'mfp-figure mfp-with-anim');
			      	this.st.mainClass = this.st.el.attr('data-effect');

			        var index = $imageLinks.index(this.st.el);
			        if (-1 !== index) {
			          this.goTo(index);
			        }
			      }
			    }
			  });
	 
	 });


}
*/