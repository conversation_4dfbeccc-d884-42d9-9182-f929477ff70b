var breakpointSmallMin = 750; // min-width: 768px  =  $width > 750px
var breakpointMediumMax = 974; // max-width: 992px  =  $width < 974px

$(function() {
    	// equalPromoboxesHeights();
    $('.link-box').matchHeight();
    $('.link-box-header').matchHeight();
    $('.teaser-info-box').matchHeight();
    $('.equal-height').matchHeight();
    $('.promobox-top').matchHeight();
    $('.promobox-bottom').matchHeight();
    $('.top-equal').matchHeight();
    $('.iconText-element').matchHeight();
    $('.teaser-landingpage-text').matchHeight();
    doublePromoboxesHeights();
    widePromoboxesHeights();
    imageTextBoxCaptionHeights();
});


$(window).load(function() {
	$('.equal-height').matchHeight();
	$('.promobox-top').matchHeight();
	$('.promobox-bottom').matchHeight();
	$('.top-equal').matchHeight();
    $('.iconText-element').matchHeight();
	doublePromoboxesHeights();
	widePromoboxesHeights();
	imageTextBoxCaptionHeights();
});

$(window).resize(function() {
    clearTimeout(window.resizedFinishedMatchHeight);
    window.resizedFinishedMatchHeight = setTimeout(function(){
    	// equalPromoboxesHeights();
    	doublePromoboxesHeights();
    	widePromoboxesHeights();
    	$('.link-box').matchHeight();
    	$('.equal-height').matchHeight();
    	$('.promobox-top').matchHeight();
    	$('.promobox-bottom').matchHeight();
    	$('.top-equal').matchHeight();
    	imageTextBoxCaptionHeights();
    }, 500);

});

function imageTextBoxCaptionHeights(){
	if( $('.imageTextBox').length > 0 ){
		if( $(window).innerWidth() > breakpointSmallMin ){
			$('.imageTextBox').each(function(){
				if( $(this).find('.img-caption').length > 0 ){
					var totalHeight = $('.col-right').height();
					var captionHeight = $('.img-caption').height();
					var paddings = 16 + 16;
					var pictureHeight = totalHeight - captionHeight - paddings;
					$(this).find('.focuspoint').height(pictureHeight);
				}
			});
		}else{
			$('.imageTextBox').each(function(){
				$(this).find('.focuspoint').height('auto');
			});
		}
	}
}


function doublePromoboxesHeights(){
	if( $(window).innerWidth() > breakpointSmallMin ){
		$('.promobox-bottom').each(function(){
			var ph = $(this).attr('style');
			var pbh = $(this).height();
			var plh = $(this).children('.promobox-left.bottom-equal').height();
			var prh = $(this).children('.promobox-right.bottom-equal').height();

			if(ph == ''){
				if(plh>prh){
					$(this).children('.bottom-equal').height(plh);
				}else{
					$(this).children('.bottom-equal').height(prh);
				}
			}else{
				$(this).children('.bottom-equal').height(pbh);
			}
		});
	}else{
		$('.bottom-equal').height('auto');
	}
	
}

function widePromoboxesHeights(){
	if( $(window).innerWidth() > breakpointSmallMin ){
		$('.promobox-wide').each(function(){
			var plh = $(this).children().children('.promobox-left').height();
			var prh = $(this).children().children('.promobox-right').height();

			if(plh>prh){
				$(this).children().children('.left-right-equal').height(plh);
			}else{
				$(this).children().children('.left-right-equal').height(prh);
			}
		});
	}else{
		$(this).children().children('.left-right-equal').height('auto');
	}
}
/*
function equalPromoboxesHeights(){
	if( $(window).innerWidth() > breakpointSmallMin && $(window).innerWidth() < breakpointMediumMax ){
		$('.promobox-top').each(function(){
			var plh = $(this).children('.promobox-left.top-equal').height();
			var prh = $(this).children('.top-equal').height();

			if(plh>prh){
				$(this).children('.promobox-right.top-equal').height(plh);
			}else{
				$(this).children('.promobox-left.top-equal').height(prh);
			}
	});
		$('.promobox-bottom').each(function(){
			var plh = $(this).children('.promobox-left.bottom-equal').height();
			var prh = $(this).children('.promobox-right.bottom-equal').height();

			if(plh>prh){
				$(this).children('.promobox-right.bottom-equal').height(plh);
			}else{
				$(this).children('.promobox-left.bottom-equal').height(prh);
			}
		});
	}else{
		// $('.top-equal').height('auto');
		// $('.bottom-equal').height('auto');

		$('.equal-height').matchHeight();
    	$('.promobox-top').matchHeight();
    	$('.promobox-bottom').matchHeight();
	}
}
*/
