$('document').ready(function(){


	/*
	 * Dropdown Menu L2 with delay
	 */
	var timer;
	var timer2;
	var timeout2;

	$(".main-nav > li ").on("mouseover", function() {
	  clearTimeout(timer);
	  $this = $(this);
	  if(  $('.main-nav > li.open') == $this ){
	  }

	  if( $('.main-nav').hasClass('dropped') ){
	  	timeout2 = 0;
	  }else{
	  	timeout2 = 1000;
	  }
	 $('.main-nav > li').removeClass('open');
	  if(  $this.children('.navbar-dropdown-container').length > 0 && !$this.hasClass('active')){
		  $this.addClass('open');
	  	  	$('.main-nav').addClass('dropped');
		  timer2 = setTimeout(function(){
		  	$('.navbar-dropdown-container').addClass('navbar-dropdown-dropped');
		  	$('.navbar-dropdown-container ul.sub-nav').removeClass('open');
		  	$this.children('.navbar-dropdown-container').children('ul').addClass('open');
		   }, timeout2);
	  }else{
	  	  $('.main-nav').removeClass('dropped');
	  	  $('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
	  	  $('.main-nav > li').removeClass('open');
	  	  $('.navbar-dropdown-container ul.sub-nav').removeClass('open');
	  }
	}).on("mouseleave", function() {
		clearTimeout(timer2);
	  timer = setTimeout(function(){
	  	$('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
	  	$('.main-nav > li').removeClass('open');
	  	$('.navbar-dropdown-container ul.sub-nav').removeClass('open');
	  }, 600);
	});


	$(".extra-sub-nav li").on("mouseover", function() {
		clearTimeout(timer);
		clearTimeout(timer2);
	  	 $('.main-nav').removeClass('dropped');
		$('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
		$('.main-nav > li').removeClass('open');
		$('.navbar-dropdown-container ul.sub-nav').removeClass('open');
	}).on("mouseleave", function() {
	});


	/*
	 * Dropdown Flyoutmenu toggler
	 */
	$('[data-action="flyout"]').on('click', function(e){ 
		e.preventDefault();


		if( $(this).hasClass('active') ){//close
			if( $(this).closest('.navbar').hasClass('active-is-open') ){
				var addClass = 'out-long';
			}else{
				var addClass = 'out';
			}

			$('.nav-flyout.out').removeClass('out')
			$('.nav-flyout.out-long').removeClass('out-long');
			$('[data-action="flyout"].active').removeClass('active');
			var target = $(this).data('target');
			$(target).removeClass(addClass);

		}else{//open

			if( $(this).closest('.navbar').hasClass('active-is-open') ){
				var addClass = 'out-long';
			}else{
				var addClass = 'out';
			}

			// activeIsOpenHide();

			$('.nav-flyout.out').removeClass('out')
			$('.nav-flyout.out-long').removeClass('out-long');
			$('[data-action="flyout"].active').removeClass('active');
			$(this).toggleClass('active');
			var target = $(this).data('target');
			$(target).addClass(addClass);
			// activeIsOpenHide();
		}

	});


});


/* Catch the srcoll movement*/
/* show/hide mainnav*/
$(function(){
    var _top = $(window).scrollTop();
    var _direction;

    $(window).scroll(function(){
        var _cur_top = $(window).scrollTop();
        if(_top < _cur_top && _cur_top!=0 )
        {
            _direction = 'down';

            // activeIsOpenHide(); 

            $('.navbar').addClass('scrolled');
            $('.navbar-mobile').addClass('scrolled');
            $('.navbar-dropdown-container ul.sub-nav').removeClass('open');
            $('[data-action="flyout"]').removeClass('active');
            $('.nav-flyout').removeClass('out').removeClass('out-long');
            $('.nav-flyout').addClass('scrolled');


        }
        else
        {
            _direction = 'up';


            $('.navbar').removeClass('scrolled');
            $('.navbar-mobile').removeClass('scrolled');
            $('.nav-flyout').removeClass('scrolled');
            $('.nav-flyout').removeClass('out');

            setTimeout(function(){
            	if(_cur_top == 0){
            		// activeIsOpenShow();
            	}
            },1000);
        }


        if(_cur_top > 0 ){
        	$('.scroll-up').fadeIn();
        }else{
        	$('.scroll-up').fadeOut();
        }

        _top = _cur_top;
    });

    $(document).click(function(){
	    $('[data-action="flyout"]').removeClass('active');
        $('.nav-flyout').removeClass('out');
        // activeIsOpenShow();
	});

	$(".navbar").click(function(e){
	    e.stopPropagation();
	});
	$(".nav-flyout").click(function(e){
	    e.stopPropagation();
	});


});

var pos = $(window).scrollTop();
$(function(){
    fixedSubNavi();

    $(window).scroll(function(){
    	pos = $(window).scrollTop();
    	fixedSubNavi();
    });

});
$(window).resize(function(){
   fixedSubNavi();
});

function fixedSubNavi(){
	var subNavOffset = 10;
	if( $('.sidebar-nav-container').length > 0 ){

		if( pos + $('.navbar').height() > $('footer').offset().top - 250  && $(window).width() > 992 ){
			$('.sidebar-nav-container').removeClass('fixed');
			$('.sidebar-nav-container').addClass('absoluteBottom');
			$('.sidebar-nav-container').css('width', $('.col-sidebar').width() );
			$('.sidebar-nav-container').css('height', $('.sub-menu').height() + 32 );
			$('.sidebar-nav-container').css('left','16px');
			$('.sidebar-nav-container').css('top', $('.col-content').height() - $('.sub-menu').height() - 32 );

		}else if( pos + $('.navbar').height() + subNavOffset  > $('#submenuOffset').offset().top  && $(window).width() > 992 ){
			$('.sidebar-nav-container').removeClass('absoluteBottom');
			$('.sidebar-nav-container').addClass('fixed');
			$('.sidebar-nav-container').css('width', $('.col-sidebar').width() );
			$('.sidebar-nav-container').css('left',$('.logo-container').offset().left);
			$('.sidebar-nav-container').css('top',$('.navbar').height() + subNavOffset);
		}else{
			$('.sidebar-nav-container').removeClass('fixed');
			$('.sidebar-nav-container').removeClass('absoluteBottom');
			$('.sidebar-nav-container').css('width','auto');
		}
	}
}

/*
..in progress
*/

/*
$('document').ready(function(){
	if( $('.navbar').hasClass('active-is-open') ){
		activeIsOpenShow();
	}

});

function activeIsOpenShow(){
	if( $('.navbar').hasClass('active-is-open') ){
		$('.navbar.active-is-open').find('.main-nav > li.active').not('.home').addClass('open');
		$('.main-nav > li.active').not('.home').find('.navbar-dropdown-container').addClass('navbar-dropdown-dropped');
		$('.navbar-dropdown-dropped > ul.sub-nav').addClass('open');
	}
}
function activeIsOpenHide(){
	if( $('.navbar').hasClass('active-is-open') ){
		$('.navbar.active-is-open').find('.main-nav > li.active').not('.home').removeClass('open');
		$('.main-nav > li.active').not('.home').find('.navbar-dropdown-container').removeClass('navbar-dropdown-dropped');
		$('.navbar-dropdown-dropped > ul.sub-nav').removeClass('open');
	}
}
*/

