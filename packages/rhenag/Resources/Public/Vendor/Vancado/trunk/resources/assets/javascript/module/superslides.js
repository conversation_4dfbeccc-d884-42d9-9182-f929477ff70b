 $(function() {
      if($('#slides').length>0){
        var $slides = $('#slides');
        Hammer($slides[0]).on("swipeleft", function(e) {
          $slides.data('superslides').animate('next');
        });
        Hammer($slides[0]).on("swiperight", function(e) {
          $slides.data('superslides').animate('prev');
        });
        $slides.superslides({
          hashchange: false,
          play: 9000,
          animation: 'fade',
          animation_speed: 'normal'
        });
        
      }
});



$('#slides').on('animated.slides', function () {
  initialize_focuspoint();
});