if( $('.teaser-home').length > 0 ){

		var timer = null;
		var duration = 12000;//in miliseconds
		var durationProgressBar = duration - 400;//in miliseconds
		var timeout = 60000;//in miliseconds
		var timeoutProgressBar = timeout - 400;//in miliseconds
		var current = $('.teaser-switch-box.active').data('item');
		var countTeasersHome = $('.teaser-switch-box').length;
		var isOperationInProgress = false;


		$(document).ready(function(){
			// setTeaserHomeHeight();

			$('.teaser-switch-box').on('click',function(e){
				e.stopPropagation();
				// e.preventDefault();
				var button = $(this).find('.cta-link');
				console.log(button);
				// button.trigger('click');
				window.location.href = $(button).attr('href');
			});



			$('.teaser-switch-box-hidden').off('click').on('click',function(e){
				e.stopPropagation();
				stopTeaserHome();
				var selectedItem = $(this).data('item');
				playSlide(selectedItem);
				if(!isOperationInProgress){
					setTimeout(function(e){
						playTeaserHome(e);
						isOperationInProgress = false;
					},timeout);
				}
				isOperationInProgress = true;
			});	


		});


		$(document).ready(function(e){
			playTeaserHome(e);
		});
		
		$(window).load(function(e){
			// setTeaserHomeHeight();
			positionTeaserHomeControllerMobile();
		});

		$(window).resize(function() {
		    // setTeaserHomeHeight();

		    clearTimeout(window.resizedFinishedteaserHome);
		    window.resizedFinishedteaserHome = setTimeout(function(){
				positionTeaserHomeControllerMobile();
		    	
		    }, 250);
		});

		function setTeaserHomeHeight(){
			var wh = $(window).height();
			$('.teaser-home').height(wh);
		}



		function playTeaserHome(e){
			startProgressBar();
			timer = setInterval(function(){
				if(current<(countTeasersHome-1)){
					current++;
				}else{
					current = 0;
				}
				playSlide(current);
				startProgressBar();
			},duration);
		}

		function stopTeaserHome(){
			clearInterval(timer);
			stopProgressBar();
		}

		function positionTeaserHomeControllerMobile(){
			if( $(window).width() < 801 ){
				var ch = $('.teaser-switch-box').height();
				$('.teaser-switch-box-container').height(ch);
			}
		}

		function playTeaserHomeNext(){
			if(current<(countTeasersHome-1)){
				current++;
			}else{
				current = 0;
			}
			$('.teaser-switch-box-hidden[data-item="'+current+'"]').trigger('click');
		}

		function playTeaserHomePrevious(){
			if( (current - 1) < 0){
				current = countTeasersHome - 1;
			}else{
				current--;
			}
			$('.teaser-switch-box-hidden[data-item="'+current+'"]').trigger('click');
		}

		function playSlide(slideId){
			if( $('.teaser-video video').length > 0 ){
				$('.teaser-video video').get(0).pause();
			}
			$('.teaser-switch-box').removeClass('active');
			$('.teaser-switch-box-hidden').removeClass('active');
			$('.teaser-bullets li').removeClass('active');
			$('.teaser-switch-box[data-item="'+slideId+'"]').addClass('active');
			$('.teaser-switch-box-hidden[data-item="'+slideId+'"]').addClass('active');
			$('.teaser-item').removeClass('active');
			$('.teaser-item[data-item="'+slideId+'"]').addClass('active');
			$('.teaser-bullets li[data-item="'+slideId+'"]').addClass('active');
			if ( $('.teaser-item[data-item="'+slideId+'"]').hasClass('teaser-video') ){
				if( $(window).width() > 960 ){
					$('#video'+slideId).get(0).play();
				}
			}
			current = $('.teaser-switch-box.active').data('item');
		}

		function startProgressBar(){
			$(".teaser-progressBar").fadeIn(10);
			$(".teaser-progressBar > span").each(function() {
			  $(this)
			    .stop()
			    .width('0px')
			    .animate({
			      width: $('.teaser-progressBar').width()
			    }, {duration: durationProgressBar,easing:'linear'});
			});
		}
		function stopProgressBar(){
			$(".teaser-progressBar > span").each(function() {
				$(this)
				.stop()
				.width('100%')
			    .animate({
			      width: '0px',
			    }, {duration: timeoutProgressBar,easing:'linear'});
			});	
		}




		if( $('#teaser-home').length > 0 ){

			// get a reference to an element
			var stage = document.getElementById('teaser-home');

			// create a manager for that element
			var mc = new Hammer.Manager(stage,{
				touchAction: 'pan-x',
				touchAction: 'pan-y'
			});

			// create a recognizer
			var Swipe = new Hammer.Swipe();

			// add the recognizer
			mc.add(Swipe);

			// subscribe to events
			mc.on('swipeleft', function(e) {
			    // do something cool
			    playTeaserHomeNext();
			});
			mc.on('swiperight', function(e) {
			    // do something cool
			    playTeaserHomePrevious();
			});
		}


}


