if( $('.teaser-landingpage2').length > 0 ){
	$(document).ready(function(){
		if( $('#video').length > 0 ){
			$('#video').get(0).play();
		}
	});
}

if( $('.teaser-landingpage').length > 0 ){

		// var breakpointSmallMin = 750; // min-width: 768px  =  $width > 750px
		var breakpointSmallMin = 983; // min-width: 1000px  =  $width > 983px
		// var teaserLandingpageMinHeight = 940;
		var teaserLandingpageMinHeight = 520;
		var teaserLandingpageMinHeightStop = 940;
		var teaserLandingpageMaxHeight = 1080;
		var teaserLandingpageMinHeightMobile = 280;
		var teaserLandingpageTextHorizontalPosition = 350;
		var teaserLandingpageTextHorizontalPositionMobile = 140;


		$(document).ready(function(){
			setTeaserLandingpageHeight();
			if( $('#video').length > 0 ){
				$('#video').get(0).play();
			}
		});


		$(window).load(function(e){
			setTeaserLandingpageHeight();
		});

		$(window).resize(function() {
		    setTeaserLandingpageHeight();

		    clearTimeout(window.resizedFinishedTeaserLandingpage);
		    window.resizedFinishedTeaserLandingpage = setTimeout(function(){
		    	
		    }, 250);
		});

		function setTeaserLandingpageHeight(){
			if( $(window).innerWidth()>breakpointSmallMin ){
				var wh = $(window).height();
				var heroHeight = wh;

				if( wh >  teaserLandingpageMaxHeight ){
					heroHeight = teaserLandingpageMaxHeight;
				}
				if( wh <  teaserLandingpageMinHeight ){
					heroHeight = 'auto';
					$('.teaser-landingpage').addClass('minHeight');
				}else{
					$('.teaser-landingpage').removeClass('minHeight');
				}

				$('.teaser-landingpage').height(heroHeight);
				$('.teaser-landingpage .teaser-canvas').height(heroHeight);
				$('.teaser-landingpage .teaser-item').height(heroHeight);

				var heroTitlePos;
				if( wh <  teaserLandingpageMinHeight){
					heroTitlePos = '170px';
				}else if( wh <  teaserLandingpageMinHeightStop){
					heroTitlePos = '590px';
				}else if( wh >=  teaserLandingpageMinHeight && wh <=  teaserLandingpageMaxHeight){
					heroTitlePos = wh - teaserLandingpageTextHorizontalPosition;
				}else if( wh >  teaserLandingpageMaxHeight){
					heroTitlePos = teaserLandingpageMaxHeight - teaserLandingpageTextHorizontalPosition;
				}else{
					heroTitlePos = teaserLandingpageMinHeight - teaserLandingpageTextHorizontalPosition;
				}
				$('.teaser-landingpage .heroTitle').css('bottom',heroTitlePos);
				
			}else{
				$('.teaser-landingpage').removeClass('minHeight');
				$('.teaser-landingpage').height('auto');
				$('.teaser-landingpage .teaser-canvas').height('auto');
				$('.teaser-landingpage .teaser-item').height('auto');
			}
		}



}

