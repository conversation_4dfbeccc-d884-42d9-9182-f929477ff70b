if($('.default-form').length > 0){


	//ON Page Scripts
	//
	//more Documentation on:
	// http://jqueryvalidation.org/documentation/
	//
	var validator = $(".default-form").validate({
	  success: "valid",
	  rules: {
	    // simple rule, converted to {required:true}
	    name: "required",
	    // compound rule
	    title: { 
	    		 selectcheck: true //funktioniert nur wenn selectBox nicht angeschaltet ist.
	    },
	    field1: {
	      required: true,
	      email: true
	    },
	    field2: {
	      required: true
	    },
	  }
	});


	jQuery.validator.addMethod('selectcheck', function (value) {
        return (value != '0');
    }, "Bitte wählen");


}