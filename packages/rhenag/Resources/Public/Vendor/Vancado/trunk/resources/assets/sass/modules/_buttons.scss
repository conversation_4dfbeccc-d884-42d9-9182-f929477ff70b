.btn{
	display: inline-block;
	border: 0;
	
	padding: 0.4em 0.8em;
	line-height: 1;
	@include border-radius(1em);
}

.btn-primary, .btn-secondary, .btn-warning {
	background-color: $color-primary;
	color: $white;
	text-decoration: none;
	@include transition($defaultTransition);
	
	margin-top: 1rem;

	&:hover,
	&:focus,
	&:active{
		text-decoration: none;
		color: $white;
		background-color: $color-primary-darker;
		@include transition($defaultTransition);


	}

}
.btn-secondary, .btn-warning {
	background-color: $color-secondary;

	&:hover,
	&:focus,
	&:active{
		background-color: $color-secondary-lighter;
	}
}

.btn-lg{
	padding: 0.6em 0.8em;
	font-size: 1.3rem;
	line-height: 1.1;
	@include border-radius(0.8em);
	font-family: $fontPrimary-light;
}