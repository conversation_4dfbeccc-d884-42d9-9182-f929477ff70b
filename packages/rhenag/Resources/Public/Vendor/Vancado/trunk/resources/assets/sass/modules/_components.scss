/*
 * Conainers
 */
.containerSplit2-row{
	> .col-6:first-child{
		// padding-left: 0;
	}
	> .col-6:last-child{
		// padding-right: 0;
	}

	@include breakpoint($containerMobile){
		> .col-6:first-child{
			// padding: 0;
		}
		> .col-6:last-child{
			// padding: 0;
		}
	}
}

/*
 * GoTop
 */
.scroll-up{
	position: fixed;
	bottom: 2rem;
    right: 2rem;
	
	display: none;
	
	width: 60px;
	height: 60px;

	a{

		width: 60px;
		height: 60px;

		background-color: $bgLight;
		border-radius: 100% 100%;

		&:after{
			content: "\f090";
			font-family: BlackTie;
			font-weight: 200;

			position: absolute;
			left: 50%;
			top: 50%;
			tranform: translate(-50% -50%);
		}
		
	}
}

/*
 * Arrows
 */
.arrow-up{
	width: 0; 
	height: 0; 
	border-left: 12px solid transparent;
	border-right: 12px solid transparent;
	border-bottom: 14px solid $gray;
	border-bottom: 14px solid red;
}


/*
 * Cicles
 */

/*
 * Icons
 */
.icon:before{
	line-height: normal;
}




/*
 * Text with Image
 */
.imageTextBox{
	padding: 1rem 0px;
	background-color: $bgDark;
	margin-bottom: 2rem;

	.col-left{
		@include span(40%);

		@include breakpoint($imageTextBoxSmall){
			@include span(100%);
		}

		.picture-mobile{
			display: none!important;
			@include breakpoint($imageTextBoxSmall){
				display: block!important;
			}
		}

		.focuspoint{
			height: 100%;
			@include breakpoint($imageTextBoxSmall){
				display: none!important;
				max-height: 400px; 

				img{
					display: block;
					max-width: 100%;
					max-height: 100% !important;
					height: auto;
					position: relative;
					top: 0!important;
					left: 0!important;
				}
			}
		}
	}
	.col-right{
		@include span(60%);
		@include last;
		@include breakpoint($imageTextBoxSmall){
			@include span(100%);
		}
	}
}



/*
 * Icon with Text
 */
.iconText-wrapper{
	text-align: center;

	.iconText-container-header{
		margin-bottom: 1rem;
		
	}
	.iconText-container-panels{
		display: table;//IE fallback	
		display: flex;
		-webkit-box-align: center;
		align-items: center;
		-webkit-box-pack: center;
		justify-content: center;
		z-index: 5;
		padding-left: 0;
		padding-right: 0;

		@include breakpoint($iconTextSmall){
			display: block;
		}
		@include breakpoint($iconTextMobile){
			display: block;
		}


		.iconText-element{

			@include span(3 of 12);
			float: none;
			display: inline-block;
			vertical-align: top;

			@include breakpoint($iconTextSmall){
				@include span(6 of 12);
			}
			@include breakpoint($iconTextMobile){
				width: 100%;
				@include span(12 of 12);
				display: block;
				float: none;
			}

				> div{
					padding: 1.5rem 1rem;
					
					.svg-icon{
						margin-bottom: 0.5rem;
					}

					.iconText-header{
						margin-bottom: 1rem;
					}
					.iconText-text{

					}
					
				}
		}
	}
}

/*
 * text-image 
 */
.text-image{
	picture, div {


		&.image-left{
			float: left;
			margin-right: 1rem;
			margin-bottom: 0.5rem;
			z-index:100;
		}
		&.image-right{
			float: right;
			margin-left: 1rem;
			margin-bottom: 0.5rem;
			z-index:100;
		}
		&.image-left-alone{
			float: left;
			margin-right: 1rem;
			margin-bottom: 0.5rem;
		}
		&.image-right-alone{
			float: right;
			margin-left: 1rem;
			margin-bottom: 0.5rem;
		}
		&.image-top{
			float: none;
			margin-bottom: 0.5rem;
		}
		&.image-top-center{
			float: none;
			margin-left: auto;
			margin-right: auto;
			margin-bottom: 0.5rem;
			text-align: center;
			width: 100%;
			display: block;

			img{
				display: inline-block;
			}
		}
		&.image-bottom{
			float: none;
			margin-top: 1rem;
		}
		&.image-bottom-center{
			float: none;
			margin-left: auto;
			margin-right: auto;
			margin-top: 0.5rem;
			text-align: center;
			width: 100%;
			display: block;

			img{
				display: inline-block;
			}
		}

		&.img-size-sm{
			max-width: 25%;
		}
		&.img-size-md{
			max-width: 50%;
		}
		&.img-size-full{
			max-width: 100%;
		}

		@include breakpoint($textImageMobile){
			width: 100%;
			display: block;
			max-width: 100%!important;
			height: auto;
			float: none!important;
			margin-left: 0!important;
			margin-right: 0!important;

			img{
				width: 100%;
				display: block;
				max-width: 100%;
				height: auto;
				margin: 0;
				float: none;
			}
		}

	}
	.text-with-image-left-alone{
		&.text-with-img-size-sm{
			margin-left: 27%;
			@include breakpoint($textImageMobile){
				margin-left: 0;
			}
		}
		&.text-with-img-size-md{
			margin-left: 52%;
			@include breakpoint($textImageMobile){
				margin-left: 0;
			}

		}
		@include breakpoint($textImageMobile){
			margin-left: 0 !important;
		}
	}
	.text-with-image-right-alone{
		&.text-with-img-size-sm{
			margin-right: 27%;
			@include breakpoint($textImageMobile){
				margin-right: 0;
			}
		}
		&.text-with-img-size-md{
			margin-right: 52%;
			@include breakpoint($textImageMobile){
				margin-right: 0;
			}
		}
		@include breakpoint($textImageMobile){
			margin-right: 0 !important;
		}
	}
}



/*
 * Cookie Consent
 */
.cc_container {
	background: $gray!important;
}

.cc_container a, .cc_container a:visited{
	color: $color-primary!important;

	&:hover,
	&:focus{
		color: $color-primary-darker!important;
	}

}

.cc_container .cc_btn, .cc_container .cc_btn:visited{
	background-color: $color-primary!important;
	color: #fff!important;

	&:hover,
	&:focus{
		color: #fff!important;
		background-color: $color-primary-darker!important;
	}
}