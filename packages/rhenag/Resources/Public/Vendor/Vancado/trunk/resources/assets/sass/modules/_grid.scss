
$susy: (
  flow: ltr,
  columns: 12, 
  gutters: 37/94,
  global-box-sizing: border-box,
  gutter-position: inside,
  math: fluid,
  // output: float,
  // last-flow: from
);


$container-style: fluid;





.fluid-container { 
  @include container; 
  max-width: 1400px;

  @include breakpoint($bigTypo){
    max-width: 1600px;
  }
}

.row{
	// margin-bottom: $gutterPercent;
  &:after, &:before{
     content: " "; /* Older browser do not support empty content */
     visibility: hidden;
     display: block;
     height: 0;
     clear: both;
  }
  margin-left: -$gutterPercent;
  margin-right: -$gutterPercent;
}

.col{
  box-sizing: border-box;
}
.col-2{
  @include span(2 of 12);
}
.col-3{
  @include span(3 of 12);

  @include breakpoint($smallScreen){
      @include span(12 of 12);
  }
  @include breakpoint($mediumScreen){
      @include span(6 of 12);
  }
}
.col-4 {
    @include span(4 of 12);
    @include breakpoint($homeNewsMedium){
      @include span(6 of 12);
    }
    @include breakpoint($homeNewsMobile){
      @include span(12 of 12);
    }
}
.col-8 {
    @include span(8 of 12);
    @include breakpoint($homeNewsMedium){
      @include span(6 of 12);
    }
    @include breakpoint($homeNewsMobile){
      @include span(12 of 12);
    }
}
.col-1-5{
   // @include span(1 of 10);
   @include span(2.4);
}
.col-6{
  @include span(6 of 12);

  @include breakpoint($smallScreen){
      @include span(12 of 12);
  }
}
.col-search{
  @include span(6 of 12);
  @include push(6);

  @include breakpoint($smallScreen){
      @include span(12 of 12);
  }
}
.col-12{
  @include span(12 of 12);
}
.col-8-center{
  @include span(8 of 12);
  @include push(2);

  @include breakpoint($teaserHomeMobile){
     @include span(12 of 12);
     @include push(0);

     padding-left: 1rem;
     padding-right: 1rem;
  }
}
.col-mid-center{
  @include span(8 of 12);
  @include push(2);

  @include breakpoint($teaserHomeMobile){
     @include span(12 of 12);
     @include push(0);
  }
  text-align: center;
}
.col-last{
	@include last;
}

.col-sidebar{
   @include span(3 of 12);

   @include breakpoint($sidebarSmall){
      @include span(12 of 12);
       padding: 0 1rem;
   }
   min-height: 1px;
}
.col-content{
   @include span(9 of 12);
    @include breakpoint($sidebarSmall){
      @include span(12 of 12);
      padding: 0 1rem;
   }
}




/*
 * Section
 */
section{
  &.section{
    padding: 2.2rem 0;
    margin: 0;

    &.light{
      background-color: $bgLight; 
    }
    &.dark{
      background-color: $bgDark; 
    }
    &.darker{
      background-color: $bgDarker; 
    }

    &:last-child{
      
    }
  }
}




/*
 * clearfix
 */
.clearfix{
    &:after, &:before{
       content: " "; /* Older browser do not support empty content */
       visibility: hidden;
       display: block;
       height: 0;
       clear: both;
    }
}


/*
 * squareBox
 */
.square-b {
  @include span(2);
  height: 0;
  // %-Padding is always relative to parent width
  padding-top: span(2);
  position: relative;

  // Inner element positioned to fit space
  span {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}


