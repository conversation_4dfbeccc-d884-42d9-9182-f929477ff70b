.svg-icon{
	zoom: 110%;
	// background-color: $color-primary;
	@include border-radius(100%);
	@include background-gradient($iconBgTopColor-primary, $iconBgBottomColor-primary);
	display: inline-block;
    width: 100px;
    height: 100px;

	svg{
		position: relative;

		 // top: 25%;
		 // -webkit-transform: translateY(-50%);
		 // -ms-transform: translateY(-50%);
		 // transform: translateY(-50%);
		 //  left: 50%;
		 // -webkit-transform: translateX(-50%);
		 // -ms-transform: translateX(-50%);
		 // transform: translateX(-50%);


		.cls-1{
			fill: $white;
		}
		.icon{
			fill: $white;

		}
	}


	&.svg-icon-sm{
		zoom: 60%;
	}




	&.svg-icon-green{
		@include background-gradient($iconBgTopColor-green, $iconBgBottomColor-green);
	}
	&.svg-icon-blue{
		@include background-gradient($iconBgTopColor-blue, $iconBgBottomColor-blue);
	}

}
@media (max-width: 992px) {
	.ce-gallery {
		width: 100%;
	}
}

/* Anpassungen von kleinen bis groeßeren Geraeten */
@media (min-width: 640px)  {
	.ce-gallery .ce-column {
		margin: 0;
		/* Abstand zwischen Bildern */
		padding: 0 5px;
		box-sizing: border-box;
		float:left;
	}

	/* Kein Abstand beim ersten und letzten Bild */
	.ce-gallery .ce-column:first-child {
		padding-left: 0;
	}
	.ce-gallery .ce-column:last-child {
		margin-right: 0;
	}

	/* Fluid Image Tags */
	.ce-gallery img,
	.ce-gallery picture {
		width: 100%;
		height: auto;
	}

	/* Spaltenbreiten je nach eingestellten Columns */
	.ce-gallery[data-ce-columns="2"] .ce-column {
		width: 50%;
	}

	.ce-gallery[data-ce-columns="3"] .ce-column {
		width: 33%;
	}

	.ce-gallery[data-ce-columns="4"] .ce-column {
		width: 25%;
	}

	.ce-gallery[data-ce-columns="5"] .ce-column {
		width: 20%;
	}
}

/* Anpassungen fuer kleine Geraete */
@media (max-width: 640px) {

	/* Ein Bild pro Zeile */
	.ce-gallery .ce-column {
		margin: 0 0 10px;
		width: 100%;
		box-sizing: border-box;
	}

	/* Fluid Image Tags */
	.ce-gallery img,
	.ce-gallery picture {
		width: 100%;
		height: auto;
	}
}


footer{
	.ce-gallery img, 
	.ce-gallery picture{
		max-width: 120px;
	}
}