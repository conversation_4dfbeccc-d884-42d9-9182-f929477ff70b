html{
	margin-right: 0!important;
}

body{
	margin: 0;
	overflow-x: hidden!important;
}

.layout-sidebar{
	
}
.col-sidebar{
	margin-bottom: 4rem;
}
.col-content{
	margin-bottom: 4rem;
}

.topSpacer{
	padding-top: $naviHeight + $subNaviHeight + 36;

	@include breakpoint($mobileNav){
		padding-top: $naviHeight + 36;

	}
}
/*
 Campaign Page
 styles for "Kampagnenseite", Nov 2019, mp 	
*/
.layout-campaign-page {
	.page {
		padding-top: 0px;
	}
	.campaign-header {

		text-align: center;
		position: fixed;
		display: block;
		height: 100px;
		width: 100%;
		z-index: 90000;
		background-color:#fff;
		opacity: .85;
		padding:10px;

		a {
			display:inline-block;
		}
	}
	.fluid-container.container-promobox {
		margin-top:2rem;
	}
	.CampaignContact .fluid-container.container-100 .content-wrapper{
		margin:auto 25%;
	}
	.text-align-left,
	.text-image .indent {
		margin:auto 25%;
		
	}

	ul li:before {
		color:#666;
	}

	.layout-campaign-page footer {
		padding: 1.2em 1rem;
		text-align: center;
	}
}


