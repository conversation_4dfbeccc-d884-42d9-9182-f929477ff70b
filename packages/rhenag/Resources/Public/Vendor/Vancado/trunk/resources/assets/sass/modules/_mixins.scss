/// Background Gradient
/// @param {Color} $startColor [#3C3C3C] - Start Color
/// @param {Color} $endColor [#999999] - End Color

@mixin background-gradient($startColor: #3C3C3C, $endColor: #999999) {
    background-color: $startColor;
    background-image: -webkit-gradient(from($startColor), to($endColor));
    background-image: -webkit-linear-gradient($startColor, $endColor);
    background-image:    -moz-linear-gradient($startColor, $endColor);
    background-image:     -ms-linear-gradient($startColor, $endColor);
    background-image:      -o-linear-gradient($startColor, $endColor);
    background-image:         linear-gradient($startColor, $endColor);
    filter:            progid:DXImageTransform.Microsoft.gradient(startColorStr='#{$startColor}', endColorStr='#{$endColor}');
}