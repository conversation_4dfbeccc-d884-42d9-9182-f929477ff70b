/* ---------------------------------------------- /*
 * Mouse animate icon
/* ---------------------------------------------- */

.mouse-icon {
	position: relative;
	border: 2px solid #fff;
    border-radius: 11px;
    height: 45px;
	width: 30px;
	margin: 0 auto;
	display: block;
	z-index: 10;
}

.mouse-icon .wheel {
	-webkit-animation-name: drop;
	-webkit-animation-duration: 1s;
	-webkit-animation-timing-function: linear;
	-webkit-animation-delay: 0s;
	-webkit-animation-iteration-count: infinite;
	-webkit-animation-play-state: running;
	animation-name: drop;
	animation-duration: 1s;
	animation-timing-function: linear;
	animation-delay: 0s;
	animation-iteration-count: infinite;
	animation-play-state: running;
}

.mouse-icon .wheel {
	position: relative;
	border-radius: 10px;
	background: #fff;
	width: 4px;
	height: 10px;
	top: 4px;
	margin-left: auto;
	margin-right: auto;
}

@-webkit-keyframes drop	{
	0%   { top:5px;  opacity: 0;}
	30%  { top:10px; opacity: 1;}
	100% { top:25px; opacity: 0;}
}

@keyframes drop	{
	0%   { top:5px;  opacity: 0;}
	30%  { top:10px; opacity: 1;}
	100% { top:25px; opacity: 0;}
}
