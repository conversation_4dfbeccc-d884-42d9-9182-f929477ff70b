.table__date{
  width: 100%;
  .table__wrap{
    font-size: 16px;
    display: flex;
    flex-direction: row;
    @include breakpoint($smallScreen){
      flex-wrap: wrap;
    }
    .table__left{
      width: 70%;
      @include breakpoint($smallScreen){
        width: 100%;
      }
      &--modules{
        padding-top: 1.5rem;
        .seminar-list-flexlist{
          display: flex;
          gap: 1.5rem;
           @include breakpoint($smallScreen){
             flex-direction: column;
            }
          .event-module--flexitem{
            min-width: 20%;
            font-size: 16px;
          }

        }
      }
    }
    .table__right{
      width: 30%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      @include breakpoint($smallScreen){
        width: 100%;
      }
      a{
        align-self: flex-end;
        @include breakpoint($smallScreen){
          align-self: center;
        }
      }
    }
  }
}

.tx-rhenag-events{
  .accordion{
    .event-listitem{
      border-bottom: 2px dotted #dadada;
      >a{
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      &-item{
        padding-bottom: 1.25rem;
        h5{
          margin: 0;
        }
        p{
          margin-bottom: 0;
        }
      }
       .newSeminarBubble{
         padding: 0px 2px 0px 4px;
         &.new{
           background-color: $green;
         }
         &.online{
           background-color: $gray-dark;
         }
         &.compact {
           background-color: #009987;
         }
       }
    }
  }
}

.seminar-form-flexlist{
  display: flex;
  flex-direction: row;
  gap: 2rem;
  margin-bottom: 1.5rem;
  @include breakpoint($smallScreen){
    flex-direction: column;
  }
  .seminar-list-flexlist{
    .seminar-list-flexitem{
      &:first-child{
        p{
          font-weight: bold;
        }
      }
      p{
        margin-bottom: 0;
      }
    }
  }
}