/*
 *  Fonts
 */


@font-face {
    font-family: 'BlissPro-ExtraLight';
    src: url($fontpath+'BlsPrW-XLt/blsprw-xlt.eot');
    src: url($fontpath+'BlsPrW-XLt/blsprw-xlt.eot?#iefix') format('embedded-opentype'),
         url($fontpath+'BlsPrW-XLt/blsprw-xlt.woff2') format('woff2'),
         url($fontpath+'BlsPrW-XLt/blsprw-xlt.woff') format('woff'),
         url($fontpath+'BlsPrW-XLt/blsprw-xlt.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;

}



@font-face {
    font-family: 'BlissPro-Light';
    src: url($fontpath+'BlsPrW-Lt/blsprw-lt.eot');
    src: url($fontpath+'BlsPrW-Lt/blsprw-lt.eot?#iefix') format('embedded-opentype'),
         url($fontpath+'BlsPrW-Lt/blsprw-lt.woff2') format('woff2'),
         url($fontpath+'BlsPrW-Lt/blsprw-lt.woff') format('woff'),
         url($fontpath+'BlsPrW-Lt/blsprw-lt.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;

}


@font-face {
    font-family: 'BlissPro-Bold';
    src: url($fontpath+'BlsPrW-Bd/blsprw-bd.eot');
    src: url($fontpath+'BlsPrW-Bd/blsprw-bd.eot?#iefix') format('embedded-opentype'),
         url($fontpath+'BlsPrW-Bd/blsprw-bd.woff2') format('woff2'),
         url($fontpath+'BlsPrW-Bd/blsprw-bd.woff') format('woff'),
         url($fontpath+'BlsPrW-Bd/blsprw-bd.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;

}



/*
 *  Fluid Typography
 */

html { 
	font-size: 16px;  
	line-height: 1.5;

	@include breakpoint($fluidTypo){
		font-size: 18px;  
		font-size: calc(16px + (18 - 16) * (100vw - 1400px) / (2500 - 1400));  
		line-height: 1.6;
	}
	@include breakpoint($bigTypo){
		font-size: 18px;  
		line-height: 1.6;
	}

}

code, kbd, pre, samp{
	display: block;
	background-color: #F9F9F9;
	border: 1px dashed #2F6FAB;
	color: black;
	line-height: 1.1em;
	padding: 1em;
	font-size: 0.6em;
	margin-bottom: 2rem;
}
body{
	font-family: $fontPrimary-extralight;
	font-weight: normal;
	color: $textColor;
}
strong{
	font-family: $fontPrimary-bold;
	font-weight: normal;
}
.small{
	font-size: 0.8em;
}
.smaller{
	font-size: 0.6em;
}
.big{
	font-size: 1.2em;
}
.bigger{
	font-family: $fontPrimary-light;
	font-size: 1.4em;
}
.text-inside{
	padding-left: 1.2em;
}
.text-align-left{
	text-align: left;
}
.text-align-center{
	text-align: center;
}
p{
	margin-top: 0;
	margin-bottom: 0.75rem;
}
p.columnized{
	-webkit-column-count: 2; /* Chrome, Safari, Opera */
	   -moz-column-count: 2; /* Firefox */
	   column-count: 2;

	    -webkit-column-gap: 40px; /* Chrome, Safari, Opera */
    -moz-column-gap: 40px; /* Firefox */
    column-gap: 40px;

    @include breakpoint($smallScreen){
    	-webkit-column-count: 1; /* Chrome, Safari, Opera */
    	   -moz-column-count: 1; /* Firefox */
    	   column-count: 1;
    }
}
p.align-center{
	text-align: center;
}

a{
	text-decoration: underline;
	color: $linkColor;

	&:hover,
	&:focus,
	&:active{
		text-decoration: none;
		color: $linkColorHover;
	}


	&.link-with-arrow-down{
		text-decoration: none;
		font-family: $fontPrimary-bold;
		
		&:before{
			color: $color-primary;
			content: "\f099";
			font-family: BlackTie;
			font-weight: normal;
			font-size: 0.65em;
			margin-right: 0.65em;
		}

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;
			&:before{
				text-decoration: none;
			}

		}
	}
	&.link-download{
		text-decoration: none;
		font-family: $fontPrimary-bold;
		
		&:before{
			color: $color-primary;
			content: "\f056";
			font-family: BlackTie;
			font-weight: normal;
			font-size: 0.65em;
			margin-right: 0.65em;
		}

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;
			&:before{
				text-decoration: none;
			}

		}
	}
	&.link-extern{
		text-decoration: none;
		font-family: $fontPrimary-bold;
		
		&:before{
			color: $color-primary;
			content: "\f05b";
			font-family: BlackTie;
			font-weight: normal;
			font-size: 0.65em;
			margin-right: 0.65em;
		}

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;
			&:before{
				text-decoration: none;
			}

		}
	}
	&.link-extern-2{
		padding-right: 20px;
		text-decoration: none;

		&:after{
			content: "\f05b";
			font-family: BlackTie;
			font-weight: normal;
			font-size: 0.65em;
    		margin-left: 0.58em;
		}

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;

			&:after{
				font-weight: 600;
			}

		}
	}

	&.link-normal{
		text-decoration: none;
		color: $linkColor;

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;
			color: $linkColorHover;
		}
	}

	&.link-highlighted{
		text-decoration: none;
		color: $linkColorHover;

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;
			color: $color-primary-darker;
		}
	}

	&.link-some {
		text-decoration: none;

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;

			&:before{
				font-weight: 600;
			}
		}

		&:before {
			display: inline-block;
			width: 1em;
			margin-right: 0.6rem;
		}

	}

	&.link-xing{

		&:before{
			content: "\f168";
			font-family: Font Awesome Brands;
			font-weight: normal;
			font-size: 1em;
		}
	}

	&.link-facebook{

		&:before{
			content: "\f39e";
			font-family: Font Awesome Brands;
			font-weight: normal;
			font-size: 1em;
		}

	}
	&.link-instagram{

		&:before{
			content: "\f16d";
			font-family: Font Awesome Brands;
			font-weight: normal;
			font-size: 1em;
		}

	}
	&.link-youtube{

		&:before{
			content: "\f167";
			font-family: Font Awesome Brands;
			font-weight: normal;
			font-size: 1em;
		}

	}
	&.link-linkedin{

		&:before{
			content: "\f08c";
			font-family: Font Awesome Brands;
			font-weight: normal;
			font-size: 1em;
		}

	}
	&.link-twitter{

		&:before{
			content: "\e61b";
			font-family: Font Awesome Brands;
			font-weight: normal;
			font-size: 1em;
		}

	}
	&.link-callback{
		text-decoration: none;
		font-family: $fontPrimary-bold;

		&:before{
			color: $color-primary;
			content: "\f005";
			font-family: BlackTie;
			font-weight: normal;
			font-size: 0.65em;
			margin-right: 0.65em;
		}

		&:hover,
		&:focus,
		&:active{
			text-decoration: none;

			&:before{
				text-decoration: none;
			}
		}
	}
}
.link-with-arrow{
	text-decoration: none;
	font-family: $fontPrimary-bold;
	
	&:before{
		color: $color-primary;
		content: "\f09b";
		font-family: BlackTie;
		font-weight: normal;
		font-size: 0.65em;
		margin-right: 0.65em;
	}

	&:hover,
	&:focus,
	&:active{
		text-decoration: none;
		&:before{
			text-decoration: none;
		}

	}
}


.img-caption{
	padding: 1rem;
	margin: 0;
	background: $bgDarker;
}

.heroTitle{
	display: inline-block;
	color: $white;
	background-color: $color-primary;
	padding: 0.3em 0.7em;
	font-size: 2.5rem;
	line-height: 1;
}
.NoBackground {
	display: inline-block;
	color: $white;
	background-color: transparent;
	padding: 0.3em 0.7em;
	font-size: 1.6rem;
	line-height: 1;
}

.heroSubtitle{
	display: inline-block;
	color: $white;
	background-color: $color-secondary;
	padding: 0.5em 1em;
	font-size: 1.5rem;
	line-height: 1.1;
}
.heroSubtitleNoBackground {
	color: #666666;
	display: block;
	font-family: BlissPro-Bold;
	font-size: 0.5em;
	font-weight: normal;
	margin-top: 0.4rem;
}

.promobox-title{
	font-size: 2rem;
	margin-bottom: 1rem;
	margin-top: 0;
	line-height: 1.2;
	color: $color-secondary;
}
.teaser-switch-title{
	font-size: 1.5rem;
	margin-bottom: 0.5rem;
	font-family: $fontPrimary-light;
	margin-top: 0;
	line-height: 1.2;
	color: $color-secondary;
	font-weight: normal;
}
.teaser-landingpage-title{
	font-family: $fontPrimary-light;
	font-size: 1.4rem;
	margin-bottom: 0.5rem;
	margin-top: 0;
	line-height: 1.2;
	color: $color-secondary;
	font-weight: normal;
}	
.teaser-special-title{
	font-size: 2rem;
	margin-bottom: 1rem;
	margin-top: 0;
	line-height: 1.2;
	color: $color-secondary;
}	
.teaser-special-subtitle{
	font-family: $fontPrimary-light;
	font-size: 1.2rem;
	margin-bottom: 0.4rem;
	margin-top: 0;
	line-height: 1.2;
	color: $color-primary;
}	
.h1,
.h1-align-center{
	font-family: $fontPrimary-extralight;
	font-size: 2.7rem;
	line-height: 1.2;
	margin-top: 0;
	margin-bottom: 2rem;
	font-weight: normal;
}
.h1-alternative,
.h1-alternative-align-center{
	font-family: $fontPrimary-extralight;
	font-size: 2.7rem;
	line-height: 1.2;
	margin-top: 0;
	margin-bottom: 2rem;
	font-weight: normal;
	color: $color-secondary;
}
.h2,
.h2-align-center{
	font-family: $fontPrimary-light;
	font-size: 1.8rem;
	line-height: 1.2;
	margin-top: 4rem;
	margin-bottom: 1rem;
	font-weight: normal;
}
.h2-alternative,
.h2-alternative-align-center{
	font-family: $fontPrimary-light;
	font-size: 1.8rem;
	line-height: 1.2;
	margin-top: 2rem;
	margin-bottom: 1rem;
	font-weight: normal;
	color: $color-secondary;
}
.h3,
.h3-align-center{
	font-family: $fontPrimary-light;
	font-size: 1.4rem;
	line-height: 1.1;
	margin-top: 0;
	margin-bottom: 1rem;
	font-weight: normal;
}
[class*="align-center"]{
	text-align: center;
}
.preline{
	font-family: $fontPrimary-light;
	font-size: 1rem;
	line-height: 1.1;
	margin-top: 0;
	margin-bottom: 0.5rem;
	font-weight: normal;
}
ul{
	list-style: none;
	margin-top: 0;
	margin-bottom: 1rem;
	padding-left: 0.5rem;

	li{
		position: relative;
		padding-left: 1.5rem;

		&:before {
		    content: "■";
		    color: $color-secondary;
		    position: absolute;
		    top: -2px;
		    left: 0;

		}

	}
}


/*
 * Text Colors
 */
.text-color-primary{
	color: $text-color-primary;
}
.text-color-green{
	color: $green;
}
.text-color-blue{
	color: $blue;
}


/*
 * Header and Footer Title and Lists
 */
.col-title{
	border-bottom: 2px solid $color-primary;
	font-family: $fontPrimary-light;
	white-space: nowrap;
	color: $white;
	padding-bottom: 6px;
	margin-bottom: 0.6rem;

	> p{
		margin: 0;
	}
}
ul.col-list{
	padding: 0;
	margin: 0;
	margin-top: 0;
	margin-bottom: 1rem;
	list-style: none;

	li{
		padding-left: 0;
		padding-bottom: 0.3rem;

		&:before{
			display: none;
		}

		a{
			text-decoration: none;

			&:hover,
			&:focus,
			&:active{
				text-decoration: none;
			}
		}
	}
}






