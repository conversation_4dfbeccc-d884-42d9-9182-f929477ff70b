/*
 * Colors
 */
$white: #fafafa !default;
$bgLight: #f8f8f8 !default;
$bgLighter: #eeeeee !default;
$bgDark: #f2f2f2 !default;
$bgDarker: #e5e5e5 !default;
$orange: #ff8d2f !default; //primary color
$orangeLight: #f8ede4 !default; //
$orangeAlternative: #ea7322 !default; //text primary color
$lila: #140043 !default; //secondary color
$lila-light: #392b5b !default; 
$gray: #666666 !default; // text color, footer bg, fyout bg
$gray-dark: #313131;
$gray-light: #a3a3a3 !default; //background gray
$light-green: #e1ece8 !default;
$green: #83b09f !default;
$dark-green: #56796d !default;
$blue: #587799 !default;
$error-red: #ff0000 !default;
$red: #b40000 !default;
$green: #7cbf12;






/*
 * general Color Variables
 */
$color-primary: $orange;
$color-primary-darker: darken($color-primary,20%);
$color-secondary: $lila;
$color-secondary-lighter: lighten($color-secondary,20%);


$text-color-primary: $orangeAlternative;
$textColor: $gray;
$linkColor: $gray;
$linkColorHover: $orangeAlternative;

$iconBgTopColor-primary: #f68b37 !default;
$iconBgBottomColor-primary: #e97322 !default;
$iconBgTopColor-green: #2de0ac !default;
$iconBgBottomColor-green: #09ce95 !default;
$iconBgTopColor-blue: #68aeff !default;
$iconBgBottomColor-blue: #489dff !default;


/*
 * Fonts
 */
$fontpath: '../fonts/BlissPro/' !default;
$fontPrimary-bold: 'BlissPro-Bold'; 
$fontPrimary-light: 'BlissPro-Light'; 
$fontPrimary-extralight: 'BlissPro-ExtraLight'; 


$baseFontSize: 16px;
$baseLineHeight: 24px;

$headerFontSize: 22px;
$headerLineHeight: 1.2;
$subHeaderFontSize: $headerFontSize - 4px; //dropdown navi
$subMenuFontSize: $headerFontSize - 2px; //Submenu fixed
$footerFontSize: 18px;
$footerLineHeight: 1.2;
$subNavFontSize: $headerFontSize - 2px;
$subNavLineHeight: 1.4;

/*
 * Transitions
 */

$defaultTransition: all, 0.25s, ease-in-out;
$naviTransition: all 0.5s ease-out 0.25s; 
$naviArrowTransition: all 0.4s ease-out 1s;
$teaserHomeImageTransition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;
$teaserLandingpageImageTransition: opacity 2s cubic-bezier(0.45, 0.05, 0.55, 0.95) 0.1s;


/*
 * Text Shadows
 */
$textShadowDefault: 2px, 2px, 5px, 5px, rgba(0, 0, 0, 0.4);


/*
 * Grid
 */
$gutterPercent: 1.1768447837%;
$sectionMargin: 4rem;

/*
 * Breakpoints
 */
$verySmallScreen: max-width 480px;
$smallScreen: max-width 767px;
$mediumScreen: 768px 992px;
$mobileNav: max-width 1250px;
$mediumNav: 1171px 1330px;
$fluidTypo: 1400px 2500px;
$bigTypo: min-width 2501px;
$teaserHomeMobile: max-width 960px;
$teaserHomeSmall: 961px 1170px;
$teaserHomeMedium: 1171px 1300px;
// $teaserLandingpageMobile: max-width 767px;
$teaserLandingpageMobile: max-width 1000px;
$teaserLandingpageLarge: min-width 1001px;
$teaserSpecialSmall: max-width 1023px;
$teaserSpecialMedium: max-width 1700px;
$promoboxMobile: max-width 992px;
$promoboxDoubleTablet: 768px 992px;
$promoboxDoubleMobile: max-width 767px;
$promobox3Columns: 993px 1300px;
$promoboxDesktop: min-width 1301px;
$promoboxWideMobile: max-width 767px;
$sidebarSmall: max-width 992px;
$imageTextBoxSmall: max-width 767px;
$testimonialsMobile: max-width 767px;
$linkBoxesMobile: max-width 767px;
$linkBoxesMedium: max-width 992px;
$infoBoxMobile: max-width 767px;
$iconTextSmall: max-width 992px;
$iconTextMobile: max-width 767px;
$textImageMobile: max-width 767px;

$formTablet: max-width 992px;
$formMobile: max-width 544px;

$containerMobile: max-width 767px;
$contactMobile: max-width 544px;

$eventMobile: max-width 767px;

$homeNewsMobile: max-width 544px;
$homeNewsMedium: max-width 767px;

$listNewsMedium: max-width 767px;
$listNewsMobile: max-width 544px;




/*
 * zIndex
 */
// $zIndex_page: 1;
$zIndex_footer: 0;
$zIndex_navbar: 100;
$zIndex_navbarDropdown: 90;
$zIndex_navbarDropdownArrow: 95;
$zIndex_navbarFlyOut: 110;


/*
 * Navi
 */
$naviHeight: 190px !default; /* Erhöht wegen Meta-Navigation + Extra-Sub-Nav */
$naviHeightLong: 240px !default; /* Entsprechend erhöht */
$naviHeightScrolled: 175px !default; /* Entsprechend erhöht */
$subNaviHeight: 72px !default;
$flyoutTop: 188px !default; /* Entsprechend erhöht */


/*
 * Teasers
 */

$teaserHomeMinHeight: 720px !default;
$teaserHomeMinHeightMobile: 280px !default;
$teaserHomeTextHorizontalPosition: 350px !default;
$teaserHomeTextHorizontalPositionMobile: 140px !default;
$teaserHomeTextHorizontalPositionSmall: 150px !default;
$teaserHomeTextHorizontalPositionMedium: 250px !default;

$teaserLandingpageMinHeight: 940px !default;
// $teaserLandingpageMaxHeightAlternative: 720px !default;
$teaserLandingpageMaxHeightAlternative: 520px !default;
$teaserLandingpageMinHeightMobile: 280px !default;
$teaserLandingpageTextHorizontalPosition: 350px !default;
$teaserLandingpageTextHorizontalPositionMobile: 140px !default;

// $teaserSidebarMinHeight: 640px !default;
$teaserSidebarMinHeight: 468px !default;
$teaserSidebarMinHeightMobile: 280px !default;

$teaserSpecialMinHeight : 468px;



/*
 * Promoboxes
 */
$promoboxMinHeight: 330px;
$promoboxMinHeightMobile: 250px;