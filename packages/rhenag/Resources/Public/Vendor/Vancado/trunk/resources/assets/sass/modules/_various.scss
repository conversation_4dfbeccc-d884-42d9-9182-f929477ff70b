//ONLY For DEV
body{
	background-color: $bgLight;
}


*{
	// @include transition($defaultTransition);

	 box-sizing: border-box;
}
div{
	 position: relative;
}

.clearfix:after {
     visibility: hidden;
     display: block;
     font-size: 0;
     content: " ";
     clear: both;
     height: 0;
     }
.clearfix { display: inline-block;  clear: both; }
/* start commented backslash hack \*/
* html .clearfix { height: 1%; }
.clearfix { display: block; }
/* close commented backslash hack */