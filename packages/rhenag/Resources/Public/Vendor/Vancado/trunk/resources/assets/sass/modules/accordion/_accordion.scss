/*
 * GLOBAL ACCORDION STYLES
 */

.accordion dd,
.accordion__panel {
   width: 100%;
   margin-left: 0;
}

.accordion {
    position:relative;
    margin-bottom: 2em;
}

.accordionTitle,
.accordion__Heading {
  display:block;
  text-decoration:none;
}
.accordionTitleActive, 
.accordionTitle.is-expanded {

}
.accordionItem {
    height:auto;
    overflow:hidden; 
    //SHAME: magic number to allow the accordion to animate
    
    max-height: 100000px;
    transition:max-height 1s;   
    
    @media screen and (min-width:48em) {
         max-height: 100000px;
        transition:max-height 0.5s
    }
}
 
.accordionItem.is-collapsed {
    max-height:0;
}
.no-js .accordionItem.is-collapsed {
  max-height: auto;
}
.animateIn {
     animation: accordionIn 0.45s normal ease-in-out both 1; 
}
.animateOut {
     animation: accordionOut 0.45s alternate ease-in-out both 1;
}
@keyframes accordionIn {
  0% {
    opacity: 0;
    transform:scale(0.9) rotateX(-60deg);
    transform-origin: 50% 0;
  }
  100% {
    opacity:1;
    transform:scale(1);
  }
}

@keyframes accordionOut {
    0% {
       opacity: 1;
       transform:scale(1);
     }
     100% {
          opacity:0;
           transform:scale(0.9) rotateX(-60deg);
       }
}



