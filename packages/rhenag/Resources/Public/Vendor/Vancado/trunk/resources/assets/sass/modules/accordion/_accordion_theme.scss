.accordion-container{
	margin-bottom: 1.5rem;
}
.accordion {
	margin-bottom: 0;
}


.accordion-default{
	dl{
		margin: 0;
	}
	dt{
		margin-top: 0.5rem;
	}

	.accordionTitle{
		text-align: center;
		padding: 0.5rem 0;
	}
	.accordion-content{
		text-align: center;
		
		.accordion-content-wrapper{
			text-align: center;
		}
	}

}



.accordion-secondary{

	dl{
		margin: 0;
	}
	dt{
		margin-top: 0.5rem;
	}
	.accordionTitle{
		background-color: $bgDarker;
		position: relative;
		font-size: 1.2rem;
		line-height: 1.4;
		padding: 10px 65px 10px 15px;

		&:after{
			content: "\f091";
			color: $color-primary;
			font-family: BlackTie;
			font-weight: normal;
			font-size: 1rem;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 30px;
			@include transition(all 0.4s);				
			margin-top: -10px;			
			transform: rotate(180deg);
		}

		&.is-collapsed{
			&:after{
				transform: rotate(0deg);
				margin-top: -10px;			
				@include transition(all 0.4s);	
			}
		}
	}

	.accordion-content-wrapper{
		padding: 30px 20px;
		background-color: $bgLighter;
	}

}