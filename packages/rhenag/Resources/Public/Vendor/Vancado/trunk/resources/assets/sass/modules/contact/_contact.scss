.contact-item {

	margin-bottom: 2rem;

	.contact-image {
		
		text-align: center;
		// @include span(4 of 12);
		width: 150px;
		height: 150px;
		max-width: 150px;
		overflow: hidden;
		border-radius: 50%;
		float: left;

		img {

		}
	}

	.contact-info {


		// @include span(8 of 12 last);
		// width: 75%;
		float: left;
		
		> div {
			padding-left:10px;


			p{
				margin-bottom: 0.5rem;
			}
		}
	}

	@include breakpoint($contactMobile){

		.contact-image {
			@include span(12 of 12);
			float: none;
			margin: 0 auto 0.75rem;


			img {
				display: inline-block;
			}
		}

		.contact-info {
			text-align: center;
			@include span(12 of 12);
			// width: 100%;
			// float: left;

			// @include span(12 of 12);
		}
	}
}