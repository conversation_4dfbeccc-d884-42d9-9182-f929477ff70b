ul.events-list {
	li {
		padding-left:0;
		margin-bottom: 1.25rem;
		line-height: 1.25rem;

		&:before {
			display: none;
		}
		a {

			text-decoration: none;

			.event-date, .event-title, .event-city {
				display: inline-block;
			}

			.event-date {
				width: 130px;
				position: relative;

				&:after {
					content: "\00B7";
					position: absolute;
					right: 5px;
				}

				&.date-only {
					width: 80px;
				}
			}

			.event-city {
				width: 130px;
				position: relative;
			}


			.event-title {
				width: calc(100% - 280px);
				position: relative;
				font-weight: bold;

				&:after {
					content: "\00B7";
					position: absolute;
					right: 5px;
				}
			}

			.event-bookedout {
				background-color: $orange;
				padding: 3px 5px;
				color: $white;
				border-radius: 5px;
				font-size: 12px;
				margin-left: 10px;
			}

			@include breakpoint($eventMobile){
				.event-city {
					width: auto;
				}
				.event-title {
					width: 100%;
					&:after {
						display: none;
					}
				}
			}
		}
	}
}

.home-events {
	background: $gray;

	&.events-2 {
		.home-events-item {
			@include span(6 of 12);

			&.last {
				@include span(6 of 12 last);
			}

			@include breakpoint($homeNewsMedium){
				@include span(12 of 12);

				&.last {
					@include span(12 of 12);
					display: none;
				}
			}

			@include breakpoint($homeNewsMobile){
				&.last {
					display: block;
				}
			}
		}
	}

	&.events-3 {
		.home-events-item {
			@include span(4 of 12);

			&.last {
				@include span(4 of 12 last);
			}

			@include breakpoint($homeNewsMedium){
				@include span(6 of 12);

				&.last {
					display: none;
				}

			}

			@include breakpoint($homeNewsMobile){
				@include span(12 of 12);
				&.last {
					@include span(12 of 12);
					display: block;
				}
			}
		}
	}

	.home-events-item {
		padding: 1rem !important;
		color: $white;

		

		.event-header {
			color: $orange;
		}

		.link-with-arrow {
			color: $white;
		}
	}
}

.events-navigation {
	margin-top: 2rem;
	background-color: #fff;
	padding: 1rem;
}