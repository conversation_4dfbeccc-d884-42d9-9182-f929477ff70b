.info-box {
	padding: 1rem;
	margin-bottom: 3rem;	
	background: $bgLighter;

	&.info-box-green {
		background: $light-green;
	}
	
	&.info-box-left {

		// width: 48%;
		// float: left;
		@include breakpoint($infoBoxMobile){
			@include span(8 of 8 last);
		}
	}

	&.info-box-right {

		// width: 48%;
		// float: right;
		@include breakpoint($infoBoxMobile){
			@include span(8 of 8 last);
		}
	}


	.icon-text {
		padding: 1rem 1rem 1rem 2.1rem;

		.svg-icon {
			position: absolute;
			left: 0;
			width: 1.875rem;
			height: 1.875rem;
		}
	}

	h3 {
		color: $lila;
	}
	b, strong {
		color: $gray;
	}


}

.info-box-green {

	h3 {
		color: $dark-green;
	}
	b, strong {
		color: $dark-green;
	}
}
