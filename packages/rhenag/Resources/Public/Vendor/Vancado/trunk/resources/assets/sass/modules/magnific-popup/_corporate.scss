.image-popup-link{
	cursor: pointer;

	&:hover,
	&:focus,
	&:active{
		outline: none;
	}


	picture{
		position: relative;
		// img{
			&:before{
				position: absolute;
				content: "";
				width: 100%;
				height: 100%;
				background: rgba(0,0,0,0);
				@include transition($defaultTransition);
			}
			&:after{
				content: "\f002";
				font-family: BlackTie;
				font-weight: 400;
				font-size: 1.5rem;
				line-height: 1.4;
				color: $white;
				opacity: 0.5;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -30%);
				@include transition($defaultTransition);
			}
		// }

	}

	&:hover,
	&:focus{
		picture{
			// img{
				&:before{
					background: rgba(0,0,0,0.5);
					@include transition($defaultTransition);
				}
				&:after{
					opacity: 1;
					transform: translate(-50%, -50%);
					@include transition($defaultTransition);
				}
			// }
		}
	}

}


.mfp-zoom-out-cur{
	cursor: inherit;
}
.mfp-zoom-out-cur .mfp-image-holder .mfp-close{
	cursor: pointer;
}



.mfp-image-holder .mfp-close, .mfp-iframe-holder .mfp-close{
	padding-right: 0;
	position: fixed;
	top: 40px;
	right: 40px;
}

.mfp-close{

	&:after{
		content: "\f00c";
		font-family: BlackTie;
		font-size: 1.5rem;
		line-height: 1.4;
		color: $bgLight;
		position: fixed;
		width: 2.25rem;
    	height: 2.25rem;
    	top: 40px;
    	right: 40px;
		@include transition($defaultTransition);
	}

	&:hover,
	&:focus{
		color: $white;
		@include transition($defaultTransition);
	}
}




//ZOOM
.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  /* ideally, transition speed should match zoom duration */
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
    opacity: 1;
}
.mfp-with-zoom.mfp-ready.mfp-bg {
    opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container,
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}