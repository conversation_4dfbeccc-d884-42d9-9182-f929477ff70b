/* fix webkit overflow sroll-x */
.mm-slideout{
	overflow-x: hidden; 
}

/* fix top position */
.mm-slideout{
	.teaser-home .teaser-canvas{
		margin-top: 0;
	}
}



.navbar-mobile{
	display: none;

	position: fixed;
	top: 0;
	left: 0;
	height: auto;
	width: 100%;
	@include background-opacity($white, 0.9);
	z-index: $zIndex_navbar;

	height: $naviHeight;

	-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0s; 
	   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0s; 
	     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0s; 
	        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0s; /* easeOutQuad */

	-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

	&.scrolled{

		top: -$naviHeight;

		-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

		-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */
	}


	@include breakpoint($mobileNav) {
			display: block;
	}
	
	.logo-container{
		position: absolute;
		left: $gutterPercent;
		top: 10px;
		max-width: 211px;

	}
	.mobileMenuButton{
		position: absolute;
		top: 0;
		right: $gutterPercent;
		padding: 8px 12px;
	    border: 1px solid $color-primary;
		@include border-radius(3px);
		top: 50%;
		-webkit-transform: translateY(-50%);
		-ms-transform: translateY(-50%);
		transform: translateY(-50%);

		span{
		    display: block;
			width: 34px;
			height: 2px;
			@include border-radius(15px);
			border: 2px solid $color-primary;

			+span{
				margin-top: 5px;
			}
		}

		&:hover,
		&:focus,
		&:active{
			background-color: $color-primary-darker;
			border-color: $color-primary-darker;

			span{
				border-color: $white;
			}
		}
	}
}

#mobileNav{
	display: none;
	background: #444;
	color: $white;
	font-family: "BlissPro-Light";
	font-weight: normal;
	font-size: 1.3rem;
	line-height: 1.1;

	@include breakpoint($mobileNav){
		&.mm-opened{
			display: block;
		}
	}

	visibility: hidden;
	
	&.mm-menu{
		visibility: visible;
	}
	.mm-panel{
		.mm-navbar{
			border-bottom: 1px solid $gray;

			.mm-title{
				color: $white;

			}
			a{
				&:before{
					border-color: $white;
				}
			}

		}
		ul.mm-listview{
			font-size: inherit;

			li{
				padding-left: 0;

				&:before{
					display: none;
				}
				&:after{
					border-color: $gray;
					left: 0;
				}
				a{

					&:before{
						border-color: $gray;
					}
					&:after{
						border-color: $white;
					}
				}
				&:hover,
				&:active,
				&:focus{
					a{
						color: $color-primary;
					}
				}

				&.active{
					a{
						color: $color-primary-darker;

					}
				}

				&.search-container{
					padding: 10px 10px 10px 20px;

					.search-query{
						width: 80%;
						border: 1px solid $gray;
						border-radius: 0px;
						padding: 4px 10px;
						text-transform: capitalize;
						color: $textColor;

						&:hover,
						&:focus,
						&:active{
							outline: none;
						}
					}

					button{
						border: none;
						background-color: transparent;

						position: absolute;
						top: 17px;
						right: 10px;

						&:hover,
						&:focus,
						&:active{
							outline: none;
						}
					}
				}

				&.meta-item{
					background-color: lighten(#444,5%);
				}

			}
		}
	}
}
