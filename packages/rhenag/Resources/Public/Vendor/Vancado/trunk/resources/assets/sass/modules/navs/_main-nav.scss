.navbar{
	display: block;
	@include breakpoint($mobileNav) {
			display: none;
	}

	position: fixed;
	top: 0;
	left: 0;
	height: auto;
	width: 100%;
	@include background-opacity($white, 1);
	z-index: $zIndex_navbar;
	@include opacity(0.85);
	border-bottom: 1px solid #d7d7d7;

	// height: $naviHeight;

	-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0.5s; 
	   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0.5s; 
	     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0.5s; 
	        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 0.5s; /* easeOutQuad */

	-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

	#navi-contact{
		position: fixed;
		top: -100px;
		right: 10%;

		background-color: $color-primary;
		a{
			color: $white;
			i{
				color: $white;
			}
		}

		&:hover,
		&:focus,
		&:active{
			background-color: $color-primary-darker;
		}

		-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

		-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */
		
	}


	&.scrolled{
		top: -$naviHeight;

		-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

		-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
		        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

		#navi-contact{
			top: 0;

			-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 1s; 
			   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 1s; 
			     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 1s; 
			        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940) 1s; /* easeOutQuad */

			-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

    		
		}

		&.active-is-open{
			&.scrolled{
				top: -$naviHeightLong;
			}
		}

	}


	.fluid-container{
		position: relative;
		.logo-container{
			position: absolute;
			left: $gutterPercent;
			top: 10px;
			max-width: 211px;
		}
		.main-nav-container{
			text-align: center;
			margin-left: auto;
			margin-right: auto;
			margin-bottom: 30px;

			ul.main-nav{
				margin: 0;
				padding: 0;
				font-size: $headerFontSize;
				line-height: $headerLineHeight;
				z-index: $zIndex_navbar;
				list-style: none;

				
				li{
					@include breakpoint($mediumNav){
						font-size: 75%;
					}

					display: inline-block;
					font-size: $headerFontSize - 2px; // Kleinere Schrift
					line-height: $headerLineHeight;
					color: $color-secondary;
					font-family: $fontPrimary-light;
					z-index: $zIndex_navbar;
					padding-left: 0;

					&:before{
						display: none;
					}

					a{
						color: $color-secondary;
						text-decoration: none;
						padding-left: 15px; // Größere Abstände
						padding-right: 15px; // Größere Abstände
						font-family: $fontPrimary-light;
						@include transition($defaultTransition);

						&:hover{
							color: $color-primary;
							@include transition($defaultTransition);
						}
					}

					&.open{
						a{
							color: $color-primary;

						}

						.navbar-dropdown-container{
							&.navbar-dropdown-dropped{
								&:before{
									opacity: 1;
								}
							}
						}

					}

					&.home{
						a{
							i{
								color: $color-secondary;
								font-size: 26px;
								@include transition($defaultTransition);
							}
							&:hover{
								i{
									color: $color-primary;
									@include transition($defaultTransition);
								}
								
							}
						}
					}

					&.active{
						> a{
							font-family: $fontPrimary-bold;
							// font-weight: 600;
							color: $color-primary;


						}
						&.has-sub{
							> a{
								&:after{
										bottom: -46px;

										content: "";
										width: 0;
										height: 0;
										position: absolute;
										left: 50%;
										margin-left: -10px;
										width: 20px;
										height: 20px;
										transform: rotate(-45deg);
										box-shadow: 1px -2px 0px #ccc;
										z-index: 85;
										background: #f0f0f0;
								}
							}
						}
						&.home,
						&.open{
							> a{
								&:after{
									// display: none;
								}
							}
						}
					}

					
					/*
					 * Second Level Menu
					 */


					.navbar-dropdown-container{
						@include background-opacity($gray, 0.8);
						background: $color-primary;
						position: absolute;
						width: 100%;
						min-width: 250px;
						top: 41px;
						left: 50%;
						z-index: $zIndex_navbarDropdown;
						transform: translateX(-50%);

						@include transition($naviTransition);
					    opacity: 0; 
					    height: 0;

					    &:before{
					    	opacity: 0;
					    	top: -14px;
					    	
					    	content: "";
					    	width: 0; 
					    	height: 0; 
					    	border-left: 12px solid transparent;
					    	border-right: 12px solid transparent;
					    	border-bottom: 14px solid $color-primary;
					    	position: absolute;
					        left: 50%;
					        margin-left: -10px;
					        z-index: $zIndex_navbarDropdownArrow;
					    }


						&.navbar-dropdown-dropped{
							@include transition($naviTransition);
							opacity: 1;
					    	height: auto;
					    	box-shadow: 0px 0px 20px 1px rgba(0,0,0,0.3);


							ul.sub-nav.open{
								display: block;
							}

						}

						ul.sub-nav{
							display: none;
							margin: 0;
							padding: 0;
							text-align: left;
							line-height: 0;
							padding: 20px 0;
							

							li{
								color: $white;
								display: block;
								font-family: $fontPrimary-light;
								font-size: $subHeaderFontSize;
								line-height: $headerLineHeight;

								a{
									color: $white;
									padding: 4px 15px;
									text-decoration: none;
									font-family: $fontPrimary-light;
									font-size: $subHeaderFontSize;
									line-height: $headerLineHeight;
									display: block;
									position: relative;
									@include transition($defaultTransition);

									
									&:after{
										display: none;
									}

									&:hover{
										color: $lila;
										@include transition($defaultTransition);
									}

								}
								&:last-child{
									a{
										&:after{
											// display: none;
										}
										
									}

								}

							}
						}
					}

				}

			}
		}
		// Meta navigation styles moved to _meta-nav.scss
	}

}
.nav-flyout{
	font-size: $footerFontSize;
	line-height: $footerLineHeight;
	background-color: $gray;
	color: $white;
	position: fixed;
	top: -1050px;
	left: 0;
	width: 100%;
	padding-top: 2rem;
	padding-bottom: 2rem;
	z-index: $zIndex_navbarFlyOut;

	@include breakpoint($mobileNav){
		display: none;
	}

	> .fluid-container > .row{
		margin: 0;
	}


	-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

	-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
	        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */


	&.out,
	&.out-long,
	{
		top: $flyoutTop;

		&.scrolled{
			top: $naviHeightScrolled;
		}

		-webkit-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			   -moz-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			     -o-transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			        transition: top 500ms cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */

			-webkit-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			   -moz-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			     -o-transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); 
			        transition-timing-function: cubic-bezier(0.250, 0.460, 0.450, 0.940); /* easeOutQuad */
	}
	&.out-long{
		top: $flyoutTop;
	}


	.arrow-up{
		position: absolute;
		right: 0;
		top: -2rem;
		margin-top: -14px;
		z-index: $zIndex_navbar;
	}

	
	a{
		color: $white;
		text-decoration: none;

		&:hover,
		&:focus,
		&:active{
			color: $color-primary;
			text-decoration: none;
		}
		
		
		&.link-highlighted{
			color: $color-primary;
		
			&:hover,
			&:focus,
			&:active{
				color: $color-primary-darker;
				text-decoration: none;
			}
		}

	}

	.box-special{
		background-color: $white;
		color: $gray;
		padding: 1rem;
		margin-bottom: 1rem;

		a.link-with-arrow{
			color: $gray;

			&:hover,
			&:active,
			&:focus{
				color: $linkColorHover;
			}
		}

		p{
			&:last-child{
				margin-bottom: 0;
			}
		}
	}
	
	&#flyout-search{
		text-align: left;
		line-height: 1;

		.row{
			margin-bottom: 0;
		}


		.searchTitle{
			display: inline-block;

		}
		form{
			display: inline-block;
			width: 	100%;

			input{
				// margin-left: 20px;
				margin-right: 15px;
				min-width: 83%;
				padding: 8px;
				color: #fff;
				border-radius: 0;
				background-color: transparent;
				border: none;
				border-bottom: 1px solid #a3a3a3;

				&:focus{
					outline: none;
				}

			}
			.submit-container{
				width: 50px;
				height: 50px;
				display: inline-block;
				float: right;

				button{
					background: transparent;
					border: none;
					padding: 0;
					margin-top: 11px;
					font-size: 1.4rem;
					outline: none;

					@include transition(all 0.2s ease-out);

					i{
						color: $color-primary;
					}

					&:hover,
					&:focus,
					&:active{
						margin-top: 11px;
						@include transform(scale(1.6));
						@include transition(all 0.2s ease-out);
					}
				}
				
			}
		}

	}
}

.extra-nav-container{
	.extra-nav{
		padding: 0;
		margin: 0;

		> li{
			display: none;
			padding: 0;
				
			.extra-navbar-dropdown-container{
				display: none;
			}

			&.active{
				display: block;

				&:before{
					display: none;
				}
				> a{
					display: none;
				}

				.extra-navbar-dropdown-container{
					background: #f0f0f0;
					display: block;
					box-shadow: 0px -1px 0px #ccc;  
					padding: 10px 0;

					.extra-sub-nav{
						list-style: none;
						margin: 0;
						padding: 0;
						text-align: center;

						li{
							display: inline-block;

							a{
								text-decoration: none;
								color: $color-secondary;
								font-family: $fontPrimary-light;
								font-size: $subMenuFontSize;
								@include transition($defaultTransition);

								&:hover,
								&:focus{
									@include transition($defaultTransition);
									text-decoration: none;
									color: $color-primary;
									font-family: $fontPrimary-light;
									font-size: $subMenuFontSize;
								}
							}

							&.active{
								a{
									font-family: $fontPrimary-bold;
									color: $color-primary;
								}
							}
							
							&:before{
								display: none;
							}
						}
					}
				}

			}
		}
	}
}













