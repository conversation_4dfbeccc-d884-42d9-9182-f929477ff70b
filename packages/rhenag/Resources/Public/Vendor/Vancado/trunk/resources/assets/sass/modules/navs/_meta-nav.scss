// Meta Navigation Styles
.navbar {
	// Meta Navigation Row
	.meta-nav-row {
		background-color: $white;
		border-bottom: 1px solid #e0e0e0;
		padding: 8px 0 0 0;

		@include breakpoint($mobileNav) {
			display: none; // Hide meta nav on mobile
		}

		.meta-nav-container {
			position: static;
			right: auto;
			top: auto;
			text-align: right;

			ul.meta-nav {
				margin: 0;
				padding: 0;
				list-style: none;
				display: inline-block;

				li {
					display: inline-block;
					text-align: center;
					font-size: 16px;
					line-height: 1.2;
					padding-left: 0;
					padding: 8px 15px;
					color: $color-secondary;
					font-family: $fontPrimary-light;
					width: auto;
					min-width: 60px;
					@include transition($defaultTransition);
					position: relative;

					@include breakpoint($mediumNav) {
						font-size: 12px;
						padding: 6px 12px;
					}

					&.newsletter {
						min-width: 78px;
					}

					&:before {
						display: none;
					}

					a {
						text-decoration: none;
						color: $color-secondary;
						font-family: $fontPrimary-light;
						display: block;
						position: relative;

						i {
							color: $color-primary;
							font-size: 20px;
							line-height: 1.2;
							margin-bottom: 5px;
							display: block;
						}

						// Hover state with underline
						&:after {
							content: '';
							position: absolute;
							bottom: -8px;
							left: 50%;
							transform: translateX(-50%);
							width: 0;
							height: 2px;
							background-color: $color-primary;
							@include transition($defaultTransition);
						}

						&[data-action="flyout"]:after {
							opacity: 0;
							bottom: -35px;
							content: "";
							width: 0;
							height: 0;
							border-left: 12px solid transparent;
							border-right: 12px solid transparent;
							border-bottom: 14px solid #515050;
							position: absolute;
							left: 50%;
							margin-left: -10px;
							background: none;
						}

						&.active[data-action="flyout"]:after {
							opacity: 1;
							bottom: -15px;
							transition: all 0.4s ease-out 1s;
						}
					}

					// Hover state
					&:hover, &:focus {
						color: $color-primary;
						@include transition($defaultTransition);

						a {
							color: $color-primary;

							&:after {
								width: 80%;
							}

							i {
								color: $color-primary;
							}
						}
					}
				}
			}
		}
	}

	// Main Navigation Row adjustments
	.main-nav-row {
		padding: 10px 0;

		@include breakpoint($mobileNav) {
			padding: 5px 0; // Smaller padding on mobile
		}

		.logo-container {
			float: left;

			@include breakpoint($mobileNav) {
				float: none;
				display: inline-block;
			}
		}

		.main-nav-container {
			float: right;

			@include breakpoint($mobileNav) {
				float: none;
				display: inline-block;
			}
		}
	}
}