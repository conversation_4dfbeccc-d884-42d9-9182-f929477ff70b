.sidebar-nav-container{		

	&.fixed{
		position: fixed;
	}
	&.absoluteBottom{
		position: absolute;
	}

	.sub-menu{
		margin: 0;
		padding: 0;
		font-family: $fontPrimary-light;
		padding: 1rem 2rem;
		background-color: #fff;

		>:before{
			display: none;
		}
		
		@include breakpoint($sidebarSmall){
			margin-bottom: 0rem;
		}

		li{
			padding: 0;
			padding: 0.6rem 0;

			@include breakpoint($sidebarSmall){
				display: inline-block;
				padding-right: 20px;

				&:last-child{
					padding-right: 0;
				}
			}

			a{
				text-decoration: none;
				font-size: $subNavFontSize;
				line-height: $subNavLineHeight;

				@include transition($defaultTransition);
				
				&:hover,
				&:focus,
				&:active{
					@include transition($defaultTransition);
					color: $color-primary;

				}
			}

			&.active{

				a{
					color: $color-primary;
					display: block;
					padding-left: 20px;

					@include transition($defaultTransition);
					

					&:before{
						color: $color-primary;
						content: "\f09b";
						font-family: BlackTie;
						font-weight: normal;
						font-size: 0.65em;
						margin-right: 0.65em;
						position: absolute;
						left: 0;
						top: 16px;

						
						@include transition($defaultTransition);
					}

					&:hover,
					&:focus,
					&:active{
						
					}
				}
			}
		}
	}
}