.news-list-view {

	.article {

		margin-left: -1.1768447837%;
		margin-right: -1.1768447837%;

		margin-bottom: 2rem;

		.news-header {
			@include span(9 of 12);
			margin-bottom: 1rem;
		}

		.news-img-wrap {
			@include span(3 of 12 last);

			background: none;
			margin: 0;

			a {
				padding: 0;
				border: 0;
			}
		}

		.teaser-text {
			
				margin-left: 0.75rem;

		}

		.news-list-date {
			padding-right: 0.5rem;
			margin-right: 0.5rem;
			border-right: 1px solid #ddd;
		}


	.page-navigation ul li {
			float: left;
			display: block;
			padding: 0 2px;
			background: none;

		}
		.page-navigation ul li::before {
			content:" ";

		}

		@include breakpoint($listNewsMedium){
			.news-header {
				@include span(12 of 12);
			}
			.news-img-wrap {
				@include span(4 of 12 last);
			}
			.teaser-text {
				@include span(8 of 12);
			}
		}
		@include breakpoint($listNewsMobile){
			.news-header {
				@include span(12 of 12);
			}
			.news-img-wrap {
				@include span(12 of 12 last);
			}
			.teaser-text {
				@include span(12 of 12);
			}

		}
	}
}

.news.news-single {
	
	.article {

		.news-img-wrap {
			float: right;

			.news-img-caption {
				padding: 1rem;
				margin: 0;
			}
		}
	}
}

.news-categories {
	margin-left: 0;
	padding-left: 0;
	margin-bottom: 2rem;


	li {
		display: inline-block;
		@include breakpoint($listNewsMobile){
			padding-bottom: 1rem;
		}

		&:before {
			display: none;
		}

		&:first-child {
			margin-left: 0;
			padding-left: 0;
			@include breakpoint($listNewsMobile){
				padding-left: 1.5rem;				
			}
		}

		a {
			text-decoration: none;
			background: $white;
			border-radius: 0.5rem;
			padding: 0.25rem 0.75rem;
			font-family: $fontPrimary-bold;

			&.active {
				color: $text-color-primary;
				&:after {
					content: "\f012";
					font-family: "BlackTie";
					font-size: 0.75rem;
				}
			}
		}

	}
}

.news-single {

	.news-list-date {
		padding-right: 0.5rem;
		margin-right: 0.5rem;
		border-right: 1px solid #ddd;
	}

	.news-img-caption {
		background: $bgDarker;
	}

	.article .news-img-wrap img {
		display: block;
	}

	.news-related-wrap {
		margin-top: 2rem;

		h3 {
			margin-bottom: 0.8rem;
		}
	}
	.article .teaser-text {
		font-size: inherit;
		font-family: $fontPrimary-bold;
		color: $textColor;
	}
}

.home-news {
	background: #fff;

	.home-news-item {
		padding: 1rem !important;

		.home-news-header {
			margin-top: 0;
		}		
	}

	&.news-3 {

		.home-news-item {

			@include span(4 of 12);

			&.last {
				@include span(4 of 12 last);
			}
			@include breakpoint($homeNewsMedium){
				@include span(6 of 12);

				&.last {
					display: none;
				}

			}

			@include breakpoint($homeNewsMobile){
				@include span(12 of 12);
				&.last {
					@include span(12 of 12);
					display: block;
				}
			}
		}
	}

	&.news-2 {

		.home-news-item {

			@include span(6 of 12);

			&.last {
				@include span(6 of 12 last);
			}

			@include breakpoint($homeNewsMedium){
				@include span(12 of 12);

				&.last {
					display: none;
				}

			}

			@include breakpoint($homeNewsMobile){
				@include span(12 of 12);
				&.last {
					@include span(12 of 12);
					display: block;
				}
			}
		}
	}
}