.tx-powermail {
	.btn-group {
		.btn {
			margin-right: 0.5em;
			margin-bottom: 1em;
			outline: none;
		}
	}
	.powermail_fieldset {
		border: 0;
		padding: 0;
		margin: 0;

		.powermail_legend {
			display: none;

		}		
	}

	.form-pages {
		/* margin-left: -$gutterPercent; */
		/* margin-right: -$gutterPercent; */
		width: 75%;
		@include breakpoint($formTablet){
			width: 100%;
		}
		
		.form-page {

			.form-field {
				@include span(12 of 12 last);
			}

			&.page-full {
				margin-left: -$gutterPercent;
				margin-right: -$gutterPercent;

				.form-field {

					&.field-small {
						@include span(4 of 12);
						
						@include breakpoint($formMobile){
							@include span(12 of 12 last);
						}
					}

					&.field-medium {
						@include span(8 of 12);
						
						@include breakpoint($formMobile){
							@include span(12 of 12 last);
						}
					}
					&.field-full {
						@include span(12 of 12 last);
					}
				}
			}

			&.page-half, &.page-half-last {

				margin-left: -$gutterPercent;
				margin-right: -$gutterPercent;				
				@include span(6 of 12 nest);

				@include breakpoint($formMobile){
					@include span(12 of 12 nest);
				}

				.form-field {
					@include span(6 of 6 last);

					@include breakpoint($formMobile){
						@include span(12 of 12 last);
					}

					&.field-small {
						@include span(2 of 6);
						
						@include breakpoint($formMobile){
							@include span(12 of 12 last);
						}
					}

					&.field-medium {
						@include span(4 of 6);

						@include breakpoint($formMobile){
							@include span(12 of 12 last);
						}
					}

					&.field-full {
						@include span(6 of 6 last);

						@include breakpoint($formMobile){
							@include span(12 of 12 last);
						}
					}
				}

				
			}

			&.page-half-last {
				@include span(6 of 12 nest last);
				@include breakpoint($formMobile){
					@include span(12 of 12 nest);
				}
			}
		}
	}

	input[type="text"], input[type="password"], input[type="email"], input[type="number"], input[type="tel"], textarea, select {
		width: 100%;
		font-size: 1rem;
		padding: 0.4rem;
		outline: none !important;
		border: 1px solid $gray-light;
		height: 2.2rem;

		&.parsley-error {
			border-color: $error-red;
			outline: none;
		}		
	}
	textarea {
		height: 10rem;
	}

	.parsley-errors-list {
		color: $error-red;
		font-size: 0.8rem;
		padding-left: 0;
		position: absolute;
		right: 0;

		li {
			list-style-type: none;
			padding-left: 0;

			&:before {
				display: none;
			}
		}		
	}

	.powermail_label {
		font-size: 0.9rem;
		line-height: 1.5rem;
	}

	.powermail_field {
		margin-bottom: 1.5rem;
	}



	/** Progressbar Option **/
	.progressbar{
		.powermail_field_error_container_marker{
			display: none;
		}
		&__icon{
			width: 64px;
			height: 64px;
			margin-bottom: 1.875rem;
			position: relative;
			svg{
				width: 135px;
				position: absolute;
				top: -30px;
				left: -34px;
			}
			path{
				fill: $gray!important;
			}
			@include breakpoint($formTablet){
				width: 100%;
				svg{
					left: 0;
					right: 0;
					margin: 0 auto;
				}
			}
		}

		&__radio{
			cursor: pointer;
			@include breakpoint($formTablet){
				margin-right: 0;
				max-width: 80%;
			}
			label{
				cursor: pointer;
			}
			&:hover{
				.progressbar__radioBox{
					border-color: $orange;
					color: $orange;
				}
			}
		}
		input[type="submit"]{
			position: relative;
			top: 1rem;
		}
		fieldset{
			margin-top: 3rem;
			width: 50%;
			@include breakpoint($formTablet){
				width: 100%;
			}
			.powermail_tab_navigation{
				top: 1rem;
				margin-bottom: 3rem;
			}
			.powermail_fieldwrap_type_, .powermail_fieldwrap_type_poll{
				.powermail_field{
					margin-top: 1.875rem;
					@include breakpoint($formTablet){
						flex-wrap: wrap;
						justify-content: center;
						gap: 1rem;
					}
				}
				label{
					font-size: $headerFontSize;
					font-family: $fontPrimary-light;
					position: relative;
					color: $gray;
					line-height: 35px;
				}
			}
			/** Poll Styling */
			.powermail_fieldwrap_type_poll{
				.powermail_field{
					margin-top: 1.875rem;
					@include breakpoint($formTablet){
						flex-wrap: wrap;
						justify-content: center;
						gap: 1rem;
						align-items: center!important;
						flex-direction: column!important;
					}
				}
				.powermail_field{
					display: flex;
					flex-direction: inherit;
					align-items: flex-start;
				}
				.progressbar__radio{
					display: block;
					margin-right: 2rem;
					&:hover{
						.progressbar__radioBox{
							border-color: $orange;
						}
						.progressbar__label{
							color: $orange;
						}
					}
					label{
						cursor: pointer;
						text-align: center;
						padding: 0 1rem;
						display: block;
						max-width: 200px;
					}
				}
				.progressbar__label{
					transition: all .5s ease-in-out;
					line-height: $baseLineHeight;
					display: flex;
					justify-content: center;
					align-items: center;
					min-height: 40px;
				}
				.progressbar__radioBox{
					border-radius: 0.5rem;
					border: 1px solid $gray;
					min-height: 40px;
					width: 100%;
					height: 100%;
					position: absolute;
					left: 0;
					z-index: 0;
					transition: all .5s ease-in-out;
				}
				input{
					position: absolute;
					opacity: 0;
					cursor: pointer;
					height: 0;
					width: 0;
					&:checked {
						+ .progressbar__radioBox{
							border-color: $orange;
							background: $orange;
							width: 100%;
							height: 100%;
							position: absolute;
							left: 0;
							z-index: -1;
							border-radius: 0.5rem;
						}
						~ .progressbar__label{
							color: $white!important;
						}
					}
				}
			}

			.powermail_tab_navigation{
				a{
					margin-top: 0;
					&:last-child{
						margin-left: 2rem;
					}
					&:first-child{
						&:only-child:not(.btn-primary){
							position: relative;
							top: -2rem;
						}
						margin-left: 0;

					}
					@include breakpoint($formTablet){
						&:last-child{
							float: right;
						}
					}
				}
			}
		}
		&__label{
			transition: all .5s ease-in-out;
			line-height: $baseLineHeight;
			min-height: 40px;
		}
		&__radio{

		}

		.poll{
			&__modal{
				position: absolute;
				top: 0;
				right: -25px;
				height: 20px;
				width: 20px;
				@include breakpoint($formTablet){
					top: unset;
					bottom: 3px;
				}
				&__button{
					width: 20px;
					height: 20px;
					background: $orange;
					font-size: 14px;
					line-height: 20px;
					text-align: center;
					border-radius: 16px;
					color: $white;
					font-family: $fontPrimary-bold;
					position: absolute;
					cursor: pointer;
				}
				&__content{
					display: none;
					background-color: $gray;
					color: $white;
					font-size: 70%;
					position: absolute;
					width: 200px;
					left: 35px;
					top: 50%;
					transform: translateY(-52%);
					z-index: 1;
					padding: 1rem;
					line-height: $baseLineHeight;
					&:before{
						content: '';
						width: 18px;
						height: 18px;
						background-color: $gray;
						transform: rotate(45deg) translateY(-52%);
						position: absolute;
						left: -15px;
						top: 50%;
						z-index: -1;
					}
					@include breakpoint($formTablet){
						left: unset;
						right: 35px;

						&:before{
							left: unset;
							right: 5px;
						}
						&--right{
							left: 35px;
							&:before{
								right: unset;
								left: -15px;
							}
						}
					}
				}
			}
		}


		.radio{
			label{
				font-size: $baseFontSize!important;
				font-size: $baseFontSize!important;
				@include breakpoint($formTablet){
					max-width: 100%;
					min-height: 40px;
					margin-right: 0;
				}
			}
		}

		#bar{
			height: 0.5rem;
			width: 100%;
			background-color: $bgDarker;
			position: relative;
			.progress{
				position: absolute;
				content: '';
				width: 0;
				height: 0.5rem;
				background-color: $orange;
				left: 0;
				top: 0;
			}
		}
	}


}

