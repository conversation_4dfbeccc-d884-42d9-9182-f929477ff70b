.promobox-double{
	@include span(6 of 12);
	margin-bottom: 2rem;

	@include breakpoint($promoboxMobile){
		@include span(12 of 12);
	}

	@include breakpoint($promobox3Columns){
		@include span(8 of 12);
	}

	.promobox{

		.promobox-top{
			background-color: #fff;
			// margin-bottom: 1rem;
			overflow: hidden;

			.promobox-left{
				width: 50%;
				height: 100%;
				display: inline-block;

				@include breakpoint($promoboxDoubleTablet){
					min-height: $promoboxMinHeightMobile;
				}
				
				@include breakpoint($promoboxDoubleMobile){
					width: 100%;
				}
				
				>div{
					padding: 15px;
					
					.promobox-title{

					}
					.promobox-bodytext{
					}
				}


				
			}
			.promobox-right{
				width: 50%;
				height: 100%;
				display: inline-block;
				float: right;

				@include breakpoint($promoboxDoubleTablet){
					min-height: $promoboxMinHeightMobile;
				}

				@include breakpoint($promoboxDoubleMobile){
					width: 100%;
					display: none;
				}

				&.mobile{
					display: none;
					@include breakpoint($promoboxDoubleMobile){
						display: block;
					}
				}

				.promobox-image{
					height: 100%;
					min-height: $promoboxMinHeight;


					@include breakpoint($promoboxMobile){
						min-height: $promoboxMinHeightMobile;
					}

					&:before{
						content: "";
						width: 35px;
						height: 35px;
						@include border-radius(100%);
						background-color: #fff;
						position: absolute;
						left: -20px;
						top: 50%;
						-webkit-transform: translateY(-50%);
						-ms-transform: translateY(-50%);
						transform: translateY(-50%);
						z-index: 1;

						@include breakpoint($promoboxDoubleMobile){
							left: 50%;
							top: auto;
							bottom: -20px;
							-webkit-transform: translateX(-50%);
							-ms-transform: translateX(-50%);
							transform: translateX(-50%);
						}
					}

				}
			}
		}

		.promobox-spacer{
			height: 1rem;
		}

		.promobox-bottom{
			background-color: #fff;

			margin-top: 1rem;
			
			@include breakpoint($promoboxDoubleTablet){
				margin-top: 2rem;
			}
			@include breakpoint($promoboxDoubleMobile){
				margin-top: 2rem;
			}

			.promobox-left{
				width: 50%;
				height: 100%;
				display: inline-block;

				@include breakpoint($promoboxDoubleMobile){
					width: 100%;
					float: none;
				}

				.promobox-image{
					height: 100%;
					min-height: $promoboxMinHeight;
					margin-bottom: -7px;

					@include breakpoint($promoboxMobile){
						min-height: $promoboxMinHeightMobile;
					}

					@include breakpoint($promoboxDoubleMobile){
						width: 100%;
					}

					&:after{
						content: "";
						width: 35px;
						height: 35px;
						@include border-radius(100%);
						background-color: #fff;
						position: absolute;
						right: -20px;
						top: 50%;
						-webkit-transform: translateY(-50%);
						-ms-transform: translateY(-50%);
						transform: translateY(-50%);
						z-index: 1;

						@include breakpoint($promoboxDoubleMobile){
							left: 50%;
							top: auto;
							bottom: -20px;
							-webkit-transform: translateX(-50%);
							-ms-transform: translateX(-50%);
							transform: translateX(-50%);
						}
					}
				}
				
			}
			.promobox-right{
				width: 50%;
				// height: 100%;
				float: right;
				display: inline-block;

				@include breakpoint($promoboxDoubleMobile){
						width: 100%;
						float: none;
				}

				>div{
					padding: 15px;
				}
			}
		}
	}
}