.promobox-single{
	@include span(6 of 12);
	margin-bottom: 2rem;


	@include breakpoint($promoboxMobile){
		@include span(12 of 12);
	}
	
	@include breakpoint($promobox3Columns){
		@include span(4 of 12);
	}

	.promobox{
		background-color: #fff;

		.promobox-image{
			min-height: $promoboxMinHeight;
		
			@include breakpoint($promoboxMobile){
				min-height: $promoboxMinHeightMobile;
			}

			&:after{
				content: "";
				width: 35px;
				height: 35px;
				@include border-radius(100%);
				background-color: #fff;
				position: absolute;
				bottom: -20px;
				left: 50%;
				-webkit-transform: translateX(-50%);
				-ms-transform: translateX(-50%);
				transform: translateX(-50%);
			}

		}
		
		.promobox-spacer{
			height: 0;
		}

		.promobox-bottom{
			@include breakpoint($promoboxMobile){
				padding-bottom: 0rem;
			}
			
			.promobox-info-container{

				padding: 0rem 1rem 1rem 1rem;
				margin-top: 1rem;

				.promobox-title{

				}

				.promobox-bodytext{
					
				}
			}
			.promobox-link{

			}
		}

	}

}