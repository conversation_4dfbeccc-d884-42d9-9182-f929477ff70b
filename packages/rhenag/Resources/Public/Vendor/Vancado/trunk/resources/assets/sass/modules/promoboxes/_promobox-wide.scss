.promobox-wide{
	@include span(12 of 12);
	margin-bottom: 2rem;

	.promobox{
		background-color: #fff;

		.promobox-left{
			width: 67%;
			height: 100%;
			display: inline-block;

			@include breakpoint($promoboxDoubleTablet){
				width: 50%;
			}

			@include breakpoint($promoboxWideMobile){
				width: 100%;
			}

			.promobox-image{
				min-height: $promoboxMinHeight;
				height: 100%;
				margin-bottom: -7px;
				
				@include breakpoint($promoboxMobile){
					min-height: $promoboxMinHeightMobile;
				}

				&:after{
					content: "";
					width: 35px;
					height: 35px;
					@include border-radius(100%);
					background-color: #fff;
					position: absolute;
					right: -20px;
					top: 50%;
					-webkit-transform: translateY(-50%);
					-ms-transform: translateY(-50%);
					transform: translateY(-50%);
					z-index: 1;

					@include breakpoint($promoboxWideMobile){
						top: auto;
						left: 50%;
						bottom: -20px;
						-webkit-transform: translateX(-50%);
						-ms-transform: translateX(-50%);
						transform: translateX(-50%);

					}
				}
		
			}

		}
		.promobox-right{
			width: 33%;
			height: 100%;
			display: inline-block;
			float: right;
			
			@include breakpoint($promoboxDoubleTablet){
				width: 50%;
			}

			@include breakpoint($promoboxWideMobile){
				width: 100%;
				float: none;
			}

			>div{
				padding: 15px;
			}

		}
	}
}