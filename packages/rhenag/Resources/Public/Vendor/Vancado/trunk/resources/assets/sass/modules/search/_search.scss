// pagination
.tx-indexedsearch-browsebox{
	ul.browsebox{
		list-style: none;
		padding: 0;
		margin: 1rem 0;

		li{
			display: inline-block;
   			padding-right: 12px;
			padding-left: 0;

   			&:before{
   				display: none;
   			}
		}

	}
}


// Result Tables
.tx-indexedsearch-res{
	table{
		margin: 0;

		td{
			padding: 0;
		}

		.tx-indexedsearch-icon{
		}
		.tx-indexedsearch-title{
		}
	}

}

//search box
#tx_indexedsearch{
	.input-append.searchfield{
		input {
			&.tx-indexedsearch-searchbox-sword{
	    		padding-right: 50px;
	    		width: 100%;
	    		display: inline-block;
				
				padding: 0.5rem 1rem;
				border: none;

			}
    	}
    	button{
    		position: absolute;
		    top: 50%;
		    right: 1rem;
		    border: none;
		    background: transparent;
		    transform: translateY(-50%);

		    &:hover{
		    	outline: none;

		    	i{
		    		color: $color-primary;
		    	}
		    }
    	}
    }
}