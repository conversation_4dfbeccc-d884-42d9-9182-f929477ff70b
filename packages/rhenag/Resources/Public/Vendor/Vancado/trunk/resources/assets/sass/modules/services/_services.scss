.services {
	
	@include span(8 of 12);
	margin-left: span(2 of 12);

	.accordion {
		@include span(12 of 12);
	}

	.service {
		@include span(4 of 12);

		height: 250px;
		perspective: 1000px;
		transform-style: preserve-3d;

		&:hover {

			.back {
				transform: rotateY(0deg);
			}
			.front {
	    		transform: rotateY(180deg);
			}
		}

		.flipper {
			transition: 0.6s;
			transform-style: preserve-3d;
			position: relative;
		}

		.front, .back {
			backface-visibility: hidden;
			transition: 0.6s;
			transform-style: preserve-3d;
			text-align: center;
			position: absolute;

			top: 0;
			left: 0;
			width: 100%;
			padding: 2rem;
			height: 230px;
		}
		.front {
			z-index: 2;
			transform: rotateY(0deg);
			background: $orange;
			color: $white;			
		}		
		.back {
			transform: rotateY(-180deg);
			border: 1px solid $orange;
		}

		.service-additional {
			background: $bgDarker;
			height: 230px;
			text-align: center;
			display: block;
		}

		svg {
			height: 100px;
		}
	}
}