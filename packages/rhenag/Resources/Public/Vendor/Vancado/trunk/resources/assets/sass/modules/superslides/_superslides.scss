#slides {
  position: relative;
}
#slides .slides-container {
  display: none;
}
#slides .scrollable {
  *zoom: 1;
  position: relative;
  top: 0;
  left: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  height: 100%;
}
#slides .scrollable:after {
  content: "";
  display: table;
  clear: both;
}

.slides-navigation {
  margin: 0 auto;
  position: absolute;
  z-index: 3;
  top: 46%;
  width: 100%;
}
.slides-navigation a {
  position: absolute;
  display: block;
}
.slides-navigation a.prev {
  left: 0;
}
.slides-navigation a.next {
  right: 0;
}

.slides-pagination {
  position: absolute;
  z-index: 3;
  text-align: center;
  width: 100%;
  bottom: 55px;
  @include breakpoint($mobileNav){
    bottom: 10px;
  }
}
.slides-pagination a {
  border: 1px solid $light-green;
  border-radius: 50%;
  width: 7px;
  height: 7px;
  display: -moz-inline-stack;
  display: inline-block;
  vertical-align: middle;
  *vertical-align: auto;
  zoom: 1;
  *display: inline;
  margin: 4px;
  overflow: hidden;
  text-indent: -100%;
}
.slides-pagination a.current {
  background: $green;
  border: 1px solid $green;
}


.slide-content-box{
  background-color: rgba($brand-blue, 0.6);
  color: $brand-white;
  position: absolute;
  right: 0;
  bottom: $sliderContentOffset;
  padding: 40px;
  max-width: $sliderContentMaxWidth;
  
  @include breakpoint($mobileNav){
    padding: 20px 20px 40px 20px;
    bottom: 0px;
      font-size: $baseFontSize;
      left: 0;
      max-width: 100%;
  }
  @include breakpoint($normalScreen){
    font-size: $baseFontSize*0.7;
  }
  @include breakpoint($wideScreen){
    max-width: $sliderContentMaxWidth_wideScreen;
  }

  .slide-headline{
     font-family: $fontSecondary-bold;
     font-size: 4em;
     line-height: 1;
     margin-bottom: 0.3em;
    @include breakpoint($mobileNav){
      font-size: 2em;
    }
  }
  .slide-subline{
     font-family: $fontPrimary-book;
     font-size: 2.2em;
     line-height: 1;
     margin-bottom: 1em;
     @include breakpoint($mobileNav){
      font-size: 1em;
    }
  }
  .slide-cta{
     font-family: $fontPrimary-medium;
     font-size: 1em;
     line-height: 1;

     @include breakpoint($normalScreen){
      font-size: $baseFontSize;
    }

     .btn-link{
        color: $brand-white;

        &:hover,&:focus{
          font-family: $fontPrimary-black;

        }
     }
  }

}
