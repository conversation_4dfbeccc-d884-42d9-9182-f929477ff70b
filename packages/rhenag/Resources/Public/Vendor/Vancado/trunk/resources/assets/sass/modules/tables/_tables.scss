// default
table{
	margin-bottom: 1.5rem;
	thead{
		td,th{
			font-family: $fontPrimary-light;
			font-weight: normal;
			vertical-align: top;
		}
	}
	td{
		padding: 1.25rem;
		vertical-align: top;
	}

}

// table-theme-boxed
table{
	&.table-theme-boxed{
		thead{
			td,th{
				font-family: $fontPrimary-light;
				font-weight: normal;
				color: $color-secondary;
				background: $bgDarker;
				padding: 1.25rem;
				font-size: 1.25rem;
				line-height: 1;
			}
		}
		tbody{
			tr{
				td{
					background: $bgLighter;
					padding: 1.25rem;
				}
				
				&:nth-child(2n){
					td{
						background: $bgLight;
					}
				}
			}
		}
	}
}

// table-theme-light
table{
	&.table-theme-light{
		thead{
			td,th{
				font-family: $fontPrimary-light;
				font-weight: normal;
				background: $bgLighter;
				border-bottom: 1px solid $color-primary;
				padding: 1.25rem;
				font-size: 1.25rem;
				line-height: 1;
				color: $color-secondary;
			}
		}
		tbody{
			td{
				border-bottom: 1px solid $color-primary;
				padding: 1.25rem;
				background: $orangeLight;

				&:first-child{
					background: transparent;
				}
				
			}
		}
	}
}


// table-prices
table{
	&.table-prices{
		thead{
			td,th{
				text-align: right;
					
				&:first-child{
					text-align: left;
				}
			}
		}
		tbody{
			td{
				text-align: right;

				&:first-child{
					text-align: left;
				}
			}
		}
		&.table-align-left{
			thead{
				td,th{
					text-align: left;
						
					&:first-child{
						text-align: left;
					}
				}
			}
			tbody{
				td{
					text-align: left;

					&:first-child{
						text-align: left;
					}
				}
			}
		}
	}
}

