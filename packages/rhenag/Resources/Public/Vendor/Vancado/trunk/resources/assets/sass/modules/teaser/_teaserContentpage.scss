.teaser-contentpage{
	margin-bottom: $sectionMargin;

	.teaser-canvas{
		min-height: $teaserSidebarMinHeight;

		@include breakpoint($teaserHomeMobile){
			min-height: $teaserSidebarMinHeightMobile;
			// margin-top: $naviHeight;
		}

		.teaser-item{
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			min-height: $teaserSidebarMinHeight;
			opacity: 0;
			@include transition($teaserHomeImageTransition);
			overflow: hidden;

			@include breakpoint($teaserHomeMobile){
				min-height: $teaserSidebarMinHeightMobile;
				position: relative;
			}

			&.active{
				opacity: 1;
				@include transition($teaserHomeImageTransition);
			}

			.fluid-container{
				min-height: $teaserSidebarMinHeight;

				@include breakpoint($teaserHomeMobile){
					min-height: $teaserSidebarMinHeightMobile;
					margin-top: $naviHeight;
				}
			}

			&.text-position-right{
				.heroTitle{
					position: absolute;
					bottom: $teaserSidebarMinHeight - $teaserHomeTextHorizontalPosition;
					right: 100px;

					@include breakpoint($teaserHomeMobile){
						bottom: $teaserSidebarMinHeightMobile - $teaserHomeTextHorizontalPositionMobile;
						right: 30px;
						font-size: 1.2rem;
					}
				}
				.heroSubtitle{
					position: absolute;
					top: $teaserHomeTextHorizontalPosition;
					right: 50px;

					@include breakpoint($teaserHomeMobile){
						top: $teaserHomeTextHorizontalPositionMobile;
						right: 10px;
						font-size: 0.8rem;
					}

				}
			}
			&.text-position-left{
				.heroTitle{
					position: absolute;
					bottom: $teaserSidebarMinHeight - $teaserHomeTextHorizontalPosition;
					left: 50px;

					@include breakpoint($teaserHomeMobile){
						bottom: $teaserSidebarMinHeightMobile - $teaserHomeTextHorizontalPositionMobile;
						left: 30px;
						font-size: 1.2rem;
					}
				}
				.heroTitleSubline{
					position: absolute;
					top: $teaserHomeTextHorizontalPosition;
					left: 100px;

					@include breakpoint($teaserHomeMobile){
						top: $teaserHomeTextHorizontalPositionMobile;
						left: 10px;
						font-size: 0.8rem;
					}

				}
				.heroSubtitle{
					position: absolute;
					top: $teaserHomeTextHorizontalPosition;
					left: 100px;

					@include breakpoint($teaserHomeMobile){
						top: $teaserHomeTextHorizontalPositionMobile;
						left: 10px;
						font-size: 0.8rem;
					}

				}

			}
		}
	}
}