.teaser-landingpage{
	position: relative;
	min-height: $teaserLandingpageMinHeight;
	margin-bottom: $sectionMargin;

	.move-down{
		display: none;
	}

	&.minHeight{
		height: auto;

		.move-down{
			display: block;
			position: absolute;
		    bottom: 60px;
		    left: 50%;
		    margin-left: -20px;
		    z-index: 9;
		}

		.teaser-canvas{
			min-height: $teaserLandingpageMaxHeightAlternative;
			max-height: $teaserLandingpageMaxHeightAlternative;
			height: $teaserLandingpageMaxHeightAlternative;

			.teaser-item{
				min-height: $teaserLandingpageMaxHeightAlternative;
				max-height: $teaserLandingpageMaxHeightAlternative;
				height: $teaserLandingpageMaxHeightAlternative;

				.heroTitle{
					bottom: $teaserLandingpageMaxHeightAlternative - $teaserLandingpageTextHorizontalPosition;
				}
			}
		}
		.teaser-info{
			position: relative;
			left: auto;
			top: auto;
			@include background-opacity($white, 0);

			.fluid-container{
				.row{
					margin-bottom: 0;
				}
			}
		}
	}

	.teaser-canvas{
		min-height: $teaserLandingpageMinHeight;

		@include breakpoint($teaserLandingpageMobile){
			min-height: $teaserLandingpageMinHeightMobile;
		}

		.teaser-item{
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			min-height: $teaserLandingpageMinHeight;
			opacity: 0;
			@include transition($teaserLandingpageImageTransition);
			overflow: hidden;

			@include breakpoint($teaserLandingpageMobile){
				min-height: $teaserLandingpageMinHeightMobile;
			}

			&.active{
				opacity: 1;
				@include transition($teaserLandingpageImageTransition);
			}

			&.text-position-right{
				.heroTitle{
					position: absolute;
					bottom: $teaserLandingpageMinHeight - $teaserLandingpageTextHorizontalPosition;
					right: 100px;

					@include breakpoint($teaserLandingpageMobile){
						bottom: $teaserLandingpageMinHeightMobile - $teaserLandingpageTextHorizontalPositionMobile !important;
						right: 30px;
						font-size: 1.2rem;
					}
				}
				.heroSubtitle{
					position: absolute;
					top: $teaserLandingpageTextHorizontalPosition;
					right: 50px;

					@include breakpoint($teaserLandingpageMobile){
						top: $teaserLandingpageTextHorizontalPositionMobile;
						right: 10px;
						font-size: 0.8rem;
					}

				}
			}
			&.text-position-left{
				.heroTitle{
					position: absolute;
					bottom: $teaserLandingpageMinHeight - $teaserLandingpageTextHorizontalPosition;
					left: 50px;

					@include breakpoint($teaserLandingpageMobile){
						bottom: $teaserLandingpageMinHeightMobile - $teaserLandingpageTextHorizontalPositionMobile;
						left: 30px;
						font-size: 1.2rem;
					}
				}
				.heroSubtitle{
					position: absolute;
					top: $teaserLandingpageTextHorizontalPosition;
					left: 100px;

					@include breakpoint($teaserLandingpageMobile){
						top: $teaserLandingpageTextHorizontalPositionMobile;
						left: 10px;
						font-size: 0.8rem;
					}

				}
			}
			&.teaser-video{

				> video{
					position: absolute;
					top: 0;
					left: 0;
					min-width: 100%; 
					min-height: 100%; 
					width: auto;
  					height: auto;

					top: 50%;
					left: 50%;
					-webkit-transform: translate(-50%, -50%);
					-ms-transform: translate(-50%, -50%);
					transform: translate(-50%, -50%);

					&::-webkit-media-controls {
					  display: none;
					}


					display: block;
					@include breakpoint($teaserLandingpageMobile){
							display: none;
					}

					&.position-center-center{
						top: 50%;
						left: 50%;
						-webkit-transform: translate(-50%, -50%);
						-ms-transform: translate(-50%, -50%);
						transform: translate(-50%, -50%);
					}
                    &.position-top-left{
						top: 0;
						left: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-top-right{
						top: 0;
						left: auto;
						right: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-center-left{
						top: 50%;
						left: 0;
						-webkit-transform: translate(0%, -50%);
						-ms-transform: translate(0%, -50%);
						transform: translate(0%, -50%);
                    }
                    &.position-center-right{
						top: 50%;
						left: auto;
						right: 0;
						-webkit-transform: translate(0%, -50%);
						-ms-transform: translate(0%, -50%);
						transform: translate(0%, -50%);
                    }
                    &.position-bottom-left{
						top: auto;
						bottom: 0;
						left: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-bottom-right{
						top: auto;
						bottom: 0;
						left: auto;
						right: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-top-center{
						top: 0;
						left: 50%;
						-webkit-transform: translate(-50%, 0%);
						-ms-transform: translate(-50%, 0%);
						transform: translate(-50%, 0%);
                    }
                    &.position-bottom-center{
						top: auto;
						bottom: 0;
						left: 50%;
						-webkit-transform: translate(-50%, 0%);
						-ms-transform: translate(-50%, 0%);
						transform: translate(-50%, 0%);
                    }

				}
				> picture{
					display: none;
					@include breakpoint($teaserLandingpageMobile){
							display: block;
					}
				}

			}




		}
		.teaser-progressBar{
			width: 100%;
			height: 4px;
			position: absolute;
			bottom: 0;
			left: 0;
			background-color: $white;
			z-index: 1;

			span{
				background-color: $color-primary;
				width: 0%;
				display: block;
				height: 4px;
			}
		}

	}
	.teaser-bullets{
		display: none;

		@include breakpoint($teaserLandingpageMobile){
			display: block;
			position: absolute;
			top: -45px;
			left: 0;
			width: 100%;
		}
		ul{
			padding: 0;
			margin: 0;
			list-style: none;
			display: inline-block;
			margin-left: auto;
			margin-right: auto;

			li{
				display: inline-block;
				padding: 0;
				@include border-radius(100%);
				border: 1px solid $white;
				width: 8px;
				height: 8px;

				&.active{
					background-color: $white;
				}

				&:before{
					display: none;
				}
			}
		}
	}
	.teaser-info{
		text-align: center;
		// margin-top: -130px;
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		@include background-opacity($white, .8);
		padding-top: 0rem;
	
		@include breakpoint($teaserLandingpageMobile){
			margin-top: 0;
			position: relative;
			left: auto;
			top: auto;
			@include background-opacity($white, 0);
		}

		.h2{
			margin-top: 2rem;
		}

		.teaser-info-box-container{

			display: table;//IE fallback
			display: -webkit-flex;
			display:         flex;
			-webkit-align-items: center;
			        align-items: center;
			-webkit-justify-content: center;
			          justify-content: center;

			z-index: 5;

			@include breakpoint($teaserLandingpageMobile){
				display: block;
			}

			.teaser-info-box{
				@include span(3 of 12);
				float: none;
				// display: inline-block;
				display: table-cell;
				vertical-align: top;
				display: flex-item;

				@include breakpoint($teaserLandingpageMobile){
					width: 100%;
					display: block;
					float: none;
					z-index: 9;

				}

				> div{
					padding: 1.5rem 1rem;

					a{
						text-decoration: none;

						&:hover,
						&:focus{
							text-decoration: none;

							.teaser-landingpage-text{
								color: $textColor;
							}

						}
					}
					
					.svg-icon{
						margin-bottom: 0.5rem;
					}

					.teaser-info-title{
						
					}
					p{

					}
					
				}

				&.active{
					
					> div{
						@include background-opacity($white, 1);

						&:before{
							content: "";
							width: 35px;
							height: 35px;
							@include border-radius(100%);
							background-color: $white;
							position: absolute;
							top: -15px;
							left: 50%;
							-webkit-transform: translateX(-50%);
							-ms-transform: translateX(-50%);
							transform: translateX(-50%);
						}
					}

				}

			}
			
		}
	}
}


//disable video for touch devices
.touch{
	.teaser-landingpage{
		.teaser-canvas{
			.teaser-item{
				&.teaser-video{
					> video{
						display: none;
					}
					> picture{
						display: block;
					}
				}
			}	
		}
	}
}