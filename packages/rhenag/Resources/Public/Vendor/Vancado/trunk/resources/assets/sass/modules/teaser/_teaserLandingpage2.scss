.teaser-landingpage2{
	margin-bottom: $sectionMargin;

	.teaser-canvas{
		min-height: $teaserHomeMinHeight;

		@include breakpoint($teaserHomeMobile){
			min-height: $teaserHomeMinHeightMobile;
			margin-top: $naviHeight;
		}

		.teaser-item{
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			min-height: $teaserHomeMinHeight;
			opacity: 0;
			@include transition($teaserHomeImageTransition);
			overflow: hidden;

			@include breakpoint($teaserHomeMobile){
				min-height: $teaserHomeMinHeightMobile;
			}

			&.active{
				opacity: 1;
				@include transition($teaserHomeImageTransition);
			}

			&.text-position-right{
				.heroTitle{
					position: absolute;
					bottom: $teaserHomeMinHeight - $teaserHomeTextHorizontalPosition;
					right: 100px;

					@include breakpoint($teaserHomeMobile){
						bottom: $teaserHomeMinHeightMobile - $teaserHomeTextHorizontalPositionMobile;
						right: 30px;
						font-size: 1.2rem;
					}
				}
				.heroSubtitle{
					position: absolute;
					top: $teaserHomeTextHorizontalPosition;
					right: 50px;

					@include breakpoint($teaserHomeMobile){
						top: $teaserHomeTextHorizontalPositionMobile;
						right: 10px;
						font-size: 0.8rem;
					}

				}
			}
			&.text-position-left{
				.heroTitle{
					position: absolute;
					bottom: $teaserHomeMinHeight - $teaserHomeTextHorizontalPosition;
					left: 50px;

					@include breakpoint($teaserHomeMobile){
						bottom: $teaserHomeMinHeightMobile - $teaserHomeTextHorizontalPositionMobile;
						left: 30px;
						font-size: 1.2rem;
					}
				}
				.heroSubtitle{
					position: absolute;
					top: $teaserHomeTextHorizontalPosition;
					left: 100px;

					@include breakpoint($teaserHomeMobile){
						top: $teaserHomeTextHorizontalPositionMobile;
						left: 10px;
						font-size: 0.8rem;
					}

				}
			}
			&.teaser-video{

				> video{
					position: absolute;
					top: 0;
					left: 0;
					min-width: 100%; 
					min-height: 100%; 
					width: auto;
  					height: auto;

					top: 50%;
					left: 50%;
					-webkit-transform: translate(-50%, -50%);
					-ms-transform: translate(-50%, -50%);
					transform: translate(-50%, -50%);

					&::-webkit-media-controls {
					  display: none;
					}


					display: block;
					@include breakpoint($teaserHomeMobile){
							display: none;
					}

					&.position-center-center{
						top: 50%;
						left: 50%;
						-webkit-transform: translate(-50%, -50%);
						-ms-transform: translate(-50%, -50%);
						transform: translate(-50%, -50%);
					}
                    &.position-top-left{
						top: 0;
						left: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-top-right{
						top: 0;
						left: auto;
						right: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-center-left{
						top: 50%;
						left: 0;
						-webkit-transform: translate(0%, -50%);
						-ms-transform: translate(0%, -50%);
						transform: translate(0%, -50%);
                    }
                    &.position-center-right{
						top: 50%;
						left: auto;
						right: 0;
						-webkit-transform: translate(0%, -50%);
						-ms-transform: translate(0%, -50%);
						transform: translate(0%, -50%);
                    }
                    &.position-bottom-left{
						top: auto;
						bottom: 0;
						left: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-bottom-right{
						top: auto;
						bottom: 0;
						left: auto;
						right: 0;
						-webkit-transform: translate(0%, 0%);
						-ms-transform: translate(0%, 0%);
						transform: translate(0%, 0%);
                    }
                    &.position-top-center{
						top: 0;
						left: 50%;
						-webkit-transform: translate(-50%, 0%);
						-ms-transform: translate(-50%, 0%);
						transform: translate(-50%, 0%);
                    }
                    &.position-bottom-center{
						top: auto;
						bottom: 0;
						left: 50%;
						-webkit-transform: translate(-50%, 0%);
						-ms-transform: translate(-50%, 0%);
						transform: translate(-50%, 0%);
                    }

				}
				> picture{
					display: none;
					@include breakpoint($teaserHomeMobile){
							display: block;
					}
				}

			}




		}
		.teaser-progressBar{
			width: 100%;
			height: 4px;
			position: absolute;
			bottom: 0;
			left: 0;
			background-color: $white;
			z-index: 1;

			span{
				background-color: $color-primary;
				width: 0%;
				display: block;
				height: 4px;
			}
		}

	}
	.teaser-bullets{
		display: none;

		@include breakpoint($teaserHomeMobile){
			display: block;
			position: absolute;
			top: -45px;
			left: 0;
			width: 100%;
		}
		ul{
			padding: 0;
			margin: 0;
			list-style: none;
			display: inline-block;
			margin-left: auto;
			margin-right: auto;

			li{
				display: inline-block;
				padding: 0;
				@include border-radius(100%);
				border: 1px solid $white;
				width: 8px;
				height: 8px;

				&.active{
					background-color: $white;
				}

				&:before{
					display: none;
				}
			}
		}
	}
	.teaser-controller{
		text-align: center;
		margin-top: -150px;
	
		@include breakpoint($teaserHomeMobile){
			margin-top: 0;
		}

		.teaser-switch-box-container{

			display: block;//IE fallback
			display: -webkit-flex;
			display:         flex;
			-webkit-align-items: center;
			        align-items: center;
			-webkit-justify-content: center;
			          justify-content: center;

			z-index: 5;

			@include breakpoint($teaserHomeMobile){
				display: block;
			}

			.teaser-switch-box{
				@include span(3 of 12);
				float: none;
				// display: inline-block;
				display: flex-item;
				cursor: pointer;

				@include breakpoint($teaserHomeMobile){
					width: 100%;
					display: block;
					float: none;
					position: absolute;
					top: 0;
					left: 0;
					z-index: 9;
					opacity: 0;



					&.active{
						opacity: 1;
						z-index: 10;
					}
					&:last-child{
						clear: both;
					}
				}

				&.teaser-switch-box-highlight{
					> div{
						// @include background-opacity($color-primary, .7);
					}
					&.active{
						> div{
							// @include background-opacity($color-primary, 1);

							&:before{
								// background-color: $color-primary;
							}
						}
					}
				}



				> div{
					@include background-opacity($white, .7);
					padding: 1.5rem 1rem 3rem;
					@include box-shadow(0px, 0px, 7px, rgba(0, 0, 0, 0.3) );
					@include transition(all 1s ease-in);
					height: 100%;

					&:before{
						content: "";
						width: 35px;
						height: 35px;
						@include border-radius(100%);
						background-color: $white;
						position: absolute;
						top: 15px;
						left: 50%;
						-webkit-transform: translateX(-50%);
						-ms-transform: translateX(-50%);
						transform: translateX(-50%);
						opacity: 0;
						
						@include transition(all 0.5s ease-out);
					}

					.svg-icon{
						margin-bottom: 1rem;
						z-index: 5;
					}

					.teaser-switch-title{

					}
					p{

					}
					.cta-link{
						position: absolute;
						bottom: 1.5rem;
						left: 50%;
						-webkit-transform: translateX(-50%);
						-ms-transform: translateX(-50%);
						transform: translateX(-50%);
					}
					
				}

				&.active{
					
					> div{
						@include background-opacity($white, 1);
						@include transition(all 1s ease-out);

						&:before{
							top: -15px;
							opacity: 1;
							@include transition(all 0.5s ease-out 1.5s);
						}
					}

				}

			}
			
		}
	}
}


//disable video for touch devices
.touch{
	.teaser-landingpage2{
		.teaser-canvas{
			.teaser-item{
				&.teaser-video{
					> video{
						// display: none;
					}
					> picture{
						// display: block;
					}
				}
			}	
		}
	}
}