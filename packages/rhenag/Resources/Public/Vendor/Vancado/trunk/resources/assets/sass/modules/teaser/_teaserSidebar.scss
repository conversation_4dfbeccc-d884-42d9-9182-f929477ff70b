.teaser-sidebar{
	margin-bottom: $sectionMargin;

	.teaser-canvas{
		min-height: $teaserSidebarMinHeight;

		@include breakpoint($teaserHomeMobile){
			min-height: $teaserSidebarMinHeightMobile;
			// margin-top: $naviHeight;
		}

		.teaser-item{
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			min-height: $teaserSidebarMinHeight;
			opacity: 0;
			@include transition($teaserHomeImageTransition);
			overflow: hidden;

			@include breakpoint($teaserHomeMobile){
				min-height: $teaserSidebarMinHeightMobile;
			}

			&.active{
				opacity: 1;
				@include transition($teaserHomeImageTransition);
			}
		}
	}
}