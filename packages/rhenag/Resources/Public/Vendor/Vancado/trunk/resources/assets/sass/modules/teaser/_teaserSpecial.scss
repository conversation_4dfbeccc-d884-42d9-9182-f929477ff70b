.teaser-special{
	min-height: $teaserSpecialMinHeight;
		@include breakpoint($teaserSpecialSmall){
			min-height: 0;
		}

	picture img{
		@include breakpoint($teaserSpecialSmall){
			//position: relative!important;
			//display: block!important;
			//width: 100%;
			//max-width: 100%;
			//min-height: 0;
			//margin-bottom: $gutterPercent;
			@include opacity(0.15);
		}
	}
	.fluid-container{
		min-height: $teaserSpecialMinHeight;
		@include breakpoint($teaserSpecialSmall){
			min-height: 0;
			height: auto;
		}
		
		.row{
			min-height: $teaserSpecialMinHeight;
			margin-bottom: 0;
			@include breakpoint($teaserSpecialSmall){
				min-height: 0;
				margin-bottom: $gutterPercent;
			}

			.teaser-content{
				@include span(4 of 12);
				@include push(5);
				@include breakpoint($teaserSpecialMedium){
					@include span(5 of 12);
					@include push(7);
				}
				@include breakpoint($teaserSpecialSmall){
					@include span(12 of 12);
					@include push(0);
					
					padding-left: 2rem;
					padding-right: 2rem;

				}
				display: block;//IE fallback
				display: flex;
				align-items: center;
				min-height: $teaserSpecialMinHeight;
				@include breakpoint($teaserSpecialSmall){
					min-height: 0;
					justify-content: center;
					text-align: center;

					> div{
						padding: 20px 0;
					}
				}
				.btn-cta{
					display: none;
					@include breakpoint($teaserSpecialMedium){
						display: inline-block;
					}
				}
			}
			.teaser-cta{
				@include span(3 of 12);
				@include breakpoint($teaserSpecialSmall){
					@include span(12 of 12);
				}
				display: flex;
				align-items: center;
				justify-content: center;
				min-height: $teaserSpecialMinHeight;
				@include breakpoint($teaserSpecialSmall){
					min-height: 0;
					margin-bottom: $gutterPercent;
				}
				@include breakpoint($teaserSpecialMedium){
					display: none;
				}
			}

		}
	}



	&.teaser-special-2{
		.fluid-container{
			.row{
				.teaser-content{
					.heroTitle{
						margin-bottom: 0;
					}
					.heroSubtitle{
						margin-left: 2rem;
					}

					@include span(6 of 12);
					@include push(0);
					@include breakpoint($teaserSpecialMedium){
						@include span(6 of 12);
						@include push(0);
					}
					@include breakpoint($teaserSpecialSmall){
						@include span(12 of 12);
						@include push(0);
						
						padding-left: 2rem;
						padding-right: 2rem;

						.heroSubtitle{
							margin-left: 0;
						}
					}
				}
				.teaser-cta{
					display: block;//IE fallback	
					display: flex;
					@include span(6 of 12);
					text-align: right;
					@include breakpoint($teaserSpecialSmall){
						@include span(12 of 12);
						text-align: center;
						display: flex;
					}
				}
			}
		}
	}

}