.tx-vnc-testimonials {
	margin-left: -1.7652671756%;
	margin-right: -1.7652671756%;

	.testimonial {
		margin-bottom: 3rem;

		@include span(4 of 8);

		&.even {
			@include span(4 of 8 last);		
		}

		.testimonial-inner {
		}

		.testimonial-image {		
			z-index: 10;
			float: left;
			width: 25%;
			position: relative;
			border-radius: 100%;
			max-width: 150px;


			img {
			}

			&:before{
				content: "";
				display: block;
				padding-top: 100%; //aspect ration 1:1
			}

		}
		.testimonial-text {
			text-align: center;
			width: 75%;
			float: left;

			.testimonial-quotation {
				color: #ff8d2f;
				font-style: italic;
			}
		}

		@include breakpoint($testimonialsMobile){
			@include span(8 of 8 last);

			&.even {
				@include span(8 of 8 last);
			}
		}
	}
}

.focuspoint img {
	//display: block;
	//height: auto;
	//left: 0;
	//margin: 0;
	//max-height: none;
	//max-width: none;
	//min-height: 100%;
	//min-width: 100%;
	//position: relative!important;
	//top: 0;
	//width: 200px;
}