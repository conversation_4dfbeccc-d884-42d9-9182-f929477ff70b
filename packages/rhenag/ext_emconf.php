<?php

/***************************************************************
 * Extension Manager/Repository config file for ext "Vancado.Rhenag".
 *
 * Auto generated 27-07-2016 10:54
 *
 * Manual updates:
 * Only the data in the array - everything else is removed by next
 * writing. "version" and "dependencies" must not be touched!
 ***************************************************************/

$EM_CONF['rhenag'] = array(
	'title' => 'rhenag basis',
	'description' => 'rhenag basis extension',
	'category' => 'misc',
	'shy' => 0,
	'version' => '10.4.0',
	'dependencies' => '',
	'conflicts' => '',
	'priority' => '',
	'loadOrder' => '',
	'module' => '',
	'state' => 'stable',
	'uploadfolder' => 0,
	'createDirs' => '',
	'modify_tables' => '',
	'clearCacheOnLoad' => 1,
	'lockType' => '',
	'author' => 'Micha Pick',
	'author_email' => '<EMAIL>',
	'author_company' => 'Vancado AG',
	'CGLcompliance' => '',
	'CGLcompliance_note' => '',
	'_md5_values_when_last_written' => 'a:0:{}',
	'suggests' => array(
	),
);
