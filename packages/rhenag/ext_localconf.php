<?php
if (!defined('TYPO3_MODE')) {
	die ('Access denied.');
}

\FluidTYPO3\Flux\Core::registerProviderExtensionKey('Vancado.Rhenag', 'Page');
\FluidTYPO3\Flux\Core::registerProviderExtensionKey('Vancado.Rhenag', 'Content');

// Upgrade Wizard TYPO3 Version 9
#$GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['ext/install']['update']['v9Updater'] = \Vancado\Rhenag\Updates\V9Updater::class;

// Our RTE configuration
$GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['default'] = 'EXT:rhenag/Configuration/RTE/rhenag.yaml';


$GLOBALS['TCA']['pages']['columns']['slug']['config']['generatorOptions']['fields'] = [['nav_title', 'title']];