


plugin.tx_rhenagcomponents {
	view {
		templateRootPaths.0 = {$plugin.tx_rhenagcomponents.view.templateRootPaths.0}
		partialRootPaths.0 = {$plugin.tx_rhenagcomponents.view.partialRootPaths.0}
		layoutRootPaths.0 = {$plugin.tx_rhenagcomponents.view.layoutRootPaths.0}
	}

	#By default the following settings only will have relevance if you have fluidcontent_core extension loaded
	settings {
		container {
			types {
				default = div
				Example = div
			}
		}
		pageUIds.personalizedContactpage={$plugin.tx_rhenagcomponents.settings.pageUIds.personalizedContactpage}
		config.contentObjectExceptionHandler = 0
	}
}

lib.contactBox = COA
lib.contactBox {

	20 = RECORDS
	20 {
		source.data = GP:aim
		source.intval = 1
		dontCheckPid = 1
		tables = tt_content
	}
}




lib.reciever_email=RECORDS
lib.reciever_email {
	source.data = GP:aim
	source.intval = 1
	dontCheckPid = 1
	tables = tt_content
	conf.tt_content = TEXT
	conf.tt_content {
		stdWrap.field = header_link
	}
}
/*
lib.reciever_email=TEXT
lib.reciever_email.value=<EMAIL>
*/
lib.reciever_name=RECORDS
lib.reciever_name {
	source.data = GP:aim
	source.intval = 1
	dontCheckPid = 1
	tables = tt_content
	conf.tt_content = TEXT
	conf.tt_content {
		stdWrap.field = header
	}
}



	plugin.tx_powermail.settings.setup.prefill {
		contact_id = TEXT
		contact_id.data = GP:aim

		contact = RECORDS
		contact {
			source.data = GP:aim
			source.intval = 1
			dontCheckPid = 1
			tables = tt_content
			conf.tt_content = TEXT
			conf.tt_content {
				stdWrap.field = header_link
			}
		}

		referrer = HMENU
		referrer {
			special = list
			# special.value = 4
			special.value.stdWrap.dataWrap = |{GP:refuid}
			#special.value.intval = 1
			special.value.insertData = 1
			1 = TMENU
			1 {
				expAll = 1
				NO = 1
				NO {
					doNotLinkIt = 1
					stdWrap.cObject = COA
					stdWrap.cObject {
						10 = TEXT
						10 {
							typolink.parameter.stdWrap.dataWrap = {GP:refuid}
							typolink.returnLast = url
							wrap = {$plugin.tx_rhenag.baseUrl}|
						}
					}
				}
			}
		}

		contactPerson = CONTENT
		contactPerson {
			table = tt_content
			select {
				fields = {$plugin.tx_rhenag.emailField}
				pidInList = {$plugin.tx_rhenag.addressPID}
				andWhere = uid = ###adr_uid###
				markers {
					adr_uid = TEXT
					adr_uid.data = GP:aim
					# Ganzzahl transformieren
					adr_uid.intval = 1
				}
			}
			renderObj = COA
			renderObj {
				10 = TEXT
				10 {
					field = {$plugin.tx_rhenag.emailField}
					wrap = |
				}
			}
		}
	}
[globalVar = GP:aim > 0]

[end]

