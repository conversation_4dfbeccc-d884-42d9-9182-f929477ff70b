<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<xliff version="1.0">
	<file source-language="en" datatype="plaintext" original="messages" product-name="rhenag_components" date="2016-08-16T11:12:38+02:00">
		<header/>
		<body>
			<trans-unit id="flux.standard">
				<source>Example page</source>
			</trans-unit>
            <trans-unit id="flux.rhenagorange">
                <source><PERSON><PERSON></source>
            </trans-unit>
            <trans-unit id="flux.rhenagblue">
                <source><PERSON><PERSON><PERSON></source>
            </trans-unit>
            <trans-unit id="flux.rhenagblue">
                <source><PERSON><PERSON><PERSON></source>
            </trans-unit>
			<trans-unit id="flux.standard.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress">
				<source>Contact Person  Address</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.description">
				<source>Contact Persons Box-Elements</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.sheets.panels">
				<source>Address Elements</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.fields.title">
				<source>Titel</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.fields.salutation">
				<source>Salutation</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.fields.first_name">
				<source>First Name</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.fields.last_name">
				<source>Last Name</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.fields.position">
				<source>Position</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.fields.tel">
				<source>Telephone:</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaddress.fields.email">
				<source>E-Mail</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion">
				<source>Element - Akkordeon ("Aufklapper")</source>
			</trans-unit>
			<trans-unit id="flux.rhenagtextimage">
				<source>Element - Bild und Text auf grauer Box</source>
			</trans-unit>
			<trans-unit id="flux.rhenagspecialteaser">
				<source>Element - Bildstreifen mit Text und Link</source>
			</trans-unit>
			<trans-unit id="flux.rhenagcontentpageteaser">
				<source>Element - Header (Bild) schmal für Unterseiten</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxsingle">
				<source>Element - Promoboxen - Halbe Spalte - Eine große</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxwide">
				<source>Element - Promoboxen - Ganze Spalte - Eine große</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble">
				<source>Element - Promoboxen - Halbe Spalte - Zwei kleine</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxes">
				<source>Element - Promoboxen - Eine große</source>
			</trans-unit>
			<trans-unit id="flux.downloadBoxes">
				<source>Element - Box mit Downloads (für registrierte Benutzer)</source>
			</trans-unit>
			<trans-unit id="flux.linkBoxes">
        		<source>Download Dateien für registrierte Nutzer</source>
      		</trans-unit>
      		<trans-unit id="flux.tarifBoxes">
        		<source>Element - Tarifbox</source>
      		</trans-unit>
      		<trans-unit id="flux.infoBox">
        		<source>Element - Infobox</source>
      		</trans-unit>
			<trans-unit id="flux.rhenagpromoboxes.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage">
				<source>Element - Header (Bild/Video) mit Icons</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.description">
				<source></source>
			</trans-unit>
            <trans-unit id="flux.homepagevideocontainer">
                <source>Neue Video Element für Patric</source>
            </trans-unit>
            <trans-unit id="flux.homepagevideocontainer.description">
                <source>Neue Video Element für Patric</source>
            </trans-unit>
			<trans-unit id="flux.teaserlandingpage2">
				<source>Element - Header (Bild/Video) ohne Icons</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.rhenagtextimage.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.rhenagcontentpageteaser.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.sheets.common">
				<source>General</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.sheets.panels">
				<source>Accordionelemente</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.sections.panels">
				<source>Element</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.objects.panel">
				<source>Boxpanele</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.objects.panel.title">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.objects.panel.bodytext">
				<source>Full text</source>
			</trans-unit>		
			<trans-unit id="flux.rhenagaccordion.objects.panel.image">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.objects.panel.imageSize">
				<source>Image Size</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.objects.panel.imageWidth">
				<source>Image Width</source>
			</trans-unit>
			<trans-unit id="flux.rhenagaccordion.objects.panel.imageAlignment">
				<source>Image Alignment</source>
			</trans-unit>
			<trans-unit id="flux.rhenagtextimage.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagtextimage.fields.bodytext">
				<source>Full text</source>
			</trans-unit>			
			<trans-unit id="flux.rhenagspecialteaser.fields.teaser-style">
				<source>Appearance</source>
			</trans-unit>
			<trans-unit id="flux.rhenagspecialteaser.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagspecialteaser.fields.subheader">
				<source>Subheadline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagspecialteaser.fields.bodytext">
				<source>Full text</source>
			</trans-unit>
			<trans-unit id="flux.rhenagspecialteaser.fields.linkButton">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="flux.rhenagspecialteaser.fields.linkLabel">
				<source>Link Label</source>
			</trans-unit>			
			<trans-unit id="flux.rhenagpromoboxes.fields.promobox-style">
				<source>Appearance</source>				
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxes.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxes.fields.bodytext">
				<source>Full text</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxes.fields.image">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxes.fields.linkButton">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxes.fields.linkLabel">
				<source>Link Label</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.sheets.box1">
				<source>Promo-Box 1</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.sheets.box2">
				<source>Promo-Box 2</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.bodytext">
				<source>Full text</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.image">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.linkButton">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.linkLabel">
				<source>Link Label</source>
			</trans-unit>
            <trans-unit id="flux.rhenagpromoboxdouble.fields.buttonStyle">
                <source>Link Style</source>
            </trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.header2">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.bodytext2">
				<source>Full text</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.image2">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.linkButton2">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="flux.rhenagpromoboxdouble.fields.linkLabel2">
				<source>Link Label</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.type">
				<source>Slide Type</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.video_loop">
				<source>Slide Video Loop</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.video_position">
				<source>Slide Video Position</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.text_position">
				<source>Slide Text Position</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.subheader">
				<source>Subheadline</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.teasertext">
				<source>Teaser text</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.fields.title">
				<source>Title</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.objects.panel">
				<source>Textgrids (maximum of 4 items)</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.objects.panel.panel_icon">
				<source>Select SVG-Icon</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.objects.panel.panel_header">
				<source>Title</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.objects.panel.panel_text">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.objects.panel.panel_link_text">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.objects.panel.link">
			  <source>Link</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.sheets.common">
				<source>General</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage.sheets.panels">
				<source>Panels</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.sheets.common">
				<source>General</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.fields.type">
				<source>Slide Type</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.fields.video_loop">
				<source>Slide Video Loop</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.fields.video_position">
				<source>Slide Video Position</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.fields.text_position">
				<source>Slide Text Position</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.teaserlandingpage2.fields.subheader">
				<source>Subheadline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagcontentpageteaser.fields.text_position">
				<source>Text Position</source>
			</trans-unit>
			<trans-unit id="flux.rhenagcontentpageteaser.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.rhenagcontentpageteaser.fields.subheader">
				<source>Subheadline</source>
			</trans-unit>
			<trans-unit id="flux.iconText">
				<source>Element - Icon mit Text</source>
			</trans-unit>
			<trans-unit id="flux.iconText.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.iconText.sheets.common">
				<source>General</source>
			</trans-unit>
			<trans-unit id="flux.iconText.sheets.panels">
				<source>IconText Elemente (maximum of 4 items)</source>
			</trans-unit>
			<trans-unit id="flux.iconText.fields.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.iconText.sections.panels">
				<source>Element</source>
			</trans-unit>
			<trans-unit id="flux.iconText.objects.panel">
				<source>IconText-Panele</source>
			</trans-unit>
			<trans-unit id="flux.iconText.objects.panel.panel_icon">
				<source>Select SVG-Icon</source>
			</trans-unit>
			<trans-unit id="flux.iconText.objects.panel.panel_header">
				<source>Title</source>
			</trans-unit>
			<trans-unit id="flux.iconText.objects.panel.panel_text">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="flux.linkBoxes.sheets.boxes">
				<source>Boxes (max. 5 items)</source>
			</trans-unit>
			<trans-unit id="flux.linkBoxes.objects.box.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.linkBoxes.objects.box.text">
				<source>Full text</source>
			</trans-unit>
			<trans-unit id="flux.tarifBoxes.sheets.boxes">
				<source>Tarife (max. 5 items)</source>
			</trans-unit>
			<trans-unit id="flux.tarifBoxes.objects.box">
				<source>Tarif</source>
			</trans-unit>
			<trans-unit id="flux.tarifBoxes.objects.box.header">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.tarifBoxes.objects.box.text">
				<source>Full text</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.sheets.list">
				<source>List</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.fields.header">
				<source>Header</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.fields.theme">
				<source>Theme</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.fields.halfSize">
				<source>Box width</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.fields.textTop">
				<source>Text top</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.fields.textBottom">
				<source>Text bottom</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.objects.item">
				<source>Item</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.objects.item.icon">
				<source>SVG-Icon</source>
			</trans-unit>
			<trans-unit id="flux.infoBox.objects.item.text">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="flux.textImage">
				<source>Element - Bild und Text</source>
			</trans-unit>
			<trans-unit id="flux.textImage.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.textImage.sheets.box1">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="flux.textImage.sheets.box2">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.preline">
				<source>Preline</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.headline">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.headlineSEOTag">
				<source>Headline SEO Tag</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.headlineStyle">
				<source>Headline Style</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.headlineAlignment">
				<source>Headline Alignment</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.text">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.image">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.imageSize">
				<source>Image Size</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.imageWidth">
				<source>Image Width</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.lightbox">
				<source>Lightbox</source>
			</trans-unit>
			<trans-unit id="flux.textImage.fields.imageAlignment">
				<source>Image Alignment</source>
			</trans-unit>
		</body>
	</file>
</xliff>
