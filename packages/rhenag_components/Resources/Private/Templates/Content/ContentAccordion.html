<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="rhenagaccordion">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-accordion.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:form.sheet name="common">
				<flux:field.select name="accordionstyle" items="{
                                0:{0:'default',1:'0'},
                                1:{0:'secondary',1:'1'}
                           }" default="0" label="Accordion-Style" />
			</flux:form.sheet>

			<flux:form.sheet name="panels">
				<flux:form.section name="panels">
					<flux:form.object name="panel">
						<flux:field.input name="title" />
					</flux:form.object>
				</flux:form.section>
			</flux:form.sheet>
			<flux:grid>

				<v:condition.type.isArray value="{panels}">
					<f:for each="{panels}" as="panel" iteration="iteration">
						<flux:grid.row>
							<flux:grid.column colPos="{v:math.sum(a: iteration.index, fail: 1, b: 10)}" name="content.{iteration.index}" label="{f:if(condition: panel.panel.title, then: panel.panel.title, else: 'Content, panel {iteration.cycle}')}">
								<flux:form.variable name="allowedContentTypes" value="rhenagcomponents_textimage,table" />
							</flux:grid.column>
						</flux:grid.row>
					</f:for>
				</v:condition.type.isArray>
			</flux:grid>


		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagaccordion" />
		</h4>

		<h1>
			<f:format.raw>{record.header}</f:format.raw>
		</h1>
		<h2>
			<f:format.raw>{title}</f:format.raw>
		</h2>
		<p>Einträge:
			<v:l key="panels" />: {panels -> f:count()}</p>
	</f:section>

	<f:section name="Main">
		<v:variable.set name="activeTabIndex" value="-1" />
		<div class="accordion-container">
			<f:for each="{panels}" as="panel" iteration="iteration">
				<div class="accordion accordion-{f:if(condition:'{accordionstyle}==1', then: 'secondary', else: 'default')}">
					<dl>
						<dt>
								<a href="#accordion{iteration.cycle}" aria-expanded="false" aria-controls="accordion{iteration.cycle}" 
								   class="accordion-title accordionTitle js-accordionTrigger  is-collapsed is-expanded {f:if(condition:'{accordionstyle}==1', then: '', else: 'link-with-arrow-down')}">{panel.panel.title}</a>
							</dt>
						<dd class="accordion-content accordionItem is-collapsed" id="accordion{iteration.cycle}" aria-hidden="true">
							<div class="accordion-content-wrapper">
								<flux:content.render area="content.{iteration.index}" />
								<div class="clearfix"></div>
							</div>
						</dd>
					</dl>
				</div>
			</f:for>
		</div>

	</f:section>




</div>