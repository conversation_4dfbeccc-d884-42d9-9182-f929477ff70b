<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="rhenagaddress">
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:field.select name="salutation" items="{
                                0:{0:'Herr',1:'1'},
                                1:{0:'Frau',1:'2'}
                           }" default="1" required="1" exclude="0" enabled="1" />
			<flux:field.input name="title" required="0" exclude="0" enabled="1" />
			<flux:field.input name="first_name" required="1" exclude="0" enabled="1" />
			<flux:field.input name="last_name" required="1" exclude="0" enabled="1" />
			<flux:field.input name="position" required="0" exclude="0" enabled="1" />
			<flux:field.input name="tel" required="0" exclude="0" enabled="1" />
			<flux:field.input name="email" required="0" exclude="0" enabled="1" />
			<flux:field.inline.fal name="image" label="Bild" minItems="0" maxItems="1" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" />
		</flux:form>
	</f:section>






	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagaddress" />
		</h4>
		<h5>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux:rhenagaddress.description" />
		</h5>

		<table>
			<tr>
				<td>
					<f:format.crop maxCharacters="50"><f:format.raw><strong>{first_name} {last_name}</strong></f:format.raw></f:format.crop>
					<f:format.raw><br />{position}</f:format.raw>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.crop maxCharacters="50"><f:format.html>{email}</f:format.html></f:format.crop>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" />
						</f:if>
					</v:content.resources.fal>
					<br/></td>
			</tr>
		</table>
	</f:section>
 
	<f:section name="Main">
		{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}
		<div class="contact-item">
			<f:if condition="{image}">
				<div class="contact-image focuspoint" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
					data-image-w="{image.width}" data-image-h="{image.height}">
					<f:image treatIdAsReference="1" src="{image.id}" alt="{first_name} {last_name}" maxWidth="200" class="img-responsive" />
				</div>
			</f:if>
			<div class="contact-info">
				<div>
					<p><strong><f:if condition="{title}">{title}</f:if> {first_name} {last_name}</strong></p>
					<p>
						<f:if condition="{position}">{position}<br></f:if>
						<f:if condition="{tel}"><i class="btr bt-phone"></i>&nbsp;{tel}</f:if>
					</p>
					<f:if condition="{page.uid}=={settings.pageUIds.personalizedContactpage}">
						<f:then>

						</f:then>
						<f:else>
							<f:if condition="{email}">
								<f:link.typolink parameter="{settings.pageUIds.personalizedContactpage}" additionalParams="&aim={record.uid}&refuid={page.uid}&no_cache=1">Kontakt</f:link.typolink>
							</f:if>
						</f:else>
					</f:if>

				</div>
			</div>
			<div class="clearfix"></div>
		</div>
	</f:section>
</div>