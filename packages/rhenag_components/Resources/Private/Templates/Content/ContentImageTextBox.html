<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="rhenagtextimage">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-bild-und-text-auf-box.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:field.input name="header" required="0" exclude="0" enabled="1" />
			<flux:field.text name="bodytext" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
			<flux:field.inline.fal name="image" label="Bild" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
				table="sys_file_reference" />

		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagtextimage" />
		</h4>
		<h5>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux:rhenagtextimage.description" />
		</h5>

		<table>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header}</strong></f:format.raw>
					</f:format.crop>
					<f:format.raw>{subheader}</f:format.raw>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.html>{bodytext}</f:format.html>
					</f:format.crop>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" /><br/>
						</f:if>
					</v:content.resources.fal>
				</td>
			</tr>
		</table>
	</f:section>

	<f:section name="Main">
		<f:if condition="{record.header}">
			<f:format.raw>
				<h1 class="h3_content">{record.header}</h1>
			</f:format.raw>
		</f:if>
		<div class="imageTextBox">
			<div class="col-left equal-height">
				{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}
				<f:if condition="{image}">

					<img class="picture-mobile img-responsive lazy" title="{image.title}" alt="{image.alternative}" src="" data-src="<f:uri.image treatIdAsReference="true" src="{image.uid}" width="700" />" data-src-retina="<f:uri.image treatIdAsReference=" true
						" src="{image.uid}" width="1400" />" />

					<div class="focuspoint" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
						data-image-w="{image.width}" data-image-h="{image.height}">
						<picture>
							<!--[if IE 9]><video style="display: none;"><![endif]-->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" width="430" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" width="860" /> 2x" media="(max-width: 480px)">
							<!-- 430xXXX -->
							<!-- 860xXXX -->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" width="700" />, <f:uri.image treatIdAsReference="true " src="{image.uid}" width="1400 " /> 2x" media="(max-width: 767px)">
							<!-- 700xXXX -->
							<!-- 1400xXXX -->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" width="430" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" width="860" /> 2x" media="(max-width: 1280px)">
							<!-- 430xXXX -->
							<!-- 860xXXX -->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" width="700" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" width="1400" /> 2x" media="(min-width: 1281px)">
							<!-- 700xXXX -->
							<!-- 1400xXXX -->
							<!--[if IE 9]></video><![endif]-->
							<img title="{image.title}" alt="{image.alternative}" src="" />
						</picture>

					</div>
					<f:if condition="{image.description}">
						<div class="img-caption" style="display:block">{image.description}</div>
					</f:if>
				</f:if>
				<div class="clearfix"></div>
			</div>
			<div class="col-right equal-height">
				<f:format.raw>
					<h3 class="h3">{header}</h3>
				</f:format.raw>
				<f:format.html>{bodytext}</f:format.html>
			</div>
			<div class="clearfix"></div>
		</div>
	</f:section>
</div>