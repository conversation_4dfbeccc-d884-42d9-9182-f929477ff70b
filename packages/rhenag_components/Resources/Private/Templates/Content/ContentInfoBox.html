<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="infoBox">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-infobox.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:field.input name="header" required="1" exclude="0" enabled="1" />
			<flux:field.select name="theme" items="{
								0:{0:'Default',1:''},
								1:{0:'Green',1:'green'}
							}" default="" />
			<flux:field.select name="halfSize" items="{
								0:{0:'full-width',1:''},
								1:{0:'half-width left side',1:'info-box-left'},
								2:{0:'half-width right side',1:'info-box-right'}
							}" default="" />
			<flux:field.text name="textTop" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
			<flux:field.text name="textBottom" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />


			<flux:form.sheet name="list">
				<flux:form.section name="items">
					<flux:form.object name="item">
						<f:render partial="SvgSelector" arguments="{fieldname: 'icon'}" />
						<flux:field.input name="text" required="0" exclude="0" enabled="1" />
					</flux:form.object>
				</flux:form.section>
			</flux:form.sheet>
		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.infoBox" />
		</h4>
		<b>{header}</b>
		<f:format.html>{textTop}</f:format.html>
		<f:format.html>{textBottom}</f:format.html>

	</f:section>

	<f:section name="Main">


		<div class="info-box {f:if(condition:theme, then:'info-box-{theme}')} {f:if(condition:halfSize, then:'{halfSize} equal-height')}">
			<f:if condition="{header}">
				<h3 class="h3">{header}</h3>
			</f:if>
			<f:if condition="{textTop}">
				<f:format.html>{textTop}</f:format.html>
			</f:if>
			<f:if condition="{items}">
				<f:for each="{items}" as="listItem" iteration="objIterator">
					<div class="icon-text">
						<div class="svg-icon svg-icon-sm">
							<f:render partial="SvgIcon" section="{listItem.item.icon}" arguments="{_all}" />
						</div>
						{listItem.item.text}
					</div>

				</f:for>
			</f:if>

			<f:if condition="{textBottom}">
				<f:format.html>{textBottom}</f:format.html>
			</f:if>
		</div>

	</f:section>
</div>