<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="rhenagpromoboxdouble">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-promoboxen-klein.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:form.sheet name="box1">
				<flux:field.input name="header" required="1" exclude="0" enabled="1" />
				<flux:field.text name="bodytext" required="1" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
				<flux:field.inline.fal name="image" minItems="1" maxItems="1" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
					table="sys_file_reference" />
                <flux:field.input name="linkButton" config="{renderType: 'inputLink'}" />
				<flux:field.input name="linkLabel" required="0" exclude="0" enabled="1" />
			</flux:form.sheet>
			<flux:form.sheet name="box2">
				<flux:field.input name="header2" required="1" exclude="0" enabled="1" />
				<flux:field.text name="bodytext2" required="1" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
				<flux:field.inline.fal name="image2" minItems="1" maxItems="1" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
					table="sys_file_reference" />
                <flux:field.input name="linkButton2" config="{renderType: 'inputLink'}" />
				<flux:field.input name="linkLabel2" required="0" exclude="0" enabled="1" />
			</flux:form.sheet>




		</flux:form>
	</f:section>

	<f:section name="Preview">

		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagpromoboxdouble" />
		</h4>
		<h5>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagpromoboxdouble" />
		</h5>

		<table class="doubleTable">
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header}</strong></f:format.raw>
					</f:format.crop>
				</td>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header2}</strong></f:format.raw>
					</f:format.crop>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.html>{bodytext}</f:format.html>
					</f:format.crop>
				</td>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.html>{bodytext2}</f:format.html>
					</f:format.crop>
				</td>
			</tr>
			<tr>
				<td>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" /><br/>
						</f:if>
					</v:content.resources.fal>
				</td>
				<td>
					<v:content.resources.fal field="image" as="images2" record="{record}">
						<f:if condition="{images2.0}">
							<f:image treatIdAsReference="1" src="{images2.0.id}" title="{images2.0.title}" width="100" alt="{images2.0.alternative}" /><br/>
						</f:if>
					</v:content.resources.fal>
				</td>
			</tr>

		</table>
		<style>
			.doubleTable {}
			
			.doubleTable td {
				padding-right: 20px;
				max-width: 20%
			}
		</style>
	</f:section>


	<f:section name="Main">

		{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')} {v:content.resources.fal(field: 'image2') -> v:iterator.first() -> v:variable.set(name: 'image2')}


		<!-- Promobox Double -->
		<div class="promobox-double">
			<div class="promobox equal-height">

				<div class="promobox-top">
					<div class="promobox-right mobile">
						<f:render section="Image" arguments="{_all}" />
					</div>
					<div class="promobox-left top-equal">
						<div>
							<f:render section="ContentArea" arguments="{_all}" />
						</div>
					</div>
					<div class="promobox-right top-equal">
						<f:render section="Image" arguments="{_all}" />
					</div>
				</div>

				<div class="promobox-bottom">
					<div class="promobox-left bottom-equal">
						<f:render section="Image2" arguments="{_all}" />
					</div>
					<div class="promobox-right bottom-equal">
						<div>
							<f:render section="ContentArea2" arguments="{_all}" />
						</div>
					</div>
					<div class="clearfix"></div>
				</div>

			</div>
		</div>

	</f:section>



	<f:section name="Image">
		<div class="promobox-image focuspoint" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
			data-image-w="{image.width}" data-image-h="{image.height}">
			<picture>
				<!--[if IE 9]><video style="display: none;"><![endif]-->
				<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="800" minHeight="400" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1600" minHeight="720" /> 2x" media="">
				<!-- 800x400-->
				<!-- 1280x720-->
				<!--[if IE 9]></video><![endif]-->
				<img title="{image.title}" alt="{image.alternative}" src="" />
			</picture>
		</div>
	</f:section>

	<f:section name="Image2">
		<div class="promobox-image focuspoint" data-focus-x="{f:if(condition:'{image2.focus_point_x}', then:'{v:math.division(a: image2.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image2.focus_point_y}', then:'{v:math.division(a: image2.focus_point_y, b: 100 )}', else:'0')}"
			data-image-w="{image2.width}" data-image-h="{image2.height}">
			<picture>
				<!--[if IE 9]><video style="display: none;"><![endif]-->
				<source srcset="<f:uri.image treatIdAsReference="true" src="{image2.uid}" maxWidth="800" minHeight="400" />, <f:uri.image treatIdAsReference="true" src="{image2.uid}" maxWidth="1600" minHeight="720" /> 2x" media="">
				<!-- 800x400-->
				<!-- 1280x720-->
				<!--[if IE 9]></video><![endif]-->
				<img title="{image2.title}" alt="{image2.alternative}" src="" />
			</picture>
		</div>
	</f:section>



	<f:section name="ContentArea">
		<h3 class="promobox-title">{header}</h3>
		<div class="promobox-bodytext">
			<f:format.html>{bodytext}</f:format.html>
		</div>
		<f:if condition="{linkButton}">
			<div class="promobox-link">
				<f:link.typolink parameter="{linkButton}" class="link-with-arrow">
					{linkLabel}
				</f:link.typolink>
			</div>
		</f:if>
	</f:section>

	<f:section name="ContentArea2">
		<h3 class="promobox-title">{header2}</h3>
		<div class="promobox-bodytext">
			<f:format.html>{bodytext2}</f:format.html>
		</div>
		<f:if condition="{linkButton2}">
			<div class="promobox-link">
				<f:link.typolink parameter="{linkButton2}" class="link-with-arrow">
					{linkLabel2}
				</f:link.typolink>
			</div>
		</f:if>
	</f:section>
</div>