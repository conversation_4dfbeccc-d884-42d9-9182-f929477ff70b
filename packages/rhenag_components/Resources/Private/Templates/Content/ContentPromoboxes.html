<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="rhenagpromoboxes">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-promoboxen-gross.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:field.select name="promobox-style" items="{
									0:{0:'LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagpromoboxsingle',1:'0'},
									1:{0:'LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagpromoboxwide',1:'1'}
								}" default="0" />
			<flux:field.input name="header" required="0" exclude="0" enabled="1" />
			<flux:field.text name="bodytext" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
			<flux:field.inline.fal name="image" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
				table="sys_file_reference" />
            <flux:field.input name="linkButton" config="{renderType: 'inputLink'}" />
			<flux:field.input name="linkLabel" required="0" exclude="0" enabled="1" />
			<flux:field.select name="buttonStyle" items="{
									0:{0:'Interner Link mit Pfeil',1:'0'},
									1:{0:'LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagblue',1:'btn-secondary'},
									2:{0:'LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagorange',1:'btn-primary'}
								}" default="0" />

		</flux:form>
	</f:section>

	<f:section name="Preview">

		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagpromoboxes" />
		</h4>
		<f:if condition="{promobox-style} == 0">
			<h5>
				<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagpromoboxsingle" />
			</h5>
		</f:if>
		<f:if condition="{promobox-style} == 1">
			<h5>
				<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagpromoboxwide" />
			</h5>
		</f:if>
		<table>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header}</strong></f:format.raw>
					</f:format.crop>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.html>{bodytext}</f:format.html>
					</f:format.crop>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" /><br/>
						</f:if>
					</v:content.resources.fal>
				</td>
			</tr>
		</table>
	</f:section>


	<f:section name="Main">

		{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}
		<f:if condition="{promobox-style}==0">
			<f:then>
				<!-- Promobox Single -->
				<div class="promobox-single">
					<div class="promobox">
						<f:render section="Image" arguments="{_all}" />
						<div class="promobox-bottom">
							<div class="promobox-info-container">
								<f:render section="ContentArea" arguments="{_all}" />
							</div>
						</div>
					</div>
				</div>
			</f:then>
			<f:else>
				<!-- Promobox Wide -->
				<div class="promobox-wide">
					<div class="promobox">
						<div class="promobox-left left-right-equal">
							<f:render section="Image" arguments="{_all}" />
						</div>
						<div class="promobox-right left-right-equal">
							<div>
								<f:render section="ContentArea" arguments="{_all}" />
							</div>
						</div>

					</div>
				</div>
			</f:else>
		</f:if>

	</f:section>



	<f:section name="Image">
		<f:if condition="{image}">
		<f:then>
			<f:if condition="{promobox-style}==1">
				<f:then>
					<div class="promobox-image focuspoint" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
						 data-image-w="{image.width}" data-image-h="{image.height}">
						<picture>
							<!--[if IE 9]><video style="display: none;"><![endif]-->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="480" minHeight="270" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="800" minHeight="400" /> 2x" media="(max-width: 480px)">
							<!-- 480x270-->
							<!-- 800x400-->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="800" minHeight="400" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1280" minHeight="720" /> 2x" media="(max-width: 1200px)">
							<!-- 800x400-->
							<!-- 1280x720-->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1280" minHeight="720" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1920" minHeight="1080" /> 2x" media="(min-width: 1201px)">
							<!-- 1280x720-->
							<!-- 1920x1080-->
							<!--[if IE 9]></video><![endif]-->
							<img title="{image.title}" alt="{image.alternative}" src="" />
						</picture>
					</div>
				</f:then>
				<f:else>
					<div class="promobox-image top-equal focuspoint" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
						 data-image-w="{image.width}" data-image-h="{image.height}">
						<picture>
							<!--[if IE 9]><video style="display: none;"><![endif]-->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1024" minHeight="400" />,
							<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1024" minWidth="1280" minHeight="720" /> 2x" media="">
							<!-- 800x400-->
							<!-- 1280x720-->
							<!--[if IE 9]></video><![endif]-->
							<img title="{image.title}" alt="{image.alternative}" src="" />
						</picture>
					</div>
				</f:else>
			</f:if>
		</f:then>
		<f:else>
			<p>kein Bild verfügbar</p>
		</f:else>

		</f:if>
	</f:section>



	<f:section name="ContentArea">
		<h3 class="promobox-title">{header}</h3>
		<div class="promobox-bodytext">
			<f:format.html>{bodytext}</f:format.html>
		</div>
		<f:if condition="{linkButton}">
			<f:if condition="{buttonStyle}">
				<f:then>
					<div class="promobox-link">
						<p class="align-center">
							<f:link.typolink parameter="{linkButton}" class="btn {buttonStyle} btn-lg btn-cta">
								{linkLabel}
							</f:link.typolink>
						</p>
					</div>
				</f:then>
				<f:else>
					<div class="promobox-link">
						<f:link.typolink parameter="{linkButton}" class="link-with-arrow">
							{linkLabel}
						</f:link.typolink>
					</div>
				</f:else>
			</f:if>
		</f:if>
	</f:section>
</div>