<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="rhenagspecialteaser">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-bildstreifen.svg" />
			<flux:form.option.group value="Rhenag Komponente" />

			<flux:field.select name="teaser-style" items="{
                                0:{0:'Text rechts',1:'0'},
                                1:{0:'Text über Bild (breit)',1:'1'}
                           }" default="0" />
			<flux:field.inline.fal name="image" label="Bild" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
				table="sys_file_reference" />
			<flux:field.input name="header" required="0" exclude="0" enabled="1" />
			<flux:field.text name="subheader" required="0" exclude="0" enabled="1" rows="3" />
			<flux:field.text name="bodytext" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
            <flux:field.input name="linkButton" config="{renderType: 'inputLink'}" />
			<flux:field.input name="linkLabel" required="0" exclude="0" enabled="1" />


		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagspecialteaser" />
		</h4>

		<table>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header}</strong></f:format.raw>
					</f:format.crop>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.raw>{subheader}</f:format.raw>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.html>{bodytext}</f:format.html>
					</f:format.crop>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" /><br/>
						</f:if>
					</v:content.resources.fal>
				</td>
			</tr>
		</table>
	</f:section>


	<f:section name="Main">

		{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}

		<div class="teaser-special {f:if(condition:'{teaser-style}==1', then: 'teaser-special-2', else: '')} focuspoint" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
			data-image-w="{image.width}" data-image-h="{image.height}">
			<f:if condition="{image}">
				<picture>
					<!--[if IE 9]><video style="display: none;"><![endif]-->
					<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" width="480" height="117" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" width="960" height="234" /> 2x" media="(max-width: 480px)">
					<!-- 480x117-->
					<!-- 960x234-->
					<source srcset="<f:uri.image treatIdAsReference=" true" src="{image.uid}" width="480" height="117" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" width="1600" height="390" /> 2x" media="(max-width: 800px)">
					<!-- 480x117-->
					<!-- 1600x390-->
					<source srcset="<f:uri.image treatIdAsReference=" true" src="{image.uid}" width="1240" height="302" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" width="1920" height="468" /> 2x" media="(max-width: 1240px)">
					<!-- 1240x302-->
					<!-- 1920x468-->
					<source srcset="<f:uri.image treatIdAsReference=" true" src="{image.uid}" width="1920" height="468" />" media="(min-width: 1241px)">
					<!-- 1920x468-->
					<!--[if IE 9]></video><![endif]-->
					<img title="{image.title}" alt="{image.alternative}" src="" />
				</picture>
			</f:if>
			<div class="fluid-container">
				<div class="row">
					<div class="teaser-content">
						<div>
							<h3 class="{f:if(condition:'{teaser-style}==1', then: 'heroTitle', else: 'teaser-special-title')}">{header}</h3>
							<p class="{f:if(condition:'{teaser-style}==1', then: 'heroSubtitle', else: 'teaser-special-subtitle')}">
								<f:format.nl2br>{subheader}</f:format.nl2br>
							</p>

							<f:format.html>{bodytext}</f:format.html>

							<f:if condition="{teaser-style}==0">
								<f:if condition="{linkButton}">
									<f:link.typolink parameter="{linkButton}" class="btn btn-primary btn-lg btn-cta">
										{linkLabel}
									</f:link.typolink>
								</f:if>
							</f:if>

						</div>
					</div>
					<f:if condition="{linkButton}">
						<div class="teaser-cta">
							<div>
								<f:link.typolink parameter="{linkButton}" class="btn btn-primary btn-lg btn-cta">
									{linkLabel}
								</f:link.typolink>
							</div>
						</div>
					</f:if>
				</div>
			</div>
		</div>

	</f:section>
</div>