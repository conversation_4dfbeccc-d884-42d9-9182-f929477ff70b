<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="rhenagcontentpageteaser">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-header-schmal.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:field.select name="text_position" items="{
			        0:{0:'Text Left',1:'position-left'},
			        1:{0:'Text Right',1:'position-right'}
			        }" />
			<flux:field.inline.fal name="image" label="Bild" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
				table="sys_file_reference" />
			<flux:field.text name="header" required="0" exclude="0" enabled="1" rows="3" />
			<flux:field.checkbox name="small" label="Headline ohne Hintergrund & ggf. Subheadline ohne Hintergrund anzeigen" default="0" exclude="0" />
			<flux:field.text name="subheader" required="0" exclude="0" enabled="1" rows="3" />


		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.rhenagcontentpageteaser" />
		</h4>

		<table>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header}</strong></f:format.raw>
					</f:format.crop>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.raw>{subheader}</f:format.raw>
				</td>
			</tr>
			<tr>
				<td>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" /><br/>
						</f:if>
					</v:content.resources.fal>
				</td>
			</tr>
		</table>
	</f:section>


	<f:section name="Main">

		{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}

		<section class="teaser-contentpage">
			<div class="teaser-canvas">
				<div class="focuspoint teaser-item teaser-image text-{text_position} active" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
					data-image-w="{image.width}" data-image-h="{image.height}">
					<f:if condition="{image}">
						<picture>
							<!--[if IE 9]><video style="display: none;"><![endif]-->
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}"  maxHeight="400" />, <f:uri.image treatIdAsReference="true" src="{image.uid}"  maxHeight="800" /> 2x" media="(max-width: 960px)">
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}"  maxHeight="600" />, <f:uri.image treatIdAsReference="true" src="{image.uid}"  maxHeight="1200" /> 2x" media="(max-width: 1240px)">
							<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}"  maxWidth="1920" />" media="(min-width: 1241px)">
							<!--[if IE 9]></video><![endif]-->
							<img title="{image.title}" alt="{image.alternative}" src="" />
							<div class="fluid-container">
								<f:if condition="{small} < 1">
									<f:if condition="{header}">
										<div class="heroTitle">
											<f:format.nl2br>{header}</f:format.nl2br>
										</div>
									</f:if>
									<f:if condition="{subheader}">
										<div class="heroSubtitle">
											<f:format.nl2br>{subheader}</f:format.nl2br>
										</div>
									</f:if>
								</f:if>
								<f:if condition="{small}==1">
									<f:if condition="{header}">
										<div class="heroTitle NoBackground teaser-special-subtitle" style="background-color:transparent;font-size:1.6rem;bottom:43px;">
											<f:format.nl2br>{header}</f:format.nl2br>
											<f:if condition="{subheader}">
												<p class="heroSubtitleNoBackground">
													<f:format.nl2br>{subheader}</f:format.nl2br>
												</p>
											</f:if>
										</div>
									</f:if>

								</f:if>
							</div>
						</picture>
					</f:if>
				</div>
			</div>
		</section>

	</f:section>
</div>