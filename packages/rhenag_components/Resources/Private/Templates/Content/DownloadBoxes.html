<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">
		<flux:form id="downloadBoxes">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-linkbox.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:field.input name="header" required="0" exclude="0" enabled="1" />
			<flux:field.text name="text" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
			<flux:field.inline.fal name="files" minItems="0" maxItems="25" showThumbs="1" collapseAll="1" allowedExtensions="pdf" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
				table="sys_file_reference" />
		</flux:form>
	</f:section>



	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.linkBoxes" />
		</h4>
		<f:if condition="{files}">
			<f:then>
				<ol class="downloadBoxes {file.extension}">
					<v:content.resources.fal field="image" as="files" record="{record}">
						<f:for each="{files}" as="file">
							<f:if condition="{file}">
								<li>{f:if(condition: file.title, then: '{file.title}', else: '{file.name}')}&nbsp;(
									<f:format.bytes>{file.size}</f:format.bytes>)<br />
									<f:if condition="{file.description}">
										<p>{file.description}</p>
									</f:if>
								</li>
							</f:if>
						</f:for>
					</v:content.resources.fal>
				</ol>
			</f:then>
			<f:else>
				<p>Keine Dateien ausgewählt - bitte auswählen</p>
			</f:else>
		</f:if>
	</f:section>


	<f:section name="Main">

		<f:if condition="{header}">
			<f:format.raw>{header}</f:format.raw>
		</f:if>
		<f:if condition="{text}">
			<f:format.html>{text}</f:format.html>
		</f:if>
		<f:if condition="{files}">

			<f:security.ifAuthenticated>
				<f:then>
					<f:for each="{v:content.resources.fal(field: 'files')}" as="file">
						<p class="downloadBoxes {file.extension}">
							<f:link.typolink target="_blank" class="link-download" title="{file.title}" parameter="{file.url}">{f:if(condition: file.title, then: '{file.title}', else: '{file.name}')}&nbsp;(
								<f:format.bytes>{file.size}</f:format.bytes>)</f:link.typolink>
						</p>
						<f:if condition="{file.description}">
							<p>{file.description}</p>
						</f:if>
					</f:for>
				</f:then>
				<f:else>
					<f:for each="{v:content.resources.fal(field: 'files')}" as="file">
						<p class="downloadBoxes {file.extension}">
							<f:link.typolink class="link-download" title="{file.title}" parameter="<f:cObject typoscriptObjectPath='lib.rhenagDownloadsRegisterUid' />">{f:if(condition: file.title, then: '{file.title}', else: '{file.name}')}&nbsp;(
								<f:format.bytes>{file.size}</f:format.bytes>)</f:link.typolink>
						</p>
						<f:if condition="{file.description}">
							<p>{file.description}</p>
						</f:if>
					</f:for>
				</f:else>
			</f:security.ifAuthenticated>
		</f:if>

		<div class="clearfix"></div>
	</f:section>




</div>