<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="homepagevideocontainer">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-header-mit-icons.svg" />
			<flux:form.option.group value="Rhenag Komponente" />

			<flux:form.sheet name="common">
				<flux:field.inline.fal name="image" label="Bild" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
					table="sys_file_reference" />
				<flux:field.file name="video_file" label="Video" required="0" exclude="0" showThumbnails="1" multiple="false" allowed="mp4" />
			</flux:form.sheet>
		</flux:form>
	</f:section>

	<f:section name="Preview">


	</f:section>


	<f:section name="Main">

	</f:section>


	<f:section name="Image">
		<picture>
			<!--[if IE 9]><video style="display: none;"><![endif]-->
			<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="500" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1000"  /> 2x" media="(max-width: 480px)">
			<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1100" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1920"  /> 2x" media="(max-width: 1000px)">
			<!-- 800x400-->
			<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1920"  />" media="(min-width: 1001px)">
			<!-- 1920x1080-->
			<!--[if IE 9]></video><![endif]-->
			<img title="{image.title}" alt="{image.alternative}" src="" />
		</picture>
	</f:section>


</div>