<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="iconText">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-icon-mit-text.svg" />
			<flux:form.option.group value="Rhenag Komponente" />

			<flux:form.sheet name="common">
				<flux:field.input name="header" required="0" exclude="0" enabled="1" />
			</flux:form.sheet>

			<flux:form.sheet name="panels">
				<flux:form.section name="panels">

					<flux:form.object name="panel">
						<f:render partial="SvgSelector" arguments="{fieldname:'panel_icon'}" />
						<flux:field.input name="panel_header" required="0" exclude="0" enabled="1" />
						<flux:field.text name="panel_text" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
					</flux:form.object>

				</flux:form.section>
			</flux:form.sheet>

		</flux:form>

	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.iconText" />
		</h4>
		<p>
			<f:format.crop maxCharacters="50">
				<f:format.raw><strong>{header}</strong></f:format.raw>
			</f:format.crop>
		</p>
		<p>EINTRÄGE:
			<v:l key="panels" />: {panels -> f:count()}</p>
	</f:section>


	<f:section name="Main">

		<div class="iconText-wrapper">
			<div class="col-12">
				<div class="iconText-container-header">
					<h3 class="h3">{header}</h3>
				</div>
			</div>
			<div class="col-12 iconText-container-panels">
				<f:for each="{panels}" as="panel" iteration="objIterator">
					<f:if condition="{objIterator.cycle} <= 3">
						<div class="iconText-element">
							<div>
								<div class="svg-icon">
									<f:render partial="SvgIcon" section="{panel.panel.panel_icon}" arguments="{_all}" />
								</div>
								<div class="iconText-header equal-height"><strong>{panel.panel.panel_header}</strong></div>
								<div class="iconText-text">
									<f:format.html>{panel.panel.panel_text}</f:format.html>
								</div>
							</div>
						</div>
					</f:if>
				</f:for>
			</div>
		</div>

	</f:section>


</div>