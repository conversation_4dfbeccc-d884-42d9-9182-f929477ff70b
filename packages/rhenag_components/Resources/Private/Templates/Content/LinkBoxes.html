<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="linkBoxes">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-linkbox.svg" />
			<flux:form.option.group value="Rhenag Komponente" />

			<flux:form.sheet name="boxes">
				<flux:form.section name="boxes">
					<flux:form.object name="box">
						<f:render partial="SvgSelector" arguments="{fieldname:'icon'}" />
						<flux:field.input name="header" required="0" exclude="0" enabled="1" />
						<flux:field.text name="text" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
					</flux:form.object>
				</flux:form.section>
			</flux:form.sheet>

		</flux:form>

	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.linkBoxes" />
		</h4>
		<p>EINTRÄGE:
			<v:l key="Boxes" />: {boxes -> f:count()}</p>
	</f:section>


	<f:section name="Main">
		<div class="link-boxes link-boxes-{boxes -> f:count()}">
			<f:for each="{boxes}" as="box" iteration="iterator">
				<div class="link-box {f:if(condition: iterator.isLast, then: 'col-last')}">
					<div>
						<div class="link-box-header">
							<div class="svg-icon svg-icon-sm">
								<f:render partial="SvgIcon" section="{box.box.icon}" arguments="{_all}" />
							</div>
							{box.box.header}
						</div>
						<div class="link-box-body">
							<f:format.html>{box.box.text}</f:format.html>
						</div>
					</div>
				</div>
			</f:for>
			<div class="clearfix"></div>
		</div>
	</f:section>


</div>