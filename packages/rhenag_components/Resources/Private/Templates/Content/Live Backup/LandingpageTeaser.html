<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="teaserlandingpage" options="{group: 'Rhenag Komponente' , icon: '{f:uri.resource(path: \'Icons/Content/icon-element-header-mit-icons.svg\')}' }">

			<flux:form.sheet name="common">
				<flux:field.inline.fal name="image" label="Bild" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
					table="sys_file_reference" />
				<flux:field.select name="type" items="{
	                       0:{0:'Image',1:'image'},
	                       1:{0:'Video',1:'video'}
	                       }" />
				<flux:field.file name="video_mp4" internal_type="db" label="Video MP4" required="0" exclude="0" showThumbnails="1" multiple="false" allowed="mp4" />
				<flux:field.file name="video_webm" internal_type="db" label="Video WEBM" required="0" exclude="0" showThumbnails="1" multiple="false" allowed="webm" />
				<flux:field.checkbox name="video_loop" required="0" exclude="0" default="false" enabled="1" />
				<flux:field.select name="video_position" items="{
                       0:{0:'Video center-center',1:'position-center-center'},
                       1:{0:'Video top-left',1:'position-top-left'},
                       2:{0:'Video top-right',1:'position-top-right'},
                       3:{0:'Video center-left',1:'position-center-left'},
                       4:{0:'Video center-right',1:'position-center-right'},
                       5:{0:'Video bottom-left',1:'position-bottom-left'},
                       6:{0:'Video bottom-right',1:'position-bottom-right'},
                       7:{0:'Video top-center',1:'position-top-center'},
                       8:{0:'Video bottom-center',1:'position-bottom-center'}
                       }" />
				<flux:field.select name="text_position" items="{
                       0:{0:'Text Left',1:'position-left'},
                       1:{0:'Text Right',1:'position-right'}
                       }" />
				<flux:field.text name="header" required="0" exclude="0" enabled="1" rows="3" />
				<flux:field.text name="subheader" required="0" exclude="0" enabled="1" rows="3" />
				<flux:field.input name="title" required="0" exclude="0" enabled="1" />
				<flux:field.text name="teasertext" required="0" exclude="0" enabled="1" validate="'trim'" rows="10" enableRichText="1" />
			</flux:form.sheet>

			<flux:form.sheet name="panels">
				<flux:form.section name="panels">

					<flux:form.object name="panel">
						<f:render partial="SvgSelector" arguments="{fieldname:'panel_icon'}" />

						<f:comment>
							<flux:field.select name="svg-icon-css" items="{
                                0:{0:'orange',1:'svg-icon'},
                                1:{0:'green',1:'svg-icon svg-icon-green'},
								2:{0:'blue',1:'svg-icon svg-icon-blue'}
                           }" default="svg-icon" label="Style Theme (SVG-Icon & Header)" />
						</f:comment>
						<flux:field.input name="panel_header" required="0" exclude="0" enabled="1" />
						<flux:field.input name="panel_text" required="0" exclude="0" enabled="1" />
						<flux:field.input name="panel_link_text" required="0" exclude="0" enabled="1" />
                        <flux:field.input name="link" config="{renderType: 'inputLink'}" />
					</flux:form.object>

				</flux:form.section>
			</flux:form.sheet>

		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.teaserlandingpage" />
		</h4>

		<table>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header}</strong></f:format.raw>
					</f:format.crop>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.raw>{subheader}</f:format.raw>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.html>{bodytext}</f:format.html>
					</f:format.crop>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" />
						</f:if>
					</v:content.resources.fal>
					<br/>
				</td>
			</tr>

		</table>

		<p>EINTRÄGE:
			<v:l key="panels" />: {panels -> f:count()}</p>
	</f:section>


	<f:section name="Main">

		{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}



		<section class="teaser-landingpage" id="teaser-landingpage">
			<div class="teaser-canvas">
				<div class="focuspoint teaser-item teaser-{type} text-{text_position} active" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
					data-image-w="{image.width}" data-image-h="{image.height}">

					<f:render section="Image" arguments="{_all}" />

					<f:if condition="{type} == 'video'">
						<f:then>
							<video id="video{v:variable.register.get(name: 'index')}" poster="<f:uri.image treatIdAsReference=" true " src="{image.uid} " width="1280 " height="720 " />" class="{video_position}" <f:if condition="{video_loop}">
								<f:then>
								loop="true" 
								</f:then>
								<f:else>
								
								</f:else>
							</f:if>
							muted="muted">
								<f:if condition="{video_mp4}">
									<f:then>
										<source src="{video_mp4}" />
									</f:then>
								</f:if>
								<f:if condition="{video_mp4}">
									<f:then>
										<source src="{video_webm}" />
									</f:then>
								</f:if>
							</video>
						</f:then>
					</f:if>

					<f:if condition="{header}">
						<div class="heroTitle">
							<f:format.nl2br>{header}</f:format.nl2br>
						</div>
					</f:if>
					<f:if condition="{subheader}">
						<div class="heroSubtitle">
							<f:format.nl2br>{subheader}</f:format.nl2br>
						</div>
					</f:if>
				</div>
				<a href="/#teaser-info" data-target="#teaser-info" class="move-down scrollTo hidden-xs">
					<div class="mouse-icon">
						<div class="wheel"></div>
					</div>
					<div class="text-center">
						<svg enable-background="new 0 0 50 50" height="30px" id="Layer_1" version="1.1" viewBox="0 0 50 50" width="30px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect fill="none" height="50" width="50"></rect><polygon fill="#fff" points="47.25,15 45.164,12.914 25,33.078 4.836,12.914 2.75,15 25,37.25 "></polygon></svg>
					</div>
				</a>
			</div>

			<div class="teaser-info" id="teaser-info">
				<div class="fluid-container">
					<div class="row">
						<div class="col-8-center">
							<f:if condition="{title}">
								<h1 class="h2">
									<f:format.raw>{title}</f:format.raw>
								</h1>
							</f:if>
							<f:if condition="{teasertext}">
								<f:format.html>{teasertext}</f:format.html>
							</f:if>
						</div>


						<div class="col-12 teaser-info-box-container">
							<f:for each="{panels}" as="panel" iteration="objIterator">


								<f:if condition="{objIterator.cycle} <= 4">
									<div class="teaser-info-box">
										<div>
											<f:link.typolink parameter="{panel.panel.link}">
												<div class="svg-icon">
													<f:render partial="SvgIcon" section="{panel.panel.panel_icon}" arguments="{_all}" />
												</div>
												<h2 class="teaser-landingpage-title equal-height text-color-primary">{panel.panel.panel_header}</h2>
												<f:if condition="{panel.panel.panel_text}">
													<div class="teaser-landingpage-text">
														<f:format.html>{panel.panel.panel_text}</f:format.html>
													</div>
												</f:if>
												<f:if condition="{panel.panel.panel_link_text}">
													<p class="link-with-arrow">{panel.panel.panel_link_text}</p>
												</f:if>
											</f:link.typolink>
										</div>
									</div>
								</f:if>


							</f:for>
						</div>

					</div>
				</div>
			</div>

		</section>

	</f:section>


	<f:section name="Image">
		<picture>
			<!--[if IE 9]><video style="display: none;"><![endif]-->
			<source srcset="<f:uri.image treatIdAsReference=" true " src="{image.uid} " maxWidth="500 " />, <f:uri.image treatIdAsReference="true " src="{image.uid} " maxWidth="1000 "  /> 2x" media="(max-width: 480px)">
			<source srcset="<f:uri.image treatIdAsReference=" true " src="{image.uid} " maxWidth="1100 " />, <f:uri.image treatIdAsReference="true " src="{image.uid} " maxWidth="1920 "  /> 2x" media="(max-width: 1000px)">
			<!-- 800x400-->
			<source srcset="<f:uri.image treatIdAsReference=" true " src="{image.uid} " maxWidth="1920 "  />" media="(min-width: 1001px)">
			<!-- 1920x1080-->
			<!--[if IE 9]></video><![endif]-->
			<img title="{image.title}" alt="{image.alternative}" src="" />
		</picture>
	</f:section>


</div>