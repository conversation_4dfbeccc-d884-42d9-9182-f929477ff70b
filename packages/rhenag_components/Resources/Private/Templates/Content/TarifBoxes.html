<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="tarifBoxes" label="Tarif Boxen">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-tarifbox.svg" />
			<flux:form.option.group value="Rhenag Komponente" />

			<flux:form.sheet name="boxes">
				<flux:form.section name="boxes">
					<flux:form.object name="box">
						<flux:field.input
								name="header"
								required="0"
								exclude="0"
								enabled="1" />
						<flux:field.input
								name="subline"
								label="Subline"
								required="0"
								exclude="0"
								enabled="1" />
						<flux:field.text
								name="text"
								required="0"
								exclude="0"
								enabled="1"
								validate="'trim'"
								rows="10"
								enableRichText="1" />
						<flux:field.file
								name="image"
								label="Bild"
								allowed="jpg,png,svg"
								showThumbnails="1" />
					</flux:form.object>
				</flux:form.section>
			</flux:form.sheet>

		</flux:form>

	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.tarifBoxes" />
		</h4>
		<p>EINTRÄGE:
			<v:l key="Boxes" />: {boxes -> f:count()}</p>
	</f:section>
 

	<f:section name="Main">
		<div class="link-boxes link-boxes-{boxes -> f:count()}">
			<f:for each="{boxes}" as="box" iteration="iterator">
				<div class="link-box tafif-box {f:if(condition: iterator.isLast, then: 'col-last')}">
					<div>
						<div class="tarif-box-header">
							<strong>{box.box.header}</strong>
							<f:if condition="{box.box.subline}"><br><span class="small">{box.box.subline}</span></f:if>
						</div>
						<f:if condition="{box.box.image}">
							<div class="tarif-box-image" style="width:100%;height:150px;overflow:hidden">
								<f:image class="tarif-box-image--image" src="{box.box.image}"/>
							</div>
						</f:if>
						<div class="link-box-body">
							<f:format.html>{box.box.text}</f:format.html>
						</div>
					</div>
				</div>
			</f:for>
			<div class="clearfix"></div>
		</div>
	</f:section>


</div>