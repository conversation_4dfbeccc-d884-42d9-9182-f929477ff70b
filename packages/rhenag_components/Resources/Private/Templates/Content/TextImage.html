<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

<f:layout name="Content" />

<f:section name="Configuration">

	<flux:form id="textImage">
		<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-bild-und-text.svg" />
		<flux:form.option.group value="Rhenag Komponente" />

		<flux:form.sheet name="box1">
			<flux:field.text name="preline" required="0" exclude="0" enabled="1" rows="2" />
			<flux:field.text name="headline" required="0" exclude="0" enabled="1" rows="2" />
			<flux:field.select name="headlineSEOTag" items="{
				                           			0:{0:'H1',1:'h1'},
				                           			1:{0:'H2',1:'h2'},
				                           			2:{0:'H3',1:'h3'}
				                           			}" />
			<flux:field.select name="headlineStyle" items="{
				                           			0:{0:'Überschrift-1',1:'h1'},
				                           			1:{0:'Überschrift-1 Alternativ',1:'h1-alternative'},
				                           			2:{0:'Überschrift-2',1:'h2'},
				                           			3:{0:'Überschrift-2 Alternativ',1:'h2-alternative'},
				                           			4:{0:'Überschrift-3',1:'h3'}
				                           			}" />
			<flux:field.select name="headlineAlignment" items="{
				                           			0:{0:'Links',1:'left'},
				                           			1:{0:'Mitte',1:'center'}
				                           			}" />
			<flux:field.text name="text" required="0" exclude="0" enabled="1" rows="1" enableRichText="1" />
		</flux:form.sheet>
		<flux:form.sheet name="box2">
			<flux:field.inline.fal name="image" minItems="0" maxItems="1" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
				table="sys_file_reference" />
			<flux:field.select name="imageAlignment" items="{
				                           			0:{0:'Links Umfließend',1:'left'},
				                           			1:{0:'Rechts Umfließend',1:'right'},
				                           			2:{0:'Oben',1:'top'},
				                           			3:{0:'Oben Mitte',1:'top-center'},
				                           			4:{0:'Unten',1:'bottom'},
				                           			5:{0:'Unten Mitte',1:'bottom-center'},
				                           			6:{0:'Links',1:'left-alone'},
				                           			7:{0:'Rechts',1:'right-alone'}
				                           			}" />
			<flux:field.select name="imageSize" items="{
               			0:{0:'Small',1:'img-size-sm'},
               			1:{0:'Medium',1:'img-size-md'},
               			2:{0:'Full',1:'img-size-full'}
               			}" />
			<flux:field.input name="imageWidth" required="0" exclude="0" enabled="1" />
			<flux:field.checkbox name="lightbox" required="0" exclude="0" default="false" enabled="1" />
		</flux:form.sheet>


	</flux:form>

</f:section>

<f:section name="Preview">
	<v:content.resources.fal field="image" as="images" record="{record}">
		<h3>
			<f:translate key="LLL:EXT:rhenag_components/Resources/Private/Language/locallang.xlf:flux.textImage" />
		</h3>
		<table class="table" style="max-width: 90%;padding:5px">
			<tr>
				<td colspan="2">
					<p>
						<f:format.crop maxCharacters="50">
							<f:format.raw>{preline}</f:format.raw>
						</f:format.crop>
					</p>
					<p>
						<f:format.crop maxCharacters="50">
							<f:format.raw><strong>{headline}</strong></f:format.raw>
						</f:format.crop>
					</p>
				</td>
			</tr>
			<tr>
				<td style="min-width:unset;max-width: 150px;padding:5px" valign="top">
					<f:if condition="{images.0}">
						<f:image maxWidth="150" treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" alt="{images.0.alternative}" />
					</f:if>
				</td>
				<td style="min-width:unset;max-width: 150px;padding:5px;text-align:left;" valign="top">
					<p>
						<f:format.crop maxCharacters="150">
							<f:format.html>{text}</f:format.html>
						</f:format.crop>
					</p>
				</td>
			</tr>
		</table>
	</v:content.resources.fal>
</f:section>


<f:section name="Main">
	{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}
	<div class="text-image">
		<f:if condition="{preline}">
			<p class="preline text-align-{headlineAlignment}">{preline}</p>
		</f:if>
		<f:if condition="{headline}">
			<{headlineSEOTag} class="{headlineStyle} text-align-{headlineAlignment}">{headline}</{headlineSEOTag}>
		</f:if>
		<f:if condition="{image}">
			<f:if condition="{imageAlignment} == 'top'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
			<f:if condition="{imageAlignment} == 'top-center'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
			<f:if condition="{imageAlignment} == 'left'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
			<f:if condition="{imageAlignment} == 'right'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
			<f:if condition="{imageAlignment} == 'left-alone'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
			<f:if condition="{imageAlignment} == 'right-alone'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
		</f:if>
		<div class="text-with-image-{imageAlignment} text-with-{imageSize}">
			<f:if condition="{text}">
				<f:format.html>{text}</f:format.html>
			</f:if>
		</div>
		<f:if condition="{image}">
			<f:if condition="{imageAlignment} == 'bottom'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
			<f:if condition="{imageAlignment} == 'bottom-center'">
				<f:render section="Image" arguments="{_all}" />
			</f:if>
		</f:if>
		<div class="clearfix"></div>
	</div>
</f:section>


<f:section name="Image">
<f:if condition="{v:content.resources.fal(field: 'image') -> v:iterator.first()}">
		{v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}
	<div class="image-{imageAlignment} {imageSize}">
		<f:if condition="{lightbox}">
			<a href="{f:uri.image(treatIdAsReference:'1', src:'{image.uid}', maxWidth:'1024')}" class="image-popup-link" title="{image.description}">
		</f:if>
		<f:link.typolink parameter="{image.link}">
			<picture class="{imageSize}">

				<f:if condition="{imageWidth}">
					<f:then>
						<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" width="768"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="1536" /> 2x" media="(max-width: 767px)"> <!-- 768-->
						<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="{imageWidth}"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="{v:math.product(a: '{imageWidth}', b: 2)}" /> 2x" media="(min-width: 768px)">
						<img title="{image.title}"  alt="{image.alternative}" src="data:image/gif;base64,R0lGODlhAQABAAAAADs=" width="{imageWidth}" />
					</f:then>
					<f:else>
						<f:if condition="{imageSize} == 'img-size-sm'">
							<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" width="768"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="1536" /> 2x" media="(max-width: 767px)"> <!-- 768-->
							<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="350"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="700" /> 2x" media="(min-width: 768px)">
						</f:if>
						<f:if condition="{imageSize} == 'img-size-md'">
							<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" width="768"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="1536" /> 2x" media="(max-width: 767px)"> <!-- 768-->
							<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="700"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="1400" /> 2x" media="(min-width: 768px)">
						</f:if>
						<f:if condition="{imageSize} == 'img-size-full'">
							<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" width="768"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="1536" /> 2x" media="(max-width: 767px)"> <!-- 768-->
							<f:if condition="{imageWidth} < 1400">
								<f:then>
									<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" width="700"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" width="1400" /> 2x" media="(min-width: 768px)">
								</f:then>
								<f:else>
									<source srcset="<f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="1400"/>, <f:uri.image treatIdAsReference="1" src="{image.uid}" maxWidth="2800" /> 2x" media="(min-width: 768px)">
								</f:else>
							</f:if>
						</f:if>
						<img title="{image.title}"  alt="{image.alternative}" src="data:image/gif;base64,R0lGODlhAQABAAAAADs=" class="img-responsive" width="{f:if(condition: '{imageWidth}', then: '{imageWidth}', else: '100%')}"/>
					</f:else>
				</f:if>
			</picture>


		</f:link.typolink>
		<f:if condition="{lightbox}">
			</a>
		</f:if>
		<f:if condition="{image.description}">
			<p class="img-caption">
				<f:format.nl2br>{image.description}</f:format.nl2br>
			</p>
		</f:if>
	</div>
</f:if>
</f:section>

</html>