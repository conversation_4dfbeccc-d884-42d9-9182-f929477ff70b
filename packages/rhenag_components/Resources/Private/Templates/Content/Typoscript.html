<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">
	<f:layout name="Content" />
	<f:section name="Configuration">
		<flux:form id="typoscript">
			<flux:form.option.icon value="EXT:rhenag_components/Resources/Public/Icons/Content/icon-element-linkbox.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:form.sheet name="typoscript">
				<flux:field.input name="typoscript" label="Typoscript object" required="0" exclude="0" enabled="1" />
			</flux:form.sheet>
		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>Typoscript</h4>
		<p>OBJECT: {typoscript}</p>
	</f:section>

	<f:section name="Main">
		<f:cObject typoscriptObjectPath="{typoscript}" />
	</f:section>
</div>