<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Templates for felogin</title>
</head>
<body>

<h1>Common markers</h1>
<p>These are substituted in all felogin item displaying templates.</p>

<h2>Markers</h2>
<ul>
	<li>###ACTION_URI###     - URI of the request for the login/logout form</li>
	<li>###EXTRA_HIDDEN###   - Hook required (additional hidden field used by kb_md5fepw extension by <PERSON><PERSON>)</li>
	<li>###ON_SUBMIT###      - Hook required (used by kb_md5fepw extension by <PERSON><PERSON>)</li>
	<li>###PREFIXID###       - Same as class name ('tx_felogin_pi1') useful to get a unique classname prefix</li>
	<li>###REDIRECT_URL###   - URL of redirection upon login</li>
	<li>###NOREDIRECT###     - if set, no redirect will be done</li>
	<li>###STORAGE_PID###    - explicit enough I guess (if not : id of the page where user are stored)</li>
	<li>###STATUS_HEADER###  - depends of the template</li>
	<li>###STATUS_MESSAGE### - depends of the template</li>
</ul>

<h2>Wrap parts</h2>
<ul>
	<li>###HEADER_VALID###  - useful to define what to show/hide</li>
	<li>###MESSAGE_VALID### - useful to define what to show/hide</li>
	<li>###FORGOTP_VALID### - useful to define what to show/hide</li>
	<li>###PERMALOGIN_VALID### - useful to define what to show/hide</li>
</ul>

<h2>felogin Language Markers (see pi/locallang.xlf)</h2>
<ul>
	<li>###EMAIL_LABEL###                 - corresponding to 'your_email'</li>
	<li>###FORGOT_PASSWORD###             - corresponding to 'forgot_password'</li>
	<li>###FORGOT_PASSWORD_BACKTOLOGIN### - corresponding to 'forgot_password_backToLogin'</li>
	<li>###FORGOT_PASSWORD_ENTEREMAIL###  - corresponding to 'forgot_password_enterEmail'</li>
	<li>###LOGIN_LABEL###                 - corresponding to 'login'</li>
	<li>###PASSWORD_LABEL###              - corresponding to 'password'</li>
	<li>###SEND_PASSWORD###               - corresponding to 'send_password'</li>
	<li>###USERNAME_LABEL###              - corresponding to 'username'</li>
</ul>

<!--###TEMPLATE_LOGIN###-->

###STATUS_HEADER###
###STATUS_MESSAGE###
<!-- ###LOGIN_FORM### -->
<div class="tx-powermail">
	<form action="###ACTION_URI###" target="_top" method="post" onsubmit="###ON_SUBMIT###">
			<div class="form-field field-medium">
				<label class="powermail_label" for="user">E-Mail Adresse *</label>
				<input class="powermail-input" type="text" id="user" name="user" value="" />
			</div>
			<div class="form-field field-medium">
					<label class="powermail_label" for="pass">Passwort *</label>
					<input class="powermail-input" type="password" id="pass" name="pass" value="" data-rsa-encryption="" />
			</div>
			<!--###PERMALOGIN_VALID###-->
				<label for="permalogin">###PERMALOGIN###</label>
				<input name="permalogin" value="0" type="hidden" ###PERMALOGIN_HIDDENFIELD_ATTRIBUTES### id="permaloginHiddenField" />
				<input name="permalogin" value="1" type="checkbox" ###PERMALOGIN_CHECKBOX_ATTRIBUTES### id="permalogin"  onclick="document.getElementById('permaloginHiddenField').disabled = this.checked;" />
			<!--###PERMALOGIN_VALID###-->
			<div>
				<input class="btn btn-primary btn-large col-4" type="submit" name="submit" value="###LOGIN_LABEL###" />
				<!--###FORGOTP_VALID###-->
				<div class="col-5" style="margin: 20px 0px 0 140px;font-size:0.8em;text-align:right;display:inline-block;"><!--###FORGOT_PASSWORD_LINK###-->###FORGOT_PASSWORD###<!--###FORGOT_PASSWORD_LINK###--></div>
				<!--###FORGOTP_VALID###-->
			</div>
			<div class="felogin-hidden">
				<input type="hidden" name="logintype" value="login" />
				<input type="hidden" name="pid" value="###STORAGE_PID###" />
				<input type="hidden" name="redirect_url" value="###REDIRECT_URL###" />
				<input type="hidden" name="###PREFIXID###[noredirect]" value="###NOREDIRECT###" />
				###EXTRA_HIDDEN###
			</div>
		</form>
</div>
<!-- ###LOGIN_FORM### -->
<!--###TEMPLATE_LOGIN###-->
<!--###TEMPLATE_LOGOUT###-->

###STATUS_HEADER###
###STATUS_MESSAGE###
<div class="tx-powermail">
<form action="###ACTION_URI###" target="_top" method="post">

		<div class="powermail">
			<label>###USERNAME_LABEL###</label>
			###USERNAME###
		</div>
	<div class="form-field field-medium">
		<label class="powermail_label" for="user">E-Mail Adresse *</label>
		<input class="powermail-input" type="text" id="user" name="user" value="" />
	</div>
		<div>
			<input class="powermail-input" type="submit" name="submit" value="###LOGOUT_LABEL###" />
		</div>

		<div class="felogin-hidden">
			<input type="hidden" name="logintype" value="logout" />
			<input type="hidden" name="pid" value="###STORAGE_PID###" />
			<input type="hidden" name="###PREFIXID###[noredirect]" value="###NOREDIRECT###" />
		</div>
</form>
</div>
<!--###TEMPLATE_LOGOUT###-->
<!--###TEMPLATE_FORGOT###-->

###STATUS_HEADER###
###STATUS_MESSAGE###


<!-- ###FORGOT_FORM### -->
<div class="tx-powermail">
<form action="###ACTION_URI###" method="post">
	<div class="felogin-hidden">
		<input type="hidden" name="tx_felogin_pi1[forgot_hash]" value="###FORGOTHASH###" />
	</div>

		<div class="form-field field-medium">
			<label class="powermail_label" for="tx_felogin_pi1-forgot-email">Benutzername oder E-Mail Adresse:</label>
		<input class="powermail-input" name="tx_felogin_pi1[forgot_email]" id="tx_felogin_pi1-forgot-email" type="text">
</div>
		<div>
			<input class="btn btn-primary btn-large col-5" type="submit" name="submit" value="###SEND_PASSWORD###" />
		</div>
	<div class="col-8" style="margin: -20px 0px 0 150px;font-size:0.8em;text-align:right;display:inline-block;">###BACKLINK_LOGIN###&nbsp;</div>
</form>
	</div>
<!-- ###FORGOT_FORM### -->
<!--###TEMPLATE_FORGOT###-->
<!--###TEMPLATE_CHANGEPASSWORD###-->

###STATUS_HEADER###
###STATUS_MESSAGE###
<!-- ###CHANGEPASSWORD_FORM### -->
<div class="tx-powermail">
<form action="###ACTION_URI###" method="post">
	<div class="form-field field-medium">
			<label class="powermail_label" for="tx_felogin_pi1-newpassword1">###NEWPASSWORD1_LABEL###</label>
			<input class="powermail-input" type="password" name="###NEWPASSWORD1###" id="tx_felogin_pi1-newpassword1" />
		</div>
	<div class="form-field field-medium">
			<label class="powermail_label" for="tx_felogin_pi1-newpassword2">###NEWPASSWORD2_LABEL###</label>
			<input class="powermail-input" type="password" name="###NEWPASSWORD2###" id="tx_felogin_pi1-newpassword2" />
		</div>
		<div>
			<input class="btn btn-primary btn-large col-4" type="submit" name="tx_felogin_pi1[changepasswordsubmit]" value="###SEND_PASSWORD###" />
		</div>
</form>
	</div>
<!-- ###CHANGEPASSWORD_FORM### -->
###BACKLINK_LOGIN###
<!--###TEMPLATE_CHANGEPASSWORD###-->