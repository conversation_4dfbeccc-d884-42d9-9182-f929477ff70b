<?php

/***************************************************************
 * Extension Manager/Repository config file for ext "Vnc.RhenagComponents".
 *
 * Auto generated 16-08-2016 11:12
 *
 * Manual updates:
 * Only the data in the array - everything else is removed by next
 * writing. "version" and "dependencies" must not be touched!
 ***************************************************************/

$EM_CONF['rhenag_components'] = array(
	'title' => 'Components for rhenag',
	'description' => 'Templates for components',
	'category' => 'misc',
	'shy' => 0,
	'version' => '10.4.0',
	'dependencies' => '',
	'conflicts' => '',
	'priority' => '',
	'loadOrder' => '',
	'module' => '',
	'state' => 'stable',
	'uploadfolder' => 0,
	'createDirs' => '',
	'modify_tables' => '',
	'clearCacheOnLoad' => 1,
	'lockType' => '',
	'author' => 'Vancado',
	'author_email' => '<EMAIL>',
	'author_company' => '',
	'CGLcompliance' => '',
	'CGLcompliance_note' => '',
	'_md5_values_when_last_written' => 'a:0:{}',
	'suggests' => array(
	),
);
