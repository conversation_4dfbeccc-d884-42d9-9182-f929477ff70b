<?php
namespace <PERSON><PERSON><PERSON>\RhenagEvents\Controller;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * EventController
 */
class EventController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{

    /**
     * eventRepository
     *
     * @var \Vancado\RhenagEvents\Domain\Repository\EventRepository
     * @TYPO3\CMS\Extbase\Annotation\Inject
     */
    protected $eventRepository = NULL;
    
    /**
     * action list
     *
     * @return void
     */
    public function listAction()
    {
        if ($partner = $this->settings['partner']) {
            $events = $this->eventRepository->getCurrentForPartner(NULL, intval($partner));    
        } else {
            $events = $this->eventRepository->getCurrent();
        }
        
        $this->view->assign('events', $events);
    }
    
    /**
     * action show
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Event $event
     * @TYPO3\CMS\Extbase\Annotation\IgnoreValidation $event
     * @return void
     */
    public function showAction(\Vancado\RhenagEvents\Domain\Model\Event $event = null)
    {
	    $this->view->assign('dateNow', new \DateTime());
    	$this->view->assign('event', $event);
    }
    
    /**
     * action archiv
     *
     * @return void
     */
    public function archivAction()
    {
        $events = $this->eventRepository->getArchived();

        $this->view->assign('events', $events);
    }

    /**
     * action teasers
     *
     * @return void
     */
    public function teasersAction()
    {
        $events = $this->eventRepository->getCurrent(isset($this->settings['limit'])?intval($this->settings['limit']):NULL);
        $this->view->assign('events', $events);
    }

    /**
     * action navigation
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Event $event
     * @return void
     */
    public function navigationAction(\Vancado\RhenagEvents\Domain\Model\Event $event = NULL)
    {
        $events = $this->eventRepository->getCurrent();
        $this->view->assign('events', $events);
        if ($event) {
            $this->view->assign('currentEvent', $event);
        }
    }

}