<?php
namespace <PERSON><PERSON><PERSON>\RhenagEvents\Controller;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * SeminarController
 */
class SeminarController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{

    /**
     * seminarRepository
     *
     * @var \Vancado\RhenagEvents\Domain\Repository\SeminarRepository
     * @TYPO3\CMS\Extbase\Annotation\Inject
     */
    protected $seminarRepository = NULL;

    /**
     * eventRepository
     *
     * @var \Vancado\RhenagEvents\Domain\Repository\EventRepository
     * @TYPO3\CMS\Extbase\Annotation\Inject
     */
    protected $eventRepository = NULL;



    /**
     * action list
     *
     * @return void
     */
    public function listAction()
    {
        $seminars = $this->seminarRepository->findAll();
	    
        $this->view->assign('seminars', $seminars);
    }
    
    /**
     * action show
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Seminar $seminar
     * @return void
     */
    public function showAction(\Vancado\RhenagEvents\Domain\Model\Seminar $seminar = null)
    {
        $this->view->assign('seminar', $seminar);
    }

    /**
     * action navigation
     *
     * @return void
     */
    public function navigationAction()
    {
        $url = explode('/',$_SERVER['REQUEST_URI']);
        $slug = $url[count($url) - 1];
        $slug = str_replace('.html','',$slug);
        $event = $this->eventRepository->getBySlug($slug);
        $events = $this->eventRepository->getCurrent();
        $this->view->assign('events', $events);
        if ($event) {
            $this->view->assign('currentEvent', $event);
        }
    }
}