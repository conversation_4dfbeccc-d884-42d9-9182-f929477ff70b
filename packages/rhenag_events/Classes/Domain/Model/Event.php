<?php
namespace <PERSON><PERSON><PERSON>\RhenagEvents\Domain\Model;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * Event
 */
class Event extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{

    /**
     * title
     *
     * @var string
     * @TYPO3\CMS\Extbase\Annotation\Validate("StringLength", options={"minimum": 1, "maximum": 256})
     */
    protected $title = '';

    /**
	 * showNewStatus
	 *
	 * @var bool
	 */
    
	protected $showNewStatus = null;
	/**
	 * showOnlineStatus
	 *
	 * @var bool
	 */
	protected $showOnlineStatus = null;

    /**
     * shortTitle
     *
     * @var string
     */
    protected $shortTitle = '';
    
    /**
     * description
     *
     * @var string
     */
    protected $description = '';
    
    /**
     * price
     *
     * @var string
     */
    protected $price = '';
    
    /**
     * startDate
     *
     * @var \DateTime|null
     */
    protected $startDate = null;
    
    /**
     * endDate
     *
     * @var \DateTime|null
     */
    protected $endDate = null;
    
    /**
     * time
     *
     * @var string
     */
    protected $time = '';
    
    /**
     * infoPdf
     *
     * @var \TYPO3\CMS\Extbase\Domain\Model\FileReference
     */
    protected $infoPdf = null;
    
    /**
     * registrationPdf
     *
     * @var \TYPO3\CMS\Extbase\Domain\Model\FileReference
     */
    protected $registrationPdf = null;
    
    /**
     * place
     *
     * @var \Vancado\RhenagEvents\Domain\Model\Place
     */
    protected $place = null;
    
    /**
     * contacts
     *
     * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact>
     */
    protected $contacts = null;

    /**
     * bookedOut
     *
     * @var bool
     */
    protected $bookedOut = false;

    /**
     * slug
     * 
     * @var string
     */
    protected $slug = '';

    /**
     * showCompactStatus
     *
     * @var bool
     */
    protected $showCompactStatus = null;

    /**
     * Returns the title
     *
     * @return string $title
     */
    public function getTitle()
    {
        return $this->title;
    }

	/**
	 * Returns the showNewStatus
	 *
	 * @return bool $showNewStatus
	 */
	public function getShowNewStatus()
	{
		return $this->showNewStatus;
	}
	/**
	 * Sets the showNewStatus
	 *
	 * @param bool $showNewStatus
	 * @return void
	 */
	public function setShowNewStatus($showNewStatus)
	{
		$this->showNewStatus = $showNewStatus;
	}

    /**
     * Returns the showCompactStatus
     *
     * @return bool $showCompactStatus
     */
    public function getShowCompactStatus()
    {
        return $this->showCompactStatus;
    }

    /**
     * Sets the showCompactStatus
     *
     * @param bool $showCompactStatus
     * @return void
     */
    public function setShowCompactStatus($showCompactStatus)
    {
        $this->showCompactStatus = $showCompactStatus;
    }

	/**
	 * Returns the showOnlineStatus
	 *
	 * @return bool $showOnlineStatus
	 */
	public function getShowOnlineStatus()
	{
		return $this->showOnlineStatus;
	}
	/**
	 * Sets the showOnlineStatus
	 *
	 * @param bool $showOnlineStatus
	 * @return void
	 */
	public function setShowOnlineStatus($showNewStatus)
	{
		$this->showOnlineStatus = $showOnlineStatus;
	}



    /**
     * Sets the title
     *
     * @param string $title
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * Returns the short title
     *
     * @return string $shortTitle
     */
    public function getShortTitle()
    {
        return $this->shortTitle;
    }
    
    /**
     * Sets the short title
     *
     * @param string $shortTitle
     * @return void
     */
    public function setShortTitle($shortTitle)
    {
        $this->shortTitle = $shortTitle;
    }
    
    /**
     * Returns the description
     *
     * @return string $description
     */
    public function getDescription()
    {
        return $this->description;
    }
    
    /**
     * Sets the description
     *
     * @param string $description
     * @return void
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }
    
    /**
     * Returns the startDate
     *
     * @return \DateTime|null $startDate
     */
    public function getStartDate()
    {
        return $this->startDate;
    }
    
    /**
     * Sets the startDate
     *
     * @param \DateTime|null $startDate
     * @return void
     */
    public function setStartDate(\DateTime $startDate)
    {
        $this->startDate = $startDate;
    }
    
    /**
     * Returns the endDate
     *
     * @return \DateTime|null $endDate
     */
    public function getEndDate()
    {
        return $this->endDate;
    }
    
    /**
     * Sets the endDate
     *
     * @param \DateTime|null $endDate
     * @return void
     */
    public function setEndDate(\DateTime $endDate)
    {
        $this->endDate = $endDate;
    }
    
    /**
     * Returns the time
     *
     * @return string $time
     */
    public function getTime()
    {
        return $this->time;
    }
    
    /**
     * Sets the time
     *
     * @param string $time
     * @return void
     */
    public function setTime($time)
    {
        $this->time = $time;
    }
    
    /**
     * Returns the price
     *
     * @return string $price
     */
    public function getPrice()
    {
        return $this->price;
    }
    
    /**
     * Sets the price
     *
     * @param string $price
     * @return void
     */
    public function setPrice($price)
    {
        $this->price = $price;
    }
    
    /**
     * Returns the infoPdf
     *
     * @return \TYPO3\CMS\Extbase\Domain\Model\FileReference $infoPdf
     */
    public function getInfoPdf()
    {
        return $this->infoPdf;
    }
    
    /**
     * Sets the infoPdf
     *
     * @param \TYPO3\CMS\Extbase\Domain\Model\FileReference $infoPdf
     * @return void
     */
    public function setInfoPdf(\TYPO3\CMS\Extbase\Domain\Model\FileReference $infoPdf)
    {
        $this->infoPdf = $infoPdf;
    }
    
    /**
     * Returns the registrationPdf
     *
     * @return \TYPO3\CMS\Extbase\Domain\Model\FileReference $registrationPdf
     */
    public function getRegistrationPdf()
    {
        return $this->registrationPdf;
    }
    
    /**
     * Sets the registrationPdf
     *
     * @param \TYPO3\CMS\Extbase\Domain\Model\FileReference $registrationPdf
     * @return void
     */
    public function setRegistrationPdf(\TYPO3\CMS\Extbase\Domain\Model\FileReference $registrationPdf)
    {
        $this->registrationPdf = $registrationPdf;
    }
    
    /**
     * Returns the place
     *
     * @return \Vancado\RhenagEvents\Domain\Model\Place $place
     */
    public function getPlace()
    {
        return $this->place;
    }
    
    /**
     * Sets the place
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Place $place
     * @return void
     */
    public function setPlace(\Vancado\RhenagEvents\Domain\Model\Place $place)
    {
        $this->place = $place;
    }
    
    /**
     * __construct
     */
    public function __construct()
    {
        //Do not remove the next line: It would break the functionality
        $this->initStorageObjects();
    }
    
    /**
     * Initializes all ObjectStorage properties
     * Do not modify this method!
     * It will be rewritten on each save in the extension builder
     * You may modify the constructor of this class instead
     *
     * @return void
     */
    protected function initStorageObjects()
    {
        $this->contacts = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();
    }
    
    /**
     * Adds a Contact
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Contact $contact
     * @return void
     */
    public function addContact(\Vancado\RhenagEvents\Domain\Model\Contact $contact)
    {
        $this->contacts->attach($contact);
    }
    
    /**
     * Removes a Contact
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove The Contact to be removed
     * @return void
     */
    public function removeContact(\Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove)
    {
        $this->contacts->detach($contactToRemove);
    }
    
    /**
     * Returns the contacts
     *
     * @return \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact> $contacts
     */
    public function getContacts()
    {
        return $this->contacts;
    }
    
    /**
     * Sets the contacts
     *
     * @param \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact> $contacts
     * @return void
     */
    public function setContacts(\TYPO3\CMS\Extbase\Persistence\ObjectStorage $contacts)
    {
        $this->contacts = $contacts;
    }


    /**
     * Returns the bookedOut
     *
     * @return bool $bookedOut
     */
    public function getBookedOut()
    {
        return $this->bookedOut;
    }
    
    /**
     * Sets the bookedOut
     *
     * @param bool $bookedOut
     * @return void
     */
    public function setBookedOut($bookedOut)
    {
        $this->bookedOut = $bookedOut;
    }

    /**
     * Returns the slug
     * 
     * @return string $slug
     */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
     * Sets the slug
     * 
     * @param string $slug
     * @return void
     */
    public function setSlug($slug)
    {
        $this->slug = $slug;
    }

}