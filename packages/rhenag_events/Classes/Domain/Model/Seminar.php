<?php
namespace <PERSON>ca<PERSON>\RhenagEvents\Domain\Model;
use TYPO3\CMS\Extbase\Annotation as Extbase;
use TYPO3\CMS\Extbase\Persistence\ObjectStorage;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

	
/**
 * Seminar
 */
class Seminar extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{
	/**
	 * dates
	 *
	 * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\SeminarDate>
	 * @TYPO3\CMS\Extbase\Annotation\ORM\Cascade remove
	 */
	protected $dates = null;



    /**
     * title
     *
     * @var string
     * @TYPO3\CMS\Extbase\Annotation\Validate("StringLength", options={"minimum": 1, "maximum": 256})
     */
    protected $title = '';

	/**
	 * subtitle
	 *
	 * @var string
	 * @TYPO3\CMS\Extbase\Annotation\Validate("StringLength", options={"minimum": 1, "maximum": 256})
	 */
	protected $subtitle = '';

    /**
     * price
     *
     * @var string
     */
    protected $price = '';


    /**
     * showPriceHint
     *
     * @var bool
     */
    protected $showPriceHint = null;

	/**
	 * showNewStatus
	 *
	 * @var bool
	 */
	protected $showNewStatus = null;

	/**
	 * showCompactStatus
	 *
	 * @var bool
	 */
	protected $showCompactStatus = null;

    /**
     * showOnlineStatus
     *
     * @var bool
     */
    protected $showOnlineStatus = null;

    /**
     * startDate
     *
     * @var \DateTime
     */
    protected $startDate = null;
    
    /**
     * endDate
     *
     * @var \DateTime
     */
    protected $endDate = null;
    
    /**
     * time
     *
     * @var string
     */
    protected $time = '';

	/**
	 * recurring
	 *
	 * @var string
	 */
	protected $recurring = '';

    /**
     * infoPdf
     *
     * @var \TYPO3\CMS\Extbase\Domain\Model\FileReference
     */
    protected $infoPdf = null;
    
    /**
     * registrationPdf
     *
     * @var \TYPO3\CMS\Extbase\Domain\Model\FileReference
     */
    protected $registrationPdf = null;
    
    /**
     * participantsGroup
     *
     * @var string
     */
    protected $participantsGroup = '';
    
    /**
     * aim
     *
     * @var string
     */
    protected $aim = '';
    
    /**
     * content
     *
     * @var string
     */
    protected $content = '';
    
    /**
     * methods
     *
     * @var string
     */
    protected $methods = '';
    
    /**
     * trainer
     *
     * @var string
     */
    protected $trainer = '';
    
    /**
     * duration
     *
     * @var string
     */
    protected $duration = '';
    
    /**
     * participantsCount
     *
     * @var string
     */
    protected $participantsCount = '';
    
    /**
     * requirements
     *
     * @var string
     */
    protected $requirements = '';
    
    /**
     * place
     *
     * @var \Vancado\RhenagEvents\Domain\Model\Place
     */
    protected $place = null;
    
    /**
     * contacts
     *
     * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact>
     */
    protected $contacts = null;

    /**
     * registration contacts
     *
     * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact>
     */
    protected $registrationContacts = null;

    /**
     * partners
     *
     * @var string
     */
    protected $partners = '';




    /**
     * Returns the title
     *
     * @return string $title
     */
    public function getTitle()
    {
        return $this->title;
    }
    
    /**
     * Sets the title
     *
     * @param string $title
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }


	/**
	 * Returns the subtitle
	 *
	 * @return string $subtitle
	 */
	public function getSubtitle()
	{
		return $this->subtitle;
	}

	/**
	 * Sets the subtitle
	 *
	 * @param string $subtitle
	 * @return void
	 */
	public function setSubtitle($subtitle)
	{
		$this->subtitle = $subtitle;
	}


    /**
     * Returns the price
     *
     * @return string $price
     */
    public function getPrice()
    {
        return $this->price;
    }
    
    /**
     * Sets the price
     *
     * @param string $price
     * @return void
     */
    public function setPrice($price)
    {
        $this->price = $price;
    }

    /**
     * Returns the showPriceHint
     *
     * @return bool $showPriceHint
     */
    public function getShowPriceHint()
    {
        return $this->showPriceHint;
    }

	/**
	 * Returns the showNewStatus
	 *
	 * @return bool $showNewStatus
	 */
	public function getShowNewStatus()
	{
		return $this->showNewStatus;
	}
	/**
	 * Sets the showNewStatus
	 *
	 * @param bool $showNewStatus
	 * @return void
	 */
	public function setShowNewStatus($showNewStatus)
	{
		$this->showNewStatus = $showNewStatus;
	}

    /**
     * Returns the showCompactStatus
     *
     * @return bool $showCompactStatus
     */
    public function getShowCompactStatus()
    {
        return $this->showCompactStatus;
    }

    /**
     * Sets the showCompactStatus
     *
     * @param bool $showCompactStatus
     * @return void
     */
    public function setShowCompactStatus($showCompactStatus)
    {
        $this->showCompactStatus = $showCompactStatus;
    }

	/**
	 * Returns the showOnlineStatus
	 *
	 * @return bool $showOnlineStatus
	 */
	public function getShowOnlineStatus()
	{
		return $this->showOnlineStatus;
	}

	/**
	 * Sets the showOnlineStatus
	 *
	 * @param bool $showOnlineStatus
	 * @return void
	 */
	public function setShowOnlineStatus($showOnlineStatus)
	{
		$this->showOnlineStatus = $showOnlineStatus;
	}

    /**
     * Returns the startDate
     *
     * @return \DateTime $startDate
     */
    public function getStartDate()
    {
        return $this->startDate;
    }
    
    /**
     * Sets the startDate
     *
     * @param \DateTime $startDate
     * @return void
     */
    public function setStartDate(\DateTime $startDate)
    {
        $this->startDate = $startDate;
    }
    
    /**
     * Returns the endDate
     *
     * @return \DateTime $endDate
     */
    public function getEndDate()
    {
        return $this->endDate;
    }
	/**
	 * Sets the endDate
	 *
	 * @param \DateTime $endDate
	 * @return void
	 */
	public function setEndDate(\DateTime $endDate)
	{
		$this->endDate = $endDate;
	}


    /**
     * Returns the time
     *
     * @return string $time
     */
    public function getTime()
    {
        return $this->time;
    }
    
    /**
     * Sets the time
     *
     * @param string $time
     * @return void
     */
    public function setTime($time)
    {
        $this->time = $time;
    }


	/**
	 * Returns the recurring
	 *
	 * @return string $recurring
	 */
	public function getRecurring()
	{
		return $this->recurring;
	}
	/**
	 * Sets the recurring
	 *
	 * @param string $recurring
	 * @return void
	 */
	public function setRecurring($recurring)
	{
		$this->recurring = $recurring;
	}


    /**
     * Returns the infoPdf
     *
     * @return \TYPO3\CMS\Extbase\Domain\Model\FileReference $infoPdf
     */
    public function getInfoPdf()
    {
        return $this->infoPdf;
    }
    
    /**
     * Sets the infoPdf
     *
     * @param \TYPO3\CMS\Extbase\Domain\Model\FileReference $infoPdf
     * @return void
     */
    public function setInfoPdf(\TYPO3\CMS\Extbase\Domain\Model\FileReference $infoPdf)
    {
        $this->infoPdf = $infoPdf;
    }
    
    /**
     * Returns the registrationPdf
     *
     * @return \TYPO3\CMS\Extbase\Domain\Model\FileReference $registrationPdf
     */
    public function getRegistrationPdf()
    {
        return $this->registrationPdf;
    }
    
    /**
     * Sets the registrationPdf
     *
     * @param \TYPO3\CMS\Extbase\Domain\Model\FileReference $registrationPdf
     * @return void
     */
    public function setRegistrationPdf(\TYPO3\CMS\Extbase\Domain\Model\FileReference $registrationPdf)
    {
        $this->registrationPdf = $registrationPdf;
    }
    
    /**
     * Returns the place
     *
     * @return \Vancado\RhenagEvents\Domain\Model\Place $place
     */
    public function getPlace()
    {
        return $this->place;
    }
    
    /**
     * Sets the place
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Place $place
     * @return void
     */
    public function setPlace(\Vancado\RhenagEvents\Domain\Model\Place $place)
    {
        $this->place = $place;
    }
    
    /**
     * __construct
     */
    public function __construct()
    {
        //Do not remove the next line: It would break the functionality
	    $this->initStorageObjects();

    }
    
    /**
     * Initializes all ObjectStorage properties
     * Do not modify this method!
     * It will be rewritten on each save in the extension builder
     * You may modify the constructor of this class instead
     *
     * @return void
     */
    protected function initStorageObjects()
    {
        $this->contacts = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();
	    $this->dates = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();

    }


	/**
	 * Adds a Date
	 *
	 * @param \Vancado\RhenagEvents\Domain\Model\SeminarDate $date
	 * @return void
	 */
	public function addDate(\Vancado\RhenagEvents\Domain\Model\Contact $date)
	{
		$this->dates->attach($date);
	}

	/**
	 * Removes a Date
	 *
	 * @param \Vancado\RhenagEvents\Domain\Model\SeminarDate $dateToRemove The Date to be removed
	 * @return void
	 */
	public function removeDate(\Vancado\RhenagEvents\Domain\Model\SeminarDate $dateToRemove)
	{
		$this->dates->detach($dateToRemove);
	}

	/**
	 * Returns the dates
	 *
	 * @return \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\SeminarDate> $dates
	 */
	public function getDates() {
		return $this->dates;
	}

    /**
     * Adds a Contact
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Contact $contact
     * @return void
     */
    public function addContact(\Vancado\RhenagEvents\Domain\Model\Contact $contact)
    {
        $this->contacts->attach($contact);
    }
    
    /**
     * Removes a Contact
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove The Contact to be removed
     * @return void
     */
    public function removeContact(\Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove)
    {
        $this->contacts->detach($contactToRemove);
    }
    
    /**
     * Returns the contacts
     *
     * @return \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact> $contacts
     */
    public function getContacts()
    {
        return $this->contacts;
    }
    
    /**
     * Sets the contacts
     *
     * @param \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact> $contacts
     * @return void
     */
    public function setContacts(\TYPO3\CMS\Extbase\Persistence\ObjectStorage $contacts)
    {
        $this->contacts = $contacts;
    }



	/**
	 * Sets the dates
	 *
	 * @param \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\SeminarDate> $dates
	 * @return void
	 */
	public function setDates(\TYPO3\CMS\Extbase\Persistence\ObjectStorage $dates)
	{
		$this->dates = $dates;
	}



     /**
     * Adds a registraion contact
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Contact $registrationContact
     * @return void
     */
    public function addRegistrationContact(\Vancado\RhenagEvents\Domain\Model\Contact $registrationContact)
    {
        $this->registrationContacts->attach($registrationContact);
    }
    
    /**
     * Removes a registraion contact
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove The registraion contact to be removed
     * @return void
     */
    public function removeRegistrationContact(\Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove)
    {
        $this->registrationContacts->detach($contactToRemove);
    }
    
    /**
     * Returns the registraion contacts
     *
     * @return \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact> $registrationContacts
     */
    public function getRegistrationContacts()
    {
        return $this->registrationContacts;
    }
    
    /**
     * Sets the registraion contacts
     *
     * @param \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Contact> $registrationContacts
     * @return void
     */
    public function setRegistrationContacts(\TYPO3\CMS\Extbase\Persistence\ObjectStorage $registrationContacts)
    {
        $this->registrationContacts = $registrationContacts;
    }
    
    /**
     * Returns the participantsGroup
     *
     * @return string $participantsGroup
     */
    public function getParticipantsGroup()
    {
        return $this->participantsGroup;
    }
    
    /**
     * Sets the participantsGroup
     *
     * @param string $participantsGroup
     * @return void
     */
    public function setParticipantsGroup($participantsGroup)
    {
        $this->participantsGroup = $participantsGroup;
    }
    
    /**
     * Returns the aim
     *
     * @return string $aim
     */
    public function getAim()
    {
        return $this->aim;
    }
    
    /**
     * Sets the aim
     *
     * @param string $aim
     * @return void
     */
    public function setAim($aim)
    {
        $this->aim = $aim;
    }
    
    /**
     * Returns the content
     *
     * @return string $content
     */
    public function getContent()
    {
        return $this->content;
    }
    
    /**
     * Sets the content
     *
     * @param string $content
     * @return void
     */
    public function setContent($content)
    {
        $this->content = $content;
    }
    
    /**
     * Returns the methods
     *
     * @return string $methods
     */
    public function getMethods()
    {
        return $this->methods;
    }
    
    /**
     * Sets the methods
     *
     * @param string $methods
     * @return void
     */
    public function setMethods($methods)
    {
        $this->methods = $methods;
    }
    
    /**
     * Returns the trainer
     *
     * @return string $trainer
     */
    public function getTrainer()
    {
        return $this->trainer;
    }
    
    /**
     * Sets the trainer
     *
     * @param string $trainer
     * @return void
     */
    public function setTrainer($trainer)
    {
        $this->trainer = $trainer;
    }
    
    /**
     * Returns the duration
     *
     * @return string $duration
     */
    public function getDuration()
    {
        return $this->duration;
    }
    
    /**
     * Sets the duration
     *
     * @param string $duration
     * @return void
     */
    public function setDuration($duration)
    {
        $this->duration = $duration;
    }
    
    /**
     * Returns the participantsCount
     *
     * @return string $participantsCount
     */
    public function getParticipantsCount()
    {
        return $this->participantsCount;
    }
    
    /**
     * Sets the participantsCount
     *
     * @param string $participantsCount
     * @return void
     */
    public function setParticipantsCount($participantsCount)
    {
        $this->participantsCount = $participantsCount;
    }
    
    /**
     * Returns the requirements
     *
     * @return string $requirements
     */
    public function getRequirements()
    {
        return $this->requirements;
    }
    
    /**
     * Sets the requirements
     *
     * @param string $requirements
     * @return void
     */
    public function setRequirements($requirements)
    {
        $this->requirements = $requirements;
    }

    /**
     * Returns the partners
     *
     * @return string $partners
     */
    public function getPartners()
    {
        return $this->partners;
    }

}