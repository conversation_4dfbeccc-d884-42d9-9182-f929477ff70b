<?php
namespace <PERSON><PERSON><PERSON>\RhenagEvents\Domain\Model;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * Seminar
 */
class SeminarDate extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{



    /**
     * startDate
     *
     * @var \DateTime
     */
    protected $startDate = null;
    
    /**
     * endDate
     *
     * @var \DateTime
     */
    protected $endDate = null;
    
    /**
     * time
     *
     * @var string
     */
    protected $time = '';

	/**
	 * record_type
	 *
	 * @var integer
	 */
	protected $recordType = 0;

	/**
	 * Modules
	 *
	 * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\SeminarModule>
	 * @TYPO3\CMS\Extbase\Annotation\ORM\Cascade remove
	 */
	protected $modules = null;


    /**
     * Returns the startDate
     *
     * @return \DateTime $startDate
     */
    public function getStartDate()
    {
        return $this->startDate;
    }
    
    /**
     * Sets the startDate
     *
     * @param \DateTime $startDate
     * @return void
     */
    public function setStartDate(\DateTime $startDate)
    {
        $this->startDate = $startDate;
    }
    
    /**
     * Returns the endDate
     *
     * @return \DateTime $endDate
     */
    public function getEndDate()
    {
        return $this->endDate;
    }
	/**
	 * Sets the endDate
	 *
	 * @param \DateTime $endDate
	 * @return void
	 */
	public function setEndDate(\DateTime $endDate)
	{
		$this->endDate = $endDate;
	}

    /**
     * Returns the time
     *
     * @return string $time
     */
    public function getTime()
    {
        return $this->time;
    }

	/**
	 * Sets the time
	 *
	 * @param string $time
	 *
	 * @return void
	 */
    public function setTime(string $time)
    {
        $this->time = $time;
    }

	/**
	 * Returns  record_type
	 *
	 * @return integer $recordType
	 */
	public function getRecordType()
	{
		return $this->recordType;
	}

	/**
	 * Sets the record_type
	 *
	 * @param integer $recordType
	 * @return void
	 */
	public function setRecordType(int $recordType)
	{
		$this->recordType =  $recordType;
	}

    /**
     * __construct
     */
    public function __construct()
    {
        //Do not remove the next line: It would break the functionality
        $this->initStorageObjects();
    }
    
    /**
     * Initializes all ObjectStorage properties
     * Do not modify this method!
     * It will be rewritten on each save in the extension builder
     * You may modify the constructor of this class instead
     *
     * @return void
     */
    protected function initStorageObjects()
    {
        $this->modules = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();
    }
    
    /**
     * Adds a Contact
     *
     * @param \Vancado\RhenagEvents\Domain\Model\SeminarModule $module
     * @return void
     */
    public function addModule(\Vancado\RhenagEvents\Domain\Model\SeminarModule $module)
    {
        $this->modules->attach($module);
    }
    
    /**
     * Removes a Module
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove The Contact to be removed
     * @return void
     */
    public function removeContact(\Vancado\RhenagEvents\Domain\Model\Contact $contactToRemove)
    {
        $this->contacts->detach($contactToRemove);
    }
    
    /**
     * Returns the modules
     *
     * @return \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\SeminarModule> $modules
     */
    public function getModules()
    {
        return $this->modules;
    }
    
    /**
     * Sets the modules
     *
     * @param \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\SeminarModule> $modules
     * @return void
     */
    public function setModules(\TYPO3\CMS\Extbase\Persistence\ObjectStorage $modules)
    {
        $this->modules = $modules;
    }



}