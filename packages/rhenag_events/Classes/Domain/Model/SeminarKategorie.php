<?php
namespace Vancado\RhenagEvents\Domain\Model;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * SeminarKategorie
 */
class SeminarKategorie extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{

    /**
     * title
     *
     * @var string
     */
    protected $title = '';
    
    /**
     * seminars
     *
     * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Seminar>
     * @TYPO3\CMS\Extbase\Annotation\ORM\Cascade remove
     */
    protected $seminars = null;



    /**
     * selected partner
     *
     * @var int
     */
    protected $selectedPartner = 0;
    
    /**
     * Returns the title
     *
     * @return string $title
     */
    public function getTitle()
    {
        return $this->title;
    }
    
    /**
     * Sets the title
     *
     * @param string $title
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }
    
    /**
     * __construct
     */
    public function __construct()
    {
        //Do not remove the next line: It would break the functionality
        $this->initStorageObjects();
    }
    
    /**
     * Initializes all ObjectStorage properties
     * Do not modify this method!
     * It will be rewritten on each save in the extension builder
     * You may modify the constructor of this class instead
     *
     * @return void
     */
    protected function initStorageObjects()
    {
        $this->seminars = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();

    }
    
    /**
     * Adds a Seminar
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Seminar $seminar
     * @return void
     */
    public function addSeminar(\Vancado\RhenagEvents\Domain\Model\Seminar $seminar)
    {
        $this->seminars->attach($seminar);
    }
    
    /**
     * Removes a Seminar
     *
     * @param \Vancado\RhenagEvents\Domain\Model\Seminar $seminarToRemove The Seminar to be removed
     * @return void
     */
    public function removeSeminar(\Vancado\RhenagEvents\Domain\Model\Seminar $seminarToRemove)
    {
        $this->seminars->detach($seminarToRemove);
    }
    
    /**
     * Returns the seminars
     *
     * @return \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Seminar> $seminars
     */
    public function getSeminars()
    {
        return $this->seminars;
    }


   /**
     * Sets the seminars
     *
     * @param \TYPO3\CMS\Extbase\Persistence\ObjectStorage<\Vancado\RhenagEvents\Domain\Model\Seminar> $seminars
     * @return void
     */
    public function setSeminars(\TYPO3\CMS\Extbase\Persistence\ObjectStorage $seminars)
    {
        $this->seminars = $seminars;
    }

    /**
     * Sets the selected partner
     *
     * @param int $partner
     * @return void
     */
    public function setSelectedPartner($partner) {
        $this->selectedPartner = $partner;
    }

    /**
     * Gets the selected partner
     *
     * @return int
     */
    public function getSelectedPartner() {
        return $this->selectedPartner;
    }

    public function getPartnerSeminars() {
        $allSeminars = $this->getSeminars();
        $seminars = [];

        if ($selectedPartner = $this->getSelectedPartner()) {
            foreach($allSeminars as $seminar) {
                $partners = explode(',', $seminar->getPartners());

                if (!empty($partners)) {
                    foreach ($partners as $partner) {
                        
                        if ($partner == $selectedPartner) {
                            $seminars[] = $seminar;
                        }
                    }
                }

            }
            return $seminars;
            
        }

        return $allSeminars;
    }

}