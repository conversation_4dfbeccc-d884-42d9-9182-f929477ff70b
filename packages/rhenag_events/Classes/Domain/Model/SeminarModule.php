<?php
namespace <PERSON><PERSON><PERSON>\RhenagEvents\Domain\Model;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * Seminar
 */
class SeminarModule extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{


	/**
	 * title
	 *
	 * @var string
	 */
	protected $title = null;

    /**
     * startDate
     *
     * @var \DateTime
     */
    protected $startDate = null;
    
    /**
     * endDate
     *
     * @var \DateTime
     */
    protected $endDate = null;
    
    /**
     * time
     *
     * @var string
     */
    protected $time = '';

	/**
	 * Returns the title
	 *
	 * @return $title
	 */
	public function getTitle()
	{
		return $this->title;
	}
	/**
	 * Sets the title
	 *
	 * @param  $title
	 * @return void
	 */
	public function setTitle($title)
	{
		$this->title = $title;
	}


    /**
     * Returns the startDate
     *
     * @return \DateTime $startDate
     */
    public function getStartDate()
    {
        return $this->startDate;
    }
    
    /**
     * Sets the startDate
     *
     * @param \DateTime $startDate
     * @return void
     */
    public function setStartDate(\DateTime $startDate)
    {
        $this->startDate = $startDate;
    }
    
    /**
     * Returns the endDate
     *
     * @return \DateTime $endDate
     */
    public function getEndDate()
    {
        return $this->endDate;
    }
	/**
	 * Sets the endDate
	 *
	 * @param \DateTime $endDate
	 * @return void
	 */
	public function setEndDate(\DateTime $endDate)
	{
		$this->endDate = $endDate;
	}


    /**
     * Returns the time
     *
     * @return string $time
     */
    public function getTime()
    {
        return $this->time;
    }
    
    /**
     * Sets the time
     *
     * @param string $time
     * @return void
     */
    public function setTime($time)
    {
        $this->time = $time;
    }

    /**
     * __construct
     */
    public function __construct()
    {
        //Do not remove the next line: It would break the functionality
        $this->initStorageObjects();
    }
    


}