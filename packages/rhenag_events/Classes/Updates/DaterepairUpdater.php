<?php
namespace <PERSON>ca<PERSON>\RhenagEvents\Updates;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Charset\CharsetConverter;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Connection;
use Symfony\Component\Console\Output\OutputInterface;
use TYPO3\CMS\Install\Updates\ChattyInterface;
use TYPO3\CMS\Install\Updates\UpgradeWizardInterface;

class Daterepair implements ChattyInterface, UpgradeWizardInterface
{
    /**
     * @var OutputInterface
     */
    protected $output;
    
    /**
     * Return the identifier for this wizard
     * This should be the same string as used in the ext_localconf class registration
     *
     * @return string
     */
    public function getIdentifier(): string
    {
        return 'daterepair';
    }

    /**
     * Return the speaking name of this wizard
     *
     * @return string
     */
    public function getTitle(): string
    {
        return 'TYPO3 Version 9';
    }

    /**
     * Return the description for this wizard
     *
     * @return string
     */
    public function getDescription(): string
    {
        return 'Update scripts events to repair dates';
    }

    /**
     * Execute the update
     *
     * Called when a wizard reports that an update is necessary
     *
     * @return bool
     */
    public function executeUpdate(): bool
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable('tx_rhenagevents_domain_model_event');
        $statement = $queryBuilder
            ->select('uid','start_date','end_date')
            ->from('tx_rhenagevents_domain_model_event')
            ->execute();
        while ($row = $statement->fetch()) {
            $startDateArr = explode('-',trim($row['start_date']));
            $startDate = mktime(12,0,0,$startDateArr[1],$startDateArr[2],$startDateArr[0]);
            $this->output->writeln($startDate."-");

            // $queryBuilder
            //     ->update('tt_content')
            //     ->where(
            //         $queryBuilder->expr()->eq('uid',$queryBuilder->createNamedParameter($row['uid']))
            //     )
            //     ->set('CType', $ctype)
            //     ->execute();
        }

        
        return true;



    }

    /**
     * Is an update necessary?
     * @return bool
     */
    public function updateNecessary(): bool
    {
        return true;
    }

    /**
     * Returns an array of class names of prerequisite classes
     * @return string[]
     */
    public function getPrerequisites(): array
    {
      return [];
    }

    public function setOutput(OutputInterface $output): void
    {
        $this->output = $output;
    }
}