<T3DataStructure>
	<sheets>
		<sDEF>
			<ROOT>
				<TCEforms>
					<sheetTitle>Function</sheetTitle>
				</TCEforms>
				<type>array</type>
				<el>
					<switchableControllerActions>
						<TCEforms>
							<label>Select function</label>
							<config>
								<type>select</type>
                                <renderType>selectSingle</renderType>
								<items>
									<numIndex index="0">
										<numIndex index="0">List</numIndex>
										<numIndex index="1">SeminarKategorie->list</numIndex>
									</numIndex>
									<numIndex index="1">
										<numIndex index="0">Single</numIndex>
										<numIndex index="1">Seminar->show</numIndex>
									</numIndex>
									<numIndex index="2">
										<numIndex index="0">Navigation</numIndex>
										<numIndex index="1">Seminar->navigation</numIndex>
									</numIndex>
								</items>
							</config>
						</TCEforms>
					</switchableControllerActions>
					<settings.partner>
						<TCEforms>
							<label>Partner</label>
							<displayCond>FIELD:switchableControllerActions:=:SeminarKategorie->list</displayCond>
							<config>
								<type>select</type>
                                <renderType>selectSingle</renderType>
								<items>
									<numIndex index="0">
										<numIndex index="0">All</numIndex>
										<numIndex index="1"></numIndex>
									</numIndex>
								</items>
								<foreign_table>tx_rhenagevents_domain_model_partner</foreign_table>
							</config>
						</TCEforms>
					</settings.partner>
				</el>
			</ROOT>
		</sDEF>
	</sheets>
</T3DataStructure>