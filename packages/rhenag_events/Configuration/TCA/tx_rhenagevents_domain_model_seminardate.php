<?php
return [
	'ctrl' => [
		'title'	=> 'LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar_date',
		'label' => 'record_type',
		'label_alt' => 'modules',
		'label_alt_force' => TRUE,
		'tstamp' => 'tstamp',
		'crdate' => 'crdate',
		'cruser_id' => 'cruser_id',
		'dividers2tabs' => TRUE,
		'versioningWS' => TRUE,
		'sortby' => 'sorting',
		'languageField' => 'sys_language_uid',
		'transOrigPointerField' => 'l10n_parent',
		'transOrigDiffSourceField' => 'l10n_diffsource',
		'delete' => 'deleted',
		'enablecolumns' => [
			'disabled' => 'hidden',
			'starttime' => 'starttime',
			'endtime' => 'endtime',
		],
		'searchFields' => '',
		'iconfile' => 'EXT:rhenag_events/Resources/Public/Icons/tx_rhenagevents_domain_model_seminar_date.png',
		'type' => 'record_type',
	],
	'interface' => [
		'showRecordFieldList' => 'sys_language_uid, l10n_parent, l10n_diffsource, hidden,modules,show_new_status, show_online_status, start_date, end_date, time',
	],
	'types' => [
		'0' => [ 'showitem' => '--palette--;;1,record_type,show_new_status,start_date,end_date,time, --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,hidden, starttime, endtime, sys_language_uid,l10n_parent, l10n_diffsource' ],
		'1' => [ 'showitem' => '--palette--;;1,record_type,show_new_status,start_date,end_date,time,modules, --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,hidden, starttime, endtime, sys_language_uid,l10n_parent, l10n_diffsource' ],
	],
	'palettes' => [
		'1' => [ 'showitem' => '' ],
	],
	'columns' => [
	
		'sys_language_uid' => [
            'exclude' => 1,
            'label'   => 'LLL:EXT:lang/Resources/Private/Language/locallang_general.xlf:LGL.language',
            'config'  => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'special' => 'languages',
                'items' => [
                	[
                    	'LLL:EXT:lang/Resources/Private/Language/locallang_general.xlf:LGL.allLanguages',
                        -1,
                    	'flags-multiple'
                    ],
                ],
                'default' => 0,
            ],
        ],
		'l10n_parent' => [
			'displayCond' => 'FIELD:sys_language_uid:>:0',
			'exclude' => 1,
			'label' => 'LLL:EXT:lang/locallang_general.xlf:LGL.l18n_parent',
			'config' => [
				'type' => 'select',
				'renderType' => 'selectSingle',
				'items' => [
					[ '', 0 ],
				],
				'foreign_table' => 'tx_rhenagevents_domain_model_seminardate',
				'foreign_table_where' => 'AND tx_rhenagevents_domain_model_seminardate.pid=###CURRENT_PID### AND tx_rhenagevents_domain_model_seminardate.sys_language_uid IN (-1,0)',
			],
		],
		'l10n_diffsource' => [
			'config' => [
				'type' => 'passthrough',
			],
		],

		't3ver_label' => [
			'label' => 'LLL:EXT:lang/locallang_general.xlf:LGL.versionLabel',
			'config' => [
				'type' => 'input',
				'size' => 30,
				'max' => 255,
			]
		],
	
		'hidden' => [
			'exclude' => 1,
			'label' => 'LLL:EXT:lang/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
			'config' => [
				'type' => 'check',
			],
		],
		'starttime' => [
			'exclude' => 1,
			'label' => 'LLL:EXT:lang/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
			'config' => [
				'type' => 'input',
				'renderType' => 'inputDateTime',
        		'eval' => 'datetime',
			],
		],
		'endtime' => [
			'exclude' => 1,
			'label' => 'LLL:EXT:lang/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
			'config' => [
				'type' => 'input',
				'renderType' => 'inputDateTime',
        		'eval' => 'datetime',
			],
		],

		'start_date' => [
			'exclude' => 1,
			'label' => 'LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar.start_date',
			'description' => 'Wenn Startdatum leer, dann wird "Nach Vereinbarung" anstelle Startdatum ausgespielt',
			'config' => [
				'type' => 'input',
				'renderType' => 'inputDateTime',
        		'eval' => 'date',
			],
		],
		'end_date' => [
			'exclude' => 1,
			'label' => 'LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar.end_date',
			'config' => [
				'type' => 'input',
				'renderType' => 'inputDateTime',
        		'eval' => 'date',
			],
		],
		'time' => [
			'exclude' => 1,
			'label' => 'LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar.time',
			'config' => [
				'type' => 'input',
				'size' => 30,
				'eval' => 'trim'
			],
		],
		'record_type' => [
			'exclude' => 0,
			'label' => 'LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar.record_type',
			'config' => [
				'type' => 'select',
				'renderType' => 'selectSingle',
				'items' => [
					['LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar.record_type.0', 0],
					['LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar.record_type.1', 1],
				]
			],
		],
		'modules' => [
			'exclude' => 0,
			'label' => 'LLL:EXT:rhenag_events/Resources/Private/Language/locallang_db.xlf:tx_rhenagevents_domain_model_seminar_module',
			'config' => [
				'type' => 'inline',
				'foreign_table' => 'tx_rhenagevents_domain_model_seminarmodule',
				'foreign_field' => 'seminar_module',
				'foreign_sortby' => 'sorting',
				'maxitems' => 9999,
				'appearance' => [
					'collapseAll' => TRUE,
					'levelLinksPosition' => 'top',
					'showSynchronizationLink' => 1,
					'useSortable' => 1,
					'newRecordLinkTitle' => 'Modul hinzufügen'
				],
			],

		],

		'seminar_date' => [
			'config' => [
				'type' => 'passthrough',
			],
		],
	],
];