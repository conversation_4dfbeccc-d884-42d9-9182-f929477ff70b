
plugin.tx_rhenagevents_event {
	view {
		# cat=plugin.tx_rhenagevents_event/file; type=string; label=Path to template root (FE)
		templateRootPath = EXT:rhenag_events/Resources/Private/Templates/
		# cat=plugin.tx_rhenagevents_event/file; type=string; label=Path to template partials (FE)
		partialRootPath = EXT:rhenag_events/Resources/Private/Partials/
		# cat=plugin.tx_rhenagevents_event/file; type=string; label=Path to template layouts (FE)
		layoutRootPath = EXT:rhenag_events/Resources/Private/Layouts/
	}
	persistence {
		# cat=plugin.tx_rhenagevents_event//a; type=string; label=Fachveranstaltungen Default storage PID
		storagePid = 169
	}
	settings {
		# cat=plugin.tx_rhenagevents_event//a; type=string; label=Fachveranstaltungen Einzel-Ansicht uid
		singlePid=175
		# cat=plugin.tx_rhenagevents_event//a; type=string; label=Fachveranstaltungen Listen-Ansicht uid
		listPid=122
		# cat=plugin.tx_rhenagevents_event//a; type=string; label=Fachveranstaltungen Registrierungs Formular uid
		registerPid=176
		# cat=plugin.tx_rhenagevents_event//a; type=string; label=Fachveranstaltungen Registrierungs Formular Absendebutton Text
		buttonTextRegister=Jetzt online anmelden
		# cat=plugin.tx_rhenagevents_event//a; type=boolean; label= Wird über iFrame geladen - verwendet dann andere Section
		inIframe =
		# cat=plugin.tx_rhenagevents_event//a; type=string; label= zusätzliche Klasse in Events-Liste
		additionalListViewClass =
		# cat=plugin.tx_rhenagevents_event//a; type=string; label= zusätzliche Klasse in Events-Singleview
		additionalSingleViewClass =
		# cat=plugin.tx_rhenagevents_event//a; type=string; label= zusätzliche Klasse in Events-Registrierungsformular
		additionalRegisterViewClass =
	}

}

plugin.tx_rhenagevents_seminar {
	view {
		# cat=plugin.tx_rhenagevents_seminar/file; type=string; label=Path to template root (FE)
		templateRootPath = EXT:rhenag_events/Resources/Private/Templates/
		# cat=plugin.tx_rhenagevents_seminar/file; type=string; label=Path to template partials (FE)
		partialRootPath = EXT:rhenag_events/Resources/Private/Partials/
		# cat=plugin.tx_rhenagevents_seminar/file; type=string; label=Path to template layouts (FE)
		layoutRootPath = EXT:rhenag_events/Resources/Private/Layouts/
	}
	persistence {
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label=Weiterbildungen/ Seminare Default storage PID
		storagePid = 169
	}
	settings {
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label=Weiterbildungen / Seminare Einzelansicht uid
		singlePid=171
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label=Weiterbildungen / Seminare Listen-Ansicht uid
		listPid=170
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label=Weiterbildungen / Seminare Registrierungs Formular uid
		registerPid=178
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label=Weiterbildungen / Seminare Registrierungs Formular Absendebutton Text
		buttonTextRegister=Jetzt online anmelden
		# cat=plugin.tx_rhenagevents_seminar//a; type=boolean; label=Weiterbildungen / Seminare Wird über iFrame geladen - verwendet dann andere Section
		inIframe =
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label= zusätzliche Klasse in Seminar-Liste
		additionalListViewClass =
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label= zusätzliche Klasse in Seminar-Singleview
		additionalSingleViewClass =
		# cat=plugin.tx_rhenagevents_seminar//a; type=string; label= zusätzliche Klasse in Seminar-Registrierungsformular
		additionalRegisterViewClass =
	}

}
