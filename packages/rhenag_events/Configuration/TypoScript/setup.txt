# load default css
page.includeCSS.rhenagseminars = typo3conf/ext/rhenag_events/Resources/Public/css/rhenagevents.css

	lib.fachVeranstaltungShortinfo = COA_INT
	lib.fachVeranstaltungShortinfo {

		20 = CONTENT
		20 {
			table = tx_rhenagevents_domain_model_event
			select {
				where = uid=###event###
				markers {
					event.data = GP:event
					event.intVal = 1
				}

				pidInList = {$plugin.tx_rhenagevents_event.persistence.storagePid}
				languageField = 0
			}

			renderObj = COA
			renderObj {
				10 = TEXT
				10 {
					stdWrap.field = title
					stdWrap.wrap = <h1 class="h1">|</h1>
				}

				20 = TEXT
				20 {
					stdWrap.field = participants_counts
					stdWrap.noTrimWrap = |<p>Teilnehmeranzahl: |</p>|
					stdWrap.required = 1
				}

				25 = CONTENT
				25 {
					wrap = |
					table =  tx_rhenagevents_domain_model_seminardate
					select {
						where = uid=###date###
						markers {
							date.data = GP:date
							date.intVal = 1
						}

						pidInList = {$plugin.tx_rhenagevents_event.persistence.storagePid}
						languageField = 0
					}
					renderObj = COA
					renderObj {
						10 = TEXT
						10 {
							date = GP:date
							wrap = |
						}
					}
				}
				#
				30 = COA
				30 {
					stdWrap.noTrimWrap = |<p>Termin: |</p>|
					stdWrap.noTrimWrap.override = | <p>Zeitraum: auf Anfrage</p>|
					stdWrap.noTrimWrap.override.if {
						isFalse.field = start_date
					}

					10 = COA
					10 {
						if {
							isTrue.field = start_date
						}
						10 = TEXT
						10 {
							stdWrap.field = start_date
							stdWrap.date = d.m.y
							stdWrap.noTrimWrap = | | bis: |
							stdWrap.noTrimWrap.override =|||
							stdWrap.noTrimWrap.override.if {
								value.field =  end_date
								equals.field = start_date
								#negate = 1
							}
						}
					}
					20 = COA
					20 {
						if {
							isTrue.field = end_date
						}
						10 = TEXT
						10 {
							if {
								value.field = end_date
								equals.field = start_date
								negate = 1
							}
							stdWrap.field = end_date
							stdWrap.date = d.m.y
							stdWrap.noTrimWrap = | ||

						}

					}

					30 = TEXT
					30 {
						if.isTrue.field = time
						field = time
						stdWrap.noTrimWrap = |<p>Uhrzeit: |</p>|

					}
				}

				40 = COA
				40 {
					10 = TEXT
					10 {
						stdWrap.field = price
						stdWrap.noTrimWrap = |<p>Preis: |.- €</p>|
					}
				}

			}
		}
	}



lib.seminarAnmeldung = COA
lib.seminarAnmeldung {
	1 =  LOAD_REGISTER
	1 {
		# loads all modules from given gp:date in register
		dateModules.cObject = CONTENT
		dateModules.cObject {
			wrap = <div class="seminar-form-flexlist">|</div>
			table =  tx_rhenagevents_domain_model_seminarmodule
			select {
				where = seminar_module=###date###
				markers {
					date.data = GP:date
					date.intVal = 1
				}
				pidInList = {$plugin.tx_rhenagevents_event.persistence.storagePid}
				languageField = 0
				orderBy = sorting
			}
			renderObj = COA
			renderObj {
				wrap = <div class="seminar-list-flexlist">|</div>
				stdWrap.override = Nach Vereinbarung
				stdWrap.override.if {
					isFalse.field = start_date
				}
				10 = TEXT
				10 {
					stdWrap.field = title
                    stdWrap.noTrimWrap = |<div class="seminar-list-flexitem">| </div>|
					parseFunc < lib.parseFunc_RTE
				}
				20 < .10
				20 {
					stdWrap.field = start_date
					stdWrap.date = d.m.Y
				}
				25 < .20
				25 {
					stdWrap.field = end_date
					stdWrap.date = d.m.Y
					required = 1
					stdWrap.noTrimWrap=|bis: ||
				}
				30 < .10
				30 {
					stdWrap.field = time
				}
			}
		}
		seminarDates.cObject = CONTENT
		seminarDates.cObject {
			table =  tx_rhenagevents_domain_model_seminardate
			select {
				where = uid=###date###
				markers {
					date.data = GP:date
					date.intVal = 1
				}
				pidInList = {$plugin.tx_rhenagevents_event.persistence.storagePid}
				languageField = 0
			}
			renderObj = COA
			renderObj {
				if {
					isTrue.field = start_date
				}
				10 = TEXT
				10 {
					field = start_date
					stdWrap.date = d.m.Y
					#required = 1
					stdWrap.override = Nach Vereinbarung
					stdWrap.override.if.isFalse.field = start_date
				}
				20 = TEXT
				20 {
					field = end_date
					stdWrap.date = d.m.Y
					required = 1
					stdWrap.noTrimWrap=| bis: ||
				}
				30 = TEXT
				30 {
					field = time
					required = 1
					stdWrap.noTrimWrap=|<br />||
				}
			}
		}
	}
	# seminar-date overview
	20 = CONTENT
	20 {

		table = tx_rhenagevents_domain_model_seminar
		select {

			where = uid=###seminar###
			markers {
				seminar.data = GP:seminar
				seminar.intVal = 1
			}
			pidInList = {$plugin.tx_rhenagevents_event.persistence.storagePid}
			languageField = 0
		}
		renderObj = COA
		renderObj {

			10 = TEXT
			10 {
				stdWrap.field = title
				stdWrap.noTrimWrap = |<h1 class="h1" style="margin-bottom:5px;">| </h1> |
			}
			15 = TEXT
			15 {
				stdWrap.field = subtitle
				stdWrap.wrap = <h2 style="margin-top:0px;">|</h2>
			}
			20 = TEXT
			20 {
				stdWrap.field = participants_counts
				stdWrap.noTrimWrap = |<p>Teilnehmeranzahl: |</p>|
				if.isTrue.field = participants_counts
			}

			30 = TEXT
			30 {
				stdWrap.dataWrap.noTrimWrap = |<p class="seminar--dates">Zeitraum: {register:seminarDates}</p>|
			#	if.isTrue.field = start_date
			}
			35 = TEXT
			35 {
				stdWrap.field = time
				stdWrap.noTrimWrap = |<p class="seminar--time">|</p>|
				required =1
			}
			35 >
			40 = TEXT
			40 {
				value = <p class="seminar--negate">Nach Vereinbarung</p>
				if.isFalse.field = start_date
			}

			# modules overview
			37 = CONTENT
			37 {
				table =  tx_rhenagevents_domain_model_seminardate
				select {
					where = uid=###date###
					markers {
						date.data = GP:date
						date.intVal = 1
					}
					pidInList = {$plugin.tx_rhenagevents_event.persistence.storagePid}
					languageField = 0
				}
				renderObj = COA
				renderObj {
					10 = TEXT
					10 {
						dataWrap =  <h2 class="seminar--module">Module</h2> {register:dateModules}
						required = 1
					}
					20 = TEXT
					20.value = <p>Nach Vereinbarung</p>
					20.if.isFalse.field = start_date
				}
			}
			40 = COA
			40 {
				10 = TEXT
				10 {
					stdWrap.field = price
					stdWrap.noTrimWrap = | <p class="seminar--price"><strong> Preis: |</strong></p>|
				}


				20 =< lib.empolyeePriceHint
				20 {
					if.isTrue.field = show_price_hint
				}
			}
		}
	}
	99 = RESTORE_REGISTER
}





lib.registrationMailSignature = COA
lib.registrationMailSignature {
	10 = TEXT
	10 {
		value =
		wrap = <br />|<br />
	}
}

lib.empolyeePriceHint = TEXT
lib.empolyeePriceHint.value = Bei Mitarbeitern der rhenag-Gruppe fallen Kosten nur an, sofern sie nicht durch den geltenden Betriebsberatungsvertrag abgedeckt sind.
lib.empolyeePriceHint.stdWrap.noTrimWrap = | | |

plugin.tx_rhenagevents_event {
	view {
		templateRootPaths.0 = {$plugin.tx_rhenagevents_event.view.templateRootPath}
		partialRootPaths.0 = {$plugin.tx_rhenagevents_event.view.partialRootPath}
		layoutRootPaths.0 = {$plugin.tx_rhenagevents_event.view.layoutRootPath}
	}

	persistence {
		storagePid = {$plugin.tx_rhenagevents_event.persistence.storagePid}
	}

	features.skipDefaultArguments = 1
	settings {
		singlePid = {$plugin.tx_rhenagevents_event.settings.singlePid}
		listPid = {$plugin.tx_rhenagevents_event.settings.listPid}
		registerPid = {$plugin.tx_rhenagevents_event.settings.registerPid}
		inIframe = {$plugin.tx_rhenagevents_event.settings.inIframe}
		additionalListViewClass = {$plugin.tx_rhenagevents_event.settings.additionalListViewClass}
		additionalSingleViewClass = {$plugin.tx_rhenagevents_event.settings.additionalSingleViewClass}
		additionalRegisterViewClass = {$plugin.tx_rhenagevents_event.settings.additionalRegisterViewClass}
	}

}

plugin.tx_rhenagevents_seminar {
	view {
		templateRootPaths.0 = {$plugin.tx_rhenagevents_seminar.view.templateRootPath}
		partialRootPaths.0 = {$plugin.tx_rhenagevents_seminar.view.partialRootPath}
		layoutRootPaths.0 = {$plugin.tx_rhenagevents_seminar.view.layoutRootPath}
	}

	persistence {
		storagePid = {$plugin.tx_rhenagevents_seminar.persistence.storagePid}
	}

	features.skipDefaultArguments = 1
	settings {
		listPid = {$plugin.tx_rhenagevents_seminar.settings.listPid}
		singlePid = {$plugin.tx_rhenagevents_seminar.settings.singlePid}
		registerPid = {$plugin.tx_rhenagevents_seminar.settings.registerPid}
		inIframe = {$plugin.tx_rhenagevents_seminar.settings.inIframe}
		additionalListViewClass = {$plugin.tx_rhenagevents_seminar.settings.additionalListViewClass}
		additionalSingleViewClass = {$plugin.tx_rhenagevents_seminar.settings.additionalSingleViewClass}
		additionalRegisterViewClass = {$plugin.tx_rhenagevents_seminar.settings.additionalRegisterViewClass}
	}
}


plugin.tx_powermail {
	settings {
		setup {

			# Exclude values from {powermail_all} by markername or fieldtype
			excludeFromPowermailAllMarker {
				# On Confirmation Page (if activated)
				confirmationPage {
					excludeFromMarkerNames = seminar_date,Seminar ID,teilnehmer2,teilnehmer3,teilnehmer4
					excludeFromFieldTypes = hidden, captcha
				}
				# On Submitpage
				submitPage {
					excludeFromMarkerNames = seminar_date,Seminar ID
					excludeFromFieldTypes = hidden, captcha
				}
				# In Mail to receiver
				receiverMail {
					excludeFromMarkerNames =
					excludeFromFieldTypes =
				}
				# In Mail to sender (if activated)
				senderMail {
					excludeFromMarkerNames =
					excludeFromFieldTypes = hidden, captcha
				}
			}
		}
	}
}

config.tx_extbase {
	persistence {
		classes {
			Vancado\RhenagEvents\Domain\Model {
				mapping {
					tableName = tx_rhenagevents_domain_model_seminar
					columns {
						sorting.mapOnProperty = sorting
					}
				}
			}
		}
	}
}
[traverse(request.getQueryParams(), 'event') > 0]

	plugin.tx_powermail.settings.setup.prefill {
		event_id = TEXT
		event_id.data = GP:event
		eventinfosinmail = RECORDS
		eventinfosinmail {
			source.data = GP:event
			source.intval = 1
			dontCheckPid = 1
			tables = tx_rhenagevents_domain_model_event
			conf.tx_rhenagevents_domain_model_event = COA
			conf.tx_rhenagevents_domain_model_event {
				10 = TEXT
				10 {
					stdWrap.field = title
				}

				20 = TEXT
				20 {
					stdWrap.field = price
					stdWrap.noTrimWrap = |, | |
					stdWrap.required = 1
				}
			}
		}


		event = RECORDS
		event {
			source.data = GP:event
			source.intval = 1
			dontCheckPid = 1
			tables = tx_rhenagevents_domain_model_event
			conf.tx_rhenagevents_domain_model_event = COA
			conf.tx_rhenagevents_domain_model_event {
				10 = TEXT
				10 {
					stdWrap.field = title
					stdWrap.noTrimWrap = |"|" |
				}
			}
		}

		sdate = RECORDS
		sdate {
			source.data = GP:event
			source.intval = 1
			dontCheckPid = 1
			tables = tx_rhenagevents_domain_model_event
			conf.tx_rhenagevents_domain_model_event = COA
			conf.tx_rhenagevents_domain_model_event {
				10 = TEXT
				10 {
					stdWrap.field = start_date
					current = 1
					stdWrap.noTrimWrap = | am ||
					stdWrap.required = 1
				}

				20 = TEXT
				20 {
					stdWrap.field = end_date
					current = 1
					stdWrap.noTrimWrap = | bis ||
					stdWrap.required = 1
				}

				30 = TEXT
				30 {
					current = 1
					if.isTrue.field = time
					stdWrap.field = time
					stdWrap.noTrimWrap = | (|)|

				}
			}
		}


	}
[end]

[traverse(request.getQueryParams(), 'tx_rhenagevents_event/event') > 0]
#[globalVar = GP:tx_rhenagevents_event|event > 0]
	plugin.tx_powermail.settings.setup.misc.addQueryString = 1
[end]
page.11 = TEXT
page.11.value = hello works not
[traverse(request.getQueryParams(), 'seminar') > 0]
    page.11 = TEXT
    page.11.value = hello works

	plugin.tx_powermail.settings.setup.prefill {
		seminar_id = TEXT
		seminar_id.data = GP:seminar
		seminar = COA
		seminar {
			10 = RECORDS
			10 {
				source.data = GP:seminar
				source.intval = 1
				dontCheckPid = 1
				tables = tx_rhenagevents_domain_model_seminar
				conf.tx_rhenagevents_domain_model_seminar = COA
				conf.tx_rhenagevents_domain_model_seminar {
					10 = TEXT
					10 {
						stdWrap.field = title
					}
					20 = TEXT
					20 {
						stdWrap.field = subtitle
					}
				}
			}

		}
		seminarmailinfos = TEXT
		seminarmailinfos.stdWrap.cObject < lib.seminarAnmeldung
		#seminarmailinfos.stdWrap.stripHtml=1
	}
[end]

plugin.tx_powermail {
	settings {
		setup {
			# Exclude values from {powermail_all} by markername or fieldtype
			excludeFromPowermailAllMarker {
				# On Confirmation Page (if activated)
				confirmationPage {
					# add some markernames (commaseparated) which should be excluded
					excludeFromMarkerNames =
					excludeFromFieldTypes = captcha
				}
				# On Submitpage
				submitPage {
					excludeFromMarkerNames =
					excludeFromFieldTypes = captcha
				}
				# In Mail to receiver
				receiverMail {
					excludeFromMarkerNames := addToList(seminarmailinfos)
					excludeFromFieldTypes =
				}
				# In Mail to sender (if activated)
				senderMail {
					excludeFromMarkerNames := addToList(seminarmailinfos,seminar)
					excludeFromFieldTypes = captcha
				}
				# In double-opt-in Mail to sender (if activated)
				optinMail {
					excludeFromMarkerNames =
					excludeFromFieldTypes = captcha
				}
			}
		}
	}
}
