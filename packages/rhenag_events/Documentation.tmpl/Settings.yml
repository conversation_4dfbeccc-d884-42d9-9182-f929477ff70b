# This is the project specific Settings.yml file.
# Place Sphinx specific build information here.
# Settings given here will replace the settings of 'conf.py'.

# Below is an example of intersphinx mapping declaration
# Add more mappings depending on what manual you want to link to
# Remove entirely if you don't need cross-linking

---
conf.py:
  copyright: 2016
  project: Rhenag Events
  version: 
  release: 
  intersphinx_mapping:
    t3tsref:
    - http://docs.typo3.org/typo3cms/TyposcriptReference/
    - null
  latex_documents:
  - - Index
    - rhenag_events.tex
    - Rhenag Events
    - 
    - manual
  latex_elements:
    papersize: a4paper
    pointsize: 10pt
    preamble: \usepackage
  html_theme_options:
    github_repository: TYPO3-Documentation/TYPO3CMS-Example-ExtensionManual
    github_branch: latest
...
