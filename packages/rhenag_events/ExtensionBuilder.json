{"modules": [{"config": {"position": [217, 0]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": true, "_default1_show": true, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": ["archiv"]}, "name": "Event", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": true, "categorizable": false, "description": "", "mapToTable": "", "parentClass": "", "sorting": false, "type": "Entity", "uid": "************"}, "propertyGroup": {"properties": [{"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "title", "propertyType": "String", "uid": "1347067320317"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "description", "propertyType": "RichText", "uid": "76504753048"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "price", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "startDate", "propertyType": "NativeDate", "uid": "138450065147"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "endDate", "propertyType": "NativeDate", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "time", "propertyType": "String", "uid": "1320460043069"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "infoPdf", "propertyType": "File", "uid": "145915810886"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "registrationPdf", "propertyType": "File", "uid": "79208585921"}]}, "relationGroup": {"relations": [{"foreignRelationClass": "", "lazyLoading": false, "propertyIsExcludeField": true, "relationDescription": "", "relationName": "place", "relationType": "manyToOne", "relationWire": "[wired]", "uid": "************"}, {"foreignRelationClass": "", "lazyLoading": false, "propertyIsExcludeField": true, "relationDescription": "", "relationName": "contacts", "relationType": "zeroToOne", "relationWire": "[wired]", "uid": "124543166685"}]}}}, {"config": {"position": [908, 69]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": true, "_default1_show": true, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": []}, "name": "Seminar", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": true, "categorizable": false, "description": "", "mapToTable": "", "parentClass": "", "sorting": false, "type": "Entity", "uid": "100708337508"}, "propertyGroup": {"properties": [{"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "title", "propertyType": "String", "uid": "1436679044703"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "price", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "startDate", "propertyType": "NativeDate", "uid": "101426043657"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "endDate", "propertyType": "NativeDate", "uid": "1435620865289"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "time", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "infoPdf", "propertyType": "File", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "registrationPdf", "propertyType": "File", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "participantsGroup", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "aim", "propertyType": "Text", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "content", "propertyType": "RichText", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "methods", "propertyType": "String", "uid": "1037517686366"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "trainer", "propertyType": "String", "uid": "1031339528108"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "duration", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "participantsCount", "propertyType": "String", "uid": "1350576654574"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "requirements", "propertyType": "String", "uid": "************"}]}, "relationGroup": {"relations": [{"foreignRelationClass": "", "lazyLoading": false, "propertyIsExcludeField": true, "relationDescription": "", "relationName": "place", "relationType": "manyToOne", "relationWire": "[wired]", "uid": "132274550504"}, {"foreignRelationClass": "", "lazyLoading": false, "propertyIsExcludeField": true, "relationDescription": "", "relationName": "contacts", "relationType": "manyToMany", "relationWire": "[wired]", "uid": "************"}]}}}, {"config": {"position": [1221, 29]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": true, "_default1_show": false, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": []}, "name": "SeminarKategorie", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": true, "categorizable": false, "description": "", "mapToTable": "", "parentClass": "", "sorting": false, "type": "Entity", "uid": "66453025"}, "propertyGroup": {"properties": [{"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "title", "propertyType": "String", "uid": "45405922147"}]}, "relationGroup": {"relations": [{"foreignRelationClass": "", "lazyLoading": false, "propertyIsExcludeField": true, "relationDescription": "", "relationName": "seminars", "relationType": "zeroToMany", "relationWire": "[wired]", "uid": "************"}]}}}, {"config": {"position": [46, 486]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": false, "_default1_show": false, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": []}, "name": "Place", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": false, "categorizable": false, "description": "", "mapToTable": "", "parentClass": "", "sorting": false, "type": "Entity", "uid": "1448073193035"}, "propertyGroup": {"properties": [{"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "title", "propertyType": "String", "uid": "1296489565122"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "addition", "propertyType": "String", "uid": "147012026247"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "address", "propertyType": "String", "uid": "1388996544649"}]}, "relationGroup": {"relations": []}}}, {"config": {"position": [533, 482]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": false, "_default1_show": false, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": []}, "name": "Contact", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": false, "categorizable": false, "description": "", "mapToTable": "", "parentClass": "", "sorting": false, "type": "Entity", "uid": "1070581189030"}, "propertyGroup": {"properties": [{"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "title", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "firstName", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "lastName", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "phone", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "email", "propertyType": "String", "uid": "************"}]}, "relationGroup": {"relations": []}}}], "properties": {"backendModules": [], "description": "", "emConf": {"category": "plugin", "custom_category": "", "dependsOn": "typo3 => 7.6.0-7.6.99\n", "disableLocalization": false, "disableVersioning": false, "skipGenerateDocumentationTemplate": false, "sourceLanguage": "en", "state": "alpha", "targetVersion": "7.6.0-7.6.99", "version": ""}, "extensionKey": "rhenag_events", "name": "Rhenag Events", "originalExtensionKey": "rhenag_events", "originalVendorName": "Vancado", "persons": [], "plugins": [{"actions": {"controllerActionCombinations": "Event => list,archiv,show", "noncacheableActions": "", "switchableActions": "List\nEvent->list\nArchiv\nEvent->archiv\nSingle\nEvent->show"}, "key": "event", "name": "Events list"}, {"actions": {"controllerActionCombinations": "SeminarKategorie => list\nSeminar => show", "noncacheableActions": "", "switchableActions": "List\nSeminarKategorie->list\nSingle\nSeminar->show"}, "key": "seminar", "name": "Seminars list"}], "vendorName": "Vancado"}, "wires": [{"src": {"moduleId": 2, "terminal": "relationWire_0", "uid": "************"}, "tgt": {"moduleId": 1, "terminal": "SOURCES", "uid": "100708337508"}}, {"src": {"moduleId": 0, "terminal": "relationWire_0", "uid": "************"}, "tgt": {"moduleId": 3, "terminal": "SOURCES", "uid": "1448073193035"}}, {"src": {"moduleId": 0, "terminal": "relationWire_1", "uid": "124543166685"}, "tgt": {"moduleId": 4, "terminal": "SOURCES", "uid": "1070581189030"}}, {"src": {"moduleId": 1, "terminal": "relationWire_0", "uid": "132274550504"}, "tgt": {"moduleId": 3, "terminal": "SOURCES", "uid": "1448073193035"}}, {"src": {"moduleId": 1, "terminal": "relationWire_1", "uid": "************"}, "tgt": {"moduleId": 4, "terminal": "SOURCES", "uid": "1070581189030"}}], "log": {"last_modified": "2016-10-06 05:30", "extension_builder_version": "7.6.0", "be_user": " (7)"}}