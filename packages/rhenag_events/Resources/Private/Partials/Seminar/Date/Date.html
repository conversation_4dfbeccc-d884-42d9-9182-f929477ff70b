<table class="table__date table-theme-boxed">
	<tr>
		<td class="table__wrap">
			<div class="table__left">
				<f:if condition="{date.startDate}">
					<f:then>
						Termin<br />
						<span class="event-date event-date--startdate">
							<strong><f:format.date format="d.m.Y">{date.startDate}</f:format.date></strong>
						</span>
					</f:then>
					<f:else>
						<strong>Nach Vereinbarung</strong>
					</f:else>
				</f:if>
				<f:if condition="{date.endDate}">
					bis <span class="event-date event-date--enddate"><strong><f:format.date format="d.m.Y">{date.endDate}</f:format.date></strong></span>
				</f:if>
				<f:if condition="{date.time}">
					<br /><span class="event-date event-date--time">{date.time}</span>
				</f:if>
				<div class="table__left--modules">

					<f:if condition="{date.recordType} > 0">
						<f:if condition="{date.modules}">
							Module<br />
							<div class="seminar-list-flexlist">
								<f:for each="{date.modules}" as="module">
									<f:render partial="Seminar/Date/Module/Module" arguments="{module: module}" />
								</f:for>
							</div>
						</f:if>
					</f:if>
				</div>
			</div>
			<div class="table__right">
				<f:link.typolink  parameter="{settings.registerPid}" additionalParams="&seminar={seminar.uid}&date={date.uid}"  class="btn btn-primary">jetzt anmelden</f:link.typolink>
			</div>
		</td>
	</tr>
</table>

