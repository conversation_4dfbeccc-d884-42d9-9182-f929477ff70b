<f:layout name="Default">
</f:layout>


<f:section name="main">
	<!-- section main -->
	<f:if condition="{events}">
		<ul class="events-list {f:if(condition: '{settings.additionalListViewClass}', then: ' {settings.additionalListViewClass}')}">
			<f:for as="event" each="{events}">

				<li>
					<f:link.action arguments="{event: event.uid}" pageUid="{settings.singlePid}" controller="Event"
								   action="show">
						<span class="event-date date-only">
                            <f:if condition="{event.startDate}">
                                <f:then>
                                    <f:format.date format="d.m.Y">{event.startDate}</f:format.date>
                                </f:then>
                                <f:else>nach Vereinbarung</f:else>
                            </f:if>
                        </span>
						<f:if condition="{event.place}">
							<span class="event-city">
                                {event.place.title}
                            </span>
						</f:if>
						<span class="event-title">
								<f:if condition="{event.showNewStatus}">
									<f:then>
										<i class="newSeminarBubble">Neu</i>
									</f:then>
								</f:if>
								<f:if condition="{event.showOnlineStatus}">
									<f:then>
										<i class="newSeminarBubble"> Online</i>
									</f:then>
								</f:if>
                            	<f:if condition="{event.showCompactStatus}">
									<f:then>
										<i class="compactSeminarBubble"> EVU kompakt</i>
									</f:then>
								</f:if>
									{event.title}
                            <f:if condition="{event.bookedOut}">
                                <span class="event-bookedout">AUSGEBUCHT</span>
							</f:if>
						</span>
					</f:link.action>
				</li>
			</f:for>
		</ul>
	</f:if>
</f:section>


<f:section name="iframe">
	<!-- Resources/Private/Templates/Event/List.html --  iframe -->
	<f:if condition="{events}">
		<table data-first-page="yes"
			   class="container events-list {f:if(condition: '{settings.additionalListViewClass}', then: ' {settings.additionalListViewClass}')}">
			<f:for as="event" each="{events}">
				<tr>
					<td class="vnc-date">
						<span class="event-date date-only">
                            <f:if condition="{event.startDate}">
                                <f:then>
                                    <f:format.date format="d.m.Y">{event.startDate}</f:format.date>
                                </f:then>
                                <f:else>nach Vereinbarung</f:else>
                            </f:if>
                        </span>
					</td>
					<td class="vnc-location">
						<f:if condition="{event.place}">
							<span class="event-city">
                                {event.place.title}
                            </span>
						</f:if>
					</td>
					<td class="vnc-title">
						<f:link.action arguments="{event: event.uid}" pageUid="{settings.singlePid}">
							<span class="event-title">
                                {event.title}
                                <f:if condition="{event.bookedOut}">
                                    <span class="event-bookedout">AUSGEBUCHT</span>
								</f:if>
							</span>
						</f:link.action>
					</td>
				</tr>
			</f:for>
		</table>
	</f:if>
	<!-- section iframe -->
</f:section>