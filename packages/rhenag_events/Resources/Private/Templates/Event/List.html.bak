<f:layout name="Default">
</f:layout>
This Template is responsible for creating a table of domain objects.

If you modify this template, do not forget to change the overwrite settings
in /Configuration/ExtensionBuilder/settings.yaml:
  Resources:
    Private:
      Templates:
        List.html: keep

Otherwise your changes will be overwritten the next time you save the extension in the extension builder
<f:section name="main">
	
    <f:if condition="{events}">
        <ul class="events-list">
            <f:for as="event" each="{events}">
                <li>
                    <f:link.action arguments="{event: event.uid}" pageUid="175">
                        <span class="event-date date-only">
                            <f:if condition="{event.startDate}">
                                <f:then>
                                    <f:format.date format="d.m.Y">{event.startDate}</f:format.date>
                                </f:then>
                                <f:else>nach Vereinbarung</f:else>
                            </f:if>
                        </span>
                        <f:if condition="{event.place}">
                            <span class="event-city">
                                {event.place.title}
                            </span>
                        </f:if>
                        <span class="event-title">
                            {event.title}                        
                            <f:if condition="{event.bookedOut}">
                                <span class="event-bookedout">AUSGEBUCHT</span>
                            </f:if>
                        </span>
                    </f:link.action>
                </li>
            </f:for>
        </ul>
    </f:if>
</f:section>