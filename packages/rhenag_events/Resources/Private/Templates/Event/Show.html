<f:layout name="Default" />




<f:section name="main">
	<!-- section main -->
<div class="tx-rhenag-events-single {f:if(condition: '{settings.additionalSingleViewClass}', then: ' {settings.additionalSingleViewClass}')}">
	<h1 class="h1">{event.title}</h1>
	<f:format.html>{event.description}</f:format.html>
		<h2 class="h2">Termine und Anmeldung</h2>
		<f:if condition="{event.endDate} > {dateNow}">
		<f:then>
		<table class="table table-theme-boxed">
			<tr>
				<td><strong>Termin</strong></td>
				<td>
					<f:if condition="{event.startDate}">
						<f:then>
							<f:format.date format="d.m.Y">{event.startDate}</f:format.date>
							<f:if condition="{event.endDate} > {event.startDate}"> - <f:format.date format="d.m.Y">{event.endDate}</f:format.date></f:if>
							<f:if condition="{event.time}">, {event.time}</f:if>
						</f:then>
						<f:else>
							nach Vereinbarung
						</f:else>
					</f:if>
				</td>
			</tr>
			<f:if condition="{event.place}">
				<tr>
					<td><strong>Ort</strong></td>
					<td>{event.place.title}<f:if condition="{event.place.addition}">, {event.place.addition}</f:if><f:if condition="{event.place.address}">, {event.place.address}</f:if></td>
				</tr>
			</f:if>
			<f:if condition="{event.price}">
				<tr>
					<td><strong>Preis</strong></td>
					<td>{event.price}</td>
				</tr>
			</f:if>
			<f:if condition="{event.contacts}">
				<tr>
					<td><strong>Ansprechpartner</strong></td>
					<td>
						<f:for each="{event.contacts}" as="contact">
							<p><f:if condition="{contact.title}">{contact.title} </f:if>{contact.firstName} {contact.lastName}<f:if condition="{contact.phone}">, Telefon {contact.phone}</f:if><f:if condition="{contact.email}">, <f:link.email email="{contact.email}" /></f:if></p>
						</f:for>
					</td>
				</tr>
			</f:if>

			<f:if condition="{event.registrationPdf}">
				<tr>
					<td><strong>Offline-Anmeldung</strong></td>
					<td><a class="link-download" target="_blank" href="fileadmin{event.registrationPdf.originalResource.originalFile.identifier}">{event.registrationPdf.originalResource.originalFile.name}</a></td>
				</tr>
			</f:if>
			<f:if condition="{event.infoPdf}">
				<tr>
					<td><strong>Agenda</strong></td>
					<td><a class="link-download" target="_blank" href="fileadmin{event.infoPdf.originalResource.originalFile.identifier}">{event.infoPdf.originalResource.originalFile.name}</a></td>
				</tr>
		</f:if>
		</table>
		<f:if condition="{event.bookedOut}">
			<f:then>
				Die Veranstaltung ist leider ausgebucht.
			</f:then>
			<f:else>

				<f:link.page pageUid="{settings.registerPid}" additionalParams="{event: event.uid}" class="btn btn-primary">Jetzt online anmelden</f:link.page>
			</f:else>
		</f:if>

		</f:then>
		<f:else>
			<p>Diese Veranstaltung hat bereits erfolgreich stattgefunden. Wenn Sie Interesse an einer Wiederholung haben, sprechen Sie uns an.
				Wir freuen uns über Ihre Nachricht an <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
		</f:else>
		</f:if>
</div>
</f:section>

<f:section name="iframe">

	<!-- trunk/typo3conf/ext/rhenag_events/Resources/Private/Templates/Event/Show.html iframe -->
	<div class="service tx-rhenag-events-single {f:if(condition: '{settings.additionalSingleViewClass}', then: ' {settings.additionalSingleViewClass}')}">
		<h1 class="h1 with-margin">{event.title}</h1>
		<f:format.html>{event.description}</f:format.html>
		<h2 class="h2">Termine und Anmeldung</h2>
		<f:if condition="{event.endDate} > {dateNow}">
			<f:then>
				<table class="event-details contenttable">
					<tr>
						<td><strong>Termin</strong></td>
						<td>
							<f:if condition="{event.startDate}">
								<f:then>
									<f:format.date format="d.m.Y">{event.startDate}</f:format.date>
									<f:if condition="{event.endDate} > {event.startDate}"> - <f:format.date format="d.m.Y">{event.endDate}</f:format.date></f:if>
									<f:if condition="{event.time}">, {event.time}</f:if>
								</f:then>
								<f:else>
									nach Vereinbarung
								</f:else>
							</f:if>
						</td>
					</tr>
					<f:if condition="{event.place}">
						<tr>
							<td><strong>Ort</strong></td>
							<td>{event.place.title}<f:if condition="{event.place.addition}">, {event.place.addition}</f:if><f:if condition="{event.place.address}">, {event.place.address}</f:if></td>
						</tr>
					</f:if>
					<f:if condition="{event.price}">
						<tr>
							<td><strong>Preis</strong></td>
							<td>{event.price}</td>
						</tr>
					</f:if>
					<f:if condition="{event.contacts}">
						<tr>
							<td><strong>Ansprechpartner</strong></td>
							<td>
								<f:for each="{event.contacts}" as="contact">
									<p><f:if condition="{contact.title}">{contact.title} </f:if>{contact.firstName} {contact.lastName}<f:if condition="{contact.phone}">, Telefon {contact.phone}</f:if><f:if condition="{contact.email}">, <f:link.email email="{contact.email}" /></f:if></p>
								</f:for>
							</td>
						</tr>
					</f:if>

					<f:if condition="{event.registrationPdf}">
						<tr>
							<td><strong>Offline-Anmeldung</strong></td>
							<td><a class="link-download" target="_blank" href="fileadmin{event.registrationPdf.originalResource.originalFile.identifier}">{event.registrationPdf.originalResource.originalFile.name}</a></td>
						</tr>
					</f:if>
					<f:if condition="{event.infoPdf}">
						<tr>
							<td><strong>Agenda</strong></td>
							<td><a class="link-download" target="_blank" href="fileadmin{event.infoPdf.originalResource.originalFile.identifier}">{event.infoPdf.originalResource.originalFile.name}</a></td>
						</tr>
					</f:if>
				</table>
				<f:if condition="{event.bookedOut}">
					<f:then>
						Die Veranstaltung ist leider ausgebucht.
					</f:then>
					<f:else>
						<f:link.page pageUid="{settings.registerPid}" additionalParams="{event: event.uid}" class="button btn btn-primary">Jetzt online anmelden</f:link.page>
					</f:else>
				</f:if>
			</f:then>

			<f:else>
				<p>Diese Veranstaltung hat bereits erfolgreich stattgefunden. Wenn Sie Interesse an einer Wiederholung haben, sprechen Sie uns an.
					Wir freuen uns über Ihre Nachricht an <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
			</f:else>
		</f:if>
	</div>
</f:section>