<f:layout name="Default">
</f:layout>
This Template is responsible for creating a table of domain objects.

If you modify this template, do not forget to change the overwrite settings
in /Configuration/ExtensionBuilder/settings.yaml:
  Resources:
    Private:
      Templates:
        Teasers.html: keep

Otherwise your changes will be overwritten the next time you save the extension in the extension builder
<f:section name="main">
    <f:if condition="{events}">
        <div class="home-events events-{settings.limit} equal-height">
            <f:for as="event" each="{events}" iteration="iterator">
                <div class="home-events-item{f:if(condition:'{iterator.isLast}', then:' last')}">
                    <f:format.date format="d.m.Y">{event.startDate}</f:format.date>
                    <h3 class="teaser-landingpage-title event-header">{event.title}</h3>
                    <f:format.html><f:format.crop maxCharacters="300">{event.description}</f:format.crop></f:format.html>
                    <f:link.action arguments="{event: event.uid}" pageUid="175" class="link-with-arrow">Weitere Informationen</f:link.action>
                </div>
            </f:for>
            <div class="clearfix"></div>
        </div>
        <f:comment>
        <ul class="events-list">
            <f:for as="event" each="{events}">
                <li>
                    <f:link.action arguments="{event: event.uid}" pageUid="175">
                        <span class="event-date date-only">
                            <f:if condition="{event.startDate} == {event.endDate}">
                                <f:then>
                                    <f:format.date format="d.m.Y">{event.startDate}</f:format.date>
                                </f:then>
                                <f:else>nach Vereinbarung</f:else>
                            </f:if>
                        </span>
                        <f:if condition="{event.place}">
                            <span class="event-city">
                                {event.place.title}
                            </span>
                        </f:if>
                        <span class="event-title">
                            {event.title}
                        </span>
                    </f:link.action>
                </li>
            </f:for>
        </ul>
        </f:comment>
    </f:if>
</f:section>