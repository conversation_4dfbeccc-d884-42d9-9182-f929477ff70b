{namespace vh=In2code\Powermail\ViewHelpers}
<f:layout name="Export" />

Render Powermail CSV Export
{mails}					All Mails for exporting
{fields}				Fields to export (drag'n drop settings in module)

<f:section name="main"><vh:String.Utf8ToUtf16><vh:string.Trim>

	<f:for each="{mails}" as="mail" iteration="index">
		<f:if condition="{index.isFirst}">
			<f:for each="{fields}" as="field">
				"<vh:String.RemoveQuote>{field.title}</vh:String.RemoveQuote>";
			</f:for>
			<br />
		</f:if>


		<f:for each="{fields}" as="field">
			<f:for each="{mail.answers}" as="answer">
				<f:if condition="{field} == {answer.field}">

					<f:if condition="{vh:Condition.IsArray(val: '{answer.value}')}">
						<f:else>
							"<vh:String.RemoveQuote>{answer.value}</vh:String.RemoveQuote>";
						</f:else>
						<f:then>
							"<vh:String.RemoveQuote>
								<f:for each="{answer.value}" as="singleValue">
									<f:if condition="{singleValue}">
										{singleValue},
									</f:if>
								</f:for>
							</vh:String.RemoveQuote>";
						</f:then>
					</f:if>

				</f:if>
			</f:for>
		</f:for>
		<br />
	</f:for>

</vh:String.Trim></vh:String.Utf8ToUtf16></f:section>