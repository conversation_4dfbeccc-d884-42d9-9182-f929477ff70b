
<f:layout name="Default" />

This Template is responsible for creating a table of domain objects.

If you modify this template, do not forget to change the overwrite settings
in /Configuration/ExtensionBuilder/settings.yaml:
  Resources:
    Private:
      Templates:
        List.html: keep

Otherwise your changes will be overwritten the next time you save the extension in the extension builder

<f:section name="main">
	<!-- main section -->
<h1>Listing for Seminar</h1>
<table  class="tx_rhenagevents" >
	<tr>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.title" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.price" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.start_date" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.end_date" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.time" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.info_pdf" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.registration_pdf" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.participants_group" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.aim" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.content" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.methods" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.trainer" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.duration" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.participants_count" /></th>
		<th><f:translate key="tx_rhenagevents_domain_model_seminar.requirements" /></th>
		<th> </th>
		<th> </th>
	</tr>

	<f:for each="{seminars}" as="seminar">
		<tr>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.title}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.price}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.startDate -> f:format.date()}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.endDate -> f:format.date()}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.time}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.infoPdf.originalResource.name}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.registrationPdf.originalResource.name}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.participantsGroup}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.aim}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.content}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.methods}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.trainer}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.duration}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.participantsCount}</f:link.action></td>
			<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.requirements}</f:link.action></td>
			<td><f:link.action action="edit" arguments="{seminar : seminar}">Edit</f:link.action></td>
			<td><f:link.action action="delete" arguments="{seminar : seminar}">Delete</f:link.action></td>
		</tr>
	</f:for>
</table>
</f:section>

<f:section name="iframe">
	<!-- iframe section unused ? -->
	<h1>Listing for Seminar</h1>
	<table  class="tx_rhenagevents" >
		<tr>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.title" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.price" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.start_date" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.end_date" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.time" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.info_pdf" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.registration_pdf" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.participants_group" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.aim" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.content" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.methods" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.trainer" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.duration" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.participants_count" /></th>
			<th><f:translate key="tx_rhenagevents_domain_model_seminar.requirements" /></th>
			<th> </th>
			<th> </th>
		</tr>

		<f:for each="{seminars}" as="seminar">
			<tr>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.title}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.price}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.startDate -> f:format.date()}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.endDate -> f:format.date()}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.time}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.infoPdf.originalResource.name}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.registrationPdf.originalResource.name}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.participantsGroup}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.aim}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.content}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.methods}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.trainer}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.duration}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.participantsCount}</f:link.action></td>
				<td><f:link.action action="show" arguments="{seminar : seminar}"> {seminar.requirements}</f:link.action></td>
				<td><f:link.action action="edit" arguments="{seminar : seminar}">Edit</f:link.action></td>
				<td><f:link.action action="delete" arguments="{seminar : seminar}">Delete</f:link.action></td>
			</tr>
		</f:for>
	</table>
</f:section>