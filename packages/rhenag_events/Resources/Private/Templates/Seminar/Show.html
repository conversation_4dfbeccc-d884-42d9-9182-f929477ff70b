{namespace v=FluidTYPO3\Vhs\ViewHelpers}
<f:layout name="Default" />

<f:section name="main">

	<h1 style="margin-bottom: 10px;" class="h1">{seminar.title} </h1>
	<f:if condition="{seminar.subtitle}">
		<h2 style="margin-top: 0;">{seminar.subtitle}</h2>
	</f:if>
	<table class="table table-theme-boxed">
		<f:if condition="{seminar.participantsGroup}">
			<tr>
				<td><strong>Teilnehmerkreis</strong></td>
				<td>{seminar.participantsGroup}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.aim}">
			<tr>
				<td><strong>Zielsetzung</strong></td>
				<td>{seminar.aim}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.content}">
			<tr>
				<td><strong>Inhalt</strong></td>
				<td><f:format.html>{seminar.content}</f:format.html>
				<f:if condition="{seminar.infoPdf}">
						<a class="link-download" target="_blank" href="fileadmin{seminar.infoPdf.originalResource.originalFile.identifier}">Seminarinhalte herunterladen</a>
				</f:if>
				</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.methods}">
			<tr>
				<td><strong>Methoden</strong></td>
				<td>{seminar.methods}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.trainer}">
			<tr>
				<td><strong>Trainer/Referenten</strong></td>
				<td>{seminar.trainer -> f:format.nl2br()}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.duration}">
			<tr>
				<td><strong>Dauer</strong></td>
				<td>{seminar.duration}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.participantsCount}">
			<tr>
				<td><strong>Teilnehmerzahl</strong></td>
				<td>{seminar.participantsCount}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.place}">
			<tr>
				<td><strong>Ort</strong></td>
				<td>{seminar.place.title}<f:if condition="{seminar.place.addition}">, {seminar.place.addition}</f:if><f:if condition="{seminar.place.address}">, {seminar.place.address}</f:if></td>
			</tr>
		</f:if>
		<f:if condition="{seminar.price}">
			<tr>
				<td><strong>Preis</strong></td>
				<td>{seminar.price}</td>
			</tr>
			<f:if condition="{seminar.showPriceHint}">
				<tr>
					<td><strong>Preishinweis</strong></td>
					<td><f:cObject typoscriptObjectPath="lib.empolyeePriceHint" /></td>
				</tr>

			</f:if>
		</f:if>
		<f:if condition="{seminar.contacts}">
			<tr>
				<td><strong>Ansprechpartner</strong></td>
				<td>
					<f:for each="{seminar.contacts}" as="contact">
						<p><f:if condition="{contact.title}">{contact.title} </f:if>{contact.firstName} {contact.lastName}<f:if condition="{contact.phone}">, Telefon {contact.phone}</f:if><f:if condition="{contact.email}">, <f:link.email email="{contact.email}" /></f:if></p>
					</f:for>
				</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.registrationContacts}">
			<tr>
				<td><strong>Anmeldung</strong></td>
				<td>
					<f:for each="{seminar.registrationContacts}" as="contact">
						<p><f:if condition="{contact.title}">{contact.title} </f:if>{contact.firstName} {contact.lastName}<f:if condition="{contact.phone}">, Telefon {contact.phone}</f:if><f:if condition="{contact.email}">, <f:link.email email="{contact.email}" /></f:if></p>
					</f:for>
				</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.registrationPdf}">
			<tr>
				<td><strong>Offline-Anmeldung</strong></td>
				<td><a class="link-download" target="_blank" href="fileadmin{seminar.registrationPdf.originalResource.originalFile.identifier}">{seminar.registrationPdf.originalResource.originalFile.name}</a></td>
			</tr>
		</f:if>
	</table>

	<h2 class="h2">Termine und Anmeldung</h2>
	<f:if condition="{seminar.dates}">
		<f:then>
			<f:for each="{seminar.dates}" as="date">
				<f:render partial="Seminar/Date/Date" arguments="{date:date,seminar:seminar}" />
			</f:for>
		</f:then>
		<f:else>
			<div class="alert alert-danger">Seminartermin-Datensatz nicht vorhanden!</div>
		</f:else>
	</f:if>

</f:section>







<f:section name="iframe">
	<!-- Resources/Private/Templates/Seminar/Show.html -- iframe -->
	<div class="service">
	<h1 class="h1 with-margin">{seminar.title}</h1>
	<table class="event-details contenttable">
		<f:if condition="{seminar.participantsGroup}">
			<tr>
				<td><strong>Teilnehmerkreis</strong></td>
				<td>{seminar.participantsGroup}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.aim}">
			<tr>
				<td><strong>Zielsetzung</strong></td>
				<td>{seminar.aim}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.content}">
			<tr>
				<td><strong>Inhalt</strong></td>
				<td><f:format.html>{seminar.content}</f:format.html>
					<f:if condition="{seminar.infoPdf}">
						<a class="link-download" target="_blank" href="fileadmin{seminar.infoPdf.originalResource.originalFile.identifier}">Seminarinhalte herunterladen</a>
					</f:if>
				</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.methods}">
			<tr>
				<td><strong>Methoden</strong></td>
				<td>{seminar.methods}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.trainer}">
			<tr>
				<td><strong>Trainer/Referenten</strong></td>
				<td>{seminar.trainer}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.duration}">
			<tr>
				<td><strong>Dauer</strong></td>
				<td>{seminar.duration}</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.participantsCount}">
			<tr>
				<td><strong>Teilnehmerzahl</strong></td>
				<td>{seminar.participantsCount}</td>
			</tr>
		</f:if>
	</table>

	<h2 class="h2">Termine und Anmeldung</h2>
	<table class="event-details contenttable">
		<tr>
			<td><strong>Termin</strong></td>
			<td>
				<f:if condition="{seminar.startDate}">
					<f:then>
						<f:if condition="{f:format.date(date: seminar.startDate, format: 'Y-m-d')} == {f:format.date(date: seminar.endDate, format: 'Y-m-d')}">
							<f:then>
								<f:format.date format="d.m.Y">{seminar.startDate}</f:format.date><f:if condition="{seminar.time}">; {seminar.time}</f:if>
							</f:then>
							<f:else>
								<f:format.date format="d.m.Y">{seminar.startDate}</f:format.date> - <f:format.date format="d.m.Y">{seminar.endDate}</f:format.date><f:if condition="{seminar.time}">; {seminar.time}</f:if>
							</f:else>
						</f:if>
					</f:then>
					<f:else>
						nach Vereinbarung
					</f:else>
				</f:if>
				<f:if condition="{seminar.recurring}">
					<p class="recurringEvent"><f:format.raw>{seminar.recurring}</f:format.raw></p>
				</f:if>
			</td>
		</tr>
		<f:if condition="{seminar.place}">
			<tr>
				<td><strong>Ort</strong></td>
				<td>{seminar.place.title}<f:if condition="{seminar.place.addition}">, {seminar.place.addition}</f:if><f:if condition="{seminar.place.address}">, {seminar.place.address}</f:if></td>
			</tr>
		</f:if>
		<f:if condition="{seminar.price}">
			<tr>
				<td><strong>Preis</strong></td>
				<td>{seminar.price}</td>
			</tr>
			<f:if condition="{seminar.showPriceHint}">
				<tr>
					<td><strong>Preishinweis</strong></td>
					<td><f:cObject typoscriptObjectPath="lib.empolyeePriceHint" /></td>
				</tr>

			</f:if>
		</f:if>

		<f:if condition="{seminar.contacts}">
			<tr>
				<td><strong>Ansprechpartner</strong></td>
				<td>
					<f:for each="{seminar.contacts}" as="contact">
						<p><f:if condition="{contact.title}">{contact.title} </f:if>{contact.firstName} {contact.lastName}<f:if condition="{contact.phone}">, Telefon {contact.phone}</f:if><f:if condition="{contact.email}">, <f:link.email email="{contact.email}" /></f:if></p>
					</f:for>
				</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.registrationContacts}">
			<tr>
				<td><strong>Anmeldung</strong></td>
				<td>
					<f:for each="{seminar.registrationContacts}" as="contact">
						<p><f:if condition="{contact.title}">{contact.title} </f:if>{contact.firstName} {contact.lastName}<f:if condition="{contact.phone}">, Telefon {contact.phone}</f:if><f:if condition="{contact.email}">, <f:link.email email="{contact.email}" /></f:if></p>
					</f:for>
				</td>
			</tr>
		</f:if>
		<f:if condition="{seminar.registrationPdf}">
			<tr>
				<td><strong>Offline-Anmeldung</strong></td>
				<td><a class="link-download" target="_blank" href="fileadmin{seminar.registrationPdf.originalResource.originalFile.identifier}">{seminar.registrationPdf.originalResource.originalFile.name}</a></td>
			</tr>
		</f:if>
	</table>
	<f:link.typolink  parameter="{settings.registerPid}" additionalParams="&seminar={seminar.uid}"  class="button btn btn-primary">jetzt anmelden</f:link.typolink>
	</div>
</f:section>