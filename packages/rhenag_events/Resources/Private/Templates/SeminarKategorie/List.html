<f:layout name="Default" />


<f:section name="main">

<div class="accordion accordion-secondary">
<f:for each="{seminarKategories}" as="seminarKategorie" iteration="iteration">
	<dl>
		<dt>
			<a href="#accordion{iteration.cycle}" aria-expanded="false" aria-controls="accordion{iteration.cycle}" class="accordion-title accordionTitle js-accordionTrigger  is-collapsed is-expanded">{seminarKategorie.title}</a>
		</dt>
		<dd class="accordion-content accordionItem is-collapsed" id="accordion{iteration.cycle}" aria-hidden="true">
			<div class="accordion-content-wrapper">
				<f:if condition="{seminarKategorie.seminars}">
					<ul class="events-list">
					<f:for each="{seminarKategorie.seminars}" as="seminar">
						<li class="event-listitem">
							<f:link.action action="show" pageUid="{settings.singlePid}" arguments="{seminar: seminar}">
								<div class="event-listitem-item">
									<h4 style="margin:0">{seminar.title}</h4>
									<f:if condition="{seminar.showNewStatus}">
										<p class="event-subtitle">{seminar.subtitle}</p>
									</f:if>
								</div>
								<div class="event-listitem-status">
									<f:if condition="{seminar.showNewStatus}">
										<f:then>
											<i class="newSeminarBubble new">Neu</i>
										</f:then>
									</f:if>
									<f:if condition="{seminar.showOnlineStatus}">
										<f:then>
											<i class="newSeminarBubble online"> Online</i>
										</f:then>
									</f:if>
								</div>
							</f:link.action>
						</li>
					</f:for>
					</ul>
				</f:if>
			</div>
		</dd>
	</dl>	
</f:for>
</div>

</f:section>


<f:section name="iframe">
<!--  Resources/Private/Templates/SeminarKategorie/List.html  --- iframe -->
	<div class="roundedbox vnc-box-seminare" data-first-page="yes">
		<f:for each="{seminarKategories}" as="seminarKategorie" iteration="iteration">
			<div class="panel panel-default"> 
				<div class="panel-heading">
					<h2 class="panel-title">
						<a data-toggle="collapse" data-parent="#accordion" href="javascript:">
							{seminarKategorie.title}
						</a>
					</h2>
				</div>
				<div class="panel-collapse collapse" role="tabpanel"> 
					<div class="panel-body">
						<p class="bodytext">
						<f:if condition="{seminarKategorie.partnerSeminars}">
							<ul class="events-list">
								<f:for each="{seminarKategorie.partnerSeminars}" as="seminar">
									<li>
										<f:link.action pageUid="{settings.singlePid}" arguments="{seminar: seminar.uid}" pluginName="seminar" controller="Seminar">
											<f:if condition="{seminar.showNewStatus}">
												<f:then>
													<span class="seminar-title-new">
														<i class="newSeminarBubble">Neu</i>{seminar.title}
													</span>
													<br />
												</f:then>
												<f:else>
													<span class="seminar-title-regular">{seminar.title}
													</span>
													<br />
												</f:else>
											</f:if>
											<span class="seminar-date">
												Datum:
												<f:if condition="{seminar.startDate}">
										 			<f:then>
										 				<f:format.date format="d.m.Y">{seminar.startDate}</f:format.date>
										 			</f:then>
													<f:else>
														nach Vereinbarung
													</f:else>
												</f:if>
											</span>
											<f:if condition="{seminar.place}">
												<br/>
												<span class="seminar-place"> Ort: {seminar.place.title}</span>
											</f:if>
										</f:link.action>
									</li>
								</f:for>
							</ul>
						</f:if>
						</p>
					</div>
				</div>
			</div>
		</f:for>
	</div>
</f:section>