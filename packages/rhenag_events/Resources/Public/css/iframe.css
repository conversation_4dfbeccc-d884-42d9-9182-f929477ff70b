html, body {
	margin: 0;
	padding: 0;
	width: '100%';
	height: '100%';
	overflow: hidden;
	position: relative;
	color:#333;
}

#iFrame {
	position: relative;
	width:100%;
	height:auto;
	min-height:200px;
	padding-bottom: 5rem;
	padding-top:5rem;
}

@media(max-width: 767px) {
	#iFrame {
		padding-top:1rem;
	}
}

#iFrame h1,
#iFrame h2,
#iFrame h3,
#iFrame h4,
#iFrame h5,
#iFrame h6 {
	text-align: center;
	font-weight: 700;
}

#iFrame h1 {
	font-size:3rem;
}
#iFrame h2 {
	font-size:2.5rem;
}
#iFrame h3 {
	font-size:2rem;
}
#iFrame h4, h5, h6 {
	font-size:1.5rem;
}

@media(max-width: 767px) {
	#iFrame h1 {
		font-size:2.4rem;
	}
	#iFrame h2 {
		font-size:2.1rem;
	}
	#iFrame h3 {
		font-size:1.8rem;
	}
	#iFrame h4, h5, h6 {
		font-size:1.5rem;
	}
}

#iFrame .events-navigation h3,
#iFrame .events-navigation ul {
	/*display: none;*/
}

#iFrame .row {
	margin: 0;
}

#iFrame .vnc-navigation {
	padding:2rem;
	padding-bottom: 0;
	background-color:#004494;
	color:#fff;
	border-radius: 15px;
	margin-top:3rem;
}

#iFrame .vnc-navigation a {
	margin-bottom: 2rem;
}

@media(max-width: 767px) {
	#iFrame .vnc-navigation {
		margin-top:1rem;
	}
}



#iFrame .vnc-navigation-link {
	margin-right:2rem;
	border:1px solid transparent;
}

#iFrame .vnc-navigation-link[data-link="start"] {
	border:1px solid rgba(255,255,255,0.2);
	transition: color 0.55s, border-color 0.55s, background-color 0.55s;
}

#iFrame .vnc-navigation-link[data-link="start"]:hover,
#iFrame .vnc-navigation-link[data-link="start"]:active,
#iFrame .vnc-navigation-link[data-link="start"]:focus {
	border-color:transparent;
}

#iFrame .vnc-navigation-link[data-link="back"]:before {
	/*content: '⮜';*/
	content: '<';
}

#iFrame .vnc-navigation-link[data-link="start"]:before {
	/*content: '⮝';*/
	content: '˄';
}

#iFrame .vnc-box-seminare {
	background-color:rgb(0, 130, 213);
	color: #000;
	padding-bottom:25px;
}

#iFrame .vnc-box-seminare .panel {
	margin-bottom:0.5rem;
}

#iFrame .vnc-box-seminare .panel-title {
	text-align: left;
}

#iFrame .events-list {
	list-style-type: none;
	margin:0;
	padding:0;
	padding-left:2rem;
}

#iFrame .events-list a {
	text-decoration: none !important;
}

#iFrame .events-list li {
	margin-bottom:3rem;
}

#iFrame .newSeminarBubble {
	/*background-color:#e74a16;*/
	background-color:#f39201;
	color:#fff;
	padding: 3px 7px 2px 9px;
	border-radius:2rem;
}

#iFrame table.events-list,
#iFrame table.events-list * {
	color:#333;
	background-color: transparent;
	border:none;
	font-weight: normal;
	padding:0;
	margin:0;
}

#iFrame table.events-list {
	margin-top:2rem;
}

@media(max-width: 767px) {
	#iFrame table.events-list {
		margin-top:1rem;
	}
}

#iFrame table.events-list td {
	vertical-align: top;
}

#iFrame table.events-list td.vnc-location {
	padding-left:2rem;
}

#iFrame table.events-list td.vnc-title {
	padding-left:5rem;
	padding-bottom:2rem;
}

#iFrame table.events-list .event-title {
	font-weight: bold;
}

@media(max-width: 767px) {
	#iFrame table.events-list td.vnc-date,
	#iFrame table.events-list td.vnc-location,
	#iFrame table.events-list td.vnc-title {
		display: inline-block;
		float:left;
	}

	#iFrame table.events-list td.vnc-title {
		width: 100%;
		clear:both;
		padding-top:1rem;
		padding-left:3rem;
		padding-bottom:3rem;
	}
}

#iFrame table.events-list .event-bookedout {
	display: inline-block;
	background-color:#e61610;
	color:#fff;
	padding: 3px 8px 2px 9px;
	border-radius:2rem;
}

#iFrame table.event-details,
#iFrame table.event-details * {
	font-weight: normal;
	color:#333;
}

#iFrame table.event-details {
	margin:3rem 0;
}

#iFrame table.event-details strong {
	font-weight: bold;
}

@media(max-width: 767px) {
	#iFrame table.event-details td {
		display: block;
	}

	#iFrame table.event-details tr td:nth-child(1) {
		background-color: #d6d6d6;
	}
	#iFrame table.event-details tr td:nth-child(2) {
		background-color: transparent;
	}

}

#iFrame.home .button:hover {
	border-color: #00c8f8;
	transition: color 0.55s, border-color 0.55s, background-color 0.55s;
}

#iFrame h1.with-margin {
	margin:5rem 0;
}

#iFrame a:hover .seminar-title-regular {
	text-decoration: underline;
}

#iFrame .events-navigation {
	display: inline-block;
	margin:5rem 0 5rem 5rem;
	float:right;
	width:40%;
	max-width:300px;
	min-height:30vh;
	padding:2rem;
	text-align: left;
	background-color: #00c8f8;
	color: #fff;
}

@media(max-width: 767px) {
	#iFrame .events-navigation {
		display: none;
	}
}

#iFrame .events-navigation h3 {
	text-align: left;
	font-size:1.5rem;
}

#iFrame .events-navigation ul {
	margin:0;
	padding:0;
	list-style-type: none;
}

#iFrame .events-navigation ul li {
    line-height: 1.1;
    margin-top: 2rem;
}

#iFrame .events-navigation ul a {
	text-decoration: none;
}

#iFrame .events-navigation ul a:hover {
	text-decoration: underline;
}

#iFrame form h1,
#iFrame form h2,
#iFrame form h3,
#iFrame form h4,
#iFrame form h5,
#iFrame form h6 {
	text-align: left;
}

#iFrame form h1 {
	margin-bottom:2rem;
}

#iFrame form h3 {
	margin:2rem 0;
}

#iFrame form label {
	margin-top:2rem;
}

#iFrame form .powermail_fieldwrap_emailhint,
#iFrame form .powermail_fieldwrap_pricehint {
	margin-top:2rem;
	font-size: 1.5rem;
	color:#666;
}

#iFrame form input[type="submit"] {
	margin-top:2rem;
	white-space: normal;
}

#iFrame form input[type="text"],
#iFrame form input[type="email"],
#iFrame form select,
#iFrame form textarea {
	width: 100%;
	max-width:500px;
}

#iFrame form div.radio {
    float: left;
    padding: 0;
    padding-right: 3rem;
    display: inline-block;
    margin: 0;
}

#iFrame form div.radio label {
	margin:0;
	padding:0;
	padding-left:20px;
}

#iFrame form div.radio input[type="radio"] {
	position: absolute;
	margin:0;
	left: 0;
	top:0.7rem;
}


#iFrame form fieldset {
	display: inline-block;
	float: left;
	width: calc(50% - 3rem);
	border-radius:15px;
	background-color: #d6d6d6;
	padding:3rem 2rem;
	padding-bottom:4rem;
	margin-bottom:2rem;
	margin-right:3rem;
}

@media(max-width:991px) {
	#iFrame form fieldset {
		width: 100%;
		margin-right: 0;
	}	
}

#iFrame form fieldset legend {
    display: block;
	position: relative;
    width: 100%;
    margin:0;
    padding: 0;
    margin-bottom: 2rem;
    top: 4rem;
}

#iFrame form .powermail_field_error_container {
	clear:both;
}

#iFrame form .parsley-errors-list {
	color:#e61610;
	list-style-type: none;
	padding:0;
	margin: 0;
	font-size:1.3rem;
}

#iFrame form[action*="weiterbildung"] > fieldset:nth-of-type(3),
#iFrame form[action*="weiterbildung"] > fieldset:nth-of-type(4),
#iFrame form[action*="weiterbildung"] > fieldset:nth-of-type(5) {
	background-color: #f5f5f5;
}

.content-wrapper {
	margin-top:3rem;
}