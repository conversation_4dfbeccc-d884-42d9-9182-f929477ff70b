
/* moved by compressor */
@import url('https://www.stadtwerke-velbert.de/fileadmin/css/normalize.min.css');
@import url('https://fonts.googleapis.com/css?family=Oxygen:400,700');
/* moved by compressor */
/*! normalize.css v4.1.1 | MIT License | github.com/necolas/normalize.css */progress,sub,sup{vertical-align:baseline}button,hr,input{overflow:visible}[type=checkbox],[type=radio],legend{box-sizing:border-box;padding:0}html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent;-webkit-text-decoration-skip:objects}a:active,a:hover{outline-width:0}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}dfn{font-style:italic}h1{font-size:2em;margin:.67em 0}mark{background-color:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}img{border-style:none}svg:not(:root){overflow:hidden}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}hr{box-sizing:content-box;height:0}button,input,select,textarea{font:inherit;margin:0}optgroup{font-weight:700}button,select{text-transform:none}[type=reset],[type=submit],button,html [type=button]{-webkit-appearance:button}[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner{border-style:none;padding:0}[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring,button:-moz-focusring{outline:ButtonText dotted 1px}fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}legend{color:inherit;display:table;max-width:100%;white-space:normal}textarea{overflow:auto}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-cancel-button,[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-input-placeholder{color:inherit;opacity:.54}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}

/*!
 * Bootstrap v3.3.6 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 *//*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */html{font-family:sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,strong{font-weight:700}dfn{font-style:italic}h1{margin:.67em 0;font-size:2em}mark{color:#000;background:#ff0}small{font-size:80%}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}img{border:0}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{height:0;-moz-box-sizing:content-box;box-sizing:content-box}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{margin:0;font:inherit;color:inherit}button{overflow:visible}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}input{line-height:normal}input[type=checkbox],input[type=radio]{-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]{;-moz-box-sizing:content-box;box-sizing:content-box;-webkit-appearance:textfield}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}fieldset{padding:.35em .625em .75em;margin:0 2px;border:1px solid silver}legend{padding:0;border:0}textarea{overflow:auto}optgroup{font-weight:700}table{border-spacing:0;border-collapse:collapse}td,th{padding:0}/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */@media print{*,:after,:before{color:#000!important;text-shadow:none!important;background:0 0!important;-webkit-box-shadow:none!important;box-shadow:none!important}a,a:visited{text-decoration:underline}a[href]:after{content:" (" attr(href) ")"}abbr[title]:after{content:" (" attr(title) ")"}a[href^="javascript:"]:after,a[href^="#"]:after{content:""}blockquote,pre{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}img,tr{page-break-inside:avoid}img{max-width:100%!important}h2,h3,p{orphans:3;widows:3}h2,h3{page-break-after:avoid}.navbar{display:none}.btn>.caret,.dropup>.btn>.caret{border-top-color:#000!important}.label{border:1px solid #000}.table{border-collapse:collapse!important}.table td,.table th{background-color:#fff!important}.table-bordered td,.table-bordered th{border:1px solid #ddd!important}}@font-face{font-family:'Glyphicons Halflings';src:url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/glyphicons-halflings-regular.eot');src:url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/glyphicons-halflings-regular.woff2') format('woff2'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/glyphicons-halflings-regular.woff') format('woff'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/glyphicons-halflings-regular.ttf') format('truetype'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg')}.glyphicon{position:relative;top:1px;display:inline-block;font-family:'Glyphicons Halflings';font-style:normal;font-weight:400;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.glyphicon-asterisk:before{content:"\002a"}.glyphicon-plus:before{content:"\002b"}.glyphicon-eur:before,.glyphicon-euro:before{content:"\20ac"}.glyphicon-minus:before{content:"\2212"}.glyphicon-cloud:before{content:"\2601"}.glyphicon-envelope:before{content:"\2709"}.glyphicon-pencil:before{content:"\270f"}.glyphicon-glass:before{content:"\e001"}.glyphicon-music:before{content:"\e002"}.glyphicon-search:before{content:"\e003"}.glyphicon-heart:before{content:"\e005"}.glyphicon-star:before{content:"\e006"}.glyphicon-star-empty:before{content:"\e007"}.glyphicon-user:before{content:"\e008"}.glyphicon-film:before{content:"\e009"}.glyphicon-th-large:before{content:"\e010"}.glyphicon-th:before{content:"\e011"}.glyphicon-th-list:before{content:"\e012"}.glyphicon-ok:before{content:"\e013"}.glyphicon-remove:before{content:"\e014"}.glyphicon-zoom-in:before{content:"\e015"}.glyphicon-zoom-out:before{content:"\e016"}.glyphicon-off:before{content:"\e017"}.glyphicon-signal:before{content:"\e018"}.glyphicon-cog:before{content:"\e019"}.glyphicon-trash:before{content:"\e020"}.glyphicon-home:before{content:"\e021"}.glyphicon-file:before{content:"\e022"}.glyphicon-time:before{content:"\e023"}.glyphicon-road:before{content:"\e024"}.glyphicon-download-alt:before{content:"\e025"}.glyphicon-download:before{content:"\e026"}.glyphicon-upload:before{content:"\e027"}.glyphicon-inbox:before{content:"\e028"}.glyphicon-play-circle:before{content:"\e029"}.glyphicon-repeat:before{content:"\e030"}.glyphicon-refresh:before{content:"\e031"}.glyphicon-list-alt:before{content:"\e032"}.glyphicon-lock:before{content:"\e033"}.glyphicon-flag:before{content:"\e034"}.glyphicon-headphones:before{content:"\e035"}.glyphicon-volume-off:before{content:"\e036"}.glyphicon-volume-down:before{content:"\e037"}.glyphicon-volume-up:before{content:"\e038"}.glyphicon-qrcode:before{content:"\e039"}.glyphicon-barcode:before{content:"\e040"}.glyphicon-tag:before{content:"\e041"}.glyphicon-tags:before{content:"\e042"}.glyphicon-book:before{content:"\e043"}.glyphicon-bookmark:before{content:"\e044"}.glyphicon-print:before{content:"\e045"}.glyphicon-camera:before{content:"\e046"}.glyphicon-font:before{content:"\e047"}.glyphicon-bold:before{content:"\e048"}.glyphicon-italic:before{content:"\e049"}.glyphicon-text-height:before{content:"\e050"}.glyphicon-text-width:before{content:"\e051"}.glyphicon-align-left:before{content:"\e052"}.glyphicon-align-center:before{content:"\e053"}.glyphicon-align-right:before{content:"\e054"}.glyphicon-align-justify:before{content:"\e055"}.glyphicon-list:before{content:"\e056"}.glyphicon-indent-left:before{content:"\e057"}.glyphicon-indent-right:before{content:"\e058"}.glyphicon-facetime-video:before{content:"\e059"}.glyphicon-picture:before{content:"\e060"}.glyphicon-map-marker:before{content:"\e062"}.glyphicon-adjust:before{content:"\e063"}.glyphicon-tint:before{content:"\e064"}.glyphicon-edit:before{content:"\e065"}.glyphicon-share:before{content:"\e066"}.glyphicon-check:before{content:"\e067"}.glyphicon-move:before{content:"\e068"}.glyphicon-step-backward:before{content:"\e069"}.glyphicon-fast-backward:before{content:"\e070"}.glyphicon-backward:before{content:"\e071"}.glyphicon-play:before{content:"\e072"}.glyphicon-pause:before{content:"\e073"}.glyphicon-stop:before{content:"\e074"}.glyphicon-forward:before{content:"\e075"}.glyphicon-fast-forward:before{content:"\e076"}.glyphicon-step-forward:before{content:"\e077"}.glyphicon-eject:before{content:"\e078"}.glyphicon-chevron-left:before{content:"\e079"}.glyphicon-chevron-right:before{content:"\e080"}.glyphicon-plus-sign:before{content:"\e081"}.glyphicon-minus-sign:before{content:"\e082"}.glyphicon-remove-sign:before{content:"\e083"}.glyphicon-ok-sign:before{content:"\e084"}.glyphicon-question-sign:before{content:"\e085"}.glyphicon-info-sign:before{content:"\e086"}.glyphicon-screenshot:before{content:"\e087"}.glyphicon-remove-circle:before{content:"\e088"}.glyphicon-ok-circle:before{content:"\e089"}.glyphicon-ban-circle:before{content:"\e090"}.glyphicon-arrow-left:before{content:"\e091"}.glyphicon-arrow-right:before{content:"\e092"}.glyphicon-arrow-up:before{content:"\e093"}.glyphicon-arrow-down:before{content:"\e094"}.glyphicon-share-alt:before{content:"\e095"}.glyphicon-resize-full:before{content:"\e096"}.glyphicon-resize-small:before{content:"\e097"}.glyphicon-exclamation-sign:before{content:"\e101"}.glyphicon-gift:before{content:"\e102"}.glyphicon-leaf:before{content:"\e103"}.glyphicon-fire:before{content:"\e104"}.glyphicon-eye-open:before{content:"\e105"}.glyphicon-eye-close:before{content:"\e106"}.glyphicon-warning-sign:before{content:"\e107"}.glyphicon-plane:before{content:"\e108"}.glyphicon-calendar:before{content:"\e109"}.glyphicon-random:before{content:"\e110"}.glyphicon-comment:before{content:"\e111"}.glyphicon-magnet:before{content:"\e112"}.glyphicon-chevron-up:before{content:"\e113"}.glyphicon-chevron-down:before{content:"\e114"}.glyphicon-retweet:before{content:"\e115"}.glyphicon-shopping-cart:before{content:"\e116"}.glyphicon-folder-close:before{content:"\e117"}.glyphicon-folder-open:before{content:"\e118"}.glyphicon-resize-vertical:before{content:"\e119"}.glyphicon-resize-horizontal:before{content:"\e120"}.glyphicon-hdd:before{content:"\e121"}.glyphicon-bullhorn:before{content:"\e122"}.glyphicon-bell:before{content:"\e123"}.glyphicon-certificate:before{content:"\e124"}.glyphicon-thumbs-up:before{content:"\e125"}.glyphicon-thumbs-down:before{content:"\e126"}.glyphicon-hand-right:before{content:"\e127"}.glyphicon-hand-left:before{content:"\e128"}.glyphicon-hand-up:before{content:"\e129"}.glyphicon-hand-down:before{content:"\e130"}.glyphicon-circle-arrow-right:before{content:"\e131"}.glyphicon-circle-arrow-left:before{content:"\e132"}.glyphicon-circle-arrow-up:before{content:"\e133"}.glyphicon-circle-arrow-down:before{content:"\e134"}.glyphicon-globe:before{content:"\e135"}.glyphicon-wrench:before{content:"\e136"}.glyphicon-tasks:before{content:"\e137"}.glyphicon-filter:before{content:"\e138"}.glyphicon-briefcase:before{content:"\e139"}.glyphicon-fullscreen:before{content:"\e140"}.glyphicon-dashboard:before{content:"\e141"}.glyphicon-paperclip:before{content:"\e142"}.glyphicon-heart-empty:before{content:"\e143"}.glyphicon-link:before{content:"\e144"}.glyphicon-phone:before{content:"\e145"}.glyphicon-pushpin:before{content:"\e146"}.glyphicon-usd:before{content:"\e148"}.glyphicon-gbp:before{content:"\e149"}.glyphicon-sort:before{content:"\e150"}.glyphicon-sort-by-alphabet:before{content:"\e151"}.glyphicon-sort-by-alphabet-alt:before{content:"\e152"}.glyphicon-sort-by-order:before{content:"\e153"}.glyphicon-sort-by-order-alt:before{content:"\e154"}.glyphicon-sort-by-attributes:before{content:"\e155"}.glyphicon-sort-by-attributes-alt:before{content:"\e156"}.glyphicon-unchecked:before{content:"\e157"}.glyphicon-expand:before{content:"\e158"}.glyphicon-collapse-down:before{content:"\e159"}.glyphicon-collapse-up:before{content:"\e160"}.glyphicon-log-in:before{content:"\e161"}.glyphicon-flash:before{content:"\e162"}.glyphicon-log-out:before{content:"\e163"}.glyphicon-new-window:before{content:"\e164"}.glyphicon-record:before{content:"\e165"}.glyphicon-save:before{content:"\e166"}.glyphicon-open:before{content:"\e167"}.glyphicon-saved:before{content:"\e168"}.glyphicon-import:before{content:"\e169"}.glyphicon-export:before{content:"\e170"}.glyphicon-send:before{content:"\e171"}.glyphicon-floppy-disk:before{content:"\e172"}.glyphicon-floppy-saved:before{content:"\e173"}.glyphicon-floppy-remove:before{content:"\e174"}.glyphicon-floppy-save:before{content:"\e175"}.glyphicon-floppy-open:before{content:"\e176"}.glyphicon-credit-card:before{content:"\e177"}.glyphicon-transfer:before{content:"\e178"}.glyphicon-cutlery:before{content:"\e179"}.glyphicon-header:before{content:"\e180"}.glyphicon-compressed:before{content:"\e181"}.glyphicon-earphone:before{content:"\e182"}.glyphicon-phone-alt:before{content:"\e183"}.glyphicon-tower:before{content:"\e184"}.glyphicon-stats:before{content:"\e185"}.glyphicon-sd-video:before{content:"\e186"}.glyphicon-hd-video:before{content:"\e187"}.glyphicon-subtitles:before{content:"\e188"}.glyphicon-sound-stereo:before{content:"\e189"}.glyphicon-sound-dolby:before{content:"\e190"}.glyphicon-sound-5-1:before{content:"\e191"}.glyphicon-sound-6-1:before{content:"\e192"}.glyphicon-sound-7-1:before{content:"\e193"}.glyphicon-copyright-mark:before{content:"\e194"}.glyphicon-registration-mark:before{content:"\e195"}.glyphicon-cloud-download:before{content:"\e197"}.glyphicon-cloud-upload:before{content:"\e198"}.glyphicon-tree-conifer:before{content:"\e199"}.glyphicon-tree-deciduous:before{content:"\e200"}.glyphicon-cd:before{content:"\e201"}.glyphicon-save-file:before{content:"\e202"}.glyphicon-open-file:before{content:"\e203"}.glyphicon-level-up:before{content:"\e204"}.glyphicon-copy:before{content:"\e205"}.glyphicon-paste:before{content:"\e206"}.glyphicon-alert:before{content:"\e209"}.glyphicon-equalizer:before{content:"\e210"}.glyphicon-king:before{content:"\e211"}.glyphicon-queen:before{content:"\e212"}.glyphicon-pawn:before{content:"\e213"}.glyphicon-bishop:before{content:"\e214"}.glyphicon-knight:before{content:"\e215"}.glyphicon-baby-formula:before{content:"\e216"}.glyphicon-tent:before{content:"\26fa"}.glyphicon-blackboard:before{content:"\e218"}.glyphicon-bed:before{content:"\e219"}.glyphicon-apple:before{content:"\f8ff"}.glyphicon-erase:before{content:"\e221"}.glyphicon-hourglass:before{content:"\231b"}.glyphicon-lamp:before{content:"\e223"}.glyphicon-duplicate:before{content:"\e224"}.glyphicon-piggy-bank:before{content:"\e225"}.glyphicon-scissors:before{content:"\e226"}.glyphicon-bitcoin:before{content:"\e227"}.glyphicon-btc:before{content:"\e227"}.glyphicon-xbt:before{content:"\e227"}.glyphicon-yen:before{content:"\00a5"}.glyphicon-jpy:before{content:"\00a5"}.glyphicon-ruble:before{content:"\20bd"}.glyphicon-rub:before{content:"\20bd"}.glyphicon-scale:before{content:"\e230"}.glyphicon-ice-lolly:before{content:"\e231"}.glyphicon-ice-lolly-tasted:before{content:"\e232"}.glyphicon-education:before{content:"\e233"}.glyphicon-option-horizontal:before{content:"\e234"}.glyphicon-option-vertical:before{content:"\e235"}.glyphicon-menu-hamburger:before{content:"\e236"}.glyphicon-modal-window:before{content:"\e237"}.glyphicon-oil:before{content:"\e238"}.glyphicon-grain:before{content:"\e239"}.glyphicon-sunglasses:before{content:"\e240"}.glyphicon-text-size:before{content:"\e241"}.glyphicon-text-color:before{content:"\e242"}.glyphicon-text-background:before{content:"\e243"}.glyphicon-object-align-top:before{content:"\e244"}.glyphicon-object-align-bottom:before{content:"\e245"}.glyphicon-object-align-horizontal:before{content:"\e246"}.glyphicon-object-align-left:before{content:"\e247"}.glyphicon-object-align-vertical:before{content:"\e248"}.glyphicon-object-align-right:before{content:"\e249"}.glyphicon-triangle-right:before{content:"\e250"}.glyphicon-triangle-left:before{content:"\e251"}.glyphicon-triangle-bottom:before{content:"\e252"}.glyphicon-triangle-top:before{content:"\e253"}.glyphicon-console:before{content:"\e254"}.glyphicon-superscript:before{content:"\e255"}.glyphicon-subscript:before{content:"\e256"}.glyphicon-menu-left:before{content:"\e257"}.glyphicon-menu-right:before{content:"\e258"}.glyphicon-menu-down:before{content:"\e259"}.glyphicon-menu-up:before{content:"\e260"}*{-moz-box-sizing:border-box;box-sizing:border-box}:after,:before{-moz-box-sizing:border-box;box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:rgba(0,0,0,0)}body{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;line-height:1.42857143;color:#333;background-color:#fff}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}a{color:#337ab7;text-decoration:none}a:focus,a:hover{text-decoration:underline}a:focus{outline:thin dotted;outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}figure{margin:0}img{vertical-align:middle}.carousel-inner>.item>a>img,.carousel-inner>.item>img,.img-responsive,.thumbnail a>img,.thumbnail>img{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.img-thumbnail{display:inline-block;max-width:100%;height:auto;padding:4px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;-webkit-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;transition:all .2s ease-in-out}.img-circle{border-radius:50%}hr{margin-top:20px;margin-bottom:20px;border:0;border-top:1px solid #eee}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}[role=button]{cursor:pointer}.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{font-family:inherit;font-weight:500;line-height:1.1;color:inherit}.h1 .small,.h1 small,.h2 .small,.h2 small,.h3 .small,.h3 small,.h4 .small,.h4 small,.h5 .small,.h5 small,.h6 .small,.h6 small,h1 .small,h1 small,h2 .small,h2 small,h3 .small,h3 small,h4 .small,h4 small,h5 .small,h5 small,h6 .small,h6 small{font-weight:400;line-height:1;color:#777}.h1,.h2,.h3,h1,h2,h3{margin-top:20px;margin-bottom:10px}.h1 .small,.h1 small,.h2 .small,.h2 small,.h3 .small,.h3 small,h1 .small,h1 small,h2 .small,h2 small,h3 .small,h3 small{font-size:65%}.h4,.h5,.h6,h4,h5,h6{margin-top:10px;margin-bottom:10px}.h4 .small,.h4 small,.h5 .small,.h5 small,.h6 .small,.h6 small,h4 .small,h4 small,h5 .small,h5 small,h6 .small,h6 small{font-size:75%}.h1,h1{font-size:36px}.h2,h2{font-size:30px}.h3,h3{font-size:24px}.h4,h4{font-size:18px}.h5,h5{font-size:14px}.h6,h6{font-size:12px}p{margin:0 0 10px}.lead{margin-bottom:20px;font-size:16px;font-weight:300;line-height:1.4}@media (min-width:768px){.lead{font-size:21px}}.small,small{font-size:85%}.mark,mark{padding:.2em;background-color:#fcf8e3}.text-left{text-align:left}.text-right{text-align:right}.text-center{text-align:center}.text-justify{text-align:justify}.text-nowrap{white-space:nowrap}.text-lowercase{text-transform:lowercase}.text-uppercase{text-transform:uppercase}.text-capitalize{text-transform:capitalize}.text-muted{color:#777}.text-primary{color:#337ab7}a.text-primary:focus,a.text-primary:hover{color:#286090}.text-success{color:#3c763d}a.text-success:focus,a.text-success:hover{color:#2b542c}.text-info{color:#31708f}a.text-info:focus,a.text-info:hover{color:#245269}.text-warning{color:#8a6d3b}a.text-warning:focus,a.text-warning:hover{color:#66512c}.text-danger{color:#a94442}a.text-danger:focus,a.text-danger:hover{color:#843534}.bg-primary{color:#fff;background-color:#337ab7}a.bg-primary:focus,a.bg-primary:hover{background-color:#286090}.bg-success{background-color:#dff0d8}a.bg-success:focus,a.bg-success:hover{background-color:#c1e2b3}.bg-info{background-color:#d9edf7}a.bg-info:focus,a.bg-info:hover{background-color:#afd9ee}.bg-warning{background-color:#fcf8e3}a.bg-warning:focus,a.bg-warning:hover{background-color:#f7ecb5}.bg-danger{background-color:#f2dede}a.bg-danger:focus,a.bg-danger:hover{background-color:#e4b9b9}.page-header{padding-bottom:9px;margin:40px 0 20px;border-bottom:1px solid #eee}ol,ul{margin-top:0;margin-bottom:10px}ol ol,ol ul,ul ol,ul ul{margin-bottom:0}.list-unstyled{padding-left:0;list-style:none}.list-inline{padding-left:0;margin-left:-5px;list-style:none}.list-inline>li{display:inline-block;padding-right:5px;padding-left:5px}dl{margin-top:0;margin-bottom:20px}dd,dt{line-height:1.42857143}dt{font-weight:700}dd{margin-left:0}@media (min-width:768px){.dl-horizontal dt{float:left;width:160px;overflow:hidden;clear:left;text-align:right;text-overflow:ellipsis;white-space:nowrap}.dl-horizontal dd{margin-left:180px}}abbr[data-original-title],abbr[title]{cursor:help;border-bottom:1px dotted #777}.initialism{font-size:90%;text-transform:uppercase}blockquote{padding:10px 20px;margin:0 0 20px;font-size:17.5px;border-left:5px solid #eee}blockquote ol:last-child,blockquote p:last-child,blockquote ul:last-child{margin-bottom:0}blockquote .small,blockquote footer,blockquote small{display:block;font-size:80%;line-height:1.42857143;color:#777}blockquote .small:before,blockquote footer:before,blockquote small:before{content:'\2014 \00A0'}.blockquote-reverse,blockquote.pull-right{padding-right:15px;padding-left:0;text-align:right;border-right:5px solid #eee;border-left:0}.blockquote-reverse .small:before,.blockquote-reverse footer:before,.blockquote-reverse small:before,blockquote.pull-right .small:before,blockquote.pull-right footer:before,blockquote.pull-right small:before{content:''}.blockquote-reverse .small:after,.blockquote-reverse footer:after,.blockquote-reverse small:after,blockquote.pull-right .small:after,blockquote.pull-right footer:after,blockquote.pull-right small:after{content:'\00A0 \2014'}address{margin-bottom:20px;font-style:normal;line-height:1.42857143}code,kbd,pre,samp{font-family:Menlo,Monaco,Consolas,"Courier New",monospace}code{padding:2px 4px;font-size:90%;color:#c7254e;background-color:#f9f2f4;border-radius:4px}kbd{padding:2px 4px;font-size:90%;color:#fff;background-color:#333;border-radius:3px;-webkit-box-shadow:inset 0 -1px 0 rgba(0,0,0,.25);box-shadow:inset 0 -1px 0 rgba(0,0,0,.25)}kbd kbd{padding:0;font-size:100%;font-weight:700;-webkit-box-shadow:none;box-shadow:none}pre{display:block;padding:9.5px;margin:0 0 10px;font-size:13px;line-height:1.42857143;color:#333;word-break:break-all;word-wrap:break-word;background-color:#f5f5f5;border:1px solid #ccc;border-radius:4px}pre code{padding:0;font-size:inherit;color:inherit;white-space:pre-wrap;background-color:transparent;border-radius:0}.pre-scrollable{max-height:340px;overflow-y:scroll}.container{padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto}@media (min-width:768px){.container{width:750px}}@media (min-width:992px){.container{width:970px}}@media (min-width:1200px){.container{width:1170px}}.container-fluid{padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto}.row{margin-right:-15px;margin-left:-15px}.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-xs-1,.col-xs-10,.col-xs-11,.col-xs-12,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9{position:relative;min-height:1px;padding-right:15px;padding-left:15px}.col-xs-1,.col-xs-10,.col-xs-11,.col-xs-12,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9{float:left}.col-xs-12{width:100%}.col-xs-11{width:91.66666667%}.col-xs-10{width:83.33333333%}.col-xs-9{width:75%}.col-xs-8{width:66.66666667%}.col-xs-7{width:58.33333333%}.col-xs-6{width:50%}.col-xs-5{width:41.66666667%}.col-xs-4{width:33.33333333%}.col-xs-3{width:25%}.col-xs-2{width:16.66666667%}.col-xs-1{width:8.33333333%}.col-xs-pull-12{right:100%}.col-xs-pull-11{right:91.66666667%}.col-xs-pull-10{right:83.33333333%}.col-xs-pull-9{right:75%}.col-xs-pull-8{right:66.66666667%}.col-xs-pull-7{right:58.33333333%}.col-xs-pull-6{right:50%}.col-xs-pull-5{right:41.66666667%}.col-xs-pull-4{right:33.33333333%}.col-xs-pull-3{right:25%}.col-xs-pull-2{right:16.66666667%}.col-xs-pull-1{right:8.33333333%}.col-xs-pull-0{right:auto}.col-xs-push-12{left:100%}.col-xs-push-11{left:91.66666667%}.col-xs-push-10{left:83.33333333%}.col-xs-push-9{left:75%}.col-xs-push-8{left:66.66666667%}.col-xs-push-7{left:58.33333333%}.col-xs-push-6{left:50%}.col-xs-push-5{left:41.66666667%}.col-xs-push-4{left:33.33333333%}.col-xs-push-3{left:25%}.col-xs-push-2{left:16.66666667%}.col-xs-push-1{left:8.33333333%}.col-xs-push-0{left:auto}.col-xs-offset-12{margin-left:100%}.col-xs-offset-11{margin-left:91.66666667%}.col-xs-offset-10{margin-left:83.33333333%}.col-xs-offset-9{margin-left:75%}.col-xs-offset-8{margin-left:66.66666667%}.col-xs-offset-7{margin-left:58.33333333%}.col-xs-offset-6{margin-left:50%}.col-xs-offset-5{margin-left:41.66666667%}.col-xs-offset-4{margin-left:33.33333333%}.col-xs-offset-3{margin-left:25%}.col-xs-offset-2{margin-left:16.66666667%}.col-xs-offset-1{margin-left:8.33333333%}.col-xs-offset-0{margin-left:0}@media (min-width:768px){.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9{float:left}.col-sm-12{width:100%}.col-sm-11{width:91.66666667%}.col-sm-10{width:83.33333333%}.col-sm-9{width:75%}.col-sm-8{width:66.66666667%}.col-sm-7{width:58.33333333%}.col-sm-6{width:50%}.col-sm-5{width:41.66666667%}.col-sm-4{width:33.33333333%}.col-sm-3{width:25%}.col-sm-2{width:16.66666667%}.col-sm-1{width:8.33333333%}.col-sm-pull-12{right:100%}.col-sm-pull-11{right:91.66666667%}.col-sm-pull-10{right:83.33333333%}.col-sm-pull-9{right:75%}.col-sm-pull-8{right:66.66666667%}.col-sm-pull-7{right:58.33333333%}.col-sm-pull-6{right:50%}.col-sm-pull-5{right:41.66666667%}.col-sm-pull-4{right:33.33333333%}.col-sm-pull-3{right:25%}.col-sm-pull-2{right:16.66666667%}.col-sm-pull-1{right:8.33333333%}.col-sm-pull-0{right:auto}.col-sm-push-12{left:100%}.col-sm-push-11{left:91.66666667%}.col-sm-push-10{left:83.33333333%}.col-sm-push-9{left:75%}.col-sm-push-8{left:66.66666667%}.col-sm-push-7{left:58.33333333%}.col-sm-push-6{left:50%}.col-sm-push-5{left:41.66666667%}.col-sm-push-4{left:33.33333333%}.col-sm-push-3{left:25%}.col-sm-push-2{left:16.66666667%}.col-sm-push-1{left:8.33333333%}.col-sm-push-0{left:auto}.col-sm-offset-12{margin-left:100%}.col-sm-offset-11{margin-left:91.66666667%}.col-sm-offset-10{margin-left:83.33333333%}.col-sm-offset-9{margin-left:75%}.col-sm-offset-8{margin-left:66.66666667%}.col-sm-offset-7{margin-left:58.33333333%}.col-sm-offset-6{margin-left:50%}.col-sm-offset-5{margin-left:41.66666667%}.col-sm-offset-4{margin-left:33.33333333%}.col-sm-offset-3{margin-left:25%}.col-sm-offset-2{margin-left:16.66666667%}.col-sm-offset-1{margin-left:8.33333333%}.col-sm-offset-0{margin-left:0}}@media (min-width:992px){.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9{float:left}.col-md-12{width:100%}.col-md-11{width:91.66666667%}.col-md-10{width:83.33333333%}.col-md-9{width:75%}.col-md-8{width:66.66666667%}.col-md-7{width:58.33333333%}.col-md-6{width:50%}.col-md-5{width:41.66666667%}.col-md-4{width:33.33333333%}.col-md-3{width:25%}.col-md-2{width:16.66666667%}.col-md-1{width:8.33333333%}.col-md-pull-12{right:100%}.col-md-pull-11{right:91.66666667%}.col-md-pull-10{right:83.33333333%}.col-md-pull-9{right:75%}.col-md-pull-8{right:66.66666667%}.col-md-pull-7{right:58.33333333%}.col-md-pull-6{right:50%}.col-md-pull-5{right:41.66666667%}.col-md-pull-4{right:33.33333333%}.col-md-pull-3{right:25%}.col-md-pull-2{right:16.66666667%}.col-md-pull-1{right:8.33333333%}.col-md-pull-0{right:auto}.col-md-push-12{left:100%}.col-md-push-11{left:91.66666667%}.col-md-push-10{left:83.33333333%}.col-md-push-9{left:75%}.col-md-push-8{left:66.66666667%}.col-md-push-7{left:58.33333333%}.col-md-push-6{left:50%}.col-md-push-5{left:41.66666667%}.col-md-push-4{left:33.33333333%}.col-md-push-3{left:25%}.col-md-push-2{left:16.66666667%}.col-md-push-1{left:8.33333333%}.col-md-push-0{left:auto}.col-md-offset-12{margin-left:100%}.col-md-offset-11{margin-left:91.66666667%}.col-md-offset-10{margin-left:83.33333333%}.col-md-offset-9{margin-left:75%}.col-md-offset-8{margin-left:66.66666667%}.col-md-offset-7{margin-left:58.33333333%}.col-md-offset-6{margin-left:50%}.col-md-offset-5{margin-left:41.66666667%}.col-md-offset-4{margin-left:33.33333333%}.col-md-offset-3{margin-left:25%}.col-md-offset-2{margin-left:16.66666667%}.col-md-offset-1{margin-left:8.33333333%}.col-md-offset-0{margin-left:0}}@media (min-width:1200px){.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9{float:left}.col-lg-12{width:100%}.col-lg-11{width:91.66666667%}.col-lg-10{width:83.33333333%}.col-lg-9{width:75%}.col-lg-8{width:66.66666667%}.col-lg-7{width:58.33333333%}.col-lg-6{width:50%}.col-lg-5{width:41.66666667%}.col-lg-4{width:33.33333333%}.col-lg-3{width:25%}.col-lg-2{width:16.66666667%}.col-lg-1{width:8.33333333%}.col-lg-pull-12{right:100%}.col-lg-pull-11{right:91.66666667%}.col-lg-pull-10{right:83.33333333%}.col-lg-pull-9{right:75%}.col-lg-pull-8{right:66.66666667%}.col-lg-pull-7{right:58.33333333%}.col-lg-pull-6{right:50%}.col-lg-pull-5{right:41.66666667%}.col-lg-pull-4{right:33.33333333%}.col-lg-pull-3{right:25%}.col-lg-pull-2{right:16.66666667%}.col-lg-pull-1{right:8.33333333%}.col-lg-pull-0{right:auto}.col-lg-push-12{left:100%}.col-lg-push-11{left:91.66666667%}.col-lg-push-10{left:83.33333333%}.col-lg-push-9{left:75%}.col-lg-push-8{left:66.66666667%}.col-lg-push-7{left:58.33333333%}.col-lg-push-6{left:50%}.col-lg-push-5{left:41.66666667%}.col-lg-push-4{left:33.33333333%}.col-lg-push-3{left:25%}.col-lg-push-2{left:16.66666667%}.col-lg-push-1{left:8.33333333%}.col-lg-push-0{left:auto}.col-lg-offset-12{margin-left:100%}.col-lg-offset-11{margin-left:91.66666667%}.col-lg-offset-10{margin-left:83.33333333%}.col-lg-offset-9{margin-left:75%}.col-lg-offset-8{margin-left:66.66666667%}.col-lg-offset-7{margin-left:58.33333333%}.col-lg-offset-6{margin-left:50%}.col-lg-offset-5{margin-left:41.66666667%}.col-lg-offset-4{margin-left:33.33333333%}.col-lg-offset-3{margin-left:25%}.col-lg-offset-2{margin-left:16.66666667%}.col-lg-offset-1{margin-left:8.33333333%}.col-lg-offset-0{margin-left:0}}table{background-color:transparent}caption{padding-top:8px;padding-bottom:8px;color:#777;text-align:left}th{text-align:left}.table{width:100%;max-width:100%;margin-bottom:20px}.table>tbody>tr>td,.table>tbody>tr>th,.table>tfoot>tr>td,.table>tfoot>tr>th,.table>thead>tr>td,.table>thead>tr>th{padding:8px;line-height:1.42857143;vertical-align:top;border-top:1px solid #ddd}.table>thead>tr>th{vertical-align:bottom;border-bottom:2px solid #ddd}.table>caption+thead>tr:first-child>td,.table>caption+thead>tr:first-child>th,.table>colgroup+thead>tr:first-child>td,.table>colgroup+thead>tr:first-child>th,.table>thead:first-child>tr:first-child>td,.table>thead:first-child>tr:first-child>th{border-top:0}.table>tbody+tbody{border-top:2px solid #ddd}.table .table{background-color:#fff}.table-condensed>tbody>tr>td,.table-condensed>tbody>tr>th,.table-condensed>tfoot>tr>td,.table-condensed>tfoot>tr>th,.table-condensed>thead>tr>td,.table-condensed>thead>tr>th{padding:5px}.table-bordered{border:1px solid #ddd}.table-bordered>tbody>tr>td,.table-bordered>tbody>tr>th,.table-bordered>tfoot>tr>td,.table-bordered>tfoot>tr>th,.table-bordered>thead>tr>td,.table-bordered>thead>tr>th{border:1px solid #ddd}.table-bordered>thead>tr>td,.table-bordered>thead>tr>th{border-bottom-width:2px}.table-striped>tbody>tr:nth-of-type(odd){background-color:#f5f5f5}.table-hover>tbody>tr:hover{background-color:#f5f5f5}table col[class*=col-]{position:static;display:table-column;float:none}table td[class*=col-],table th[class*=col-]{position:static;display:table-cell;float:none}.table>tbody>tr.active>td,.table>tbody>tr.active>th,.table>tbody>tr>td.active,.table>tbody>tr>th.active,.table>tfoot>tr.active>td,.table>tfoot>tr.active>th,.table>tfoot>tr>td.active,.table>tfoot>tr>th.active,.table>thead>tr.active>td,.table>thead>tr.active>th,.table>thead>tr>td.active,.table>thead>tr>th.active{background-color:#f5f5f5}.table-hover>tbody>tr.active:hover>td,.table-hover>tbody>tr.active:hover>th,.table-hover>tbody>tr:hover>.active,.table-hover>tbody>tr>td.active:hover,.table-hover>tbody>tr>th.active:hover{background-color:#e8e8e8}.table>tbody>tr.success>td,.table>tbody>tr.success>th,.table>tbody>tr>td.success,.table>tbody>tr>th.success,.table>tfoot>tr.success>td,.table>tfoot>tr.success>th,.table>tfoot>tr>td.success,.table>tfoot>tr>th.success,.table>thead>tr.success>td,.table>thead>tr.success>th,.table>thead>tr>td.success,.table>thead>tr>th.success{background-color:#dff0d8}.table-hover>tbody>tr.success:hover>td,.table-hover>tbody>tr.success:hover>th,.table-hover>tbody>tr:hover>.success,.table-hover>tbody>tr>td.success:hover,.table-hover>tbody>tr>th.success:hover{background-color:#d0e9c6}.table>tbody>tr.info>td,.table>tbody>tr.info>th,.table>tbody>tr>td.info,.table>tbody>tr>th.info,.table>tfoot>tr.info>td,.table>tfoot>tr.info>th,.table>tfoot>tr>td.info,.table>tfoot>tr>th.info,.table>thead>tr.info>td,.table>thead>tr.info>th,.table>thead>tr>td.info,.table>thead>tr>th.info{background-color:#d9edf7}.table-hover>tbody>tr.info:hover>td,.table-hover>tbody>tr.info:hover>th,.table-hover>tbody>tr:hover>.info,.table-hover>tbody>tr>td.info:hover,.table-hover>tbody>tr>th.info:hover{background-color:#c4e3f3}.table>tbody>tr.warning>td,.table>tbody>tr.warning>th,.table>tbody>tr>td.warning,.table>tbody>tr>th.warning,.table>tfoot>tr.warning>td,.table>tfoot>tr.warning>th,.table>tfoot>tr>td.warning,.table>tfoot>tr>th.warning,.table>thead>tr.warning>td,.table>thead>tr.warning>th,.table>thead>tr>td.warning,.table>thead>tr>th.warning{background-color:#fcf8e3}.table-hover>tbody>tr.warning:hover>td,.table-hover>tbody>tr.warning:hover>th,.table-hover>tbody>tr:hover>.warning,.table-hover>tbody>tr>td.warning:hover,.table-hover>tbody>tr>th.warning:hover{background-color:#faf2cc}.table>tbody>tr.danger>td,.table>tbody>tr.danger>th,.table>tbody>tr>td.danger,.table>tbody>tr>th.danger,.table>tfoot>tr.danger>td,.table>tfoot>tr.danger>th,.table>tfoot>tr>td.danger,.table>tfoot>tr>th.danger,.table>thead>tr.danger>td,.table>thead>tr.danger>th,.table>thead>tr>td.danger,.table>thead>tr>th.danger{background-color:#f2dede}.table-hover>tbody>tr.danger:hover>td,.table-hover>tbody>tr.danger:hover>th,.table-hover>tbody>tr:hover>.danger,.table-hover>tbody>tr>td.danger:hover,.table-hover>tbody>tr>th.danger:hover{background-color:#ebcccc}.table-responsive{min-height:.01%;overflow-x:auto}@media screen and (max-width:767px){.table-responsive{width:100%;margin-bottom:15px;overflow-y:hidden;-ms-overflow-style:-ms-autohiding-scrollbar;border:1px solid #ddd}.table-responsive>.table{margin-bottom:0}.table-responsive>.table>tbody>tr>td,.table-responsive>.table>tbody>tr>th,.table-responsive>.table>tfoot>tr>td,.table-responsive>.table>tfoot>tr>th,.table-responsive>.table>thead>tr>td,.table-responsive>.table>thead>tr>th{white-space:nowrap}.table-responsive>.table-bordered{border:0}.table-responsive>.table-bordered>tbody>tr>td:first-child,.table-responsive>.table-bordered>tbody>tr>th:first-child,.table-responsive>.table-bordered>tfoot>tr>td:first-child,.table-responsive>.table-bordered>tfoot>tr>th:first-child,.table-responsive>.table-bordered>thead>tr>td:first-child,.table-responsive>.table-bordered>thead>tr>th:first-child{border-left:0}.table-responsive>.table-bordered>tbody>tr>td:last-child,.table-responsive>.table-bordered>tbody>tr>th:last-child,.table-responsive>.table-bordered>tfoot>tr>td:last-child,.table-responsive>.table-bordered>tfoot>tr>th:last-child,.table-responsive>.table-bordered>thead>tr>td:last-child,.table-responsive>.table-bordered>thead>tr>th:last-child{border-right:0}.table-responsive>.table-bordered>tbody>tr:last-child>td,.table-responsive>.table-bordered>tbody>tr:last-child>th,.table-responsive>.table-bordered>tfoot>tr:last-child>td,.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}}fieldset{min-width:0;padding:0;margin:0;border:0}legend{display:block;width:100%;padding:0;margin-bottom:20px;font-size:21px;line-height:inherit;color:#333;border:0;border-bottom:1px solid #e5e5e5}label{display:inline-block;max-width:100%;margin-bottom:5px;font-weight:700}input[type=search]{-moz-box-sizing:border-box;box-sizing:border-box}input[type=checkbox],input[type=radio]{margin:4px 0 0;margin-top:1px\9;line-height:normal}input[type=file]{display:block}input[type=range]{display:block;width:100%}select[multiple],select[size]{height:auto}input[type=file]:focus,input[type=checkbox]:focus,input[type=radio]:focus{outline:thin dotted;outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}output{display:block;padding-top:7px;font-size:14px;line-height:1.42857143;color:#555}.form-control{display:block;width:100%;height:34px;padding:6px 12px;font-size:14px;line-height:1.42857143;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);box-shadow:inset 0 1px 1px rgba(0,0,0,.075);-webkit-transition:border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;-o-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s}.form-control:focus{border-color:#66afe9;outline:0;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)}.form-control::-moz-placeholder{color:#999;opacity:1}.form-control:-ms-input-placeholder{color:#999}.form-control::-webkit-input-placeholder{color:#999}.form-control::-ms-expand{background-color:transparent;border:0}.form-control[disabled],.form-control[readonly],fieldset[disabled] .form-control{background-color:#eee;opacity:1}.form-control[disabled],fieldset[disabled] .form-control{cursor:not-allowed}textarea.form-control{height:auto}input[type=search]{-webkit-appearance:none}@media screen and (-webkit-min-device-pixel-ratio:0){input[type=date].form-control,input[type=time].form-control,input[type=datetime-local].form-control,input[type=month].form-control{line-height:34px}.input-group-sm input[type=date],.input-group-sm input[type=time],.input-group-sm input[type=datetime-local],.input-group-sm input[type=month],input[type=date].input-sm,input[type=time].input-sm,input[type=datetime-local].input-sm,input[type=month].input-sm{line-height:30px}.input-group-lg input[type=date],.input-group-lg input[type=time],.input-group-lg input[type=datetime-local],.input-group-lg input[type=month],input[type=date].input-lg,input[type=time].input-lg,input[type=datetime-local].input-lg,input[type=month].input-lg{line-height:46px}}.form-group{margin-bottom:15px}.checkbox,.radio{position:relative;display:block;margin-top:10px;margin-bottom:10px}.checkbox label,.radio label{min-height:20px;padding-left:20px;margin-bottom:0;font-weight:400;cursor:pointer}.checkbox input[type=checkbox],.checkbox-inline input[type=checkbox],.radio input[type=radio],.radio-inline input[type=radio]{position:absolute;margin-top:4px\9;margin-left:-20px}.checkbox+.checkbox,.radio+.radio{margin-top:-5px}.checkbox-inline,.radio-inline{position:relative;display:inline-block;padding-left:20px;margin-bottom:0;font-weight:400;vertical-align:middle;cursor:pointer}.checkbox-inline+.checkbox-inline,.radio-inline+.radio-inline{margin-top:0;margin-left:10px}fieldset[disabled] input[type=checkbox],fieldset[disabled] input[type=radio],input[type=checkbox].disabled,input[type=checkbox][disabled],input[type=radio].disabled,input[type=radio][disabled]{cursor:not-allowed}.checkbox-inline.disabled,.radio-inline.disabled,fieldset[disabled] .checkbox-inline,fieldset[disabled] .radio-inline{cursor:not-allowed}.checkbox.disabled label,.radio.disabled label,fieldset[disabled] .checkbox label,fieldset[disabled] .radio label{cursor:not-allowed}.form-control-static{min-height:34px;padding-top:7px;padding-bottom:7px;margin-bottom:0}.form-control-static.input-lg,.form-control-static.input-sm{padding-right:0;padding-left:0}.input-sm{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-sm{height:30px;line-height:30px}select[multiple].input-sm,textarea.input-sm{height:auto}.form-group-sm .form-control{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.form-group-sm select.form-control{height:30px;line-height:30px}.form-group-sm select[multiple].form-control,.form-group-sm textarea.form-control{height:auto}.form-group-sm .form-control-static{height:30px;min-height:32px;padding:6px 10px;font-size:12px;line-height:1.5}.input-lg{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-lg{height:46px;line-height:46px}select[multiple].input-lg,textarea.input-lg{height:auto}.form-group-lg .form-control{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.form-group-lg select.form-control{height:46px;line-height:46px}.form-group-lg select[multiple].form-control,.form-group-lg textarea.form-control{height:auto}.form-group-lg .form-control-static{height:46px;min-height:38px;padding:11px 16px;font-size:18px;line-height:1.3333333}.has-feedback{position:relative}.has-feedback .form-control{padding-right:42.5px}.form-control-feedback{position:absolute;top:0;right:0;z-index:2;display:block;width:34px;height:34px;line-height:34px;text-align:center;pointer-events:none}.form-group-lg .form-control+.form-control-feedback,.input-group-lg+.form-control-feedback,.input-lg+.form-control-feedback{width:46px;height:46px;line-height:46px}.form-group-sm .form-control+.form-control-feedback,.input-group-sm+.form-control-feedback,.input-sm+.form-control-feedback{width:30px;height:30px;line-height:30px}.has-success .checkbox,.has-success .checkbox-inline,.has-success .control-label,.has-success .help-block,.has-success .radio,.has-success .radio-inline,.has-success.checkbox label,.has-success.checkbox-inline label,.has-success.radio label,.has-success.radio-inline label{color:#3c763d}.has-success .form-control{border-color:#3c763d;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.has-success .form-control:focus{border-color:#2b542c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #67b168;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #67b168}.has-success .input-group-addon{color:#3c763d;background-color:#dff0d8;border-color:#3c763d}.has-success .form-control-feedback{color:#3c763d}.has-warning .checkbox,.has-warning .checkbox-inline,.has-warning .control-label,.has-warning .help-block,.has-warning .radio,.has-warning .radio-inline,.has-warning.checkbox label,.has-warning.checkbox-inline label,.has-warning.radio label,.has-warning.radio-inline label{color:#8a6d3b}.has-warning .form-control{border-color:#8a6d3b;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.has-warning .form-control:focus{border-color:#66512c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #c0a16b;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #c0a16b}.has-warning .input-group-addon{color:#8a6d3b;background-color:#fcf8e3;border-color:#8a6d3b}.has-warning .form-control-feedback{color:#8a6d3b}.has-error .checkbox,.has-error .checkbox-inline,.has-error .control-label,.has-error .help-block,.has-error .radio,.has-error .radio-inline,.has-error.checkbox label,.has-error.checkbox-inline label,.has-error.radio label,.has-error.radio-inline label{color:#a94442}.has-error .form-control{border-color:#a94442;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.has-error .form-control:focus{border-color:#843534;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483}.has-error .input-group-addon{color:#a94442;background-color:#f2dede;border-color:#a94442}.has-error .form-control-feedback{color:#a94442}.has-feedback label~.form-control-feedback{top:25px}.has-feedback label.sr-only~.form-control-feedback{top:0}.help-block{display:block;margin-top:5px;margin-bottom:10px;color:#737373}@media (min-width:768px){.form-inline .form-group{display:inline-block;margin-bottom:0;vertical-align:middle}.form-inline .form-control{display:inline-block;width:auto;vertical-align:middle}.form-inline .form-control-static{display:inline-block}.form-inline .input-group{display:inline-table;vertical-align:middle}.form-inline .input-group .form-control,.form-inline .input-group .input-group-addon,.form-inline .input-group .input-group-btn{width:auto}.form-inline .input-group>.form-control{width:100%}.form-inline .control-label{margin-bottom:0;vertical-align:middle}.form-inline .checkbox,.form-inline .radio{display:inline-block;margin-top:0;margin-bottom:0;vertical-align:middle}.form-inline .checkbox label,.form-inline .radio label{padding-left:0}.form-inline .checkbox input[type=checkbox],.form-inline .radio input[type=radio]{position:relative;margin-left:0}.form-inline .has-feedback .form-control-feedback{top:0}}.form-horizontal .checkbox,.form-horizontal .checkbox-inline,.form-horizontal .radio,.form-horizontal .radio-inline{padding-top:7px;margin-top:0;margin-bottom:0}.form-horizontal .checkbox,.form-horizontal .radio{min-height:27px}.form-horizontal .form-group{margin-right:-15px;margin-left:-15px}@media (min-width:768px){.form-horizontal .control-label{padding-top:7px;margin-bottom:0;text-align:right}}.form-horizontal .has-feedback .form-control-feedback{right:15px}@media (min-width:768px){.form-horizontal .form-group-lg .control-label{padding-top:11px;font-size:18px}}@media (min-width:768px){.form-horizontal .form-group-sm .control-label{padding-top:6px;font-size:12px}}.btn{display:inline-block;padding:6px 12px;margin-bottom:0;font-size:14px;font-weight:400;line-height:1.42857143;text-align:center;white-space:nowrap;vertical-align:middle;-ms-touch-action:manipulation;touch-action:manipulation;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-image:none;border:1px solid transparent;border-radius:4px}.btn.active.focus,.btn.active:focus,.btn.focus,.btn:active.focus,.btn:active:focus,.btn:focus{outline:thin dotted;outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}.btn.focus,.btn:focus,.btn:hover{color:#333;text-decoration:none}.btn.active,.btn:active{background-image:none;outline:0;-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,.125);box-shadow:inset 0 3px 5px rgba(0,0,0,.125)}.btn.disabled,.btn[disabled],fieldset[disabled] .btn{cursor:not-allowed;filter:alpha(opacity=65);-webkit-box-shadow:none;box-shadow:none;opacity:.65}a.btn.disabled,fieldset[disabled] a.btn{pointer-events:none}.btn-default{color:#333;background-color:#fff;border-color:#ccc}.btn-default.focus,.btn-default:focus{color:#333;background-color:#e6e6e6;border-color:#8c8c8c}.btn-default:hover{color:#333;background-color:#e6e6e6;border-color:#adadad}.btn-default.active,.btn-default:active,.open>.dropdown-toggle.btn-default{color:#333;background-color:#e6e6e6;border-color:#adadad}.btn-default.active.focus,.btn-default.active:focus,.btn-default.active:hover,.btn-default:active.focus,.btn-default:active:focus,.btn-default:active:hover,.open>.dropdown-toggle.btn-default.focus,.open>.dropdown-toggle.btn-default:focus,.open>.dropdown-toggle.btn-default:hover{color:#333;background-color:#d4d4d4;border-color:#8c8c8c}.btn-default.active,.btn-default:active,.open>.dropdown-toggle.btn-default{background-image:none}.btn-default.disabled.focus,.btn-default.disabled:focus,.btn-default.disabled:hover,.btn-default[disabled].focus,.btn-default[disabled]:focus,.btn-default[disabled]:hover,fieldset[disabled] .btn-default.focus,fieldset[disabled] .btn-default:focus,fieldset[disabled] .btn-default:hover{background-color:#fff;border-color:#ccc}.btn-default .badge{color:#fff;background-color:#333}.btn-primary{color:#fff;background-color:#337ab7;border-color:#2e6da4}.btn-primary.focus,.btn-primary:focus{color:#fff;background-color:#286090;border-color:#122b40}.btn-primary:hover{color:#fff;background-color:#286090;border-color:#204d74}.btn-primary.active,.btn-primary:active,.open>.dropdown-toggle.btn-primary{color:#fff;background-color:#286090;border-color:#204d74}.btn-primary.active.focus,.btn-primary.active:focus,.btn-primary.active:hover,.btn-primary:active.focus,.btn-primary:active:focus,.btn-primary:active:hover,.open>.dropdown-toggle.btn-primary.focus,.open>.dropdown-toggle.btn-primary:focus,.open>.dropdown-toggle.btn-primary:hover{color:#fff;background-color:#204d74;border-color:#122b40}.btn-primary.active,.btn-primary:active,.open>.dropdown-toggle.btn-primary{background-image:none}.btn-primary.disabled.focus,.btn-primary.disabled:focus,.btn-primary.disabled:hover,.btn-primary[disabled].focus,.btn-primary[disabled]:focus,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary.focus,fieldset[disabled] .btn-primary:focus,fieldset[disabled] .btn-primary:hover{background-color:#337ab7;border-color:#2e6da4}.btn-primary .badge{color:#337ab7;background-color:#fff}.btn-success{color:#fff;background-color:#5cb85c;border-color:#4cae4c}.btn-success.focus,.btn-success:focus{color:#fff;background-color:#449d44;border-color:#255625}.btn-success:hover{color:#fff;background-color:#449d44;border-color:#398439}.btn-success.active,.btn-success:active,.open>.dropdown-toggle.btn-success{color:#fff;background-color:#449d44;border-color:#398439}.btn-success.active.focus,.btn-success.active:focus,.btn-success.active:hover,.btn-success:active.focus,.btn-success:active:focus,.btn-success:active:hover,.open>.dropdown-toggle.btn-success.focus,.open>.dropdown-toggle.btn-success:focus,.open>.dropdown-toggle.btn-success:hover{color:#fff;background-color:#398439;border-color:#255625}.btn-success.active,.btn-success:active,.open>.dropdown-toggle.btn-success{background-image:none}.btn-success.disabled.focus,.btn-success.disabled:focus,.btn-success.disabled:hover,.btn-success[disabled].focus,.btn-success[disabled]:focus,.btn-success[disabled]:hover,fieldset[disabled] .btn-success.focus,fieldset[disabled] .btn-success:focus,fieldset[disabled] .btn-success:hover{background-color:#5cb85c;border-color:#4cae4c}.btn-success .badge{color:#5cb85c;background-color:#fff}.btn-info{color:#fff;background-color:#5bc0de;border-color:#46b8da}.btn-info.focus,.btn-info:focus{color:#fff;background-color:#31b0d5;border-color:#1b6d85}.btn-info:hover{color:#fff;background-color:#31b0d5;border-color:#269abc}.btn-info.active,.btn-info:active,.open>.dropdown-toggle.btn-info{color:#fff;background-color:#31b0d5;border-color:#269abc}.btn-info.active.focus,.btn-info.active:focus,.btn-info.active:hover,.btn-info:active.focus,.btn-info:active:focus,.btn-info:active:hover,.open>.dropdown-toggle.btn-info.focus,.open>.dropdown-toggle.btn-info:focus,.open>.dropdown-toggle.btn-info:hover{color:#fff;background-color:#269abc;border-color:#1b6d85}.btn-info.active,.btn-info:active,.open>.dropdown-toggle.btn-info{background-image:none}.btn-info.disabled.focus,.btn-info.disabled:focus,.btn-info.disabled:hover,.btn-info[disabled].focus,.btn-info[disabled]:focus,.btn-info[disabled]:hover,fieldset[disabled] .btn-info.focus,fieldset[disabled] .btn-info:focus,fieldset[disabled] .btn-info:hover{background-color:#5bc0de;border-color:#46b8da}.btn-info .badge{color:#5bc0de;background-color:#fff}.btn-warning{color:#fff;background-color:#f0ad4e;border-color:#eea236}.btn-warning.focus,.btn-warning:focus{color:#fff;background-color:#ec971f;border-color:#985f0d}.btn-warning:hover{color:#fff;background-color:#ec971f;border-color:#d58512}.btn-warning.active,.btn-warning:active,.open>.dropdown-toggle.btn-warning{color:#fff;background-color:#ec971f;border-color:#d58512}.btn-warning.active.focus,.btn-warning.active:focus,.btn-warning.active:hover,.btn-warning:active.focus,.btn-warning:active:focus,.btn-warning:active:hover,.open>.dropdown-toggle.btn-warning.focus,.open>.dropdown-toggle.btn-warning:focus,.open>.dropdown-toggle.btn-warning:hover{color:#fff;background-color:#d58512;border-color:#985f0d}.btn-warning.active,.btn-warning:active,.open>.dropdown-toggle.btn-warning{background-image:none}.btn-warning.disabled.focus,.btn-warning.disabled:focus,.btn-warning.disabled:hover,.btn-warning[disabled].focus,.btn-warning[disabled]:focus,.btn-warning[disabled]:hover,fieldset[disabled] .btn-warning.focus,fieldset[disabled] .btn-warning:focus,fieldset[disabled] .btn-warning:hover{background-color:#f0ad4e;border-color:#eea236}.btn-warning .badge{color:#f0ad4e;background-color:#fff}.btn-danger{color:#fff;background-color:#d9534f;border-color:#d43f3a}.btn-danger.focus,.btn-danger:focus{color:#fff;background-color:#c9302c;border-color:#761c19}.btn-danger:hover{color:#fff;background-color:#c9302c;border-color:#ac2925}.btn-danger.active,.btn-danger:active,.open>.dropdown-toggle.btn-danger{color:#fff;background-color:#c9302c;border-color:#ac2925}.btn-danger.active.focus,.btn-danger.active:focus,.btn-danger.active:hover,.btn-danger:active.focus,.btn-danger:active:focus,.btn-danger:active:hover,.open>.dropdown-toggle.btn-danger.focus,.open>.dropdown-toggle.btn-danger:focus,.open>.dropdown-toggle.btn-danger:hover{color:#fff;background-color:#ac2925;border-color:#761c19}.btn-danger.active,.btn-danger:active,.open>.dropdown-toggle.btn-danger{background-image:none}.btn-danger.disabled.focus,.btn-danger.disabled:focus,.btn-danger.disabled:hover,.btn-danger[disabled].focus,.btn-danger[disabled]:focus,.btn-danger[disabled]:hover,fieldset[disabled] .btn-danger.focus,fieldset[disabled] .btn-danger:focus,fieldset[disabled] .btn-danger:hover{background-color:#d9534f;border-color:#d43f3a}.btn-danger .badge{color:#d9534f;background-color:#fff}.btn-link{font-weight:400;color:#337ab7;border-radius:0}.btn-link,.btn-link.active,.btn-link:active,.btn-link[disabled],fieldset[disabled] .btn-link{background-color:transparent;-webkit-box-shadow:none;box-shadow:none}.btn-link,.btn-link:active,.btn-link:focus,.btn-link:hover{border-color:transparent}.btn-link:focus,.btn-link:hover{color:#23527c;text-decoration:underline;background-color:transparent}.btn-link[disabled]:focus,.btn-link[disabled]:hover,fieldset[disabled] .btn-link:focus,fieldset[disabled] .btn-link:hover{color:#777;text-decoration:none}.btn-group-lg>.btn,.btn-lg{padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.btn-group-sm>.btn,.btn-sm{padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.btn-group-xs>.btn,.btn-xs{padding:1px 5px;font-size:12px;line-height:1.5;border-radius:3px}.btn-block{display:block;width:100%}.btn-block+.btn-block{margin-top:5px}input[type=button].btn-block,input[type=reset].btn-block,input[type=submit].btn-block{width:100%}.fade{opacity:0;-webkit-transition:opacity .15s linear;-o-transition:opacity .15s linear;transition:opacity .15s linear}.fade.in{opacity:1}.collapse{display:none}.collapse.in{display:block}tr.collapse.in{display:table-row}tbody.collapse.in{display:table-row-group}.collapsing{position:relative;height:0;overflow:hidden;-webkit-transition-timing-function:ease;-o-transition-timing-function:ease;transition-timing-function:ease;-webkit-transition-duration:.35s;-o-transition-duration:.35s;transition-duration:.35s;-webkit-transition-property:height,visibility;-o-transition-property:height,visibility;transition-property:height,visibility}.caret{display:inline-block;width:0;height:0;margin-left:2px;vertical-align:middle;border-top:4px dashed;border-top:4px solid\9;border-right:4px solid transparent;border-left:4px solid transparent}.dropdown,.dropup{position:relative}.dropdown-toggle:focus{outline:0}.dropdown-menu{position:absolute;top:100%;left:0;z-index:1000;display:none;float:left;min-width:160px;padding:5px 0;margin:2px 0 0;font-size:14px;text-align:left;list-style:none;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #ccc;border:1px solid rgba(0,0,0,.15);border-radius:4px;-webkit-box-shadow:0 6px 12px rgba(0,0,0,.175);box-shadow:0 6px 12px rgba(0,0,0,.175)}.dropdown-menu.pull-right{right:0;left:auto}.dropdown-menu .divider{height:1px;margin:9px 0;overflow:hidden;background-color:#e5e5e5}.dropdown-menu>li>a{display:block;padding:3px 20px;clear:both;font-weight:400;line-height:1.42857143;color:#333;white-space:nowrap}.dropdown-menu>li>a:focus,.dropdown-menu>li>a:hover{color:#262626;text-decoration:none;background-color:#f5f5f5}.dropdown-menu>.active>a,.dropdown-menu>.active>a:focus,.dropdown-menu>.active>a:hover{color:#fff;text-decoration:none;background-color:#337ab7;outline:0}.dropdown-menu>.disabled>a,.dropdown-menu>.disabled>a:focus,.dropdown-menu>.disabled>a:hover{color:#777}.dropdown-menu>.disabled>a:focus,.dropdown-menu>.disabled>a:hover{text-decoration:none;cursor:not-allowed;background-color:transparent;background-image:none;filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.open>.dropdown-menu{display:block}.open>a{outline:0}.dropdown-menu-right{right:0;left:auto}.dropdown-menu-left{right:auto;left:0}.dropdown-header{display:block;padding:3px 20px;font-size:12px;line-height:1.42857143;color:#777;white-space:nowrap}.dropdown-backdrop{position:fixed;top:0;right:0;bottom:0;left:0;z-index:990}.pull-right>.dropdown-menu{right:0;left:auto}.dropup .caret,.navbar-fixed-bottom .dropdown .caret{content:"";border-top:0;border-bottom:4px dashed;border-bottom:4px solid\9}.dropup .dropdown-menu,.navbar-fixed-bottom .dropdown .dropdown-menu{top:auto;bottom:100%;margin-bottom:2px}@media (min-width:768px){.navbar-right .dropdown-menu{right:0;left:auto}.navbar-right .dropdown-menu-left{right:auto;left:0}}.btn-group,.btn-group-vertical{position:relative;display:inline-block;vertical-align:middle}.btn-group-vertical>.btn,.btn-group>.btn{position:relative;float:left}.btn-group-vertical>.btn.active,.btn-group-vertical>.btn:active,.btn-group-vertical>.btn:focus,.btn-group-vertical>.btn:hover,.btn-group>.btn.active,.btn-group>.btn:active,.btn-group>.btn:focus,.btn-group>.btn:hover{z-index:2}.btn-group .btn+.btn,.btn-group .btn+.btn-group,.btn-group .btn-group+.btn,.btn-group .btn-group+.btn-group{margin-left:-1px}.btn-toolbar{margin-left:-5px}.btn-toolbar .btn,.btn-toolbar .btn-group,.btn-toolbar .input-group{float:left}.btn-toolbar>.btn,.btn-toolbar>.btn-group,.btn-toolbar>.input-group{margin-left:5px}.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle){border-radius:0}.btn-group>.btn:first-child{margin-left:0}.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn:last-child:not(:first-child),.btn-group>.dropdown-toggle:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.btn-group>.btn-group{float:left}.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-bottom-left-radius:0}.btn-group .dropdown-toggle:active,.btn-group.open .dropdown-toggle{outline:0}.btn-group>.btn+.dropdown-toggle{padding-right:8px;padding-left:8px}.btn-group>.btn-lg+.dropdown-toggle{padding-right:12px;padding-left:12px}.btn-group.open .dropdown-toggle{-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,.125);box-shadow:inset 0 3px 5px rgba(0,0,0,.125)}.btn-group.open .dropdown-toggle.btn-link{-webkit-box-shadow:none;box-shadow:none}.btn .caret{margin-left:0}.btn-lg .caret{border-width:5px 5px 0;border-bottom-width:0}.dropup .btn-lg .caret{border-width:0 5px 5px}.btn-group-vertical>.btn,.btn-group-vertical>.btn-group,.btn-group-vertical>.btn-group>.btn{display:block;float:none;width:100%;max-width:100%}.btn-group-vertical>.btn-group>.btn{float:none}.btn-group-vertical>.btn+.btn,.btn-group-vertical>.btn+.btn-group,.btn-group-vertical>.btn-group+.btn,.btn-group-vertical>.btn-group+.btn-group{margin-top:-1px;margin-left:0}.btn-group-vertical>.btn:not(:first-child):not(:last-child){border-radius:0}.btn-group-vertical>.btn:first-child:not(:last-child){border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn:last-child:not(:first-child){border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:4px;border-bottom-left-radius:4px}.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-top-right-radius:0}.btn-group-justified{display:table;width:100%;table-layout:fixed;border-collapse:separate}.btn-group-justified>.btn,.btn-group-justified>.btn-group{display:table-cell;float:none;width:1%}.btn-group-justified>.btn-group .btn{width:100%}.btn-group-justified>.btn-group .dropdown-menu{left:auto}[data-toggle=buttons]>.btn input[type=checkbox],[data-toggle=buttons]>.btn input[type=radio],[data-toggle=buttons]>.btn-group>.btn input[type=checkbox],[data-toggle=buttons]>.btn-group>.btn input[type=radio]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}.input-group{position:relative;display:table;border-collapse:separate}.input-group[class*=col-]{float:none;padding-right:0;padding-left:0}.input-group .form-control{position:relative;z-index:2;float:left;width:100%;margin-bottom:0}.input-group .form-control:focus{z-index:3}.input-group-lg>.form-control,.input-group-lg>.input-group-addon,.input-group-lg>.input-group-btn>.btn{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-group-lg>.form-control,select.input-group-lg>.input-group-addon,select.input-group-lg>.input-group-btn>.btn{height:46px;line-height:46px}select[multiple].input-group-lg>.form-control,select[multiple].input-group-lg>.input-group-addon,select[multiple].input-group-lg>.input-group-btn>.btn,textarea.input-group-lg>.form-control,textarea.input-group-lg>.input-group-addon,textarea.input-group-lg>.input-group-btn>.btn{height:auto}.input-group-sm>.form-control,.input-group-sm>.input-group-addon,.input-group-sm>.input-group-btn>.btn{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-group-sm>.form-control,select.input-group-sm>.input-group-addon,select.input-group-sm>.input-group-btn>.btn{height:30px;line-height:30px}select[multiple].input-group-sm>.form-control,select[multiple].input-group-sm>.input-group-addon,select[multiple].input-group-sm>.input-group-btn>.btn,textarea.input-group-sm>.form-control,textarea.input-group-sm>.input-group-addon,textarea.input-group-sm>.input-group-btn>.btn{height:auto}.input-group .form-control,.input-group-addon,.input-group-btn{display:table-cell}.input-group .form-control:not(:first-child):not(:last-child),.input-group-addon:not(:first-child):not(:last-child),.input-group-btn:not(:first-child):not(:last-child){border-radius:0}.input-group-addon,.input-group-btn{width:1%;white-space:nowrap;vertical-align:middle}.input-group-addon{padding:6px 12px;font-size:14px;font-weight:400;line-height:1;color:#555;text-align:center;background-color:#eee;border:1px solid #ccc;border-radius:4px}.input-group-addon.input-sm{padding:5px 10px;font-size:12px;border-radius:3px}.input-group-addon.input-lg{padding:10px 16px;font-size:18px;border-radius:6px}.input-group-addon input[type=checkbox],.input-group-addon input[type=radio]{margin-top:0}.input-group .form-control:first-child,.input-group-addon:first-child,.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group>.btn,.input-group-btn:first-child>.dropdown-toggle,.input-group-btn:last-child>.btn-group:not(:last-child)>.btn,.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.input-group-addon:first-child{border-right:0}.input-group .form-control:last-child,.input-group-addon:last-child,.input-group-btn:first-child>.btn-group:not(:first-child)>.btn,.input-group-btn:first-child>.btn:not(:first-child),.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group>.btn,.input-group-btn:last-child>.dropdown-toggle{border-top-left-radius:0;border-bottom-left-radius:0}.input-group-addon:last-child{border-left:0}.input-group-btn{position:relative;font-size:0;white-space:nowrap}.input-group-btn>.btn{position:relative}.input-group-btn>.btn+.btn{margin-left:-1px}.input-group-btn>.btn:active,.input-group-btn>.btn:focus,.input-group-btn>.btn:hover{z-index:2}.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group{margin-right:-1px}.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group{z-index:2;margin-left:-1px}.nav{padding-left:0;margin-bottom:0;list-style:none}.nav>li{position:relative;display:block}.nav>li>a{position:relative;display:block;padding:10px 15px}.nav>li>a:focus,.nav>li>a:hover{text-decoration:none;background-color:#eee}.nav>li.disabled>a{color:#777}.nav>li.disabled>a:focus,.nav>li.disabled>a:hover{color:#777;text-decoration:none;cursor:not-allowed;background-color:transparent}.nav .open>a,.nav .open>a:focus,.nav .open>a:hover{background-color:#eee;border-color:#337ab7}.nav .nav-divider{height:1px;margin:9px 0;overflow:hidden;background-color:#e5e5e5}.nav>li>a>img{max-width:none}.nav-tabs{border-bottom:1px solid #ddd}.nav-tabs>li{float:left;margin-bottom:-1px}.nav-tabs>li>a{margin-right:2px;line-height:1.42857143;border:1px solid transparent;border-radius:4px 4px 0 0}.nav-tabs>li>a:hover{border-color:#eee #eee #ddd}.nav-tabs>li.active>a,.nav-tabs>li.active>a:focus,.nav-tabs>li.active>a:hover{color:#555;cursor:default;background-color:#fff;border:1px solid #ddd;border-bottom-color:transparent}.nav-tabs.nav-justified{width:100%;border-bottom:0}.nav-tabs.nav-justified>li{float:none}.nav-tabs.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-tabs.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-tabs.nav-justified>li{display:table-cell;width:1%}.nav-tabs.nav-justified>li>a{margin-bottom:0}}.nav-tabs.nav-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:focus,.nav-tabs.nav-justified>.active>a:hover{border:1px solid #ddd}@media (min-width:768px){.nav-tabs.nav-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:focus,.nav-tabs.nav-justified>.active>a:hover{border-bottom-color:#fff}}.nav-pills>li{float:left}.nav-pills>li>a{border-radius:4px}.nav-pills>li+li{margin-left:2px}.nav-pills>li.active>a,.nav-pills>li.active>a:focus,.nav-pills>li.active>a:hover{color:#fff;background-color:#337ab7}.nav-stacked>li{float:none}.nav-stacked>li+li{margin-top:2px;margin-left:0}.nav-justified{width:100%}.nav-justified>li{float:none}.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-justified>li{display:table-cell;width:1%}.nav-justified>li>a{margin-bottom:0}}.nav-tabs-justified{border-bottom:0}.nav-tabs-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:focus,.nav-tabs-justified>.active>a:hover{border:1px solid #ddd}@media (min-width:768px){.nav-tabs-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:focus,.nav-tabs-justified>.active>a:hover{border-bottom-color:#fff}}.tab-content>.tab-pane{display:none}.tab-content>.active{display:block}.nav-tabs .dropdown-menu{margin-top:-1px;border-top-left-radius:0;border-top-right-radius:0}.navbar{position:relative;min-height:50px;margin-bottom:20px;border:1px solid transparent}@media (min-width:768px){.navbar{border-radius:4px}}@media (min-width:768px){.navbar-header{float:left}}.navbar-collapse{padding-right:15px;padding-left:15px;overflow-x:visible;-webkit-overflow-scrolling:touch;border-top:1px solid transparent;-webkit-box-shadow:inset 0 1px 0 rgba(255,255,255,.1);box-shadow:inset 0 1px 0 rgba(255,255,255,.1)}.navbar-collapse.in{overflow-y:auto}@media (min-width:768px){.navbar-collapse{width:auto;border-top:0;-webkit-box-shadow:none;box-shadow:none}.navbar-collapse.collapse{display:block!important;height:auto!important;padding-bottom:0;overflow:visible!important}.navbar-collapse.in{overflow-y:visible}.navbar-fixed-bottom .navbar-collapse,.navbar-fixed-top .navbar-collapse,.navbar-static-top .navbar-collapse{padding-right:0;padding-left:0}}.navbar-fixed-bottom .navbar-collapse,.navbar-fixed-top .navbar-collapse{max-height:340px}@media (max-device-width:480px) and (orientation:landscape){.navbar-fixed-bottom .navbar-collapse,.navbar-fixed-top .navbar-collapse{max-height:200px}}.container-fluid>.navbar-collapse,.container-fluid>.navbar-header,.container>.navbar-collapse,.container>.navbar-header{margin-right:-15px;margin-left:-15px}@media (min-width:768px){.container-fluid>.navbar-collapse,.container-fluid>.navbar-header,.container>.navbar-collapse,.container>.navbar-header{margin-right:0;margin-left:0}}.navbar-static-top{z-index:1000;border-width:0 0 1px}@media (min-width:768px){.navbar-static-top{border-radius:0}}.navbar-fixed-bottom,.navbar-fixed-top{position:fixed;right:0;left:0;z-index:1030}@media (min-width:768px){.navbar-fixed-bottom,.navbar-fixed-top{border-radius:0}}.navbar-fixed-top{top:0;border-width:0 0 1px}.navbar-fixed-bottom{bottom:0;margin-bottom:0;border-width:1px 0 0}.navbar-brand{float:left;height:50px;padding:15px 15px;font-size:18px;line-height:20px}.navbar-brand:focus,.navbar-brand:hover{text-decoration:none}.navbar-brand>img{display:block}@media (min-width:768px){.navbar>.container .navbar-brand,.navbar>.container-fluid .navbar-brand{margin-left:-15px}}.navbar-toggle{position:relative;float:right;padding:9px 10px;margin-top:8px;margin-right:15px;margin-bottom:8px;background-color:transparent;background-image:none;border:1px solid transparent;border-radius:4px}.navbar-toggle:focus{outline:0}.navbar-toggle .icon-bar{display:block;width:22px;height:2px;border-radius:1px}.navbar-toggle .icon-bar+.icon-bar{margin-top:4px}@media (min-width:768px){.navbar-toggle{display:none}}.navbar-nav{margin:7.5px -15px}.navbar-nav>li>a{padding-top:10px;padding-bottom:10px;line-height:20px}@media (max-width:767px){.navbar-nav .open .dropdown-menu{position:static;float:none;width:auto;margin-top:0;background-color:transparent;border:0;-webkit-box-shadow:none;box-shadow:none}.navbar-nav .open .dropdown-menu .dropdown-header,.navbar-nav .open .dropdown-menu>li>a{padding:5px 15px 5px 25px}.navbar-nav .open .dropdown-menu>li>a{line-height:20px}.navbar-nav .open .dropdown-menu>li>a:focus,.navbar-nav .open .dropdown-menu>li>a:hover{background-image:none}}@media (min-width:768px){.navbar-nav{float:left;margin:0}.navbar-nav>li{float:left}.navbar-nav>li>a{padding-top:15px;padding-bottom:15px}}.navbar-form{padding:10px 15px;margin-top:8px;margin-right:-15px;margin-bottom:8px;margin-left:-15px;border-top:1px solid transparent;border-bottom:1px solid transparent;-webkit-box-shadow:inset 0 1px 0 rgba(255,255,255,.1),0 1px 0 rgba(255,255,255,.1);box-shadow:inset 0 1px 0 rgba(255,255,255,.1),0 1px 0 rgba(255,255,255,.1)}@media (min-width:768px){.navbar-form .form-group{display:inline-block;margin-bottom:0;vertical-align:middle}.navbar-form .form-control{display:inline-block;width:auto;vertical-align:middle}.navbar-form .form-control-static{display:inline-block}.navbar-form .input-group{display:inline-table;vertical-align:middle}.navbar-form .input-group .form-control,.navbar-form .input-group .input-group-addon,.navbar-form .input-group .input-group-btn{width:auto}.navbar-form .input-group>.form-control{width:100%}.navbar-form .control-label{margin-bottom:0;vertical-align:middle}.navbar-form .checkbox,.navbar-form .radio{display:inline-block;margin-top:0;margin-bottom:0;vertical-align:middle}.navbar-form .checkbox label,.navbar-form .radio label{padding-left:0}.navbar-form .checkbox input[type=checkbox],.navbar-form .radio input[type=radio]{position:relative;margin-left:0}.navbar-form .has-feedback .form-control-feedback{top:0}}@media (max-width:767px){.navbar-form .form-group{margin-bottom:5px}.navbar-form .form-group:last-child{margin-bottom:0}}@media (min-width:768px){.navbar-form{width:auto;padding-top:0;padding-bottom:0;margin-right:0;margin-left:0;border:0;-webkit-box-shadow:none;box-shadow:none}}.navbar-nav>li>.dropdown-menu{margin-top:0;border-top-left-radius:0;border-top-right-radius:0}.navbar-fixed-bottom .navbar-nav>li>.dropdown-menu{margin-bottom:0;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.navbar-btn{margin-top:8px;margin-bottom:8px}.navbar-btn.btn-sm{margin-top:10px;margin-bottom:10px}.navbar-btn.btn-xs{margin-top:14px;margin-bottom:14px}.navbar-text{margin-top:15px;margin-bottom:15px}@media (min-width:768px){.navbar-text{float:left;margin-right:15px;margin-left:15px}}@media (min-width:768px){.navbar-left{float:left!important}.navbar-right{float:right!important;margin-right:-15px}.navbar-right~.navbar-right{margin-right:0}}.navbar-default{background-color:#f5f5f5;border-color:#e7e7e7}.navbar-default .navbar-brand{color:#777}.navbar-default .navbar-brand:focus,.navbar-default .navbar-brand:hover{color:#5e5e5e;background-color:transparent}.navbar-default .navbar-text{color:#777}.navbar-default .navbar-nav>li>a{color:#777}.navbar-default .navbar-nav>li>a:focus,.navbar-default .navbar-nav>li>a:hover{color:#333;background-color:transparent}.navbar-default .navbar-nav>.active>a,.navbar-default .navbar-nav>.active>a:focus,.navbar-default .navbar-nav>.active>a:hover{color:#555;background-color:#e7e7e7}.navbar-default .navbar-nav>.disabled>a,.navbar-default .navbar-nav>.disabled>a:focus,.navbar-default .navbar-nav>.disabled>a:hover{color:#ccc;background-color:transparent}.navbar-default .navbar-toggle{border-color:#ddd}.navbar-default .navbar-toggle:focus,.navbar-default .navbar-toggle:hover{background-color:#ddd}.navbar-default .navbar-toggle .icon-bar{background-color:#888}.navbar-default .navbar-collapse,.navbar-default .navbar-form{border-color:#e7e7e7}.navbar-default .navbar-nav>.open>a,.navbar-default .navbar-nav>.open>a:focus,.navbar-default .navbar-nav>.open>a:hover{color:#555;background-color:#e7e7e7}@media (max-width:767px){.navbar-default .navbar-nav .open .dropdown-menu>li>a{color:#777}.navbar-default .navbar-nav .open .dropdown-menu>li>a:focus,.navbar-default .navbar-nav .open .dropdown-menu>li>a:hover{color:#333;background-color:transparent}.navbar-default .navbar-nav .open .dropdown-menu>.active>a,.navbar-default .navbar-nav .open .dropdown-menu>.active>a:focus,.navbar-default .navbar-nav .open .dropdown-menu>.active>a:hover{color:#555;background-color:#e7e7e7}.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a,.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:focus,.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:hover{color:#ccc;background-color:transparent}}.navbar-default .navbar-link{color:#777}.navbar-default .navbar-link:hover{color:#333}.navbar-default .btn-link{color:#777}.navbar-default .btn-link:focus,.navbar-default .btn-link:hover{color:#333}.navbar-default .btn-link[disabled]:focus,.navbar-default .btn-link[disabled]:hover,fieldset[disabled] .navbar-default .btn-link:focus,fieldset[disabled] .navbar-default .btn-link:hover{color:#ccc}.navbar-inverse{background-color:#222;border-color:#080808}.navbar-inverse .navbar-brand{color:#9d9d9d}.navbar-inverse .navbar-brand:focus,.navbar-inverse .navbar-brand:hover{color:#fff;background-color:transparent}.navbar-inverse .navbar-text{color:#9d9d9d}.navbar-inverse .navbar-nav>li>a{color:#9d9d9d}.navbar-inverse .navbar-nav>li>a:focus,.navbar-inverse .navbar-nav>li>a:hover{color:#fff;background-color:transparent}.navbar-inverse .navbar-nav>.active>a,.navbar-inverse .navbar-nav>.active>a:focus,.navbar-inverse .navbar-nav>.active>a:hover{color:#fff;background-color:#080808}.navbar-inverse .navbar-nav>.disabled>a,.navbar-inverse .navbar-nav>.disabled>a:focus,.navbar-inverse .navbar-nav>.disabled>a:hover{color:#444;background-color:transparent}.navbar-inverse .navbar-toggle{border-color:#333}.navbar-inverse .navbar-toggle:focus,.navbar-inverse .navbar-toggle:hover{background-color:#333}.navbar-inverse .navbar-toggle .icon-bar{background-color:#fff}.navbar-inverse .navbar-collapse,.navbar-inverse .navbar-form{border-color:#101010}.navbar-inverse .navbar-nav>.open>a,.navbar-inverse .navbar-nav>.open>a:focus,.navbar-inverse .navbar-nav>.open>a:hover{color:#fff;background-color:#080808}@media (max-width:767px){.navbar-inverse .navbar-nav .open .dropdown-menu>.dropdown-header{border-color:#080808}.navbar-inverse .navbar-nav .open .dropdown-menu .divider{background-color:#080808}.navbar-inverse .navbar-nav .open .dropdown-menu>li>a{color:#9d9d9d}.navbar-inverse .navbar-nav .open .dropdown-menu>li>a:focus,.navbar-inverse .navbar-nav .open .dropdown-menu>li>a:hover{color:#fff;background-color:transparent}.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a,.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:focus,.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:hover{color:#fff;background-color:#080808}.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a,.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:focus,.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:hover{color:#444;background-color:transparent}}.navbar-inverse .navbar-link{color:#9d9d9d}.navbar-inverse .navbar-link:hover{color:#fff}.navbar-inverse .btn-link{color:#9d9d9d}.navbar-inverse .btn-link:focus,.navbar-inverse .btn-link:hover{color:#fff}.navbar-inverse .btn-link[disabled]:focus,.navbar-inverse .btn-link[disabled]:hover,fieldset[disabled] .navbar-inverse .btn-link:focus,fieldset[disabled] .navbar-inverse .btn-link:hover{color:#444}.breadcrumb{padding:8px 15px;margin-bottom:20px;list-style:none;background-color:#f5f5f5;border-radius:4px}.breadcrumb>li{display:inline-block}.breadcrumb>li+li:before{padding:0 5px;color:#ccc;content:"/\00a0"}.breadcrumb>.active{color:#777}.pagination{display:inline-block;padding-left:0;margin:20px 0;border-radius:4px}.pagination>li{display:inline}.pagination>li>a,.pagination>li>span{position:relative;float:left;padding:6px 12px;margin-left:-1px;line-height:1.42857143;color:#337ab7;text-decoration:none;background-color:#fff;border:1px solid #ddd}.pagination>li:first-child>a,.pagination>li:first-child>span{margin-left:0;border-top-left-radius:4px;border-bottom-left-radius:4px}.pagination>li:last-child>a,.pagination>li:last-child>span{border-top-right-radius:4px;border-bottom-right-radius:4px}.pagination>li>a:focus,.pagination>li>a:hover,.pagination>li>span:focus,.pagination>li>span:hover{z-index:2;color:#23527c;background-color:#eee;border-color:#ddd}.pagination>.active>a,.pagination>.active>a:focus,.pagination>.active>a:hover,.pagination>.active>span,.pagination>.active>span:focus,.pagination>.active>span:hover{z-index:3;color:#fff;cursor:default;background-color:#337ab7;border-color:#337ab7}.pagination>.disabled>a,.pagination>.disabled>a:focus,.pagination>.disabled>a:hover,.pagination>.disabled>span,.pagination>.disabled>span:focus,.pagination>.disabled>span:hover{color:#777;cursor:not-allowed;background-color:#fff;border-color:#ddd}.pagination-lg>li>a,.pagination-lg>li>span{padding:10px 16px;font-size:18px;line-height:1.3333333}.pagination-lg>li:first-child>a,.pagination-lg>li:first-child>span{border-top-left-radius:6px;border-bottom-left-radius:6px}.pagination-lg>li:last-child>a,.pagination-lg>li:last-child>span{border-top-right-radius:6px;border-bottom-right-radius:6px}.pagination-sm>li>a,.pagination-sm>li>span{padding:5px 10px;font-size:12px;line-height:1.5}.pagination-sm>li:first-child>a,.pagination-sm>li:first-child>span{border-top-left-radius:3px;border-bottom-left-radius:3px}.pagination-sm>li:last-child>a,.pagination-sm>li:last-child>span{border-top-right-radius:3px;border-bottom-right-radius:3px}.pager{padding-left:0;margin:20px 0;text-align:center;list-style:none}.pager li{display:inline}.pager li>a,.pager li>span{display:inline-block;padding:5px 14px;background-color:#fff;border:1px solid #ddd;border-radius:15px}.pager li>a:focus,.pager li>a:hover{text-decoration:none;background-color:#eee}.pager .next>a,.pager .next>span{float:right}.pager .previous>a,.pager .previous>span{float:left}.pager .disabled>a,.pager .disabled>a:focus,.pager .disabled>a:hover,.pager .disabled>span{color:#777;cursor:not-allowed;background-color:#fff}.label{display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em}a.label:focus,a.label:hover{color:#fff;text-decoration:none;cursor:pointer}.label:empty{display:none}.btn .label{position:relative;top:-1px}.label-default{background-color:#777}.label-default[href]:focus,.label-default[href]:hover{background-color:#5e5e5e}.label-primary{background-color:#337ab7}.label-primary[href]:focus,.label-primary[href]:hover{background-color:#286090}.label-success{background-color:#5cb85c}.label-success[href]:focus,.label-success[href]:hover{background-color:#449d44}.label-info{background-color:#5bc0de}.label-info[href]:focus,.label-info[href]:hover{background-color:#31b0d5}.label-warning{background-color:#f0ad4e}.label-warning[href]:focus,.label-warning[href]:hover{background-color:#ec971f}.label-danger{background-color:#d9534f}.label-danger[href]:focus,.label-danger[href]:hover{background-color:#c9302c}.badge{display:inline-block;min-width:10px;padding:3px 7px;font-size:12px;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:middle;background-color:#777;border-radius:10px}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.btn-group-xs>.btn .badge,.btn-xs .badge{top:0;padding:1px 5px}a.badge:focus,a.badge:hover{color:#fff;text-decoration:none;cursor:pointer}.list-group-item.active>.badge,.nav-pills>.active>a>.badge{color:#337ab7;background-color:#fff}.list-group-item>.badge{float:right}.list-group-item>.badge+.badge{margin-right:5px}.nav-pills>li>a>.badge{margin-left:3px}.jumbotron{padding-top:30px;padding-bottom:30px;margin-bottom:30px;color:inherit;background-color:#eee}.jumbotron .h1,.jumbotron h1{color:inherit}.jumbotron p{margin-bottom:15px;font-size:21px;font-weight:200}.jumbotron>hr{border-top-color:#d5d5d5}.container .jumbotron,.container-fluid .jumbotron{padding-right:15px;padding-left:15px;border-radius:6px}.jumbotron .container{max-width:100%}@media screen and (min-width:768px){.jumbotron{padding-top:48px;padding-bottom:48px}.container .jumbotron,.container-fluid .jumbotron{padding-right:60px;padding-left:60px}.jumbotron .h1,.jumbotron h1{font-size:63px}}.thumbnail{display:block;padding:4px;margin-bottom:20px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;-webkit-transition:border .2s ease-in-out;-o-transition:border .2s ease-in-out;transition:border .2s ease-in-out}.thumbnail a>img,.thumbnail>img{margin-right:auto;margin-left:auto}a.thumbnail.active,a.thumbnail:focus,a.thumbnail:hover{border-color:#337ab7}.thumbnail .caption{padding:9px;color:#333}.alert{padding:15px;margin-bottom:20px;border:1px solid transparent;border-radius:4px}.alert h4{margin-top:0;color:inherit}.alert .alert-link{font-weight:700}.alert>p,.alert>ul{margin-bottom:0}.alert>p+p{margin-top:5px}.alert-dismissable,.alert-dismissible{padding-right:35px}.alert-dismissable .close,.alert-dismissible .close{position:relative;top:-2px;right:-21px;color:inherit}.alert-success{color:#3c763d;background-color:#dff0d8;border-color:#d6e9c6}.alert-success hr{border-top-color:#c9e2b3}.alert-success .alert-link{color:#2b542c}.alert-info{color:#31708f;background-color:#d9edf7;border-color:#bce8f1}.alert-info hr{border-top-color:#a6e1ec}.alert-info .alert-link{color:#245269}.alert-warning{color:#8a6d3b;background-color:#fcf8e3;border-color:#faebcc}.alert-warning hr{border-top-color:#f7e1b5}.alert-warning .alert-link{color:#66512c}.alert-danger{color:#a94442;background-color:#f2dede;border-color:#ebccd1}.alert-danger hr{border-top-color:#e4b9c0}.alert-danger .alert-link{color:#843534}@-webkit-keyframes progress-bar-stripes{from{background-position:40px 0}to{background-position:0 0}}@-o-keyframes progress-bar-stripes{from{background-position:40px 0}to{background-position:0 0}}@keyframes progress-bar-stripes{from{background-position:40px 0}to{background-position:0 0}}.progress{height:20px;margin-bottom:20px;overflow:hidden;background-color:#f5f5f5;border-radius:4px;-webkit-box-shadow:inset 0 1px 2px rgba(0,0,0,.1);box-shadow:inset 0 1px 2px rgba(0,0,0,.1)}.progress-bar{float:left;width:0;height:100%;font-size:12px;line-height:20px;color:#fff;text-align:center;background-color:#337ab7;-webkit-box-shadow:inset 0 -1px 0 rgba(0,0,0,.15);box-shadow:inset 0 -1px 0 rgba(0,0,0,.15);-webkit-transition:width .6s ease;-o-transition:width .6s ease;transition:width .6s ease}.progress-bar-striped,.progress-striped .progress-bar{background-image:-webkit-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:-o-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);-webkit-background-size:40px 40px;background-size:40px 40px}.progress-bar.active,.progress.active .progress-bar{-webkit-animation:progress-bar-stripes 2s linear infinite;-o-animation:progress-bar-stripes 2s linear infinite;animation:progress-bar-stripes 2s linear infinite}.progress-bar-success{background-color:#5cb85c}.progress-striped .progress-bar-success{background-image:-webkit-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:-o-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent)}.progress-bar-info{background-color:#5bc0de}.progress-striped .progress-bar-info{background-image:-webkit-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:-o-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent)}.progress-bar-warning{background-color:#f0ad4e}.progress-striped .progress-bar-warning{background-image:-webkit-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:-o-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent)}.progress-bar-danger{background-color:#d9534f}.progress-striped .progress-bar-danger{background-image:-webkit-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:-o-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-image:linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent)}.media{margin-top:15px}.media:first-child{margin-top:0}.media,.media-body{overflow:hidden;zoom:1}.media-body{width:10000px}.media-object{display:block}.media-object.img-thumbnail{max-width:none}.media-right,.media>.pull-right{padding-left:10px}.media-left,.media>.pull-left{padding-right:10px}.media-body,.media-left,.media-right{display:table-cell;vertical-align:top}.media-middle{vertical-align:middle}.media-bottom{vertical-align:bottom}.media-heading{margin-top:0;margin-bottom:5px}.media-list{padding-left:0;list-style:none}.list-group{padding-left:0;margin-bottom:20px}.list-group-item{position:relative;display:block;padding:10px 15px;margin-bottom:-1px;background-color:#fff;border:1px solid #ddd}.list-group-item:first-child{border-top-left-radius:4px;border-top-right-radius:4px}.list-group-item:last-child{margin-bottom:0;border-bottom-right-radius:4px;border-bottom-left-radius:4px}a.list-group-item,button.list-group-item{color:#555}a.list-group-item .list-group-item-heading,button.list-group-item .list-group-item-heading{color:#333}a.list-group-item:focus,a.list-group-item:hover,button.list-group-item:focus,button.list-group-item:hover{color:#555;text-decoration:none;background-color:#f5f5f5}button.list-group-item{width:100%;text-align:left}.list-group-item.disabled,.list-group-item.disabled:focus,.list-group-item.disabled:hover{color:#777;cursor:not-allowed;background-color:#eee}.list-group-item.disabled .list-group-item-heading,.list-group-item.disabled:focus .list-group-item-heading,.list-group-item.disabled:hover .list-group-item-heading{color:inherit}.list-group-item.disabled .list-group-item-text,.list-group-item.disabled:focus .list-group-item-text,.list-group-item.disabled:hover .list-group-item-text{color:#777}.list-group-item.active,.list-group-item.active:focus,.list-group-item.active:hover{z-index:2;color:#fff;background-color:#337ab7;border-color:#337ab7}.list-group-item.active .list-group-item-heading,.list-group-item.active .list-group-item-heading>.small,.list-group-item.active .list-group-item-heading>small,.list-group-item.active:focus .list-group-item-heading,.list-group-item.active:focus .list-group-item-heading>.small,.list-group-item.active:focus .list-group-item-heading>small,.list-group-item.active:hover .list-group-item-heading,.list-group-item.active:hover .list-group-item-heading>.small,.list-group-item.active:hover .list-group-item-heading>small{color:inherit}.list-group-item.active .list-group-item-text,.list-group-item.active:focus .list-group-item-text,.list-group-item.active:hover .list-group-item-text{color:#c7ddef}.list-group-item-success{color:#3c763d;background-color:#dff0d8}a.list-group-item-success,button.list-group-item-success{color:#3c763d}a.list-group-item-success .list-group-item-heading,button.list-group-item-success .list-group-item-heading{color:inherit}a.list-group-item-success:focus,a.list-group-item-success:hover,button.list-group-item-success:focus,button.list-group-item-success:hover{color:#3c763d;background-color:#d0e9c6}a.list-group-item-success.active,a.list-group-item-success.active:focus,a.list-group-item-success.active:hover,button.list-group-item-success.active,button.list-group-item-success.active:focus,button.list-group-item-success.active:hover{color:#fff;background-color:#3c763d;border-color:#3c763d}.list-group-item-info{color:#31708f;background-color:#d9edf7}a.list-group-item-info,button.list-group-item-info{color:#31708f}a.list-group-item-info .list-group-item-heading,button.list-group-item-info .list-group-item-heading{color:inherit}a.list-group-item-info:focus,a.list-group-item-info:hover,button.list-group-item-info:focus,button.list-group-item-info:hover{color:#31708f;background-color:#c4e3f3}a.list-group-item-info.active,a.list-group-item-info.active:focus,a.list-group-item-info.active:hover,button.list-group-item-info.active,button.list-group-item-info.active:focus,button.list-group-item-info.active:hover{color:#fff;background-color:#31708f;border-color:#31708f}.list-group-item-warning{color:#8a6d3b;background-color:#fcf8e3}a.list-group-item-warning,button.list-group-item-warning{color:#8a6d3b}a.list-group-item-warning .list-group-item-heading,button.list-group-item-warning .list-group-item-heading{color:inherit}a.list-group-item-warning:focus,a.list-group-item-warning:hover,button.list-group-item-warning:focus,button.list-group-item-warning:hover{color:#8a6d3b;background-color:#faf2cc}a.list-group-item-warning.active,a.list-group-item-warning.active:focus,a.list-group-item-warning.active:hover,button.list-group-item-warning.active,button.list-group-item-warning.active:focus,button.list-group-item-warning.active:hover{color:#fff;background-color:#8a6d3b;border-color:#8a6d3b}.list-group-item-danger{color:#a94442;background-color:#f2dede}a.list-group-item-danger,button.list-group-item-danger{color:#a94442}a.list-group-item-danger .list-group-item-heading,button.list-group-item-danger .list-group-item-heading{color:inherit}a.list-group-item-danger:focus,a.list-group-item-danger:hover,button.list-group-item-danger:focus,button.list-group-item-danger:hover{color:#a94442;background-color:#ebcccc}a.list-group-item-danger.active,a.list-group-item-danger.active:focus,a.list-group-item-danger.active:hover,button.list-group-item-danger.active,button.list-group-item-danger.active:focus,button.list-group-item-danger.active:hover{color:#fff;background-color:#a94442;border-color:#a94442}.list-group-item-heading{margin-top:0;margin-bottom:5px}.list-group-item-text{margin-bottom:0;line-height:1.3}.panel{margin-bottom:20px;background-color:#fff;border:1px solid transparent;border-radius:4px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.05);box-shadow:0 1px 1px rgba(0,0,0,.05)}.panel-body{padding:15px}.panel-heading{padding:10px 15px;border-bottom:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px}.panel-heading>.dropdown .dropdown-toggle{color:inherit}.panel-title{margin-top:0;margin-bottom:0;font-size:16px;color:inherit}.panel-title>.small,.panel-title>.small>a,.panel-title>a,.panel-title>small,.panel-title>small>a{color:inherit}.panel-footer{padding:10px 15px;background-color:#f5f5f5;border-top:1px solid #ddd;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.list-group,.panel>.panel-collapse>.list-group{margin-bottom:0}.panel>.list-group .list-group-item,.panel>.panel-collapse>.list-group .list-group-item{border-width:1px 0;border-radius:0}.panel>.list-group:first-child .list-group-item:first-child,.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child{border-top:0;border-top-left-radius:3px;border-top-right-radius:3px}.panel>.list-group:last-child .list-group-item:last-child,.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child{border-bottom:0;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child{border-top-left-radius:0;border-top-right-radius:0}.panel-heading+.list-group .list-group-item:first-child{border-top-width:0}.list-group+.panel-footer{border-top-width:0}.panel>.panel-collapse>.table,.panel>.table,.panel>.table-responsive>.table{margin-bottom:0}.panel>.panel-collapse>.table caption,.panel>.table caption,.panel>.table-responsive>.table caption{padding-right:15px;padding-left:15px}.panel>.table-responsive:first-child>.table:first-child,.panel>.table:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child,.panel>.table:first-child>thead:first-child>tr:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table:first-child>thead:first-child>tr:first-child th:first-child{border-top-left-radius:3px}.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table:first-child>thead:first-child>tr:first-child th:last-child{border-top-right-radius:3px}.panel>.table-responsive:last-child>.table:last-child,.panel>.table:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child{border-bottom-left-radius:3px}.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child{border-bottom-right-radius:3px}.panel>.panel-body+.table,.panel>.panel-body+.table-responsive,.panel>.table+.panel-body,.panel>.table-responsive+.panel-body{border-top:1px solid #ddd}.panel>.table>tbody:first-child>tr:first-child td,.panel>.table>tbody:first-child>tr:first-child th{border-top:0}.panel>.table-bordered,.panel>.table-responsive>.table-bordered{border:0}.panel>.table-bordered>tbody>tr>td:first-child,.panel>.table-bordered>tbody>tr>th:first-child,.panel>.table-bordered>tfoot>tr>td:first-child,.panel>.table-bordered>tfoot>tr>th:first-child,.panel>.table-bordered>thead>tr>td:first-child,.panel>.table-bordered>thead>tr>th:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,.panel>.table-responsive>.table-bordered>thead>tr>th:first-child{border-left:0}.panel>.table-bordered>tbody>tr>td:last-child,.panel>.table-bordered>tbody>tr>th:last-child,.panel>.table-bordered>tfoot>tr>td:last-child,.panel>.table-bordered>tfoot>tr>th:last-child,.panel>.table-bordered>thead>tr>td:last-child,.panel>.table-bordered>thead>tr>th:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,.panel>.table-responsive>.table-bordered>thead>tr>th:last-child{border-right:0}.panel>.table-bordered>tbody>tr:first-child>td,.panel>.table-bordered>tbody>tr:first-child>th,.panel>.table-bordered>thead>tr:first-child>td,.panel>.table-bordered>thead>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th,.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,.panel>.table-responsive>.table-bordered>thead>tr:first-child>th{border-bottom:0}.panel>.table-bordered>tbody>tr:last-child>td,.panel>.table-bordered>tbody>tr:last-child>th,.panel>.table-bordered>tfoot>tr:last-child>td,.panel>.table-bordered>tfoot>tr:last-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}.panel>.table-responsive{margin-bottom:0;border:0}.panel-group{margin-bottom:20px}.panel-group .panel{margin-bottom:0;border-radius:4px}.panel-group .panel+.panel{margin-top:5px}.panel-group .panel-heading{border-bottom:0}.panel-group .panel-heading+.panel-collapse>.list-group,.panel-group .panel-heading+.panel-collapse>.panel-body{border-top:1px solid #ddd}.panel-group .panel-footer{border-top:0}.panel-group .panel-footer+.panel-collapse .panel-body{border-bottom:1px solid #ddd}.panel-default{border-color:#ddd}.panel-default>.panel-heading{color:#333;background-color:#f5f5f5;border-color:#ddd}.panel-default>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ddd}.panel-default>.panel-heading .badge{color:#f5f5f5;background-color:#333}.panel-default>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ddd}.panel-primary{border-color:#337ab7}.panel-primary>.panel-heading{color:#fff;background-color:#337ab7;border-color:#337ab7}.panel-primary>.panel-heading+.panel-collapse>.panel-body{border-top-color:#337ab7}.panel-primary>.panel-heading .badge{color:#337ab7;background-color:#fff}.panel-primary>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#337ab7}.panel-success{border-color:#d6e9c6}.panel-success>.panel-heading{color:#3c763d;background-color:#dff0d8;border-color:#d6e9c6}.panel-success>.panel-heading+.panel-collapse>.panel-body{border-top-color:#d6e9c6}.panel-success>.panel-heading .badge{color:#dff0d8;background-color:#3c763d}.panel-success>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#d6e9c6}.panel-info{border-color:#bce8f1}.panel-info>.panel-heading{color:#31708f;background-color:#d9edf7;border-color:#bce8f1}.panel-info>.panel-heading+.panel-collapse>.panel-body{border-top-color:#bce8f1}.panel-info>.panel-heading .badge{color:#d9edf7;background-color:#31708f}.panel-info>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#bce8f1}.panel-warning{border-color:#faebcc}.panel-warning>.panel-heading{color:#8a6d3b;background-color:#fcf8e3;border-color:#faebcc}.panel-warning>.panel-heading+.panel-collapse>.panel-body{border-top-color:#faebcc}.panel-warning>.panel-heading .badge{color:#fcf8e3;background-color:#8a6d3b}.panel-warning>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#faebcc}.panel-danger{border-color:#ebccd1}.panel-danger>.panel-heading{color:#a94442;background-color:#f2dede;border-color:#ebccd1}.panel-danger>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ebccd1}.panel-danger>.panel-heading .badge{color:#f2dede;background-color:#a94442}.panel-danger>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ebccd1}.embed-responsive{position:relative;display:block;height:0;padding:0;overflow:hidden}.embed-responsive .embed-responsive-item,.embed-responsive embed,.embed-responsive iframe,.embed-responsive object,.embed-responsive video{position:absolute;top:0;bottom:0;left:0;width:100%;height:100%;border:0}.embed-responsive-16by9{padding-bottom:56.25%}.embed-responsive-4by3{padding-bottom:75%}.well{min-height:20px;padding:19px;margin-bottom:20px;background-color:#f5f5f5;border:1px solid #e3e3e3;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.05);box-shadow:inset 0 1px 1px rgba(0,0,0,.05)}.well blockquote{border-color:#ddd;border-color:rgba(0,0,0,.15)}.well-lg{padding:24px;border-radius:6px}.well-sm{padding:9px;border-radius:3px}.close{float:right;font-size:21px;font-weight:700;line-height:1;color:#000;text-shadow:0 1px 0 #fff;filter:alpha(opacity=20);opacity:.2}.close:focus,.close:hover{color:#000;text-decoration:none;cursor:pointer;filter:alpha(opacity=50);opacity:.5}button.close{-webkit-appearance:none;padding:0;cursor:pointer;background:0 0;border:0}.modal-open{overflow:hidden}.modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;overflow:hidden;-webkit-overflow-scrolling:touch;outline:0}.modal.fade .modal-dialog{-webkit-transition:-webkit-transform .3s ease-out;-o-transition:-o-transform .3s ease-out;transition:transform .3s ease-out;-webkit-transform:translate(0,-25%);-ms-transform:translate(0,-25%);-o-transform:translate(0,-25%);transform:translate(0,-25%)}.modal.in .modal-dialog{-webkit-transform:translate(0,0);-ms-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}.modal-open .modal{overflow-x:hidden;overflow-y:auto}.modal-dialog{position:relative;width:auto;margin:10px}.modal-content{position:relative;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #999;border:1px solid rgba(0,0,0,.2);border-radius:6px;outline:0;-webkit-box-shadow:0 3px 9px rgba(0,0,0,.5);box-shadow:0 3px 9px rgba(0,0,0,.5)}.modal-backdrop{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1040;background-color:#000}.modal-backdrop.fade{filter:alpha(opacity=0);opacity:0}.modal-backdrop.in{filter:alpha(opacity=50);opacity:.5}.modal-header{padding:15px;border-bottom:1px solid #e5e5e5}.modal-header .close{margin-top:-2px}.modal-title{margin:0;line-height:1.42857143}.modal-body{position:relative;padding:15px}.modal-footer{padding:15px;text-align:right;border-top:1px solid #e5e5e5}.modal-footer .btn+.btn{margin-bottom:0;margin-left:5px}.modal-footer .btn-group .btn+.btn{margin-left:-1px}.modal-footer .btn-block+.btn-block{margin-left:0}.modal-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}@media (min-width:768px){.modal-dialog{width:600px;margin:30px auto}.modal-content{-webkit-box-shadow:0 5px 15px rgba(0,0,0,.5);box-shadow:0 5px 15px rgba(0,0,0,.5)}.modal-sm{width:300px}}@media (min-width:992px){.modal-lg{width:900px}}.tooltip{position:absolute;z-index:1070;display:block;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:12px;font-style:normal;font-weight:400;line-height:1.42857143;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;word-wrap:normal;white-space:normal;filter:alpha(opacity=0);opacity:0;line-break:auto}.tooltip.in{filter:alpha(opacity=90);opacity:.9}.tooltip.top{padding:5px 0;margin-top:-3px}.tooltip.right{padding:0 5px;margin-left:3px}.tooltip.bottom{padding:5px 0;margin-top:3px}.tooltip.left{padding:0 5px;margin-left:-3px}.tooltip-inner{max-width:200px;padding:3px 8px;color:#fff;text-align:center;background-color:#000;border-radius:4px}.tooltip-arrow{position:absolute;width:0;height:0;border-color:transparent;border-style:solid}.tooltip.top .tooltip-arrow{bottom:0;left:50%;margin-left:-5px;border-width:5px 5px 0;border-top-color:#000}.tooltip.top-left .tooltip-arrow{right:5px;bottom:0;margin-bottom:-5px;border-width:5px 5px 0;border-top-color:#000}.tooltip.top-right .tooltip-arrow{bottom:0;left:5px;margin-bottom:-5px;border-width:5px 5px 0;border-top-color:#000}.tooltip.right .tooltip-arrow{top:50%;left:0;margin-top:-5px;border-width:5px 5px 5px 0;border-right-color:#000}.tooltip.left .tooltip-arrow{top:50%;right:0;margin-top:-5px;border-width:5px 0 5px 5px;border-left-color:#000}.tooltip.bottom .tooltip-arrow{top:0;left:50%;margin-left:-5px;border-width:0 5px 5px;border-bottom-color:#000}.tooltip.bottom-left .tooltip-arrow{top:0;right:5px;margin-top:-5px;border-width:0 5px 5px;border-bottom-color:#000}.tooltip.bottom-right .tooltip-arrow{top:0;left:5px;margin-top:-5px;border-width:0 5px 5px;border-bottom-color:#000}.popover{position:absolute;top:0;left:0;z-index:1060;display:none;max-width:276px;padding:1px;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;font-style:normal;font-weight:400;line-height:1.42857143;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;word-wrap:normal;white-space:normal;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #ccc;border:1px solid rgba(0,0,0,.2);border-radius:6px;-webkit-box-shadow:0 5px 10px rgba(0,0,0,.2);box-shadow:0 5px 10px rgba(0,0,0,.2);line-break:auto}.popover.top{margin-top:-10px}.popover.right{margin-left:10px}.popover.bottom{margin-top:10px}.popover.left{margin-left:-10px}.popover-title{padding:8px 14px;margin:0;font-size:14px;background-color:#f5f5f5;border-bottom:1px solid #ebebeb;border-radius:5px 5px 0 0}.popover-content{padding:9px 14px}.popover>.arrow,.popover>.arrow:after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid}.popover>.arrow{border-width:11px}.popover>.arrow:after{content:"";border-width:10px}.popover.top>.arrow{bottom:-11px;left:50%;margin-left:-11px;border-top-color:#999;border-top-color:rgba(0,0,0,.25);border-bottom-width:0}.popover.top>.arrow:after{bottom:1px;margin-left:-10px;content:" ";border-top-color:#fff;border-bottom-width:0}.popover.right>.arrow{top:50%;left:-11px;margin-top:-11px;border-right-color:#999;border-right-color:rgba(0,0,0,.25);border-left-width:0}.popover.right>.arrow:after{bottom:-10px;left:1px;content:" ";border-right-color:#fff;border-left-width:0}.popover.bottom>.arrow{top:-11px;left:50%;margin-left:-11px;border-top-width:0;border-bottom-color:#999;border-bottom-color:rgba(0,0,0,.25)}.popover.bottom>.arrow:after{top:1px;margin-left:-10px;content:" ";border-top-width:0;border-bottom-color:#fff}.popover.left>.arrow{top:50%;right:-11px;margin-top:-11px;border-right-width:0;border-left-color:#999;border-left-color:rgba(0,0,0,.25)}.popover.left>.arrow:after{right:1px;bottom:-10px;content:" ";border-right-width:0;border-left-color:#fff}.carousel{position:relative}.carousel-inner{position:relative;width:100%;overflow:hidden}.carousel-inner>.item{position:relative;display:none;-webkit-transition:.6s ease-in-out left;-o-transition:.6s ease-in-out left;transition:.6s ease-in-out left}.carousel-inner>.item>a>img,.carousel-inner>.item>img{line-height:1}@media all and (transform-3d),(-webkit-transform-3d){.carousel-inner>.item{-webkit-transition:-webkit-transform .6s ease-in-out;-o-transition:-o-transform .6s ease-in-out;transition:transform .6s ease-in-out;-webkit-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000px;perspective:1000px}.carousel-inner>.item.active.right,.carousel-inner>.item.next{left:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}.carousel-inner>.item.active.left,.carousel-inner>.item.prev{left:0;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}.carousel-inner>.item.active,.carousel-inner>.item.next.left,.carousel-inner>.item.prev.right{left:0;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}.carousel-inner>.active,.carousel-inner>.next,.carousel-inner>.prev{display:block}.carousel-inner>.active{left:0}.carousel-inner>.next,.carousel-inner>.prev{position:absolute;top:0;width:100%}.carousel-inner>.next{left:100%}.carousel-inner>.prev{left:-100%}.carousel-inner>.next.left,.carousel-inner>.prev.right{left:0}.carousel-inner>.active.left{left:-100%}.carousel-inner>.active.right{left:100%}.carousel-control{position:absolute;top:0;bottom:0;left:0;width:15%;font-size:20px;color:#fff;text-align:center;text-shadow:0 1px 2px rgba(0,0,0,.6);background-color:rgba(0,0,0,0);filter:alpha(opacity=50);opacity:.5}.carousel-control.left{background-image:-webkit-linear-gradient(left,rgba(0,0,0,.5) 0,rgba(0,0,0,.0001) 100%);background-image:-o-linear-gradient(left,rgba(0,0,0,.5) 0,rgba(0,0,0,.0001) 100%);background-image:-webkit-gradient(linear,left top,right top,from(rgba(0,0,0,.5)),to(rgba(0,0,0,.0001)));background-image:linear-gradient(to right,rgba(0,0,0,.5) 0,rgba(0,0,0,.0001) 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);background-repeat:repeat-x}.carousel-control.right{right:0;left:auto;background-image:-webkit-linear-gradient(left,rgba(0,0,0,.0001) 0,rgba(0,0,0,.5) 100%);background-image:-o-linear-gradient(left,rgba(0,0,0,.0001) 0,rgba(0,0,0,.5) 100%);background-image:-webkit-gradient(linear,left top,right top,from(rgba(0,0,0,.0001)),to(rgba(0,0,0,.5)));background-image:linear-gradient(to right,rgba(0,0,0,.0001) 0,rgba(0,0,0,.5) 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);background-repeat:repeat-x}.carousel-control:focus,.carousel-control:hover{color:#fff;text-decoration:none;filter:alpha(opacity=90);outline:0;opacity:.9}.carousel-control .glyphicon-chevron-left,.carousel-control .glyphicon-chevron-right,.carousel-control .icon-next,.carousel-control .icon-prev{position:absolute;top:50%;z-index:5;display:inline-block;margin-top:-10px}.carousel-control .glyphicon-chevron-left,.carousel-control .icon-prev{left:50%;margin-left:-10px}.carousel-control .glyphicon-chevron-right,.carousel-control .icon-next{right:50%;margin-right:-10px}.carousel-control .icon-next,.carousel-control .icon-prev{width:20px;height:20px;font-family:serif;line-height:1}.carousel-control .icon-prev:before{content:'\2039'}.carousel-control .icon-next:before{content:'\203a'}.carousel-indicators{position:absolute;bottom:10px;left:50%;z-index:15;width:60%;padding-left:0;margin-left:-30%;text-align:center;list-style:none}.carousel-indicators li{display:inline-block;width:10px;height:10px;margin:1px;text-indent:-999px;cursor:pointer;background-color:#000\9;background-color:rgba(0,0,0,0);border:1px solid #fff;border-radius:10px}.carousel-indicators .active{width:12px;height:12px;margin:0;background-color:#fff}.carousel-caption{position:absolute;right:15%;bottom:20px;left:15%;z-index:10;padding-top:20px;padding-bottom:20px;color:#fff;text-align:center;text-shadow:0 1px 2px rgba(0,0,0,.6)}.carousel-caption .btn{text-shadow:none}@media screen and (min-width:768px){.carousel-control .glyphicon-chevron-left,.carousel-control .glyphicon-chevron-right,.carousel-control .icon-next,.carousel-control .icon-prev{width:30px;height:30px;margin-top:-10px;font-size:30px}.carousel-control .glyphicon-chevron-left,.carousel-control .icon-prev{margin-left:-10px}.carousel-control .glyphicon-chevron-right,.carousel-control .icon-next{margin-right:-10px}.carousel-caption{right:20%;left:20%;padding-bottom:30px}.carousel-indicators{bottom:20px}}.btn-group-vertical>.btn-group:after,.btn-group-vertical>.btn-group:before,.btn-toolbar:after,.btn-toolbar:before,.clearfix:after,.clearfix:before,.container-fluid:after,.container-fluid:before,.container:after,.container:before,.dl-horizontal dd:after,.dl-horizontal dd:before,.form-horizontal .form-group:after,.form-horizontal .form-group:before,.modal-footer:after,.modal-footer:before,.modal-header:after,.modal-header:before,.nav:after,.nav:before,.navbar-collapse:after,.navbar-collapse:before,.navbar-header:after,.navbar-header:before,.navbar:after,.navbar:before,.pager:after,.pager:before,.panel-body:after,.panel-body:before,.row:after,.row:before{display:table;content:" "}.btn-group-vertical>.btn-group:after,.btn-toolbar:after,.clearfix:after,.container-fluid:after,.container:after,.dl-horizontal dd:after,.form-horizontal .form-group:after,.modal-footer:after,.modal-header:after,.nav:after,.navbar-collapse:after,.navbar-header:after,.navbar:after,.pager:after,.panel-body:after,.row:after{clear:both}.center-block{display:block;margin-right:auto;margin-left:auto}.pull-right{float:right!important}.pull-left{float:left!important}.hide{display:none!important}.show{display:block!important}.invisible{visibility:hidden}.text-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.hidden{display:none!important}.affix{position:fixed}@-ms-viewport{width:device-width}.visible-lg,.visible-md,.visible-sm,.visible-xs{display:none!important}.visible-lg-block,.visible-lg-inline,.visible-lg-inline-block,.visible-md-block,.visible-md-inline,.visible-md-inline-block,.visible-sm-block,.visible-sm-inline,.visible-sm-inline-block,.visible-xs-block,.visible-xs-inline,.visible-xs-inline-block{display:none!important}@media (max-width:767px){.visible-xs{display:block!important}table.visible-xs{display:table!important}tr.visible-xs{display:table-row!important}td.visible-xs,th.visible-xs{display:table-cell!important}}@media (max-width:767px){.visible-xs-block{display:block!important}}@media (max-width:767px){.visible-xs-inline{display:inline!important}}@media (max-width:767px){.visible-xs-inline-block{display:inline-block!important}}@media (min-width:768px) and (max-width:991px){.visible-sm{display:block!important}table.visible-sm{display:table!important}tr.visible-sm{display:table-row!important}td.visible-sm,th.visible-sm{display:table-cell!important}}@media (min-width:768px) and (max-width:991px){.visible-sm-block{display:block!important}}@media (min-width:768px) and (max-width:991px){.visible-sm-inline{display:inline!important}}@media (min-width:768px) and (max-width:991px){.visible-sm-inline-block{display:inline-block!important}}@media (min-width:992px) and (max-width:1199px){.visible-md{display:block!important}table.visible-md{display:table!important}tr.visible-md{display:table-row!important}td.visible-md,th.visible-md{display:table-cell!important}}@media (min-width:992px) and (max-width:1199px){.visible-md-block{display:block!important}}@media (min-width:992px) and (max-width:1199px){.visible-md-inline{display:inline!important}}@media (min-width:992px) and (max-width:1199px){.visible-md-inline-block{display:inline-block!important}}@media (min-width:1200px){.visible-lg{display:block!important}table.visible-lg{display:table!important}tr.visible-lg{display:table-row!important}td.visible-lg,th.visible-lg{display:table-cell!important}}@media (min-width:1200px){.visible-lg-block{display:block!important}}@media (min-width:1200px){.visible-lg-inline{display:inline!important}}@media (min-width:1200px){.visible-lg-inline-block{display:inline-block!important}}@media (max-width:767px){.hidden-xs{display:none!important}}@media (min-width:768px) and (max-width:991px){.hidden-sm{display:none!important}}@media (min-width:992px) and (max-width:1199px){.hidden-md{display:none!important}}@media (min-width:1200px){.hidden-lg{display:none!important}}.visible-print{display:none!important}@media print{.visible-print{display:block!important}table.visible-print{display:table!important}tr.visible-print{display:table-row!important}td.visible-print,th.visible-print{display:table-cell!important}}.visible-print-block{display:none!important}@media print{.visible-print-block{display:block!important}}.visible-print-inline{display:none!important}@media print{.visible-print-inline{display:inline!important}}.visible-print-inline-block{display:none!important}@media print{.visible-print-inline-block{display:inline-block!important}}@media print{.hidden-print{display:none!important}}
/*# sourceMappingURL=bootstrap.min.css.map */

/*! jQuery UI - v1.12.1 - 2016-09-14
* http://jqueryui.com
* Includes: core.css, accordion.css, autocomplete.css, menu.css, button.css, controlgroup.css, checkboxradio.css, datepicker.css, dialog.css, draggable.css, resizable.css, progressbar.css, selectable.css, selectmenu.css, slider.css, sortable.css, spinner.css, tabs.css, tooltip.css, theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?ffDefault=Arial%2CHelvetica%2Csans-serif&fsDefault=1em&fwDefault=normal&cornerRadius=3px&bgColorHeader=e9e9e9&bgTextureHeader=flat&borderColorHeader=dddddd&fcHeader=333333&iconColorHeader=444444&bgColorContent=ffffff&bgTextureContent=flat&borderColorContent=dddddd&fcContent=333333&iconColorContent=444444&bgColorDefault=f6f6f6&bgTextureDefault=flat&borderColorDefault=c5c5c5&fcDefault=454545&iconColorDefault=777777&bgColorHover=ededed&bgTextureHover=flat&borderColorHover=cccccc&fcHover=2b2b2b&iconColorHover=555555&bgColorActive=007fff&bgTextureActive=flat&borderColorActive=003eff&fcActive=ffffff&iconColorActive=ffffff&bgColorHighlight=fffa90&bgTextureHighlight=flat&borderColorHighlight=dad55e&fcHighlight=777620&iconColorHighlight=777620&bgColorError=fddfdf&bgTextureError=flat&borderColorError=f1a899&fcError=5f3f3f&iconColorError=cc0000&bgColorOverlay=aaaaaa&bgTextureOverlay=flat&bgImgOpacityOverlay=0&opacityOverlay=30&bgColorShadow=666666&bgTextureShadow=flat&bgImgOpacityShadow=0&opacityShadow=30&thicknessShadow=5px&offsetTopShadow=0px&offsetLeftShadow=0px&cornerRadiusShadow=8px
* Copyright jQuery Foundation and other contributors; Licensed MIT */

/* Layout helpers
----------------------------------*/
.ui-helper-hidden {
    display: none;
}
.ui-helper-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.ui-helper-reset {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    line-height: 1.3;
    text-decoration: none;
    font-size: 100%;
    list-style: none;
}
.ui-helper-clearfix:before,
.ui-helper-clearfix:after {
    content: "";
    display: table;
    border-collapse: collapse;
}
.ui-helper-clearfix:after {
    clear: both;
}
.ui-helper-zfix {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0;
    filter:Alpha(Opacity=0); /* support: IE8 */
}

.ui-front {
    z-index: 100;
}


/* Interaction Cues
----------------------------------*/
.ui-state-disabled {
    cursor: default !important;
    pointer-events: none;
}


/* Icons
----------------------------------*/
.ui-icon {
    display: inline-block;
    vertical-align: middle;
    margin-top: -.25em;
    position: relative;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat;
}

.ui-widget-icon-block {
    left: 50%;
    margin-left: -8px;
    display: block;
}

/* Misc visuals
----------------------------------*/

/* Overlays */
.ui-widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.ui-accordion .ui-accordion-header {
    display: block;
    cursor: pointer;
    position: relative;
    margin: 2px 0 0 0;
    padding: .5em .5em .5em .7em;
    font-size: 100%;
}
.ui-accordion .ui-accordion-content {
    padding: 1em 2.2em;
    border-top: 0;
    overflow: auto;
}
.ui-autocomplete {
    position: absolute;
    top: 0;
    left: 0;
    cursor: default;
}
.ui-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: block;
    outline: 0;
}
.ui-menu .ui-menu {
    position: absolute;
}
.ui-menu .ui-menu-item {
    margin: 0;
    cursor: pointer;
    /* support: IE10, see #8844 */
    list-style-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
}
.ui-menu .ui-menu-item-wrapper {
    position: relative;
    padding: 3px 1em 3px .4em;
}
.ui-menu .ui-menu-divider {
    margin: 5px 0;
    height: 0;
    font-size: 0;
    line-height: 0;
    border-width: 1px 0 0 0;
}
.ui-menu .ui-state-focus,
.ui-menu .ui-state-active {
    margin: -1px;
}

/* icon support */
.ui-menu-icons {
    position: relative;
}
.ui-menu-icons .ui-menu-item-wrapper {
    padding-left: 2em;
}

/* left-aligned */
.ui-menu .ui-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: .2em;
    margin: auto 0;
}

/* right-aligned */
.ui-menu .ui-menu-icon {
    left: auto;
    right: 0;
}
.ui-button {
    padding: .4em 1em;
    display: inline-block;
    position: relative;
    line-height: normal;
    margin-right: .1em;
    cursor: pointer;
    vertical-align: middle;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    /* Support: IE <= 11 */
    overflow: visible;
}

.ui-button,
.ui-button:link,
.ui-button:visited,
.ui-button:hover,
.ui-button:active {
    text-decoration: none;
}

/* to make room for the icon, a width needs to be set here */
.ui-button-icon-only {
    width: 2em;
    box-sizing: border-box;
    text-indent: -9999px;
    white-space: nowrap;
}

/* no icon support for input elements */
input.ui-button.ui-button-icon-only {
    text-indent: 0;
}

/* button icon element(s) */
.ui-button-icon-only .ui-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -8px;
    margin-left: -8px;
}

.ui-button.ui-icon-notext .ui-icon {
    padding: 0;
    width: 2.1em;
    height: 2.1em;
    text-indent: -9999px;
    white-space: nowrap;

}

input.ui-button.ui-icon-notext .ui-icon {
    width: auto;
    height: auto;
    text-indent: 0;
    white-space: normal;
    padding: .4em 1em;
}

/* workarounds */
/* Support: Firefox 5 - 40 */
input.ui-button::-moz-focus-inner,
button.ui-button::-moz-focus-inner {
    border: 0;
    padding: 0;
}
.ui-controlgroup {
    vertical-align: middle;
    display: inline-block;
}
.ui-controlgroup > .ui-controlgroup-item {
    float: left;
    margin-left: 0;
    margin-right: 0;
}
.ui-controlgroup > .ui-controlgroup-item:focus,
.ui-controlgroup > .ui-controlgroup-item.ui-visual-focus {
    z-index: 9999;
}
.ui-controlgroup-vertical > .ui-controlgroup-item {
    display: block;
    float: none;
    width: 100%;
    margin-top: 0;
    margin-bottom: 0;
    text-align: left;
}
.ui-controlgroup-vertical .ui-controlgroup-item {
    box-sizing: border-box;
}
.ui-controlgroup .ui-controlgroup-label {
    padding: .4em 1em;
}
.ui-controlgroup .ui-controlgroup-label span {
    font-size: 80%;
}
.ui-controlgroup-horizontal .ui-controlgroup-label + .ui-controlgroup-item {
    border-left: none;
}
.ui-controlgroup-vertical .ui-controlgroup-label + .ui-controlgroup-item {
    border-top: none;
}
.ui-controlgroup-horizontal .ui-controlgroup-label.ui-widget-content {
    border-right: none;
}
.ui-controlgroup-vertical .ui-controlgroup-label.ui-widget-content {
    border-bottom: none;
}

/* Spinner specific style fixes */
.ui-controlgroup-vertical .ui-spinner-input {

    /* Support: IE8 only, Android < 4.4 only */
    width: 75%;
    width: calc( 100% - 2.4em );
}
.ui-controlgroup-vertical .ui-spinner .ui-spinner-up {
    border-top-style: solid;
}

.ui-checkboxradio-label .ui-icon-background {
    box-shadow: inset 1px 1px 1px #ccc;
    border-radius: .12em;
    border: none;
}
.ui-checkboxradio-radio-label .ui-icon-background {
    width: 16px;
    height: 16px;
    border-radius: 1em;
    overflow: visible;
    border: none;
}
.ui-checkboxradio-radio-label.ui-checkboxradio-checked .ui-icon,
.ui-checkboxradio-radio-label.ui-checkboxradio-checked:hover .ui-icon {
    background-image: none;
    width: 8px;
    height: 8px;
    border-width: 4px;
    border-style: solid;
}
.ui-checkboxradio-disabled {
    pointer-events: none;
}
.ui-datepicker {
    width: 17em;
    padding: .2em .2em 0;
    display: none;
}
.ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: .2em 0;
}
.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    position: absolute;
    top: 2px;
    width: 1.8em;
    height: 1.8em;
}
.ui-datepicker .ui-datepicker-prev-hover,
.ui-datepicker .ui-datepicker-next-hover {
    top: 1px;
}
.ui-datepicker .ui-datepicker-prev {
    left: 2px;
}
.ui-datepicker .ui-datepicker-next {
    right: 2px;
}
.ui-datepicker .ui-datepicker-prev-hover {
    left: 1px;
}
.ui-datepicker .ui-datepicker-next-hover {
    right: 1px;
}
.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
    display: block;
    position: absolute;
    top:12px; left:11px;
    width:0; height:0; border-right:6px solid #fff; border-top:6px solid transparent; border-bottom:6px solid transparent;
}
.ui-datepicker .ui-datepicker-next span { border-right:0; border-left:6px solid #fff; left:12px; }
.ui-datepicker .ui-datepicker-title {
    margin: 0 2.3em;
    line-height: 1.8em;
    text-align: center;
}
.ui-datepicker .ui-datepicker-title select {
    font-size: 1em;
    margin: 1px 0;
}
.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
    width: 45%;
}
.ui-datepicker table {
    width: 100%;
    font-size: .9em;
    border-collapse: collapse;
    margin: 0 0 .4em;
}
.ui-datepicker th {
    padding: .7em .3em;
    text-align: center;
    font-weight: bold;
    border: 0;
}
.ui-datepicker td {
    border: 0;
    padding: 1px;
}
.ui-datepicker td span,
.ui-datepicker td a {
    display: block;
    padding: .2em;
    text-align: right;
    text-decoration: none;
    background:#f5f5f5;
}


.ui-datepicker td a.ui-state-highlight {
    background: #00c8f8;
    border-left:1px solid #00c8f8; border-right:1px solid #00c8f8;
    color: #fff;
}

.home .ui-datepicker td a.ui-state-highlight {
    background:#004494;
    border-left:1px solid #004494; border-right:1px solid #004494;
}
.wasser .ui-datepicker td a.ui-state-highlight {
    background:#52bfd5;
    border-left:1px solid #52bfd5; border-right:1px solid #52bfd5;
}
.strom .ui-datepicker td a.ui-state-highlight {
    background:#e74a16;
    border-left:1px solid #e74a16; border-right:1px solid #e74a16;
}
.gas .ui-datepicker td a.ui-state-highlight {
    background:#fecb01;
    border-left:1px solid #fecb01; border-right:1px solid #fecb01;
}
.baeder .ui-datepicker td a.ui-state-highlight {
    background:#00c8f8;
    border-left:1px solid #00c8f8; border-right:1px solid #00c8f8;
}
.service .ui-datepicker td a.ui-state-highlight {
    background: #d6d6d6;
    border-left:1px solid #d6d6d6; border-right:1px solid #d6d6d6;
}
.energie .ui-datepicker td a.ui-state-highlight {
    background: #98c21d;
    border-left:1px solid #98c21d; border-right:1px solid #98c21d;
}

.ui-datepicker .ui-datepicker-buttonpane {
    background-image: none;
    margin: .7em 0 0 0;
    padding: 0 .2em;
    border-left: 0;
    border-right: 0;
    border-bottom: 0;
}
.ui-datepicker .ui-datepicker-buttonpane button {
    float: right;
    margin: .5em .2em .4em;
    cursor: pointer;
    padding: .2em .6em .3em .6em;
    width: auto;
    overflow: visible;
}
.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
    float: left;
}

/* with multiple calendars */
.ui-datepicker.ui-datepicker-multi {
    width: auto;
}
.ui-datepicker-multi .ui-datepicker-group {
    float: left;
}
.ui-datepicker-multi .ui-datepicker-group table {
    width: 95%;
    margin: 0 auto .4em;
}
.ui-datepicker-multi-2 .ui-datepicker-group {
    width: 50%;
}
.ui-datepicker-multi-3 .ui-datepicker-group {
    width: 33.3%;
}
.ui-datepicker-multi-4 .ui-datepicker-group {
    width: 25%;
}
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
    border-left-width: 0;
}
.ui-datepicker-multi .ui-datepicker-buttonpane {
    clear: left;
}
.ui-datepicker-row-break {
    clear: both;
    width: 100%;
    font-size: 0;
}

/* RTL support */
.ui-datepicker-rtl {
    direction: rtl;
}
.ui-datepicker-rtl .ui-datepicker-prev {
    right: 2px;
    left: auto;
}
.ui-datepicker-rtl .ui-datepicker-next {
    left: 2px;
    right: auto;
}
.ui-datepicker-rtl .ui-datepicker-prev:hover {
    right: 1px;
    left: auto;
}
.ui-datepicker-rtl .ui-datepicker-next:hover {
    left: 1px;
    right: auto;
}

.home div.ui-datepicker .ui-datepicker-prev,
.home div.ui-datepicker .ui-datepicker-next {
    background:#004494;
}
.wasser div.ui-datepicker .ui-datepicker-prev,
.wasser div.ui-datepicker .ui-datepicker-next{
    background:#52bfd5;
}
.strom div.ui-datepicker .ui-datepicker-prev,
.strom div.ui-datepicker .ui-datepicker-next{
    background:#e74a16;
}
.gas div.ui-datepicker .ui-datepicker-prev,
.gas div.ui-datepicker .ui-datepicker-next{
    background:#fecb01;
}
.baeder div.ui-datepicker .ui-datepicker-prev,
.baeder div.ui-datepicker .ui-datepicker-next{
    background:#00c8f8;
}
.service div.ui-datepicker .ui-datepicker-prev,
.service div.ui-datepicker .ui-datepicker-next{
    background: #d6d6d6
}
.energie div.ui-datepicker .ui-datepicker-prev,
.energie div.ui-datepicker .ui-datepicker-next{
    background: #98c21d
}

.ui-datepicker-rtl .ui-datepicker-buttonpane {
    clear: right;
}
.ui-datepicker-rtl .ui-datepicker-buttonpane button {
    float: left;
}
.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,
.ui-datepicker-rtl .ui-datepicker-group {
    float: right;
}
.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
    border-right-width: 0;
    border-left-width: 1px;
}

/* Icons */
.ui-datepicker .ui-icon {
    display: block;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat;
    left: .5em;
    top: .3em;
}
.ui-dialog {
    position: absolute;
    top: 0;
    left: 0;
    padding: .2em;
    outline: 0;
}
.ui-dialog .ui-dialog-titlebar {
    padding: .4em 1em;
    position: relative;
}
.ui-dialog .ui-dialog-title {
    float: left;
    margin: .1em 0;
    white-space: nowrap;
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.ui-dialog .ui-dialog-titlebar-close {
    position: absolute;
    right: .3em;
    top: 50%;
    width: 20px;
    margin: -10px 0 0 0;
    padding: 1px;
    height: 20px;
}
.ui-dialog .ui-dialog-content {
    position: relative;
    border: 0;
    padding: .5em 1em;
    background: none;
    overflow: auto;
}
.ui-dialog .ui-dialog-buttonpane {
    text-align: left;
    border-width: 1px 0 0 0;
    background-image: none;
    margin-top: .5em;
    padding: .3em 1em .5em .4em;
}
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
    float: right;
}
.ui-dialog .ui-dialog-buttonpane button {
    margin: .5em .4em .5em 0;
    cursor: pointer;
}
.ui-dialog .ui-resizable-n {
    height: 2px;
    top: 0;
}
.ui-dialog .ui-resizable-e {
    width: 2px;
    right: 0;
}
.ui-dialog .ui-resizable-s {
    height: 2px;
    bottom: 0;
}
.ui-dialog .ui-resizable-w {
    width: 2px;
    left: 0;
}
.ui-dialog .ui-resizable-se,
.ui-dialog .ui-resizable-sw,
.ui-dialog .ui-resizable-ne,
.ui-dialog .ui-resizable-nw {
    width: 7px;
    height: 7px;
}
.ui-dialog .ui-resizable-se {
    right: 0;
    bottom: 0;
}
.ui-dialog .ui-resizable-sw {
    left: 0;
    bottom: 0;
}
.ui-dialog .ui-resizable-ne {
    right: 0;
    top: 0;
}
.ui-dialog .ui-resizable-nw {
    left: 0;
    top: 0;
}
.ui-draggable .ui-dialog-titlebar {
    cursor: move;
}
.ui-draggable-handle {
    -ms-touch-action: none;
    touch-action: none;
}
.ui-resizable {
    position: relative;
}
.ui-resizable-handle {
    position: absolute;
    font-size: 0.1px;
    display: block;
    -ms-touch-action: none;
    touch-action: none;
}
.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
    display: none;
}
.ui-resizable-n {
    cursor: n-resize;
    height: 7px;
    width: 100%;
    top: -5px;
    left: 0;
}
.ui-resizable-s {
    cursor: s-resize;
    height: 7px;
    width: 100%;
    bottom: -5px;
    left: 0;
}
.ui-resizable-e {
    cursor: e-resize;
    width: 7px;
    right: -5px;
    top: 0;
    height: 100%;
}
.ui-resizable-w {
    cursor: w-resize;
    width: 7px;
    left: -5px;
    top: 0;
    height: 100%;
}
.ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px;
}
.ui-resizable-sw {
    cursor: sw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    bottom: -5px;
}
.ui-resizable-nw {
    cursor: nw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    top: -5px;
}
.ui-resizable-ne {
    cursor: ne-resize;
    width: 9px;
    height: 9px;
    right: -5px;
    top: -5px;
}
.ui-progressbar {
    height: 2em;
    text-align: left;
    overflow: hidden;
}
.ui-progressbar .ui-progressbar-value {
    margin: -1px;
    height: 100%;
}
.ui-progressbar .ui-progressbar-overlay {
    background: url("data:image/gif;base64,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");
    height: 100%;
    filter: alpha(opacity=25); /* support: IE8 */
    opacity: 0.25;
}
.ui-progressbar-indeterminate .ui-progressbar-value {
    background-image: none;
}
.ui-selectable {
    -ms-touch-action: none;
    touch-action: none;
}
.ui-selectable-helper {
    position: absolute;
    z-index: 100;
    border: 1px dotted black;
}
.ui-selectmenu-menu {
    padding: 0;
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    display: none;
}
.ui-selectmenu-menu .ui-menu {
    overflow: auto;
    overflow-x: hidden;
    padding-bottom: 1px;
}
.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
    font-size: 1em;
    font-weight: bold;
    line-height: 1.5;
    padding: 2px 0.4em;
    margin: 0.5em 0 0 0;
    height: auto;
    border: 0;
}
.ui-selectmenu-open {
    display: block;
}
.ui-selectmenu-text {
    display: block;
    margin-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
}
.ui-selectmenu-button.ui-button {
    text-align: left;
    white-space: nowrap;
    width: 14em;
}
.ui-selectmenu-icon.ui-icon {
    float: right;
    margin-top: 0;
}
.ui-slider {
    position: relative;
    text-align: left;
}
.ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 1.2em;
    height: 1.2em;
    cursor: default;
    -ms-touch-action: none;
    touch-action: none;
}
.ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0;
}

/* support: IE8 - See #6727 */
.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
    filter: inherit;
}

.ui-slider-horizontal {
    height: .8em;
}
.ui-slider-horizontal .ui-slider-handle {
    top: -.3em;
    margin-left: -.6em;
}
.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%;
}
.ui-slider-horizontal .ui-slider-range-min {
    left: 0;
}
.ui-slider-horizontal .ui-slider-range-max {
    right: 0;
}

.ui-slider-vertical {
    width: .8em;
    height: 100px;
}
.ui-slider-vertical .ui-slider-handle {
    left: -.3em;
    margin-left: 0;
    margin-bottom: -.6em;
}
.ui-slider-vertical .ui-slider-range {
    left: 0;
    width: 100%;
}
.ui-slider-vertical .ui-slider-range-min {
    bottom: 0;
}
.ui-slider-vertical .ui-slider-range-max {
    top: 0;
}
.ui-sortable-handle {
    -ms-touch-action: none;
    touch-action: none;
}
.ui-spinner {
    position: relative;
    display: inline-block;
    overflow: hidden;
    padding: 0;
    vertical-align: middle;
}
.ui-spinner-input {
    border: none;
    background: none;
    color: inherit;
    padding: .222em 0;
    margin: .2em 0;
    vertical-align: middle;
    margin-left: .4em;
    margin-right: 2em;
}
.ui-spinner-button {
    width: 1.6em;
    height: 50%;
    font-size: .5em;
    padding: 0;
    margin: 0;
    text-align: center;
    position: absolute;
    cursor: default;
    display: block;
    overflow: hidden;
    right: 0;
}
/* more specificity required here to override default borders */
.ui-spinner a.ui-spinner-button {
    border-top-style: none;
    border-bottom-style: none;
    border-right-style: none;
}
.ui-spinner-up {
    top: 0;
}
.ui-spinner-down {
    bottom: 0;
}
.ui-tabs {
    position: relative;/* position: relative prevents IE scroll bug (element with position: relative inside container with overflow: auto appear as "fixed") */
    padding: .2em;
}
.ui-tabs .ui-tabs-nav {
    margin: 0;
    padding: .2em .2em 0;
}
.ui-tabs .ui-tabs-nav li {
    list-style: none;
    float: left;
    position: relative;
    top: 0;
    margin: 1px .2em 0 0;
    border-bottom-width: 0;
    padding: 0;
    white-space: nowrap;
}
.ui-tabs .ui-tabs-nav .ui-tabs-anchor {
    float: left;
    padding: .5em 1em;
    text-decoration: none;
}
.ui-tabs .ui-tabs-nav li.ui-tabs-active {
    margin-bottom: -1px;
    padding-bottom: 1px;
}
.ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
    cursor: text;
}
.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor {
    cursor: pointer;
}
.ui-tabs .ui-tabs-panel {
    display: block;
    border-width: 0;
    padding: 1em 1.4em;
    background: none;
}
.ui-tooltip {
    padding: 8px;
    position: absolute;
    z-index: 9999;
    max-width: 300px;
}
body .ui-tooltip {
    border-width: 2px;
}
/* Component containers
----------------------------------*/
.ui-widget {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
}
.ui-widget .ui-widget {
    font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
}
.ui-widget.ui-widget-content {
    border: 1px solid #c5c5c5;
}
.ui-widget-content {
    border: 1px solid #dddddd;
    background: #ffffff;
    color: #333333;
}
.ui-widget-content a {
    color: #333333;
}
.ui-widget-header {
    border: 1px solid #dddddd;
    background: #e9e9e9;
    color: #333333;
    font-weight: bold;
}
.ui-widget-header a {
    color: #333333;
}

/* Interaction states
----------------------------------*/
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,

    /* We use html here because we need a greater specificity to make sure disabled
    works properly when clicked or hovered */
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
    border: 1px solid #c5c5c5;
    background: #f6f6f6;
    font-weight: normal;
    color: #454545;
}
.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
    color: #454545;
    text-decoration: none;
}
.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
    border: 1px solid #cccccc;
    background: #ededed;
    font-weight: normal;
    color: #2b2b2b;
}
.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited,
a.ui-button:hover,
a.ui-button:focus {
    color: #2b2b2b;
    text-decoration: none;
}

.ui-visual-focus {
    box-shadow: 0 0 3px 1px rgb(94, 158, 214);
}
.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    border: 1px solid #003eff;
    background: #007fff;
    font-weight: normal;
    color: #ffffff;
}
.ui-icon-background,
.ui-state-active .ui-icon-background {
    border: #003eff;
    background-color: #ffffff;
}
.ui-state-active a,
.ui-state-active a:link,
.ui-state-active a:visited {
    color: #ffffff;
    text-decoration: none;
}

/* Interaction Cues
----------------------------------*/
.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
    border: 1px solid #dad55e;
    background: #fffa90;
    color: #777620;
}
.ui-state-checked {
    border: 1px solid #dad55e;
    background: #fffa90;
}
.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
    color: #777620;
}
.ui-state-error,
.ui-widget-content .ui-state-error,
.ui-widget-header .ui-state-error {
    border: 1px solid #f1a899;
    background: #fddfdf;
    color: #5f3f3f;
}
.ui-state-error a,
.ui-widget-content .ui-state-error a,
.ui-widget-header .ui-state-error a {
    color: #5f3f3f;
}
.ui-state-error-text,
.ui-widget-content .ui-state-error-text,
.ui-widget-header .ui-state-error-text {
    color: #5f3f3f;
}
.ui-priority-primary,
.ui-widget-content .ui-priority-primary,
.ui-widget-header .ui-priority-primary {
    font-weight: bold;
}
.ui-priority-secondary,
.ui-widget-content .ui-priority-secondary,
.ui-widget-header .ui-priority-secondary {
    opacity: .7;
    filter:Alpha(Opacity=70); /* support: IE8 */
    font-weight: normal;
}
.ui-state-disabled,
.ui-widget-content .ui-state-disabled,
.ui-widget-header .ui-state-disabled {
    opacity: .35;
    filter:Alpha(Opacity=35); /* support: IE8 */
    background-image: none;
}
.ui-state-disabled .ui-icon {
    filter:Alpha(Opacity=35); /* support: IE8 - See #6059 */
}

/* Icons
----------------------------------*/

/* states and images */
.ui-icon {
    width: 16px;
    height: 16px;
}
.ui-icon,
.ui-widget-content .ui-icon {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/images/ui-icons_444444_256x240.png');
}
.ui-widget-header .ui-icon {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/images/ui-icons_444444_256x240.png');
}
.ui-state-hover .ui-icon,
.ui-state-focus .ui-icon,
.ui-button:hover .ui-icon,
.ui-button:focus .ui-icon {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/images/ui-icons_555555_256x240.png');
}
.ui-state-active .ui-icon,
.ui-button:active .ui-icon {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/images/ui-icons_ffffff_256x240.png');
}
.ui-state-highlight .ui-icon,
.ui-button .ui-state-highlight.ui-icon {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/images/ui-icons_777620_256x240.png');
}
.ui-state-error .ui-icon,
.ui-state-error-text .ui-icon {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/images/ui-icons_cc0000_256x240.png');
}
.ui-button .ui-icon {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/images/ui-icons_777777_256x240.png');
}

/* positioning */
.ui-icon-blank { background-position: 16px 16px; }
.ui-icon-caret-1-n { background-position: 0 0; }
.ui-icon-caret-1-ne { background-position: -16px 0; }
.ui-icon-caret-1-e { background-position: -32px 0; }
.ui-icon-caret-1-se { background-position: -48px 0; }
.ui-icon-caret-1-s { background-position: -65px 0; }
.ui-icon-caret-1-sw { background-position: -80px 0; }
.ui-icon-caret-1-w { background-position: -96px 0; }
.ui-icon-caret-1-nw { background-position: -112px 0; }
.ui-icon-caret-2-n-s { background-position: -128px 0; }
.ui-icon-caret-2-e-w { background-position: -144px 0; }
.ui-icon-triangle-1-n { background-position: 0 -16px; }
.ui-icon-triangle-1-ne { background-position: -16px -16px; }
.ui-icon-triangle-1-e { background-position: -32px -16px; }
.ui-icon-triangle-1-se { background-position: -48px -16px; }
.ui-icon-triangle-1-s { background-position: -65px -16px; }
.ui-icon-triangle-1-sw { background-position: -80px -16px; }
.ui-icon-triangle-1-w { background-position: -96px -16px; }
.ui-icon-triangle-1-nw { background-position: -112px -16px; }
.ui-icon-triangle-2-n-s { background-position: -128px -16px; }
.ui-icon-triangle-2-e-w { background-position: -144px -16px; }
.ui-icon-arrow-1-n { background-position: 0 -32px; }
.ui-icon-arrow-1-ne { background-position: -16px -32px; }
.ui-icon-arrow-1-e { background-position: -32px -32px; }
.ui-icon-arrow-1-se { background-position: -48px -32px; }
.ui-icon-arrow-1-s { background-position: -65px -32px; }
.ui-icon-arrow-1-sw { background-position: -80px -32px; }
.ui-icon-arrow-1-w { background-position: -96px -32px; }
.ui-icon-arrow-1-nw { background-position: -112px -32px; }
.ui-icon-arrow-2-n-s { background-position: -128px -32px; }
.ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }
.ui-icon-arrow-2-e-w { background-position: -160px -32px; }
.ui-icon-arrow-2-se-nw { background-position: -176px -32px; }
.ui-icon-arrowstop-1-n { background-position: -192px -32px; }
.ui-icon-arrowstop-1-e { background-position: -208px -32px; }
.ui-icon-arrowstop-1-s { background-position: -224px -32px; }
.ui-icon-arrowstop-1-w { background-position: -240px -32px; }
.ui-icon-arrowthick-1-n { background-position: 1px -48px; }
.ui-icon-arrowthick-1-ne { background-position: -16px -48px; }
.ui-icon-arrowthick-1-e { background-position: -32px -48px; }
.ui-icon-arrowthick-1-se { background-position: -48px -48px; }
.ui-icon-arrowthick-1-s { background-position: -64px -48px; }
.ui-icon-arrowthick-1-sw { background-position: -80px -48px; }
.ui-icon-arrowthick-1-w { background-position: -96px -48px; }
.ui-icon-arrowthick-1-nw { background-position: -112px -48px; }
.ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }
.ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }
.ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }
.ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }
.ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }
.ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }
.ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }
.ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }
.ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }
.ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }
.ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }
.ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }
.ui-icon-arrowreturn-1-w { background-position: -64px -64px; }
.ui-icon-arrowreturn-1-n { background-position: -80px -64px; }
.ui-icon-arrowreturn-1-e { background-position: -96px -64px; }
.ui-icon-arrowreturn-1-s { background-position: -112px -64px; }
.ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }
.ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }
.ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }
.ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }
.ui-icon-arrow-4 { background-position: 0 -80px; }
.ui-icon-arrow-4-diag { background-position: -16px -80px; }
.ui-icon-extlink { background-position: -32px -80px; }
.ui-icon-newwin { background-position: -48px -80px; }
.ui-icon-refresh { background-position: -64px -80px; }
.ui-icon-shuffle { background-position: -80px -80px; }
.ui-icon-transfer-e-w { background-position: -96px -80px; }
.ui-icon-transferthick-e-w { background-position: -112px -80px; }
.ui-icon-folder-collapsed { background-position: 0 -96px; }
.ui-icon-folder-open { background-position: -16px -96px; }
.ui-icon-document { background-position: -32px -96px; }
.ui-icon-document-b { background-position: -48px -96px; }
.ui-icon-note { background-position: -64px -96px; }
.ui-icon-mail-closed { background-position: -80px -96px; }
.ui-icon-mail-open { background-position: -96px -96px; }
.ui-icon-suitcase { background-position: -112px -96px; }
.ui-icon-comment { background-position: -128px -96px; }
.ui-icon-person { background-position: -144px -96px; }
.ui-icon-print { background-position: -160px -96px; }
.ui-icon-trash { background-position: -176px -96px; }
.ui-icon-locked { background-position: -192px -96px; }
.ui-icon-unlocked { background-position: -208px -96px; }
.ui-icon-bookmark { background-position: -224px -96px; }
.ui-icon-tag { background-position: -240px -96px; }
.ui-icon-home { background-position: 0 -112px; }
.ui-icon-flag { background-position: -16px -112px; }
.ui-icon-calendar { background-position: -32px -112px; }
.ui-icon-cart { background-position: -48px -112px; }
.ui-icon-pencil { background-position: -64px -112px; }
.ui-icon-clock { background-position: -80px -112px; }
.ui-icon-disk { background-position: -96px -112px; }
.ui-icon-calculator { background-position: -112px -112px; }
.ui-icon-zoomin { background-position: -128px -112px; }
.ui-icon-zoomout { background-position: -144px -112px; }
.ui-icon-search { background-position: -160px -112px; }
.ui-icon-wrench { background-position: -176px -112px; }
.ui-icon-gear { background-position: -192px -112px; }
.ui-icon-heart { background-position: -208px -112px; }
.ui-icon-star { background-position: -224px -112px; }
.ui-icon-link { background-position: -240px -112px; }
.ui-icon-cancel { background-position: 0 -128px; }
.ui-icon-plus { background-position: -16px -128px; }
.ui-icon-plusthick { background-position: -32px -128px; }
.ui-icon-minus { background-position: -48px -128px; }
.ui-icon-minusthick { background-position: -64px -128px; }
.ui-icon-close { background-position: -80px -128px; }
.ui-icon-closethick { background-position: -96px -128px; }
.ui-icon-key { background-position: -112px -128px; }
.ui-icon-lightbulb { background-position: -128px -128px; }
.ui-icon-scissors { background-position: -144px -128px; }
.ui-icon-clipboard { background-position: -160px -128px; }
.ui-icon-copy { background-position: -176px -128px; }
.ui-icon-contact { background-position: -192px -128px; }
.ui-icon-image { background-position: -208px -128px; }
.ui-icon-video { background-position: -224px -128px; }
.ui-icon-script { background-position: -240px -128px; }
.ui-icon-alert { background-position: 0 -144px; }
.ui-icon-info { background-position: -16px -144px; }
.ui-icon-notice { background-position: -32px -144px; }
.ui-icon-help { background-position: -48px -144px; }
.ui-icon-check { background-position: -64px -144px; }
.ui-icon-bullet { background-position: -80px -144px; }
.ui-icon-radio-on { background-position: -96px -144px; }
.ui-icon-radio-off { background-position: -112px -144px; }
.ui-icon-pin-w { background-position: -128px -144px; }
.ui-icon-pin-s { background-position: -144px -144px; }
.ui-icon-play { background-position: 0 -160px; }
.ui-icon-pause { background-position: -16px -160px; }
.ui-icon-seek-next { background-position: -32px -160px; }
.ui-icon-seek-prev { background-position: -48px -160px; }
.ui-icon-seek-end { background-position: -64px -160px; }
.ui-icon-seek-start { background-position: -80px -160px; }
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.ui-icon-seek-first { background-position: -80px -160px; }
.ui-icon-stop { background-position: -96px -160px; }
.ui-icon-eject { background-position: -112px -160px; }
.ui-icon-volume-off { background-position: -128px -160px; }
.ui-icon-volume-on { background-position: -144px -160px; }
.ui-icon-power { background-position: 0 -176px; }
.ui-icon-signal-diag { background-position: -16px -176px; }
.ui-icon-signal { background-position: -32px -176px; }
.ui-icon-battery-0 { background-position: -48px -176px; }
.ui-icon-battery-1 { background-position: -64px -176px; }
.ui-icon-battery-2 { background-position: -80px -176px; }
.ui-icon-battery-3 { background-position: -96px -176px; }
.ui-icon-circle-plus { background-position: 0 -192px; }
.ui-icon-circle-minus { background-position: -16px -192px; }
.ui-icon-circle-close { background-position: -32px -192px; }
.ui-icon-circle-triangle-e { background-position: -48px -192px; }
.ui-icon-circle-triangle-s { background-position: -64px -192px; }
.ui-icon-circle-triangle-w { background-position: -80px -192px; }
.ui-icon-circle-triangle-n { background-position: -96px -192px; }
.ui-icon-circle-arrow-e { background-position: -112px -192px; }
.ui-icon-circle-arrow-s { background-position: -128px -192px; }
.ui-icon-circle-arrow-w { background-position: -144px -192px; }
.ui-icon-circle-arrow-n { background-position: -160px -192px; }
.ui-icon-circle-zoomin { background-position: -176px -192px; }
.ui-icon-circle-zoomout { background-position: -192px -192px; }
.ui-icon-circle-check { background-position: -208px -192px; }
.ui-icon-circlesmall-plus { background-position: 0 -208px; }
.ui-icon-circlesmall-minus { background-position: -16px -208px; }
.ui-icon-circlesmall-close { background-position: -32px -208px; }
.ui-icon-squaresmall-plus { background-position: -48px -208px; }
.ui-icon-squaresmall-minus { background-position: -64px -208px; }
.ui-icon-squaresmall-close { background-position: -80px -208px; }
.ui-icon-grip-dotted-vertical { background-position: 0 -224px; }
.ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }
.ui-icon-grip-solid-vertical { background-position: -32px -224px; }
.ui-icon-grip-solid-horizontal { background-position: -48px -224px; }
.ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }
.ui-icon-grip-diagonal-se { background-position: -80px -224px; }


/* Misc visuals
----------------------------------*/

/* Corner radius */
.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
    border-top-left-radius: 3px;
}
.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
    border-top-right-radius: 3px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
    border-bottom-left-radius: 3px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
    border-bottom-right-radius: 3px;
}

/* Overlays */
.ui-widget-overlay {
    background: #aaaaaa;
    opacity: .3;
    filter: Alpha(Opacity=30); /* support: IE8 */
}
.ui-widget-shadow {
    -webkit-box-shadow: 0px 0px 5px #666666;
    box-shadow: 0px 0px 5px #666666;
}

/* 
 *  Owl Carousel - Animate Plugin
 */
.owl-carousel .animated {
	-webkit-animation-duration: 1000ms;
	animation-duration: 1000ms;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}
.owl-carousel .owl-animated-in {
	z-index: 0;
}
.owl-carousel .owl-animated-out {
	z-index: 1;
}
.owl-carousel .fadeOut {
	-webkit-animation-name: fadeOut;
	animation-name: fadeOut;
}
 @-webkit-keyframes fadeOut {
0% {
 opacity: 1;
}
 100% {
 opacity: 0;
}
}
@keyframes fadeOut {
0% {
 opacity: 1;
}
 100% {
 opacity: 0;
}
}
.owl-height {
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}
.owl-carousel {
	display: none;
	width: 100%;
	-webkit-tap-highlight-color: transparent;
	position: relative;
	z-index: 1;
}
.owl-carousel .owl-stage {
	position: relative;
	-ms-touch-action: pan-Y;
}
.owl-carousel .owl-stage:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
.owl-carousel .owl-stage-outer {
	position: relative;
	overflow: hidden;
	-webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel .owl-controls .owl-nav .owl-prev, .owl-carousel .owl-controls .owl-nav .owl-next, .owl-carousel .owl-controls .owl-dot {
	cursor: pointer;
	cursor: hand;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.owl-carousel.owl-loaded {
	display: block;
}
.owl-carousel.owl-loading {
	opacity: 0;
	display: block;
}
.owl-carousel.owl-hidden {
	opacity: 0;
}
.owl-carousel .owl-refresh .owl-item {
	display: none;
}
.owl-carousel .owl-item {
	position: relative;
	min-height: 1px;
	float: left;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.owl-carousel .owl-item img {
	display: block;
	width: 100%; /*-webkit-transform-style: preserve-3d;*/
}
.owl-carousel.owl-text-select-on .owl-item {
	-webkit-user-select: auto;
	-moz-user-select: auto;
	-ms-user-select: auto;
	user-select: auto;
}
.owl-carousel .owl-grab {
	cursor: move;
	cursor: -webkit-grab;
	cursor: -o-grab;
	cursor: -ms-grab;
	cursor: grab;
}
.owl-carousel.owl-rtl {
	direction: rtl;
}
.owl-carousel.owl-rtl .owl-item {
	float: right;
}
/* No Js */
.no-js .owl-carousel {
	display: block;
}
/* 
 * 	Owl Carousel - Lazy Load Plugin
 */
.owl-carousel .owl-item .owl-lazy {
	opacity: 0;
	-webkit-transition: opacity 400ms ease;
	-moz-transition: opacity 400ms ease;
	-ms-transition: opacity 400ms ease;
	-o-transition: opacity 400ms ease;
	transition: opacity 400ms ease;
}
.owl-carousel .owl-item img {
	transform-style: preserve-3d;
}
/* 
 * 	Owl Carousel - Video Plugin
 */
.owl-carousel .owl-video-wrapper {
	position: relative;
	height: 100%;
	background: #000;
}
.owl-carousel .owl-video-play-icon {
	position: absolute;
	height: 80px;
	width: 80px;
	left: 50%;
	top: 50%;
	margin-left: -40px;
	margin-top: -40px; /*background: url('https://www.stadtwerke-velbert.de/fileadmin/css/vendor/owl.video.play.png') no-repeat;*/
	cursor: pointer;
	z-index: 1;
	-webkit-backface-visibility: hidden;
	-webkit-transition: scale 100ms ease;
	-moz-transition: scale 100ms ease;
	-ms-transition: scale 100ms ease;
	-o-transition: scale 100ms ease;
	transition: scale 100ms ease;
}
.owl-carousel .owl-video-play-icon:hover {
	-webkit-transition: scale(1.3, 1.3);
	-moz-transition: scale(1.3, 1.3);
	-ms-transition: scale(1.3, 1.3);
	-o-transition: scale(1.3, 1.3);
	transition: scale(1.3, 1.3);
}
.owl-carousel .owl-video-playing .owl-video-tn, .owl-carousel .owl-video-playing .owl-video-play-icon {
	display: none;
}
.owl-carousel .owl-video-tn {
	opacity: 0;
	height: 100%;
	background-position: center center;
	background-repeat: no-repeat;
	-webkit-background-size: contain;
	-moz-background-size: contain;
	-o-background-size: contain;
	background-size: contain;
	-webkit-transition: opacity 400ms ease;
	-moz-transition: opacity 400ms ease;
	-ms-transition: opacity 400ms ease;
	-o-transition: opacity 400ms ease;
	transition: opacity 400ms ease;
}
.owl-carousel .owl-video-frame {
	position: relative;
	z-index: 1;
}
/* 
 * 	Default theme - Owl Carousel CSS File
 */
.owl-theme .owl-controls {
	margin-top: 0;
	text-align: center;
	-webkit-tap-highlight-color: transparent;
}
.owl-theme .owl-controls .owl-nav div {
	color: white;
	font-size: 14px;
	margin: 5px;
	padding: 4px 7px;
	background: #d6d6d6;
	display: inline-block;
	cursor: pointer;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}
.owl-theme .owl-controls .owl-nav div:hover {
	background: #869791;
	color: white;
	text-decoration: none;
}
.owl-theme .owl-controls .owl-nav .disabled {
	opacity: 0.5;
	cursor: default;
}
.owl-theme .owl-dots .owl-dot {
	display: inline-block;
	zoom: 1;
*display: inline;
}
.owl-theme .owl-dots .owl-dot span {
	width: 10px;
	height: 10px;
	margin: 0 1px;
	background:#fff;
	display: block;
}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
	background: #444;
}

/* ==========================================================================
   $BASE-PICKER
   ========================================================================== */
/**
 * Note: the root picker element should *NOT* be styled more than what’s here.
 */
.picker {
  font-size: 16px;
  text-align: left;
  line-height: 1.2;
  color: #000000;
  position: absolute;
  z-index: 10000;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
/**
 * The picker input element.
 */
.picker__input {
  cursor: default;
}
/**
 * When the picker is opened, the input element is “activated”.
 */
.picker__input.picker__input--active {
  border-color: #0089ec;
}
/**
 * The holder is the only “scrollable” top-level container element.
 */
.picker__holder {
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/*!
 * Classic picker styling for pickadate.js
 * Demo: http://amsul.github.io/pickadate.js
 */
/**
 * Note: the root picker element should *NOT* be styled more than what’s here.
 */
.picker {
  width: 100%;
}
/**
 * The holder is the base of the picker.
 */
.picker__holder {
  position: absolute;
  background: #ffffff;
  border: 1px solid #aaaaaa;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0 0 5px 5px;
  box-sizing: border-box;
  min-width: 176px;
  max-width: 466px;
  max-height: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transform: translateY(-1em) perspective(600px) rotateX(10deg);
          transform: translateY(-1em) perspective(600px) rotateX(10deg);
  transition: -webkit-transform 0.15s ease-out, opacity 0.15s ease-out, max-height 0s 0.15s, border-width 0s 0.15s;
  transition: transform 0.15s ease-out, opacity 0.15s ease-out, max-height 0s 0.15s, border-width 0s 0.15s;
}
/**
 * The frame and wrap work together to ensure that
 * clicks within the picker don’t reach the holder.
 */
.picker__frame {
  padding: 1px;
}
.picker__wrap {
  margin: -1px;
}
/**
 * When the picker opens...
 */
.picker--opened .picker__holder {
  max-height: 25em;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  -moz-opacity: 1;
  opacity: 1;
  border-top-width: 1px;
  border-bottom-width: 1px;
  -webkit-transform: translateY(0) perspective(600px) rotateX(0);
          transform: translateY(0) perspective(600px) rotateX(0);
  transition: -webkit-transform 0.15s ease-out, opacity 0.15s ease-out, max-height 0s, border-width 0s;
  transition: transform 0.15s ease-out, opacity 0.15s ease-out, max-height 0s, border-width 0s;
  box-shadow: 0 6px 18px 1px rgba(0, 0, 0, 0.12);
}

/* ==========================================================================
   $BASE-DATE-PICKER
   ========================================================================== */
/**
 * The picker box.
 */
.picker__box {
  padding: 0 1em;
}
/**
 * The header containing the month and year stuff.
 */
.picker__header {
  text-align: center;
  position: relative;
  margin-top: .75em;
}
/**
 * The month and year labels.
 */
.picker__month,
.picker__year {
  font-weight: 500;
  display: inline-block;
  margin-left: .25em;
  margin-right: .25em;
}
.picker__year {
  color: #999999;
  font-size: .8em;
  font-style: italic;
}
/**
 * The month and year selectors.
 */
.picker__select--month,
.picker__select--year {
  border: 1px solid #b7b7b7;
  height: 2em;
  padding: .5em;
  margin-left: .25em;
  margin-right: .25em;
}
@media (min-width: 24.5em) {
  .picker__select--month,
  .picker__select--year {
    margin-top: -0.5em;
  }
}
.picker__select--month {
  width: 35%;
}
.picker__select--year {
  width: 22.5%;
}
.picker__select--month:focus,
.picker__select--year:focus {
  border-color: #0089ec;
}
/**
 * The month navigation buttons.
 */
.picker__nav--prev,
.picker__nav--next {
  position: absolute;
  padding: .5em 1.25em;
  width: 1em;
  height: 1em;
  box-sizing: content-box;
  top: -0.25em;
}
@media (min-width: 24.5em) {
  .picker__nav--prev,
  .picker__nav--next {
    top: -0.33em;
  }
}
.picker__nav--prev {
  left: -1em;
  padding-right: 1.25em;
}
@media (min-width: 24.5em) {
  .picker__nav--prev {
    padding-right: 1.5em;
  }
}
.picker__nav--next {
  right: -1em;
  padding-left: 1.25em;
}
@media (min-width: 24.5em) {
  .picker__nav--next {
    padding-left: 1.5em;
  }
}
.picker__nav--prev:before,
.picker__nav--next:before {
  content: " ";
  border-top: .5em solid transparent;
  border-bottom: .5em solid transparent;
  border-right: 0.75em solid #000000;
  width: 0;
  height: 0;
  display: block;
  margin: 0 auto;
}
.picker__nav--next:before {
  border-right: 0;
  border-left: 0.75em solid #000000;
}
.picker__nav--prev:hover,
.picker__nav--next:hover {
  cursor: pointer;
  color: #000000;
  background: #b1dcfb;
}
.picker__nav--disabled,
.picker__nav--disabled:hover,
.picker__nav--disabled:before,
.picker__nav--disabled:before:hover {
  cursor: default;
  background: none;
  border-right-color: #f5f5f5;
  border-left-color: #f5f5f5;
}
/**
 * The calendar table of dates
 */
.picker__table {
  text-align: center;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
  font-size: inherit;
  width: 100%;
  margin-top: .75em;
  margin-bottom: .5em;
}
@media (min-height: 33.875em) {
  .picker__table {
    margin-bottom: .75em;
  }
}
.picker__table td {
  margin: 0;
  padding: 0;
}
/**
 * The weekday labels
 */
.picker__weekday {
  width: 14.285714286%;
  font-size: .75em;
  padding-bottom: .25em;
  color: #999999;
  font-weight: 500;
  /* Increase the spacing a tad */
}
@media (min-height: 33.875em) {
  .picker__weekday {
    padding-bottom: .5em;
  }
}
/**
 * The days on the calendar
 */
.picker__day {
  padding: .3125em 0;
  font-weight: 200;
  border: 1px solid transparent;
}
.picker__day--today {
  position: relative;
}
.picker__day--today:before {
  content: " ";
  position: absolute;
  top: 2px;
  right: 2px;
  width: 0;
  height: 0;
  border-top: 0.5em solid #0059bc;
  border-left: .5em solid transparent;
}
.picker__day--disabled:before {
  border-top-color: #aaaaaa;
}
.picker__day--outfocus {
  color: #dddddd;
}
.picker__day--infocus:hover,
.picker__day--outfocus:hover {
  cursor: pointer;
  color: #000000;
  background: #b1dcfb;
}
.picker__day--highlighted {
  border-color: #0089ec;
}
.picker__day--highlighted:hover,
.picker--focused .picker__day--highlighted {
  cursor: pointer;
  color: #000000;
  background: #b1dcfb;
}
.picker__day--selected,
.picker__day--selected:hover,
.picker--focused .picker__day--selected {
  background: #0089ec;
  color: #ffffff;
}
.picker__day--disabled,
.picker__day--disabled:hover,
.picker--focused .picker__day--disabled {
  background: #f5f5f5;
  border-color: #f5f5f5;
  color: #dddddd;
  cursor: default;
}
.picker__day--highlighted.picker__day--disabled,
.picker__day--highlighted.picker__day--disabled:hover {
  background: #bbbbbb;
}
/**
 * The footer containing the "today", "clear", and "close" buttons.
 */
.picker__footer {
  text-align: center;
}
.picker__button--today,
.picker__button--clear,
.picker__button--close {
  border: 1px solid #ffffff;
  background: #ffffff;
  font-size: .8em;
  padding: .66em 0;
  font-weight: bold;
  width: 33%;
  display: inline-block;
  vertical-align: bottom;
}
.picker__button--today:hover,
.picker__button--clear:hover,
.picker__button--close:hover {
  cursor: pointer;
  color: #000000;
  background: #b1dcfb;
  border-bottom-color: #b1dcfb;
}
.picker__button--today:focus,
.picker__button--clear:focus,
.picker__button--close:focus {
  background: #b1dcfb;
  border-color: #0089ec;
  outline: none;
}
.picker__button--today:before,
.picker__button--clear:before,
.picker__button--close:before {
  position: relative;
  display: inline-block;
  height: 0;
}
.picker__button--today:before,
.picker__button--clear:before {
  content: " ";
  margin-right: .45em;
}
.picker__button--today:before {
  top: -0.05em;
  width: 0;
  border-top: 0.66em solid #0059bc;
  border-left: .66em solid transparent;
}
.picker__button--clear:before {
  top: -0.25em;
  width: .66em;
  border-top: 3px solid #ee2200;
}
.picker__button--close:before {
  content: "\D7";
  top: -0.1em;
  vertical-align: top;
  font-size: 1.1em;
  margin-right: .35em;
  color: #777777;
}
.picker__button--today[disabled],
.picker__button--today[disabled]:hover {
  background: #f5f5f5;
  border-color: #f5f5f5;
  color: #dddddd;
  cursor: default;
}
.picker__button--today[disabled]:before {
  border-top-color: #aaaaaa;
}

/* ==========================================================================
   $CLASSIC-DATE-PICKER
   ========================================================================== */

/* ==========================================================================
   $BASE-TIME-PICKER
   ========================================================================== */
/**
 * The list of times.
 */
.picker__list {
  list-style: none;
  padding: 0.75em 0 4.2em;
  margin: 0;
}
/**
 * The times on the clock.
 */
.picker__list-item {
  border-bottom: 1px solid #dddddd;
  border-top: 1px solid #dddddd;
  margin-bottom: -1px;
  position: relative;
  background: #ffffff;
  padding: .75em 1.25em;
}
@media (min-height: 46.75em) {
  .picker__list-item {
    padding: .5em 1em;
  }
}
/* Hovered time */
.picker__list-item:hover {
  cursor: pointer;
  color: #000000;
  background: #b1dcfb;
  border-color: #0089ec;
  z-index: 10;
}
/* Highlighted and hovered/focused time */
.picker__list-item--highlighted {
  border-color: #0089ec;
  z-index: 10;
}
.picker__list-item--highlighted:hover,
.picker--focused .picker__list-item--highlighted {
  cursor: pointer;
  color: #000000;
  background: #b1dcfb;
}
/* Selected and hovered/focused time */
.picker__list-item--selected,
.picker__list-item--selected:hover,
.picker--focused .picker__list-item--selected {
  background: #0089ec;
  color: #ffffff;
  z-index: 10;
}
/* Disabled time */
.picker__list-item--disabled,
.picker__list-item--disabled:hover,
.picker--focused .picker__list-item--disabled {
  background: #f5f5f5;
  border-color: #f5f5f5;
  color: #dddddd;
  cursor: default;
  border-color: #dddddd;
  z-index: auto;
}
/**
 * The clear button
 */
.picker--time .picker__button--clear {
  display: block;
  width: 80%;
  margin: 1em auto 0;
  padding: 1em 1.25em;
  background: none;
  border: 0;
  font-weight: 500;
  font-size: .67em;
  text-align: center;
  text-transform: uppercase;
  color: #666;
}
.picker--time .picker__button--clear:hover,
.picker--time .picker__button--clear:focus {
  color: #000000;
  background: #b1dcfb;
  background: #ee2200;
  border-color: #ee2200;
  cursor: pointer;
  color: #ffffff;
  outline: none;
}
.picker--time .picker__button--clear:before {
  top: -0.25em;
  color: #666;
  font-size: 1.25em;
  font-weight: bold;
}
.picker--time .picker__button--clear:hover:before,
.picker--time .picker__button--clear:focus:before {
  color: #ffffff;
  border-color: #ffffff;
}

/* ==========================================================================
   $CLASSIC-TIME-PICKER
   ========================================================================== */
/**
 * Note: the root picker element should __NOT__ be styled
 * more than what’s here. Style the `.picker__holder` instead.
 */
.picker--time {
  min-width: 256px;
  max-width: 320px;
}
/**
 * The holder is the base of the picker.
 */
.picker--time .picker__holder {
  background: #f5f5f5;
}
@media (min-height: 40.125em) {
  .picker--time .picker__holder {
    font-size: .875em;
  }
}
/**
 * The box contains the list of times.
 */
.picker--time .picker__box {
  padding: 0;
  position: relative;
}

/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */@font-face{font-family:'FontAwesome';src:url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/fontawesome-webfont.eot?v=4.7.0');src:url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'),url('https://www.stadtwerke-velbert.de/fileadmin/css/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');font-weight:normal;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper-pp:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-resistance:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"}.fa-gitlab:before{content:"\f296"}.fa-wpbeginner:before{content:"\f297"}.fa-wpforms:before{content:"\f298"}.fa-envira:before{content:"\f299"}.fa-universal-access:before{content:"\f29a"}.fa-wheelchair-alt:before{content:"\f29b"}.fa-question-circle-o:before{content:"\f29c"}.fa-blind:before{content:"\f29d"}.fa-audio-description:before{content:"\f29e"}.fa-volume-control-phone:before{content:"\f2a0"}.fa-braille:before{content:"\f2a1"}.fa-assistive-listening-systems:before{content:"\f2a2"}.fa-asl-interpreting:before,.fa-american-sign-language-interpreting:before{content:"\f2a3"}.fa-deafness:before,.fa-hard-of-hearing:before,.fa-deaf:before{content:"\f2a4"}.fa-glide:before{content:"\f2a5"}.fa-glide-g:before{content:"\f2a6"}.fa-signing:before,.fa-sign-language:before{content:"\f2a7"}.fa-low-vision:before{content:"\f2a8"}.fa-viadeo:before{content:"\f2a9"}.fa-viadeo-square:before{content:"\f2aa"}.fa-snapchat:before{content:"\f2ab"}.fa-snapchat-ghost:before{content:"\f2ac"}.fa-snapchat-square:before{content:"\f2ad"}.fa-pied-piper:before{content:"\f2ae"}.fa-first-order:before{content:"\f2b0"}.fa-yoast:before{content:"\f2b1"}.fa-themeisle:before{content:"\f2b2"}.fa-google-plus-circle:before,.fa-google-plus-official:before{content:"\f2b3"}.fa-fa:before,.fa-font-awesome:before{content:"\f2b4"}.fa-handshake-o:before{content:"\f2b5"}.fa-envelope-open:before{content:"\f2b6"}.fa-envelope-open-o:before{content:"\f2b7"}.fa-linode:before{content:"\f2b8"}.fa-address-book:before{content:"\f2b9"}.fa-address-book-o:before{content:"\f2ba"}.fa-vcard:before,.fa-address-card:before{content:"\f2bb"}.fa-vcard-o:before,.fa-address-card-o:before{content:"\f2bc"}.fa-user-circle:before{content:"\f2bd"}.fa-user-circle-o:before{content:"\f2be"}.fa-user-o:before{content:"\f2c0"}.fa-id-badge:before{content:"\f2c1"}.fa-drivers-license:before,.fa-id-card:before{content:"\f2c2"}.fa-drivers-license-o:before,.fa-id-card-o:before{content:"\f2c3"}.fa-quora:before{content:"\f2c4"}.fa-free-code-camp:before{content:"\f2c5"}.fa-telegram:before{content:"\f2c6"}.fa-thermometer-4:before,.fa-thermometer:before,.fa-thermometer-full:before{content:"\f2c7"}.fa-thermometer-3:before,.fa-thermometer-three-quarters:before{content:"\f2c8"}.fa-thermometer-2:before,.fa-thermometer-half:before{content:"\f2c9"}.fa-thermometer-1:before,.fa-thermometer-quarter:before{content:"\f2ca"}.fa-thermometer-0:before,.fa-thermometer-empty:before{content:"\f2cb"}.fa-shower:before{content:"\f2cc"}.fa-bathtub:before,.fa-s15:before,.fa-bath:before{content:"\f2cd"}.fa-podcast:before{content:"\f2ce"}.fa-window-maximize:before{content:"\f2d0"}.fa-window-minimize:before{content:"\f2d1"}.fa-window-restore:before{content:"\f2d2"}.fa-times-rectangle:before,.fa-window-close:before{content:"\f2d3"}.fa-times-rectangle-o:before,.fa-window-close-o:before{content:"\f2d4"}.fa-bandcamp:before{content:"\f2d5"}.fa-grav:before{content:"\f2d6"}.fa-etsy:before{content:"\f2d7"}.fa-imdb:before{content:"\f2d8"}.fa-ravelry:before{content:"\f2d9"}.fa-eercast:before{content:"\f2da"}.fa-microchip:before{content:"\f2db"}.fa-snowflake-o:before{content:"\f2dc"}.fa-superpowers:before{content:"\f2dd"}.fa-wpexplorer:before{content:"\f2de"}.fa-meetup:before{content:"\f2e0"}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}

/* =Import Required styles
========================================================================================*/

/**/


/* =Box Sizing
========================================================================================*/

* {
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}

*:before,
*:after {
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="search"],
textarea,
select,
input[type="button"],
input[type="submit"],
button {
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}


/* =Deafult Tag & General Classes
========================================================================================*/

html,
body {
    -webkit-font-smoothing: antialiased;
    -moz-font-smoothing: antialiased;
    -ms-font-smoothing: antialiased;
    font-smoothing: antialiased;
    /* Fix for webkit rendering */
    -webkit-text-size-adjust: 100%;
}

body {
    font: 18px/1.5 'Oxygen', Arial, Helvetica, sans-serif;
    color: #444;
}

img {
    vertical-align: top;
    border: 0;
    height: auto;
}

a,
input[type="button"],
input[type="submit"],
button,
table th,
table td,
.news-box {
    transition: background-color 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000), color 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
}
i.custom {
    transition: color 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
}

.allanim,
#mainmenu li a:before,
#header,
.navigation-row,
.pools-list-trigger:after,
.owl-theme .owl-dots .owl-dot span,
.category-nav > ul > li > a,
#mainmenu > ul > li > a:after,
.benefit-details p,
.benefit-col h3,
#menu,
.benefit-details,
.navtrigger:after,
.category-nav > ul > li > a:after,
.megamenu-col > h3 a:after,
.tabMobiletrigger,
.tabMobiletrigger:after,
#cources li h4,
#cources li a:after,
.category-box {
    transition: all 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
}

a {
    color: inherit;
    text-decoration: underline;
}

a:hover {
    text-decoration: underline;
}

@media screen and (-ms-high-contrast:active),
(-ms-high-contrast:none) {
    a:active {
        background-color: transparent;
    }
}

a.speciallink {
    text-align: center;
    width: 100%;
    display: block;
}

strong {
    font-weight: bold;
}

small,
.small {
    font-size: 85%;
}

sub,
sup {
    font-size: 80%
}

p {
    margin: 0 0 20px 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    line-height: 1.3;
    margin: 0 0 10px;
    font-weight: 700;
}

h1,
h2 {
    font-size: 36px;
    margin-bottom: 35px;
}

h3 {
    font-size: 28px;
}

h4 {
    font-size: 24px;
}

h5 {
    font-size: 20px;
}

h6 {
    font-size: 18px;
}

.align-center {
    text-align: center;
}

.home h1,
.home h2 {
    color: #004494;
}

.wasser h1,
.wasser h2 {
    color: #52bfd5;
}

.strom h1,
.strom h2 {
    color: #e74a16;
}

.gas h1,
.gas h2 {
    color: #fecb01;
}

.baeder h1,
.baeder h2 {
    color: #00c8f8
}

.service h1,
.service h2 {
    color: #004494
}

.energie h1,
.energie h2 {
    color: #97BF0D
}

.breitband h1,
.breitband h2 {
    color: #52B06D
}

.cf:after,
.topnav:after,
.tabnav:after,
.contact-details {
    content: "";
    display: table;
    clear: both;
}

.nodisplay {
    display: none;
}

.nodisplay_strict {
    display: none !important;
}

.alignleft {
    float: left;
}

.alignright {
    float: right;
}

:focus,
a:focus {
    outline: none;
}

a:focus,
a:hover {
    text-decoration: none;
}

.text_cont p {
    padding: 0 0 10px 0;
}

.nopad p,
p.nopad {
    padding: 0;
}

.spacer {
    height: 25px;
}


/* =Blockquote & highlight
========================================================================================*/

blockquote {
    border-left: solid 6px #e1e1e1;
    margin-left: 0;
    padding-left: 15px;
}

blockquote small {
    font-size: 85%;
    display: block;
    color: #999;
    padding: 5px 0 0 0;
    font-style: italic;
}

.highlight,
mark {
    background: #fffd64;
}


/* =Text Size, color & Custom list style
========================================================================================*/

.text-bigger {
    font-size: 22px;
}

.text-big {
    font-size: 20px;
}

.text-small {
    font-size: 12px;
}

.text-muted {
    color: #777;
}

.text-primary {
    color: #f34834;
}

.text-secondary {
    color: #7979a9;
}

.bg-muted {
    background: #777;
}

.bg-primary {
    background: #e34f26;
}

.bg-secondary {
    background: #7979a9;
}

.text-lc {
    text-transform: lowercase;
}

.text-uc {
    text-transform: uppercase;
}

.has-show {
    display: block !important;
}

.has-hide {
    display: none !important;
}

.has-error {
    padding: 8px 2px 0;
    display: block;
    color: #e74a16;
}

input.has-error {
    border: solid 1px #f34834 !important;
}

.tall-space {
    margin: 45px 0;
}

.medium-space {
    margin: 20px 0;
}

.custom-arrow-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.custom-arrow-list li {
    position: relative;
    padding-left: 20px;
}

.custom-arrow-list li:before {
    content: '';
    border: solid 2px #bbb;
    border-left: 0;
    border-top: 0;
    position: absolute;
    left: 1px;
    top: 9px;
    transform: rotate(-45deg);
    width: 7px;
    height: 7px;
}


/* =Divider Width
========================================================================================*/

hr {
    border: 0;
    border-bottom: solid 1px #e1e1e1;
    margin: 20px 0;
    position: relative;
    width: 100%;
}

hr:before {
    background: #888;
    bottom: 0;
    content: "";
    display: block;
    height: 1px;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 0;
    position: absolute;
    width: 40px;
}

.dashed-border {
    border-bottom-style: dashed
}

.dotted-border {
    border-bottom-style: dotted;
}

.double-border {
    border-bottom-style: double;
    border-bottom-width: 3px;
}

.double-border:before {
    height: 3px;
}


/* =Drop Caps
========================================================================================*/

p.drop-caps:first-child:first-letter {
    float: left;
    font-size: 75px;
    line-height: 60px;
    padding: 4px;
    margin-right: 5px;
    margin-top: 5px;
    font-family: Georgia;
}

p.drop-caps.secundary:first-child:first-letter {
    background-color: #e74a16;
    color: #fff;
    padding: 6px;
    margin-right: 11px;
    border-radius: 4px;
}


/* =Image Caps
========================================================================================*/

.image-block {
    padding: 25px 0;
}

.image-block:after {
    display: table;
    clear: both;
    content: '';
}

.image-block figure {
    width: 33.33333%;
    float: left;
    position: relative;
    border: solid 1px #e5e5e5;
    padding: 5px;
    background: #fff;
    margin: 0 20px 10px 0;
}

.image-block figure img, .roundedbox img {
   /* width: 100%;*/
    height: auto;
}

.image-block.right-align figure {
    float: right;
    margin: 0 0 10px 20px;
}

.image-block figcaption {
    display: block;
    font-weight: 700;
    position: absolute;
    left: 5px;
    right: 5px;
    bottom: 5px;
    margin: 0 auto;
    background: rgba(0, 0, 0, 0.40);
    padding: 13px 10px;
    color: #fff;
    text-align: center;
    line-height: 1.25;
}

.image-block h3 {
    margin-bottom: 20px;
}


/* =Section Caps
========================================================================================*/

.section-block {
    padding: 25px 0;
    background: #fff;
}


/* =Layout Width
========================================================================================*/

#wrapper {}

.container {
    max-width: 100%;
}

#maincontent {
    padding: 30px 0 0px;
}

#maincontent .csc-default > .container {
    padding: 40px 0 40px;
}

#maincontent .Tx-Formhandler .csc-default > .container {
    padding: 0px 15px 0px 0px;
}

.one-column {
    width: auto !important;
    float: none !important;
}

#maincontent .container.teaserreihe {
    padding: 0px;
    margin-bottom: 30px;
}

#maincontent .container.teaserreihe.padding-korrektur {
    margin-top: 0px;
    margin-bottom: 50px;
}


/* =Common-Page (common classes for all pages)
========================================================================================*/

#header {
    position: absolute;
    width: 100%;
    padding: 13px 0 0;
    left: 0;
    z-index: 999999999999;
}
.naivigation-row {
    z-index: 99999;
}

#header.fixed {
    padding: 0;
    position: fixed;
    top: 0;
    width: 100%;
}

#header.fixed .navigation-row {
    margin-top: 0;
    border-top: 1px solid #004494;
    border-bottom: 1px solid #004494;
}

#header a {
    text-decoration: none;
}

#logo {
    float: left;
    width: 530px;
    position: relative;
}

#logo img {
    max-width: 90%;
    height: auto;
    width: auto;
}

.header-right-section {
    float: right;
    font-size: 12px;
    position: relative;
    padding-right: 75px;
}

.header-top {
    background: #fff;
    padding: 18px 0 5px;
    position: relative;
}

.topnav {
    margin: 0 -4px 7px;
    padding: 0;
    list-style: none;
}

.topnav li {
    float: left;
    padding: 0 4px;
    width: 66%;
    text-align: center;
}
.topnav li:last-of-type {
    width: 34%;
}

.topnav li a {
    background: #ebebeb;
    color: #444;
    padding: 3px 8px 4px;
    display: block;
}

.topnav li a:before {
    content: '>';
    margin-right: 4px;
}

.topnav li a:hover {
    background: #00c8f8;
    color: #fff;
    text-decoration: none;
}

.search-box input[type="search"],
.search-box input[type="text"] {
    height: 25px;
    padding: 2px 10px 2px 28px;
    background: #ebebeb;
    font-size: 12px;
    border: 0;
}

.search-box input[type="search"]:focus {
    border: 0;
}

.action-box {
    width: 258px;
}

.search {
    position: relative;
}

.search:before {
    content: '';
    position: absolute;
    left: 4px;
    top: 4px;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat -105px 0;
    width: 18px;
    height: 18px;
}

.roundedbox {
    border-radius: 15px;
    padding: 25px 38px 45px;
    color: white;
}

.container > .csc-default > .roundedbox {
    margin: 25px 0px 0px;
}

.home .roundedbox {
    background-color: #004494;
}

.wasser .roundedbox {
    background-color: #52bfd5;
}

.strom .roundedbox {
    background-color: #e74a16;
}

.gas .roundedbox {
    background-color: #fecb01;
}

.baeder .roundedbox {
    background: #00c8f8;
}

.service .roundedbox {
    background-color: #004494
}

.energie .roundedbox {
    background-color: #97BF0D
}
.breitband .roundedbox {
    background-color: #52B06D
}

.roundedbox h1,
.roundedbox h2,
.roundedbox h3,
.roundedbox h4,
.roundedbox h5 {
    color: inherit;
}

.vcenter {
    transform: translateY(50%);
}

.sidebarContainer {
    position: relative;
}

.sidebarContainer.container > .container {
    padding-left: 0px;
}


/* Allgemeine Styles für Banner */

.banner-wrapper {
    margin-bottom: 80px;
    color: white;
    margin-top: 0px;
}

.banner-wrapper > h2 {
    margin: 0 auto 35px;
    max-width: 1170px;
    width: 100%;
    padding: 0 15px;
    text-align: center;
}

.bannerbg {
    padding: 98px 0 98px;
    position: relative;
}

.banneroverlay {
    padding: 30px 0 15px;
    margin-bottom: -98px;
}

.banner-wrapper .roundedbox {
    margin-top: 50px;
    margin-bottom: 50px;
}

.banner-wrapper .padding-korrektur .roundedbox {
    margin-top: -20px;
    margin-bottom: 0px;
}

.bannerbg .banner-info-bubble {
    position: relative;
    top: 0;
    left: 0;
    float: right;
    margin-left: 100px;
    text-align: center;
    margin-top: -20px;
    margin-bottom: 40px;
}

.home .banneroverlay {
    background: rgba(0, 68, 148, 0.8);
}

.wasser .banneroverlay {
    background: rgba(82, 191, 213, 0.8);
}

.strom .banneroverlay {
    background: rgba(231, 74, 22, 0.8);
}

.gas .banneroverlay {
    background: rgba(254, 203, 1, 0.8);
}

.baeder .banneroverlay {
    background: rgba(0, 200, 248, 0.8);
}

.service .banneroverlay {
    background: rgba(214, 214, 214, 0.8);
}

.energie .banneroverlay {
    background: rgba(151, 191, 13, 0.8);
}
.breitband .banneroverlay {
    background: rgba(82, 176, 109, 0.8);
}


/* Responsive Menu line icon*/

#menu {
    color: #444;
    text-align: center;
    background: #ebebeb;
    padding-top: 8px;
    width: 68px;
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
}

.menulines-button {
    padding: 0;
    cursor: pointer;
    -webkit-user-select: none;
       -moz-user-select: none;
        -ms-user-select: none;
            user-select: none;
    color: #fff;
    text-decoration: none;
}

.menulines-button em {
    font-style: normal;
    display: block;
    margin-bottom: 2px;
}

.menulines-button:hover {
    text-decoration: none;
}

.menulines {
    display: inline-block;
    width: 28px;
    height: 4px;
    margin: 0 auto;
    background: #aaa;
    position: relative;
    transition: all 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
    opacity: 1;
}

.menulines:before,
.menulines:after {
    display: inline-block;
    width: 28px;
    height: 4px;
    background: #aaa;
    position: absolute;
    left: 0;
    content: '';
    transition: all 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
    transform-origin: 0.28571rem center;
}

.menulines:before {
    top: 7px;
}

.menulines:after {
    top: -7px;
}

#menu.menuopen {
    height: 200%;
}

.navigation-row {
    background: #fff;
    margin-top: 15px;
}

#mainmenu {
    float: left;
    font-size: 16px;
}

#mainmenu > ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

#mainmenu > ul > li {
    float: left;
}

#mainmenu > ul > li {
    text-transform: uppercase;
    font-weight: 700;
    line-height: 50px;
}

#mainmenu > ul > li > a {
    text-decoration: none;
    padding: 0 23px;
    display: block;
    color: #004494;
    position: relative;
}

#mainmenu > ul > li > a:after,
.pools-list-trigger:after,
.pools-list-trigger.list-open:after {
    content: '';
    width: 0;
    height: 0;
    border-top: 26px solid #ebebeb;
    border-left: 35px solid transparent;
    border-right: 35px solid transparent;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
}

#mainmenu > ul > li:not(.hasnav) > a:after {
    display: none;
}

#mainmenu li a span,
.pools-list-trigger span {
    position: relative;
    z-index: 1;
}

.navigation-row .pools-list-trigger span {
    font-size: 16px
}

#mainmenu > ul > li.active > a,
#mainmenu > ul > li > a:hover,
#mainmenu > ul > li > a.current {
    background: #ebebeb;
}

#mainmenu > ul > li.active > a:after,
#mainmenu > ul > li > a:hover:after,
.pools-list-trigger:hover:after,
.pools-list-trigger:hover:after,
.hovered .pools-list-trigger:after,
#mainmenu > ul > li > a.current:after {
    opacity: 1;
    top: 100%;
}

.navigation-row .category-nav > ul {
    text-align: center;
    margin: 0;
    font-size: 0;
    background: #fff;
    padding: 0 15px 25px 10px;
    position: relative;
}

.navigation-row .category-nav > ul > li {
    vertical-align: middle;
    font-size: 16px;
    width: 50%;
    height: 80px;
    margin-top: 0px;
    padding: 20px 13px 0px;
    display: block;
}
 .navigation-row .category-nav > ul > li.nav16,  .navigation-row .category-nav > ul > li.nav17,  .navigation-row .category-nav > ul > li.nav18,  .navigation-row .category-nav > ul > li.nav130,  .navigation-row .firmenkunden .category-nav > ul > li.nav21,  .navigation-row .firmenkunden .category-nav > ul > li.nav23,  .navigation-row .firmenkunden .category-nav > ul > li.nav23 {
    width: 44%;
}

.category-nav > ul > li > a {
    position: relative;
    line-height: 1.3;
    margin-bottom: 0px;
    text-align: left;
    height: 65px;
    padding-left: 75px;
    margin-left: 0px;
    display: inline-block;
    width: 100%;
}

 .navigation-row .category-nav > ul > li.nav16,  .navigation-row .category-nav > ul > li.nav17,  .navigation-row .category-nav > ul > li.nav18,  .navigation-row .category-nav > ul > li.nav130,  .navigation-row .firmenkunden .category-nav > ul > li.nav21,  .navigation-row .firmenkunden .category-nav > ul > li.nav23,  .navigation-row .firmenkunden .category-nav > ul > li.nav23 {
    width: 44%;
}
 .navigation-row .category-nav > ul > li.nav131,  .navigation-row .category-nav > ul > li.nav71,  .navigation-row .category-nav > ul > li.nav19,  .navigation-row .category-nav > ul > li.nav20,  .navigation-row .firmenkunden .category-nav > ul > li.nav24,  .navigation-row .firmenkunden .category-nav > ul > li.nav132,  .navigation-row .firmenkunden .category-nav > ul > li.nav133 {
    width: 55%;
}

.category-nav > ul > li > a:before {
    content: '';
    display: inline-block;
    margin-bottom: -25px;
    margin-left: -75px;
    margin-right: 10px;
    height: 65px !important;
    width: 65px !important;
    background-size: 65px 65px !important;
}
 .category-nav > ul > li.nav16 > a:before,  .category-nav > ul > li.nav21 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/wasser.png') no-repeat;
}
 .category-nav > ul > li.nav17 > a:before,  .category-nav > ul > li.nav22 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/strom.png') no-repeat;
}
 .category-nav > ul > li.nav18 > a:before,  .category-nav > ul > li.nav23 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/gas.png') no-repeat;
}
 .category-nav > ul > li.nav131 > a:before,  .category-nav > ul > li.nav133 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/energiedl.png') no-repeat;
}
 .category-nav > ul > li.nav19 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/energieberatung.png') no-repeat;
}
 .category-nav > ul > li.nav20 > a:before,  .category-nav > ul > li.nav24 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/service.png') no-repeat;
}
 .category-nav > ul > li.nav71 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/kundenkonto.png') no-repeat;
}
 .category-nav > ul > li.nav130 > a:before,  .category-nav > ul > li.nav132 > a:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/menu_icons/breitband.png') no-repeat;
}
 .category-nav > ul > li.nav131 > a:before,  .category-nav > ul > li.nav133 > a:before,  .category-nav > ul > li.nav19 > a:before,  .category-nav > ul > li.nav71 > a:before {
    margin-bottom: -34px;
}
 .navigation-row .category-nav > ul > li.nav131,  .navigation-row .category-nav > ul > li.nav19,  .navigation-row .category-nav > ul > li.nav71,  .navigation-row .category-nav > ul > li.nav20,  .navigation-row .firmenkunden .category-nav > ul > li.nav24,  .navigation-row .firmenkunden .category-nav > ul > li.nav132,  .navigation-row .firmenkunden .category-nav > ul > li.nav133 {
    position: absolute;
    left: 45%;
}
 .navigation-row .category-nav > ul > li.nav19 {
    width: 44%;
}
 .navigation-row .category-nav > ul > li.nav131,  .navigation-row .firmenkunden .category-nav > ul > li.nav132 {
    top: 0px;
}
 .navigation-row .category-nav > ul > li.nav19,  .navigation-row .firmenkunden .category-nav > ul > li.nav133 {
    top: 80px;
}
 .navigation-row .category-nav > ul > li.nav71,  .navigation-row .firmenkunden .category-nav > ul > li.nav24 {
    top: 160px;
}
 .navigation-row .category-nav > ul > li.nav20 {
    top: 240px;
}

.category-nav > ul > li.gas > a:before {
    background-position: -67px 0;
    width: 44px;
}

.category-nav > ul > li.wasser > a:before{
    background-position: -113px 0;
    width: 46px;
}

.category-nav > ul > li.energie > a:before{
    background-position: -161px 0;
    width: 48px;
}

.category-nav > ul > li.service > a:before{
    background-position: -211px 0;
    width: 50px;
}


.category-nav {
    position: absolute;
    width: 480px;
    padding-top: 37px;
    display: none;
    top: 100%;
    background-color: rgba(255,255,255,0.01); /* bugfix5000 */
}

.firmenkunden .category-nav {
    width: 480px;
}
.navunternehmen .category-nav {
    width: 380px;
}

.navunternehmen .category-nav > ul {
    padding: 0px;
}

.navunternehmen .category-nav > ul > li {
    font-weight: 400;
    display: block;
    vertical-align: middle;
    font-size: 16px;
    margin-top: 0px;
    padding: 0px;
    width: 100%;
    height: 30px;
    padding-top: 0px;
}

.navunternehmen .category-nav > ul > li > a {
    padding: 5px 20px;
    height: auto;
    text-align: center;
    display: block;
}

.navunternehmen .category-nav > ul > li > a:before {
    height: 0px !important;
    width: 0px !important;
}

.navunternehmen .category-nav > ul > li.active > a,
.navunternehmen .category-nav > ul > li > a:hover {
    background-color: #004494;
    color: white;
}

.submenu.category-nav > ul > li > a {
    opacity: 0.3;
}

.submenu.category-nav > ul > li > a.current,
.submenu.category-nav > ul > li > a:hover {
    opacity: 1;
}

.sub-navigation {
    position: absolute;
    left: 100%;
    top: 0;
    background: #ebebeb;
    width: 400px;
    display: none;
    min-height: 100%;
}

.sub-navigation ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.sub-navigation h4 {
    background: #e74a16;
    font-size: 16px;
    color: #fff;
    font-weight: 400;
    padding: 5px 0;
    margin: 0;
}

.sub-navigation h4 a {
    display: block;
}

.sub-navigation ul li a {
    font-weight: 400;
    line-height: 1.3;
    display: block;
    padding: 5px 15px;
    transition: none;
}

.sub-navigation ul li a:hover,
.sub-navigation ul li.active a {
    color: #fff;
    background: rgba(231, 74, 22, 0.5);
}

.sub-navigation.subWasser ul li a:hover,
.sub-navigation.subWasser ul li.active a {
    color: #004494;
    background: rgba(82, 191, 213, 0.5);
}

.sub-navigation.subWasser h4 {
    background: #52bfd5;
}

.sub-navigation.subGas ul li a:hover,
.sub-navigation.subGas ul li.active a {
    color: #004494;
    background: rgba(254, 203, 1, 0.5);
}

.sub-navigation.subGas h4 {
    background: #fecb01;
}

.sub-navigation.subEnergie ul li a:hover,
.sub-navigation.subEnergie ul li.active a,
.sub-navigation.subService ul li a:hover,
.sub-navigation.subService ul li.active a {
    color: #fff;
    background: rgba(0, 68, 148, 0.5);
}

.sub-navigation.subEnergie h4,
.sub-navigation.subService h4 {
    background: #004494;
}

.sub-navigation.subBreitband h4 {
    background-color: #52B06D;

}
.sub-navigation.subBreitband ul li a:hover,
.sub-navigation.subBreitband ul li.active {
    background: rgba(82, 176, 109, 0.5);
}

.pools-info-box {
    float: right;
    position: relative;
}

.pools-list-trigger {
    background: #00c8f8;
    color: #fff;
    display: block;
    line-height: 50px;
    padding: 0 55px;
    position: relative;
    font-weight: 700;
    font-size: 14px;
    text-transform: uppercase;
}

.pools-list-trigger:hover {
    text-decoration: none;
}

.pools-list {
    position: absolute;
    top: 100%;
    padding-top: 37px;
    width: 100%;
    display: none;
}

.pools-list ul {
    margin: 0;
    padding: 0;
    list-style: none;
    background: #fff;
}

.pools-list ul li a {
    color: #004494;
    text-align: center;
    display: block;
    text-transform: uppercase;
    padding: 5px 20px;
}

.pools-list ul li a:hover,
.pools-list ul li.active a {
    color: #fff;
    background: #00c8f8;
}

.pools-list-trigger:after,
.pools-list-trigger.list-open:after {
    border-top-color: #00c8f8;
}

.megamenu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-width: 1140px;
    width: 100%;
    margin: 0 auto;
    background: #ebebeb;
    z-index: 3;
    margin-top: 15px;
    display: none;
    z-index: 999999;
 max-height: 500px;
 overflow-y:scroll;
 font-size: 16px;
}

.fixed .megamenu {
    margin-top: 0;
}

.megamenu .row {
    margin: 0;
}

.megamenu a {
    display: block;
}

.megamenu .col-sm-3 {
    padding: 0;
}

.megamenu-col > h3 {
    margin: 0;
    border-bottom: 1px solid #fff;
    border-right: 1px solid #fff;
}

.megamenu-col > h3 a {
    background: #d6d6d6;
    display: block;
    text-align: center;
    font-size: 14px;
    text-transform: uppercase;
    color: #004494;
    line-height: 51px;
    padding: 0 15px;
}

.category-box {
    border-right: 1px solid #fff;
    position: relative;
    padding: 10px 10px 10px 19px;
}

.category-box:before {
    content: '';
    width: 9px;
    height: 100%;
    background: #d6d6d6;
    top: 0;
    left: 0;
    position: absolute;
}

.category-box:hover {
    background: #d6d6d6;
    color: #f8aa00;
}

.category-box:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background: #fff;
}

.category-box h4 {
    text-transform: uppercase;
    font-size: 16px;
    margin: 0;
}

.category-box h4 a {
    color: #444;
}

.category-box ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.category-box li a {
    color: #444;
}

.category-box.wasser-box:before,
.category-box.box16:before,
.category-box.box21:before,
.category-box.box16:hover,
.category-box.box21:hover,
.category-box.box16.active,
.category-box.box21.active {
    background: #52bfd5;
}

.category-box.box16.active a, .category-box.box21.active a,
.category-box.box16:hover a, .category-box.box21:hover a {
    color: #ebebeb;
}

.category-box.box16 li a:hover,
.category-box.box16 h4 a:hover,
.category-box.box21 li a:hover,
.category-box.box21 h4 a:hover,
.category-box.box16 li.active a,
.category-box.box16 h4.active a,
.category-box.box21 li.active a,
.category-box.box21 h4.active a{
    color: #004494;
}

.category-box.strom-box:before,
.category-box.box17:before,
.category-box.box22:before,
.category-box.box17:hover,
.category-box.box22:hover,
.category-box.box17.active,
.category-box.box22.active {
    background: #e74a16;
}

.category-box.strom-box li a:hover,
.category-box.strom-box h4 a:hover,
.category-box.box17 li a:hover,
.category-box.box17 h4 a:hover,
.category-box.box22 li a:hover,
.category-box.box22 h4 a:hover,
.category-box.box17 li.active a,
.category-box.box17 h4.active a,
.category-box.box22 li.active a,
.category-box.box22 h4.active a {
    color: #f8aa00;
}

.category-box.gas-box:before,
.category-box.box18:before,
.category-box.box23:before,
.category-box.box18:hover,
.category-box.box23:hover,
.category-box.box18.active,
.category-box.box23.active {
    background: #fecb01;
}

.category-box.gas-box li a:hover,
.category-box.gas-box h4 a:hover,
.category-box.box18 li a:hover,
.category-box.box18 h4 a:hover,
.category-box.box23 li a:hover,
.category-box.box23 h4 a:hover,
.category-box.box18 li.active a,
.category-box.box18 h4.active a,
.category-box.box23 li.active a,
.category-box.box23 h4.active a {
    color: #ebebeb;
}

.category-box.box130:before,
.category-box.box132:before,
.category-box.box130:hover,
.category-box.box132:hover,
.category-box.box130.active,
.category-box.box132.active {
    background: #52B06D;
}
.category-box.box131:before,
.category-box.box133:before,
.category-box.box131:hover,
.category-box.box133:hover,
.category-box.box131.active,
.category-box.box133.active {
    background: #97BF0D;
}

.category-box li a:hover,
.category-box h4 a:hover,
.category-box li.active a,
.category-box h4.active a {
    color: #f8aa00;
}

.category-box.energie-box h4 a:hover,
.category-box.box19 h4 a:hover,
.category-box.box19 h4.active a {
    color: #f8aa00;
}

.megamenu-col.pools-col > h3 a {
    background: #00c8f8;
    color: #fff;
}

.pools-col .category-box:before,
.pools-col .category-box:hover,
.pools-col .category-box.active {
    background: #00c8f8;
}

.pools-col .category-box:hover > h4 > a,
.pools-col .category-box.active > h4 > a {
    color: #f8aa00;
}

.pools-col .category-box h4 a:hover,
.pools-col .category-box h4.active a {
    color: #f8aa00;
}

.megamenu-col.company-col > h3 a {
    background: #004494;
    color: #fff;
}

.company-col .category-box:before,
.company-col .category-box:hover,
.company-col .category-box.active {
    background: #004494;

}
.company-col .category-box.active a, .company-col .category-box:hover a {
    color: #ebebeb;
}

.company-col .category-box a:hover, .company-col .category-box .active a{
    color: #f8aa00;
}

.main-banner {
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/banner-01.jpg') no-repeat 50% 0;
    -ms-background-size: cover;
    background-size: cover;
    padding-top: 33.95833333333333%;
  position: relative;
}
.main-banner.small-banner {
  /*padding-top: 50vh;*/
}
.main-banner.small-banner.overlay-wrapper  {
  padding-top: 100px;
}
.fullscreen-banner {
    min-height: 100vh;
    padding-bottom: 10px;
    background-size: cover;
    background-position: center;
}
.fullscreen-banner .row.heading {
    margin-top:200px;
}
.fullscreen-banner .row.heading > div {
    margin-bottom:-15px;
}
.fullscreen-banner h2 {
    font-size: 58px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.fullscreen-banner h2 img {
    height: 100px;
    margin-right: 15px;
}
.anchor-teaser {
    padding: 5px;
}
.anchor-teaser-double .half {
    height: 50%;
}
.anchor-teaser-double .half + .half {
    border-top: 2px dotted #444;
    padding-top: 10px;
}
.stoerer-overlay {
    /*position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;*/
    height: 20vh;
    min-height: 270px;
}
.stoerer-overlay img.stoerer-links {
    position: absolute;
    left: 0;
    bottom: 0px;
    /*max-width: 23vw;*/
    max-height: 50%;
    max-width: 28%;
    width: auto;
   /* max-height: calc(100% - 180px);*/
}

.stoerer-overlay img.stoerer-rechts {
    position: absolute;
    right: 2vw;
    bottom: 10px;
    width: auto;
    max-height: 45%;
    max-width: 23%;
    /*width: 20%;
    width: 21vw;
    max-height: calc(100% - 180px);*/
}
.stoerer-overlay .stoerer-overlay-text {
    position: absolute;
    left: 27vw;
    bottom: 20px;
    width: 46vw;
    max-height: calc(100% - 200px);
}
.stoerer-overlay-text p {
    margin-top:-40px;
}
.stoerer-overlay-text h2 {
    font-size: 56px;
    font-weight: normal;
    text-transform:uppercase;
    text-align:center;
    color:#00a5c8;
}
.stoerer-overlay-text h3 {
    font-size: 32px;
    font-weight: normal;
    text-transform:uppercase;
    text-align:center;
    color:#00a5c8;
    margin-top:-40px;
}

.stoerer-overlay-text h4 {
    font-size: 20px;
    font-weight: bold;
    text-transform:uppercase;
    text-align:center;
    color:#41ba7d;
    margin-top:-10px;
}
.stoerer-overlay-text img {
    max-width:100%;
}


.footer-block {
    background: #004494;
    color: #fff;
    text-align: center;
    padding: 70px 0 70px;
}

#footer {
    margin-top: 60px;
}

#footer:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/footer-bg.png') no-repeat 0 0;
    display: block;
    height: 231px;
    max-width: 1140px;
    width: 100%;
    margin: 0 auto;
}
.combert-home #footer:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/footer-bg-combert.png') no-repeat 0 0;
}
.combert-home .footer-block {
    background: #41BA7D;
}

#footer h3 {
    margin-bottom: 55px;
    font-size: 36px;
}

.contact-box {
    border: 5px solid #fff;
    border-radius: 15px;
    padding: 115px 20px 20px;
    font-size: 20px;
    font-weight: 700;
    position: relative;
    display: block;
    color: #fff;
}

.contact-box:hover {
    background: #1772dd;
}

.contact-box:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat 0 -46px;
    width: 70px;
    height: 70px;
    left: 0;
    right: 0;
    margin: 0 auto;
    position: absolute;
    top: 20px;
}

.contact-label {
    display: table;
    width: 100%;
}

.contact-label span {
    display: table-cell;
    vertical-align: bottom;
}

.retail-service:before {
    background-position: -72px -46px;
}

.email-service:before {
    background-position: -144px -46px;
}

.location-col:before {
    background-position: -216px -46px;
}

.service-center:before {
    background-position: 0 -118px;
}

.ausbau:before {
    background: url('https://www.stadtwerke-velbert.de/medien/Bilder/breitband/microsite/icon-ausbaustatus.png');
    background-size: 70px;
}

.press-contact:before {
    background: url('https://www.stadtwerke-velbert.de/medien/Bilder/kko.png');
    background-size: 70px;
}


.newsletter:before {
background: url('https://www.stadtwerke-velbert.de/medien/Bilder/breitband/microsite/icons/newsletter_footer.png');
background-size: 70px;
}


.login:before {
background: url('https://www.stadtwerke-velbert.de/medien/Bilder/breitband/microsite/icons/login_footer.png');
background-size: 70px;
}


.app-info:before {
    background-position: -144px -118px;
}

.follow-us:before {
    background-position: -216px -118px;
}

.service-center:after {
    vertical-align: middle;
}

.service-center .contact-label span {
    vertical-align: middle;
}

.contact-wrapper .row {
    margin: -24px -14px 0;
}

.contact-wrapper .col-sm-4 {
    padding: 0 14px;
    margin-top: 24px;
}

.contact-wrapper {
    margin-bottom: 58px;
}

.fmenu {
    clear: both;
    padding: 100px 0 0;
    margin: 0;
    list-style: none;
    font-size: 0;
}

.fmenu li {
    display: inline-block;
    font-size: 16px;
}

.fmenu li a {
    color: #fff;
    text-transform: uppercase;
}

.fmenu li:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 16px;
    width: 1px;
    background: #fff;
    margin: 0 6px 0 5px;
}

.fmenu li:first-child:before {
    display: none;
}

.fmenu li a:hover,
.fmenu li.active a {
    color: #00c8f8;
}


/* =Home-Page (only homepage used classes)
========================================================================================*/

.sprites {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg');
}

.sp-after:after {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg');
    display: inline-block;
    width: 25px;
    height: 25px;
    margin-left: 10px;
    content: '';
    vertical-align: middle;
}

.sp-before:before {
    background-image: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg');
    display: inline-block;
    width: 25px;
    height: 25px;
    margin-right: 10px;
    content: '';
    vertical-align: middle;
}

.banner-content {
    background: rgba(231, 75, 24, 0.8);
    padding: 30px 0 40px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    color: #fff;
    text-align: center;
}

.button.btn-orange {
    background: #004494;
}

.button.btn-orange:hover {
    background: #004494;
    color: #ffffff;
   /* border-color: #1772dd;*/
}

.banner-content .container {
    position: relative;
}

.banner-content .button {
    float: right;
}

.banner-content h2 {
    color: #fff;
    margin: 0 0 5px;
    padding: 0 200px;
}

.banner-content h3 {
    font-size: 20px;
    margin-bottom: 15px;
}

.banner-info-bubble {
    font-size: 0;
    padding: 20px 30px;
    color: #444;
    background: #fff;
    width: 221px;
    height: 221px;
    border-radius: 50%;
    position: absolute;
    right: 15px;
    top: -195px;
}

.banner-info-bubble:after {
    content: '';
    height: 100%;
    display: inline-block;
    vertical-align: middle;
}

.banner-info-bubble h4 {
    font-size: 16px;
    margin: 0 0 15px;
}

.bubble-content {
    font-size: 16px;
    position: relative;
    z-index: 1;
    display: inline-block;
    vertical-align: middle;
}

.bubble-content p {
    margin: 0;
}

.banner-info-bubble:before {
    content: "";
    position: absolute;
    bottom: 20px;
    left: -33px;
    border: 0;
    border-width: 0 70px 50px 0;
    border-style: solid;
    border-color: transparent #fff;
    display: block;
    width: 0;
    z-index: 0;
    transform: rotate(-55deg);
}
.home-slider .owl-prev {
    position: absolute;
    width: 50%;
    height: 80vh;
    z-index: 9;
    display: table;
    cursor:pointer;
}
.home-slider .owl-next {
    position: absolute;
    left:50%;
    top:0;
    width: 50%;
    height: 80vh;
    z-index: 9;
    display: table;
    cursor:pointer;
}
.home-slider.small-items .owl-next {
    height: 50vh;
    left:75%;
    width: 25%;
}

.home-slider.small-items .owl-prev {
    height: 50vh;
    width: 25%;
}
.home-slider .owl-prev i{
    vertical-align: middle;
    padding-left: 5%;
    display: none;
    color: rgba(2255,255,255, 0.8);
    filter: drop-shadow(0px 0px 7px #222);
}
.home-slider .owl-next i{
    vertical-align: middle;
    padding-right: 5%;
    display: none;
    color: rgba(2255,255,255, 0.8);
    text-align: right;
    filter: drop-shadow(0px 0px 7px #222);
}
.home-slider .owl-prev:hover i, .home-slider .owl-next:hover i{
    display: table-cell;
}
.home-slider .owl-dots {
    padding: 0 20px;
    max-width: 1170px;
    width: 100%;
    margin: 0 auto;
    position: absolute;
    bottom: 12px;
    left: 0;
    right: 0;
    text-align: left;
    font-size: 0;
    z-index: 999;
}

.home-slider .item {
    width:100%;
    height: 800px;
    height: 100vh;
    background-position: center top;
}
.home-slider .item.small-item {
    /*height: 400px;
    height: 50vh;*/
    padding-top: 100px;
    height: initial;
}

.news-wrapper {
    text-align: center;
    margin-bottom: 110px;
}

.news-wrapper .container {
    max-width: 1774px;
    width: 100%;
}

.news-wrapper h2 {
    margin-bottom: 35px;
    margin-top: 50px;
}

.home .news-wrapper h2 {
    color: #004494;
}

.wasser .news-wrapper h2 {
    color: #52bfd5;
}

.strom .news-wrapper h2 {
    color: #e74a16;
}

.gas .news-wrapper h2 {
    color: #fecb01;
}

.baeder .news-wrapper h2 {
    color: #00c8f8;
}

.service .news-wrapper h2 {
    color: #004494
}

.energie .news-wrapper h2 {
    color: #97BF0D
}
.breitband .news-wrapper h2 {
    color: #52B06D
}



.news-box-outer {
    max-width: 1774px;
    padding: 0 15px;
    overflow: hidden;
    width: 100%;
}

.news-slider {
    width: 100%;
    margin-bottom: 60px;
    position: relative;
}

.news-box.is-selected {
    width: 50%;
    margin: 0 5%;
    background: #00c8f8;
    font-size: 16px;
    padding: 50px 30px;
}

.home .news-box.is-selected {
    background: #98c21d;
}

.news-box {
    width: 19%;
    color: #fff;
    background: #c0c0c0;
    font-size: 14px;
    border-radius: 15px;
    padding: 25px 15px;
    margin: 0 1%;
}

.news-box h3 {
    font-size: 20px;
    padding: 0 0%;
    margin-top: 10px;
}

.news-box.is-selected h3 {
    font-size: 28px;
    padding: 0 0%;
    margin-bottom: 25px;
}

.news-box.is-selected a {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
}

.news-box.is-selected:hover {
    background-color: #00B9E6
}

.news-box p {
    margin: 0;
}

.news-box .news-list-date {
    position: absolute;
    top: 1em;
    left: 1em;
}

.flickity-prev-next-button svg {
    display: none;
}

.flickity-prev-next-button {
    border: 0;
    left: 21%;
    position: absolute;
    z-index: 1;
    top: 50%;
    margin-top: -30px;
    padding: 0;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat 0 -194px !important;
    width: 37px;
    height: 59px;
}

.flickity-prev-next-button.previous {
    display: none
}

.flickity-prev-next-button.next {
    background-position: -41px -194px !important;
    left: auto;
    right: 21%;
}

.flickity-prev-next-button:hover {
    background-color: transparent;
    opacity: 0.75;
}

.flickity-viewport {
    position: relative;
    z-index: 1;
}

.news-wrapper .button-row {
    max-width: 1140px;
    width: 100%;
    margin: 0 auto;
}

.dividerline {
    border-top: 1px dotted #444;
    width: 100%;
    height: 1px;
    margin-bottom: 30px;
    margin-top: 50px;
}

.news-list-view.container .page-navigation .f3-widget-paginator{
    list-style: none;
    padding-left:0px;
  margin-bottom:50px;
}
.news-list-view.container .page-navigation ul.f3-widget-paginator li {
    display: inline;
    border-radius: 0;
    border: 1px solid #fff;
    font-size: 16px;
    display: inline-block;
    line-height: 1.2;
    padding: 5px 20px 6px;
    vertical-align: bottom;
    text-decoration: none;
    background-color: #004494;
    color:white;
}
.news-list-view.container .page-navigation ul.f3-widget-paginator li.current {
    border: 1px solid #004494;
    background-color: #fff;
    color:#004494;
}
.news-single.container .teaser-text {
    font-weight: bold;
}

.news-list-view.container .article {
    width: 100%;
    position: relative
}
.news-list-view.container .article .news-img-wrap, .news-list-view.container .article .teaser-text {
    display:table-cell;
    vertical-align: top;
}

.news-list-view.container .article .news-img-wrap img {
    max-height: 200px;
    width: auto;
    margin: 0px 10px 10px 0px;
    max-width:none;
}

.news-list-view.container .article .header h3>a {
    text-decoration: none
}

.news.news-single.container {
    margin-top: 50px;
}

.news.news-single.container .article .news-img-wrap {
    margin-bottom: 30px;
}

.news > .news-list-view > .article > .teaser-text > div[itemprop="description"] > .news-list-date,
.news > .news-list-view > .article > .teaser-text > div[itemprop="description"] > p.bodytext {
    display: inline
}

.news > .news-list-view > .article > .teaser-text > div[itemprop="description"] > .news-list-date {
    font-weight: 700;
}

div.news-box.is-selected h3 {
    margin-top: 10px;
}

.news > .news-list-view > .article > .footer {
    position: absolute;
    bottom: -30px;
    right: 0px
}

.news > .news-list-view > .article > .footer > p,
.news-single> .article > .footer > p {
    margin: 0px
}

.news-single > .article > .news-img-wrap > .outer > .mediaelement {
    text-align: center
}

.pools-wrapper {
    text-align: center;
    margin-bottom: 110px;
}

.pools-wrapper h2,
.pool-detail-row h2 {
    margin: 0 auto 35px;
    max-width: 1170px;
    width: 100%;
    padding: 0 15px;
    text-align: center;
}

.pools-wrapper .button {
    background-color: rgba(0, 68, 148, 1)
}

.pools-row {
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/schwimmbad-teaser-bg.jpg') no-repeat 50% 0;
    -ms-background-size: cover;
    background-size: cover;
    padding: 98px 0 0;
    position: relative;
    margin-bottom: 48px;
}

.pools-row:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(59, 29, 143, 0.4);
    height: 100%;
}

.pool-box {
    border: 5px solid #fff;
    border-radius: 15px;
    color: #fff;
    padding: 24px 20px 20px;
}

.pool-info span {
    display: block;
}

.pool-info {
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 700;
}

.pools-row .container {
    position: relative;
    z-index: 1;
}

.pool-box .button {
    font-weight: 700;
}

.news-row {
    background: rgba(0, 200, 248, 0.75);
    font-size: 20px;
    color: #fff;
    font-weight: 700;
    padding: 21px 0;
    margin-top: 104px;
    position: relative;
    z-index: 1;
}

.news-row span {
    margin-left: 20px;
}

.button-row {
    text-align: right;
    margin: 50px auto 0;
}

.benefits-wrapper {
    text-align: center;
}

.benefits-wrapper h2 {
    color: #0b8e36;
    margin: 0 auto 35px;
    max-width: 1170px;
    width: 100%;
    padding: 0 15px;
}

.benefits-row {
    margin-bottom: 60px;
}

.benefit-col {
    padding: 0;
    position: relative;
    overflow: hidden;
    background: #004494;
}

.benefits-row .row {
    margin: 0;
}

.benefit-col img {
    width: 100%;
    transition: opacity 1s, transform 1s;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    height: auto;
  opacity: 0.7;
}

.benefit-col:hover img {
    opacity: 0.3;
  /* transform: scale3d(1.2, 1.2, 1)  rotate(10deg); */
}

.benefit-details {
    position: absolute;
    padding: 15px 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
}

.benefit-details p {
    margin: 0 35px;
    text-align: left;
    opacity: 0;
    height: 0;
    transform: translateY(100px);
}

.benefit-col h3 {
    color: #fff;
    padding: 0 15px;
    margin: 0;
}

.benefit-col:hover .benefit-details p,
.benefit-col:hover h3 {
    transform: none;
    opacity: 1;
}

.benefit-col:hover .benefit-details p {
    height: auto;
    transform: translateY(0);
    margin-bottom: 5px;
    margin-top: 40px;
}


/* =Form Style
========================================================================================*/

button,
input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="search"],
input[type="url"],
textarea,
select,
input[type="submit"],
input[type="button"] {
    font-size: 100%;
    margin: 0;
    vertical-align: baseline;
    *vertical-align: middle;
}

button,
input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="search"],
input[type="url"],
textarea,
input[type="submit"],
input[type="button"] {
    -webkit-appearance: none;
    border-radius: 0;
}

button,
input {
    line-height: normal;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="search"],
input[type="url"],
textarea,
select,
select[multiple],
select[size] {
    font-size: 14px;
    background: #fff;
    padding: 2px 10px 4px;
    height: 31px;
    line-height: 1.4;
    border: solid 1px #bbb;
    color: #454648;
    width: 100%;
    font-family: 'Oxygen', Arial, Helvetica, sans-serif;
    transition: border-color 200ms ease-in;
}

input[type="text"].input-lg,
input[type="password"].input-lg,
input[type="email"].input-lg,
input[type="tel"].input-lg,
input[type="search"].input-lg,
input[type="url"].input-lg {
    height: 58px;
}

input[type="text"].input-sm,
input[type="password"].input-sm,
input[type="email"].input-sm,
input[type="tel"].input-sm,
input[type="search"].input-sm,
input[type="url"].input-sm {
    height: 38px;
}

textarea {
    width: 100%;
    height: 160px;
    overflow: auto;
    resize: vertical;
    padding: 10px 15px;
    font-family: 'Oxygen', Arial, Helvetica, sans-serif;
}

select {
    /*padding: 2px 42px 4px 10px;*/
    /*font-size: 14px;*/
    /*-webkit-appearance: none;*/
    /*-moz-appearance: none;*/
    /*-ms-appearance: none;*/
    /*appearance: none;*/
    /*position: relative;*/
    /*z-index: 1;*/
    /*background: none;*/
    /*line-height: 1.25;*/
    /*border-radius: 0;*/
}

select::-ms-expand {
    display: none;
}

.custom-select {
    display: block;
    margin: 0;
    position: relative;
    /* background: #fff; */
}

.custom-select:before {
    position: absolute;
    right: 1px;
    top: 1px;
    width: 24px;
    background: #cccccc;
    content: '';
    height: 29px;
    border-left: 1px solid #bbb;
    z-index: 11111;
}

.custom-select:after {
    content: '';
    border-top: solid 5px #fff;
    border-right: solid 5px transparent;
    border-left: solid 5px transparent;
    position: absolute;
    right: 7px;
    z-index: 11111;
    top: 12px;
}

.picker.picker--time {
    z-index: 111111
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="search"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
    border: solid 1px #acacac;
}

input::-moz-focus-inner,
button::-moz-focus-inner {
    border: 0;
    padding: 0;
}

input[type="button"],
input[type="submit"],
button {
    background: #e74a16;
    border: 1px solid rgba(170, 170, 170, 0.5);
    color: #fff;
    cursor: pointer;
    width: auto;
    overflow: visible;
    padding: 14px 45px;
    vertical-align: middle;
    text-decoration: none;
    font-size: 16px;
    font-family: 'Oxygen', Arial, Helvetica, sans-serif;
}

input[type="button"]:hover,
input[type="submit"]:hover,
button:hover {
    background: #004494;
}

input[type="checkbox"],
input[type="radio"] {
    margin: 4px 8px 0;
    vertical-align: top;
}

::-webkit-input-placeholder {
    opacity: 1;
}

::-moz-placeholder {
    opacity: 1;
}

:-ms-input-placeholder {
    opacity: 1;
}

:-moz-placeholder {
    opacity: 1;
}

.ez-hide {
    opacity: 0;
    filter: alpha(opacity=0);
}

.ez-checkbox {
    background-position: 0 -26px;
    display: inline-block;
    width: 18px;
    height: 18px;
}

.ez-checkbox.disabled,
.ez-radio.disabled {
    opacity: 0.6
}

.ez-radio {
    background-position: -53px -25px;
    display: inline-block;
    width: 20px;
    height: 20px;
}

.ez-checked {
    background-position: -17px -25px;
}

.ez-checked.disabled {
    background-position: -36px -25px;
}

.ez-selected {
    background-position: -73px -25px;
}

.ez-selected.disabled {
    background-position: -93px -26px;
}

.ez-checkbox,
.ez-radio {
    zoom: 1;
    *display: inline;
    vertical-align: middle;
    margin-right: 8px;
}

.ez-checkbox input,
.ez-radio input {
    margin: 2px 0 0;
    display: block;
    width: 16px;
    height: 16px;
    vertical-align: top;
}

.ez-radio input {
    width: 18px;
    height: 18px;
}

input[disabled] {
    background-color: #eee
}


/* =BreadCrumbs
========================================================================================*/

.breadcrumbs {
    font-size: 9px;
    text-transform: uppercase;
    clear: both;
}

.breadcrumbs ul {
    margin: 0;
    padding: 0;
}

.breadcrumbs li {
    color: #004494;
    display: inline-block;
    position: relative;
}

.breadcrumbs li:before {
    content: '>';
    display: inline-block;
    vertical-align: middle;
    margin: -3px 3px 0;
}

.breadcrumbs li:first-child:before {
    display: none;
}

.breadcrumbs li a {
    color: #004494;
    text-decoration: none;
}

.breadcrumbs li a:hover {
    color: #00c8f8;
}


/* =Form list
========================================================================================*/

.form-block {
    margin: 10px 0;
}

.form-group {
    padding: 6px 0
}

.form-group:after {
    clear: both;
    content: '';
    display: table;
}

.formlist .form-group ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.forgot-link {
    float: right;
}

.form-block .form-group label {
    display: inline-block;
    padding: 0 0 5px;
    font-weight: normal;
}

.form-inline .form-group .field-box {
    width: 70%;
    float: left;
}

.form-inline .form-group {
    display: block;
    margin: 0;
}


/* =Form list
========================================================================================*/

.Tx-Formhandler h3 {
    font-size: 24px;
    border-bottom: 1px solid black;
    margin: 1em 0em;
}

.Tx-Formhandler span.smalltext {
    font-size: 0.75em;
    display: block;
}

.Tx-Formhandler span.smalltext > a {
    margin-left: 10px;
    color: red;
    text-decoration: none
}

.Tx-Formhandler .globalerrormessage {
    color: red;
    font-weight: bold
}

input.pagesearchfield {
    width: 200px;
    padding: 20px 20px;
}

input.pagesubmitfield {
    padding: 10px 20px;
}

input.headersubmitfield {
    display: none;
}

.tx-indexedsearch-res strong {
    font-weight: normal;
    font-style: italic
}

.tx-indexedsearch .tx-indexedsearch-res TD.tx-indexedsearch-descr {
    font-style: normal
}

#strombertform #verbrauchsstelleabweichend_con,
#strombertform #rechnungsanschriftabweichend_con {
    display: none
}


/* =Common button CSS
========================================================================================*/

.button {
    border-radius: 0;
   /* border: 1px solid #fff;*/
    font-size: 16px;
    color: #fff;
    display: inline-block;
    line-height: 1.2;
    padding: 5px 30px 6px;
    vertical-align: bottom;
    text-decoration: none;
}

.home .button {
    background-color: #1772dd;
}

.home .button:hover {
    background-color: #00c8f8!important;
}

.wasser .button {
    background-color: #004494;
}

.strom .button {
    background-color: #004494;
}

.gas .button {
    background-color: #004494;
}

.baeder .button {
    background-color: #004494;
}

.service .button {
    background-color: #004494;
}

.energie .button {
    background-color: #004494;
}
.breitband .button {
     background-color: #004494;
 }

.button:before {
    content: '>';
    margin-right: 6px;
}

.button:hover {
    background-color: #1772dd !important;
    color: #eeeeee;
}

.button.btn-lg,
input[type="submit"].btn-lg,
input[type="button"].btn-lg,
button.btn-lg {
    padding: 7px 30px 8px;
    font-size: 18px;
}

.button.btn-sm,
input[type="submit"].btn-sm,
input[type="button"].btn-sm,
button.btn-sm {
    padding: 3px 30px 5px;
    font-size: 13px;
}

.button.btn-lightgray,
input[type="submit"].btn-lightgray,
input[type="button"].btn-lightgray,
button.btn-lightgray {
    background: #969fb0;
    color: #fff;
}

.button.btn-lightgray:hover,
input[type="submit"].btn-lightgray:hover,
input[type="button"].btn-lightgray:hover,
button.btn-lightgray:hover {
    background: #757f90;
}

.button.btn-secondary,
input[type="submit"].btn-secondary,
input[type="button"].btn-secondary,
button.btn-secondary {
    background: #004494;
    color: #fff;
}

.button.btn-secondary:hover,
input[type="submit"].btn-secondary:hover,
input[type="button"].btn-secondary:hover,
button.btn-secondary:hover {
    background: #1772dd;
    color: #ffffff;
}

.contact-button.btn-secondary
 {
    background: #1772dd!important;
    color: #fff;
}

.contact-button.btn-secondary:hover
 {
    background: #00c8f8!important;
    color: #ffffff;
}


.button.btn-outline,
input[type="submit"].btn-outline,
input[type="button"].btn-outline,
button.btn-outline {
    background: #fff;
    border: solid 1px #e74a16;
    color: #f34834;
}

.button.btn-outline:hover,
input[type="submit"].btn-outline:hover,
input[type="button"].btn-outline:hover,
button.btn-outline:hover {
    background: #e74a16;
    color: #fff;
}

.button.btn-block {
    display: block;
}

.buttonset .button {
    margin: 10px 7px;
    text-align: center;
    min-width: 135px;
}


/* =Table CSS
========================================================================================*/

table {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: rgba(0, 68, 148, 0.5)
}

table {
    width: 100%;
}

table th,
table td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

table th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

table tr:nth-child(odd) td {
    background: #00c8f8;
}

table tr.selected td {
    background: #fbfbfb;
}

table thead th {
    vertical-align: middle;
}

table caption + thead tr:first-child th,
table caption + thead tr:first-child td,
table colgroup + thead tr:first-child th,
table colgroup + thead tr:first-child td,
table thead:first-child tr:first-child th,
table thead:first-child tr:first-child td {
    border-top: 0;
}

.home table.contenttable {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: #3369A9;
}

.home table.contenttable th,
.home table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: white;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}
.home table.contenttable td.pMonat, .home table.contenttable td.pDetails {
    font-size: 14px;
}
.home table.contenttable td.pMonat > .monatspreisgesamt {
    font-size: 16px;
}

.home table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
    color: white;
}

.home table.contenttable tr:nth-child(odd) td {
    background: #004494;
    color: white;
}

.home table.contenttable tr.selected td {
    background: #fbfbfb;
    color: white;
}

.wasser table.contenttable {
    max-width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    background: #E8F6FF;
}

.wasser table.contenttable th,
.wasser table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

.wasser table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

.wasser table.contenttable tr:nth-child(odd) td {
  /*  background: #52bfd5;*/
}

.wasser table.contenttable tr.selected td {
    background: #fbfbfb;
}

.strom table.contenttable {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: #FFF0F4;
}

.strom table.contenttable th,
.strom table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

.strom table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

.strom table.contenttable tr:nth-child(odd) td {
    background: #e74a16;
}

.strom table.contenttable tr.selected td {
    background: #fbfbfb;
}

.gas table.contenttable {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: #FEF4EC;
}

.gas table.contenttable th,
.gas table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

.gas table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

.gas table.contenttable tr:nth-child(odd) td {
    background: #fecb01;
}

.gas table.contenttable tr.selected td {
    background: #fbfbfb;
}

.baeder table.contenttable {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: #c9f1fd;
}

.baeder table.contenttable th,
.baeder table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

.baeder table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

.baeder table.contenttable tr:nth-child(odd) td {
    background: #00c8f8;
}

.baeder table.contenttable tr.selected td {
    background: #fbfbfb;
}

.service table.contenttable {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: #ececec;
}

.service table.contenttable th,
.service table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

.service table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

.service table.contenttable tr:nth-child(odd) td {
    background: #d6d6d6;
}

.service table.contenttable tr.selected td {
    background: #fbfbfb;
}

.energie table.contenttable {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: #EDFFF6;
}

.energie table.contenttable th,
.energie table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

.energie table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

.energie table.contenttable tr:nth-child(odd) td {
    background: #97BF0D;
}

.energie table.contenttable tr.selected td {
    background: #fbfbfb;
}


.breitband table.contenttable {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    background: #B6DEC0;
}

.breitband table.contenttable th,
.breitband table.contenttable td {
    padding: 12px 8px;
    text-align: left;
    color: #444;
    font-size: 16px;
    font-weight: 700;
    vertical-align: middle;
}

.breitband table.contenttable th {
    background: #ececec;
    font-weight: 700;
    font-size: 16px;
}

.breitband table.contenttable tr:nth-child(odd) td {
    background: #52B06D;
}

.breitband table.contenttable tr.selected td {
    background: #fbfbfb;
}

/* =tabnav CSS
========================================================================================*/

.tab-data {
    padding-bottom: 120px;
}

.tab-heading {
    border-bottom: 5px solid #00c8f8;
    margin-bottom: 45px;
}
.tab-heading.strom {
    border-color: #e74a16
}
.tab-heading.gas {
    border-color: #fecb01
}
.tab-heading.wasser {
    border-color: #52bfd5
}

.tabnav {
    margin: 0;
    padding: 0;
    list-style: none;
}

.tabnav li {
    float: left;
    position: relative;
    margin: 0;
    width: 33.333%;
    text-align: center;
}

.tabnav li:first-child {
    margin: 0;
}

.tabnav li h2 {
    margin: 0;
    font-size: 15px;
}

.tabnav li a {
    display: block;
    padding: 17px 12px;
    font-weight: 700;
    border-radius: 12px 12px 0 0;
    font-size: 36px;
    text-decoration: none;
    color: #fff;
    background: #99e9fc;
}

.tabnav li.active a,
.tabnav li a:hover {
    background-color: #00c8f8;
}
.tabcontent {
    display: none;
}
.tabcontent p a:hover,
.accordion-data p a:hover {
    color: #00305d
}
.tabcontent h4 {
    margin: 0 0 15px;
}

/* =Accordian CSS
========================================================================================*/

.accordion-databox .accordion-row {}

.accordion-databox .accordion-row:first-child {
    border-top: 0;
}

.accordion-databox .accordion-row h5 {
    cursor: pointer;
    margin: 0;
    background: #f5f5f5;
    color: #555;
    position: relative;
    padding: 16px 54px 16px 16px;
    font-weight: 600;
    font-size: 15px;
    margin-bottom: 7px;
    text-transform: uppercase;
}

.accordion-databox .accordion-row h5:before {
    position: absolute;
    right: 0;
    top: 0;
    content: '';
    width: 54px;
    height: 100%;
    z-index: 1;
    background: rgba(0, 0, 0, 0.08);
}

.accordion-databox .accordion-row h5:after {
    content: '';
    position: absolute;
    right: 18px;
    top: 50%;
    margin-top: -4px;
    border-top: solid 8px #818181;
    border-left: solid 8px transparent;
    border-right: solid 8px transparent;
    z-index: 2
}

.accordion-databox .accordion-row h5.open {
    color: #272c2f;
}

.accordion-databox .accordion-row h5.open:after {
    border-top: 0;
    border-bottom: solid 8px #272c2f;
}

.accordion-data {
    display: none;
    padding: 15px 0;
}

.accordion-data p {
    padding: 0 6px;
    margin: 20px 0 0;
}

.accordion-data p:first-child {
    margin-top: 0;
}



/* =Alertbox CSS
========================================================================================*/

.alert {
    padding: 16px 16px 16px 55px;
    margin: 20px 0;
    color: #444444;
    position: relative;
}

.alert:before {
    position: absolute;
    content: '';
    left: 15px;
    top: 15px;
    width: 24px;
    height: 24px;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat 0 0;
}

.alert-notification {
    background-color: #fff0f4;
    border: solid 1px #d9534f;
    color: #d9534f;
}

.alert-notification:before {
    background-position: 0 0;
}

.alert-info {
    background-color: #e8f6ff;
    border: solid 1px #0082d5;
    color: #0082d5;
}

.alert-info:before {
    background-position: -26px 0;
}

.alert-warning {
    background-color: #fef4ec;
    border: solid 1px #ff9948;
    color: #ff9948;
}

.alert-warning:before {
    background-position: -52px 0;
}

.alert-success {
    background-color: #edfff6;
    border: solid 1px #04be5b;
    color: #04be5b;
}

.alert-success:before {
    background-position: -79px 0;
}

.alert-normal {
    background-color: #efefef;
    border: solid 1px #999;
    padding-left: 16px;
    color: #555;
}

.alert-normal:before {
    display: none;
}


/* =Default page CSS
========================================================================================*/

.heading-listblock {
    padding: 20px 0;
}

.heading-listblock p,
.heading-listblock blockquote {
    margin: 0 0 35px;
}

.heading-listblock p:last-child {
    margin-bottom: 0;
}

.heading-listblock .paragraph-block p {
    margin: 0 0 20px;
}

.list-block ul,
.list-block ol {
    padding-left: 22px;
    margin-left: -5px;
}

.list-block ul li,
.list-block ol li {
    margin: 5px 0;
}

.list-block ul.custom-arrow-list {
    margin: 0;
    padding: 0;
}


/* =Bathrooms page CSS
========================================================================================*/

.pool-image {
    margin-bottom: 36px;
}

.pool-image img {
    max-width: 100%;
}

.pool-details a.button {
    text-align: center;
    font-weight: 700;
}

.pool-details {
    color: #232323;
    font-size: 20px;
    margin-bottom: 20px;
}

.pool-details p strong {
    display: block;
}

.pool-contact-box {
    background: #f39201;
    max-width: 670px;
    width: 100%;
    margin: 0 auto;
    border-radius: 15px;
    color: #fff;
    padding: 15px 90px 5px 25px;
    position: relative;
}

.tab-container .pool-contact-box {
    margin-top: 58px;
}

.pool-contact-box:after {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat -74px -46px;
    width: 64px;
    height: 68px;
    position: absolute;
    right: 9px;
    top: 10px;
}

.tel-number a {
    color: #fff;
    text-decoration: none;
}

.pool-contact-box h4 {
    margin-bottom: 10px;
}

.pool-contact-box p {
    margin-bottom: 15px;
}

.information-row {
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/info-bg.jpg') no-repeat 50% 100%;
    -ms-background-size: cover;
    background-size: cover;
    color: #232323;
    padding: 40px 0;
    margin-bottom: 130px;
}

.signup-box .button {
    background: #00c8f8;
    width: 100%;
    padding: 4px 15px;
    margin-top: 8px;
    border: 0;
}

.signup-box input[type="email"] {
    background: #ebebeb;
    border: 1px solid #d6d6d6;
    border: 1px solid rgba(214, 214, 214, 0.5);
}

.signup-box label {
    color: #120639;
    font-weight: 400;
}

.information-row h2 {
    color: #fff;
    text-align: center;
    margin-bottom: 50px;
}

.info-box {
    padding-left: 230px;
}

.info-box .row {
    margin: 0 -50px;
}

.info-box .col-sm-6 {
    padding: 0 50px;
}

.info-box .info-col {
    padding-right: 80px;
}

.courses-wrapper h2 {
    text-align: center;
    margin-bottom: 30px;
}

.courses-info {
    max-width: 710px;
    width: 100%;
    padding: 0 15px;
    margin: 0 auto 30px;
}

.courses-container {
    overflow: hidden;
    margin-bottom: 50px;
}

#cources {
    margin: 0 -5px;
    padding: 15px 0 0;
    list-style: none;
}

#cources:after {
    clear: both;
    content: '';
    display: table;
}

#cources li h4 {
    word-wrap: break-word;
    width: auto;
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: 0;
    color: #fff;
    margin: 0;
    background: rgba(0, 200, 248, 0.8);
    font-size: 16px;
    padding: 5px 30px;
}

#cources li {
    padding: 5px;
    float: left;
    position: relative;
}

#cources li a {
    display: block;
    position: relative;
    overflow: hidden;
    padding: 0px;
}

#cources li a:before {
    content: '';
    margin-right: 0px;
}

#cources li a:after {
    content: '';
    width: 0;
    height: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 200, 248, 0.8);
    position: absolute;
}

#cources li a:hover:after {
    width: 100%;
    height: 100%;
}

#cources li a:hover h4 {
    background: none;
    right: 50%;
    bottom: 50%;
    transform: translate(50%, 50%);
}

.percent01 {
    width: 0.9%;
}

.percent02 {
    width: 01.9%;
}

.percent03 {
    width: 02.9%;
}

.percent04 {
    width: 03.9%;
}

.percent05 {
    width: 04.9%;
}

.percent06 {
    width: 05.9%;
}

.percent07 {
    width: 06.9%;
}

.percent08 {
    width: 07.9%;
}

.percent09 {
    width: 08.9%;
}

.percent10 {
    width: 09.9%;
}

.percent11 {
    width: 10.9%;
}

.percent12 {
    width: 11.9%;
}

.percent13 {
    width: 12.9%;
}

.percent14 {
    width: 13.9%;
}

.percent15 {
    width: 14.9%;
}

.percent16 {
    width: 15.9%;
}

.percent17 {
    width: 16.9%;
}

.percent18 {
    width: 17.9%;
}

.percent19 {
    width: 18.9%;
}

.percent20 {
    width: 19.9%;
}

.percent21 {
    width: 20.9%;
}

.percent22 {
    width: 21.9%;
}

.percent23 {
    width: 22.9%;
}

.percent24 {
    width: 23.9%;
}

.percent25 {
    width: 24.9%;
}

.percent26 {
    width: 25.9%;
}

.percent27 {
    width: 26.9%;
}

.percent28 {
    width: 27.9%;
}

.percent29 {
    width: 28.9%;
}

.percent30 {
    width: 29.9%;
}

.percent31 {
    width: 30.9%;
}

.percent32 {
    width: 31.9%;
}

.percent33 {
    width: 32.9%;
}

.percent34 {
    width: 33.9%;
}

.percent35 {
    width: 34.9%;
}

.percent36 {
    width: 35.9%;
}

.percent37 {
    width: 36.9%;
}

.percent38 {
    width: 37.9%;
}

.percent39 {
    width: 38.9%;
}

.percent40 {
    width: 39.9%;
}

.percent41 {
    width: 40.9%;
}

.percent42 {
    width: 41.9%;
}

.percent43 {
    width: 42.9%;
}

.percent44 {
    width: 43.9%;
}

.percent45 {
    width: 44.9%;
}

.percent46 {
    width: 45.9%;
}

.percent47 {
    width: 46.9%;
}

.percent48 {
    width: 47.9%;
}

.percent49 {
    width: 48.9%;
}

.percent50 {
    width: 49.9%;
}

.percent51 {
    width: 50.9%;
}

.percent52 {
    width: 51.9%;
}

.percent53 {
    width: 52.9%;
}

.percent54 {
    width: 53.9%;
}

.percent55 {
    width: 54.9%;
}

.percent56 {
    width: 55.9%;
}

.percent57 {
    width: 56.9%;
}

.percent58 {
    width: 57.9%;
}

.percent59 {
    width: 58.9%;
}

.percent60 {
    width: 59.9%;
}

.percent61 {
    width: 60.9%;
}

.percent62 {
    width: 61.9%;
}

.percent63 {
    width: 62.9%;
}

.percent64 {
    width: 63.9%;
}

.percent65 {
    width: 64.9%;
}

.percent66 {
    width: 65.9%;
}

.percent67 {
    width: 66.9%;
}

.percent68 {
    width: 67.9%;
}

.percent69 {
    width: 68.9%;
}

.percent70 {
    width: 69.9%;
}

.percent71 {
    width: 70.9%;
}

.percent72 {
    width: 71.9%;
}

.percent73 {
    width: 72.9%;
}

.percent74 {
    width: 73.9%;
}

.percent75 {
    width: 74.9%;
}

.percent76 {
    width: 75.9%;
}

.percent77 {
    width: 76.9%;
}

.percent78 {
    width: 77.9%;
}

.percent79 {
    width: 78.9%;
}

.percent80 {
    width: 79.9%;
}

.percent81 {
    width: 80.9%;
}

.percent82 {
    width: 81.9%;
}

.percent83 {
    width: 82.9%;
}

.percent84 {
    width: 83.9%;
}

.percent85 {
    width: 84.9%;
}

.percent86 {
    width: 85.9%;
}

.percent87 {
    width: 86.9%;
}

.percent88 {
    width: 87.9%;
}

.percent89 {
    width: 88.9%;
}

.percent90 {
    width: 89.9%;
}

.percent91 {
    width: 90.9%;
}

.percent92 {
    width: 91.9%;
}

.percent93 {
    width: 92.9%;
}

.percent94 {
    width: 93.9%;
}

.percent95 {
    width: 94.9%;
}

.percent96 {
    width: 95.9%;
}

.percent97 {
    width: 96.9%;
}

.percent98 {
    width: 97.9%;
}

.percent99 {
    width: 98.9%;
}

.percent100 {
    width: 99.9%;
}

#cources li img {
    width: 100%;
    height: auto;
}

.sidebar {
    position: absolute;
    top: 0;
    right: 100%;
    margin-right: 15px;
    text-align: left;
    word-wrap: break-word;
}

.orange-box {
    position: relative;
    background: #f39201;
    border-radius: 15px;
    padding: 30px;
    color: #fff;
    width: 258px;
    z-index: 99;
}

.orange-box {
    position: relative;
    background: #f39201;
    border-radius: 15px;
    padding: 30px;
    color: #fff;
    width: 258px;
    z-index: 99;
}
.orange-box a {
    text-decoration: underline;
    font-weight: 700;
    color:white;
}

.orange-box a:hover {
    text-decoration: none!important;

}

.download-box:after {
    content: '';
    position: absolute;
    right: -2px;
    top: -2px;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat -79px -194px;
    width: 54px;
    height: 57px;
}

.orange-box h4 {
    font-size: 28px;
    margin: 0 0 25px;
}

.download-box ul {
    margin: 0;
    padding: 0 0 0 15px;
}

.download-box ul li {
    margin-bottom: 18px;
}

.download-box ul li {
    margin-left: 10px;
}

.tab-container .container {
    position: relative;
}

.time-info {
    min-width: 130px;
}


/* =Bathrooms-bottom page CSS
========================================================================================*/

.pool-detail-row {
    margin-bottom: 100px;
}

.pool-detail-box {
    font-weight: 700;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/pool-box-bg.jpg') no-repeat 50% 0;
    -ms-background-size: cover;
    background-size: cover;
    color: #fff;
    padding: 75px 0 50px;
    position: relative;
}

.pool-detail-box:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(59, 29, 143, 0.3);
    z-index: 0;
}

.pool-detail-box .container {
    position: relative;
    z-index: 1;
    padding-left: 260px;
}

.pool-detail-box .banner-info-bubble {
    position: relative;
    top: 0;
    left: 0;
    float: right;
    margin-left: 100px;
    text-align: center;
    margin-top: -20px;
}

.pool-detail-box .banner-info-bubble h4 {
    margin: 0;
}

.pool-type {
    background: #00c8f8;
    color: #fff;
    border-radius: 15px;
    padding: 30px 75px 60px 30px;
}

.pool-type h3 {
    text-align: center;
    margin-bottom: 40px;
}

.pools-types .container {
    position: relative;
}

.pools-types {
    margin-bottom: 110px;
}

.course-percent29 {
    width: 29.875%;
}

.course-percent28 {
    width: 28.6875%;
}

.course-percent41 {
    width: 41.427%;
}

.course-percent33 {
    width: 33.4375%;
}

.course-percent24 {
    width: 24.5%;
}

.course-percent17 {
    width: 17.1875%;
}

.course-percent25 {
    width: 24.85%;
}

div.courses-wrapper {
    margin-bottom: 0;
}

.time-table-wrapper {
    padding-top: 20px;
    margin-bottom: 48px;
}

.timings-table {
    margin-top: 90px;
}

.timings-table h2 {
    text-align: center;
    margin-bottom: 30px;
}

table th {
    background: #00c8f8;
}

.deal-box {
    max-width: 700px;
    width: 100%;
    margin: 0 auto;
    padding: 0 15px;
}

.deal-box figure {
    float: left;
    margin-right: 25px;
}

.deal-info {
    overflow: hidden;
}

.contact-row h2 {
    text-align: center;
}

.contact-row .pool-details {
    font-size: 16px;
    max-width: 670px;
    width: 100%;
    margin: 0 auto;
    padding: 70px 0 55px;
    padding-right: 50px;
}

.contact-row .pool-details p:not(:first-child) strong {
    display: inline-block;
}

.contact-row .pool-details p a {
    color: #00e;
    text-decoration: underline;
}

.contact-row .pool-details p a:hover {
    color: #00c8f8;
}

.direction-map {
    border-top: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
}

.direction-map img {
    width: 100%;
}

.info-title {
    background: #fff;
}

.subscription-info h2 {
    color: #00c8f8;
    margin-bottom: 30px;
}

.information-row.subscription-info {
    padding-top: 0;
}

.subscription-info .info-box {
    padding-top: 40px;
}

.subscription-info {
    margin-bottom: 50px;
}

.contact-row {
    padding: 110px 0 180px;
}


/* =Electricity page CSS
========================================================================================*/

.tarif-wrapper {
    margin-bottom: 140px;
}

.tarif-wrapper h2,
.rates-overview h2 {
    color: #e74a16;
    margin-bottom: 45px;
    text-align: center;
}

.brand-info-box {
    background: #e74a16;
    color: #fff;
    border-radius: 15px;
    padding: 30px 25px;
}

.brand-logo-box {
    background: #ebebeb;
    text-align: center;
    padding: 45px;
}

.brand-logo-box figure {
    display: block;
    height: 100%;
}

.brand-logo-box figure:after {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 100%;
}

.brand-logo-box img {
    max-width: 100%;
    width: 100%;
    display: inline-block;
    vertical-align: middle;
    margin-top: -5px;
}

.brand-info-box h4 {
    font-size: 20px;
    margin-bottom: 30px;
}

.advantages {
    clear: both;
    padding: 20px 0 0 35px;
}

.energy-wrapper h2 {
    color: #e74a16;
    margin: 0 auto 35px;
    text-align: center;
    max-width: 1170px;
    width: 100%;
    padding: 0 15px;
}

.energy-info-row {
    padding: 34px 0;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/energy-box-bg.jpg') no-repeat 50% 0;
    -ms-background-size: cover;
    background-size: cover;
}

.energy-box {
    border-radius: 15px;
    background: #98c21d;
    color: #fff;
    padding: 25px 30px 20px;
    margin-bottom: 32px;
}

.energy-box h3 {
    text-align: center;
    font-size: 20px;
    margin-bottom: 15px;
}

.energy-info-row .container {
    max-width: 714px;
    width: 100%;
}

.rates-overview {
    padding: 105px 0;
    text-align: center;
}

.rates-overview .container {
    position: relative;
}

.rates-overview h2 {
    margin-bottom: 30px;
}

.step-col {
    position: relative;
    border-radius: 12px;
    background: #e74a16;
    font-weight: 700;
    color: #fff;
    padding: 40px 20px;
}

.step-number {
    position: absolute;
    line-height: 1;
    font-size: 178px;
    opacity: 0.3;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 50%;
    transform: translateY(-50%);
    margin-top: -7px;
}

.step-info {
    display: table;
    height: 100%;
    width: 100%;
    position: relative;
}

.step-info p {
    margin: 0;
    display: table-cell;
    vertical-align: middle;
}

.rates-steps-row {
    padding-top: 20px;
}

.power-consumption-row {
    text-align: center;
    color: #fff;
    background: #e74a16;
    padding: 50px 0 55px;
    margin-bottom: 75px;
}

.usage-table {
    background: #fff;
    margin-top: 30px;
}

table.usage-table th,
table.usage-table td {
    font-size: 10px;
    font-weight: 700;
    color: #232323;
    padding: 5px 10px;
}

table.usage-table th {
    background: #7a7a7a;
}

table.usage-table tr:nth-child(odd) td {
    background: #ececec;
}

table.usage-table td:last-child {
    text-align: center;
}

.usage-table .ez-checkbox,
.usage-table .ez-radio {
    margin: 0;
}

.power-info-box {
    max-width: 680px;
    width: 100%;
    margin: 0 auto;
}

.power-info-box:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/single-user-icon.png') no-repeat;
    width: 261px;
    height: 288px;
    position: absolute;
    top: -25px;
    left: 0;
    z-index: -1;
}

.double-user:before {
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/double-user-icon.png') no-repeat;
    width: 367px;
    height: 284px;
}

.family-user:before {
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/family-icon.png') no-repeat;
    width: 323px;
    height: 294px;
}

.home-user:before {
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/home-icon.png') no-repeat;
    width: 400px;
    height: 305px;
}

.power-consumption-row .container {
    position: relative;
    z-index: 1;
}

.rate-calculator {
    color: #fff;
    padding-top: 100px;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/rate-calculator-bg.jpg') no-repeat 50% 0;
    -ms-background-size: cover;
    background-size: cover;
    position: relative;
    z-index: 1;
    margin-bottom: 45px;
}

.rate-calculator:after,
.contact-form-wrapper:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    right: 0;
    background: rgba(35, 35, 35, 0.4);
    z-index: -1;
}

.rate-calculator h2,
.contact-form-wrapper h2 {
    color: #fff;
    text-align: center;
    margin-bottom: 45px;
}

.rate-calculator .container {
    max-width: 690px;
    width: 100%;
    position: relative;
}

.rate-info-box {
    background: rgba(231, 74, 22, 0.8);
    text-align: center;
    padding: 30px 0 15px;
    margin-top: 55px;
}

.total-consuption h4 {
    font-size: 20px;
    margin-bottom: 20px;
}

.total-power {
    max-width: 338px;
    width: 100%;
    display: block;
    margin: 0 auto;
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    background: #444;
    padding: 17px 50px;
    margin-bottom: 14px;
}

.rate-info-box .banner-info-bubble h4 {
    margin: 0;
}

.rate-info-box .banner-info-bubble {
    padding: 20px 6px;
    right: -115px;
    top: -120px;
}

.rate-info-box .banner-info-bubble a {
    color: #444;
    text-decoration: underline;
}

.rate-info-box .banner-info-bubble a:hover {
    color: #e74a16;
}

.view-more {
    color: #929292;
    font-weight: 700;
    position: relative;
    -webkit-appearance: none !important;
    padding-right: 40px;
    display: inline-block;
}

.view-more:hover {
    color: #e74a16;
      cursor: pointer;
}

.view-more:after {
    content: '';
    background: #e74a16;
    border-radius: 0 4px 4px 0;
    width: 25px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 17px;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
}

.home .view-more:after {
    background-color: #004494;
}

.wasser .view-more:after {
    background-color: #52bfd5;
}

.gas .view-more:after {
    background-color: #fecb01;
}

.baeder .view-more:after {
    background-color: #00c8f8;
}

.service .view-more:after {
    background-color: #004494
}

.energie .view-more:after {
    background-color: #97BF0D
}
.breitband .view-more:after {
    background-color: #52B06D;
}

.view-more:before {
    content: '';
    position: absolute;
    top: 50%;
    right: 8px;
    border-top: 8px solid #fff;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    width: 0;
    height: 0;
    z-index: 2;
    transform: translateY(-50%);
}

.rates-wrapper {
    margin-bottom: 120px;
}

.contact-form-wrapper {
    padding-top: 85px;
    color: #fff;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/contact-bg.jpg') no-repeat 50% 0;
    -ms-background-size: cover;
    background-size: cover;
    position: relative;
    z-index: 1;
}

.contact-form-wrapper .container {
    max-width: 572px;
    width: 100%;
}

.contact-option span {
    display: block;
    margin-top: 8px;
}

.form-block .form-group > label {
    float: left;
    min-width: 250px;
    padding: 3px 15px 0 0;
    margin: 0;
}

.contact-form .form-inline .form-group .field-box {
    overflow: hidden;
    float: none;
    width: auto;
}

.form-block .form-group.contact-option label {
    padding: 0;
    margin: 0;
    overflow: hidden;
    display: block;
}

.contact-option .ez-radio {
    float: left;
}

.contact-form input[type="submit"] {
    width: 100%;
    font-weight: 700;
    border-radius: 4px;
    margin-top: 30px;
}

.contact-form .form-block .row {
    margin: 0 -6px;
}

.contact-form .form-block .row > div {
    padding: 0 6px;
}

.form-inline .form-group.contact-option {
    margin-bottom: 10px;
}

.contact-form .error {
    border: 2px solid #e74a16;
}

.contact-form .ez-radio.error {
    background-color: #e74a16;
}

.home .contact-form input[type="button"],
.home .contact-form input[type="submit"] {
    background: #004494;
}

.wasser .contact-form input[type="button"],
.wasser .contact-form input[type="submit"] {
    background: #52bfd5;
}

.strom .contact-form input[type="button"],
.strom .contact-form input[type="submit"] {
    background: #e74a16;
}

.gas .contact-form input[type="button"],
.gas .contact-form input[type="submit"] {
    background: #fecb01;
}

.baeder .contact-form input[type="button"],
.baeder .contact-form input[type="submit"] {
    background: #00c8f8
}

.service .contact-form input[type="button"],
.service .contact-form input[type="submit"] {
    background: #004494
}

.energie .contact-form input[type="button"],
.energie .contact-form input[type="submit"] {
    background: #97BF0D
}

.breitband .contact-form input[type="button"],
.breitband .contact-form input[type="submit"] {
    background: #52B06D
}

.home .contact-form input[type="button"]:hover,
.home .contact-form input[type="submit"]:hover,
.wasser .contact-form input[type="button"]:hover,
.wasser .contact-form input[type="submit"]:hover,
.strom .contact-form input[type="button"]:hover,
.strom .contact-form input[type="submit"]:hover,
.gas .contact-form input[type="button"]:hover,
.gas .contact-form input[type="submit"]:hover,
.baeder .contact-form input[type="button"]:hover,
.baeder .contact-form input[type="submit"]:hover,
.service .contact-form input[type="button"]:hover,
.service .contact-form input[type="submit"]:hover
.energie .contact-form input[type="button"]:hover,
.energie .contact-form input[type="submit"]:hover
.breitband .contact-form input[type="button"]:hover,
.breitband .contact-form input[type="submit"]:hover

{
    background: #1772dd!important;
}

#lieferbeginn_start {
    width: auto;
    display: inline;
    margin-left: 26px;
}

.ui-datepicker table th {
    background: #fff;
    font-size: 15px;
    text-align: center;
}

.ui-datepicker table {
    background: #fff;
}

.ui-datepicker table tr td {
    background: #fff;
    text-align: center;
    font-size: 15px;
}

.ui-datepicker td.ui-state-disabled span,
.ui-datepicker td.ui-state-disabled a {
    color: #ccc;
}


/*.home .ui-widget-header { background-color: #004494;}*/

.contact-col {
    float: left;
    position: relative;
    padding: 6px 10px 6px 78px;
}

.contact-col strong {
    font-size: 20px;
    display: block;
}

.email-detail {
    float: right;
    padding-right: 0;
}

.contact-col a {
    text-decoration: none;
    color: #fff;
}

.email-detail a:hover {
    text-decoration: underline;
}

.contact-details {
    width: 100%;
    margin-top: -45px;
    margin-bottom: 65px;
}

.contact-col:before {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat -137px -196px;
    width: 59px;
    height: 59px;
    position: absolute;
    left: 0;
    top: 0;
}

.email-detail.contact-col:before {
    background-position: -200px -196px;
}

.wasser .contact-col:before {
    background-position: -2px -285px;
}

.wasser .email-detail.contact-col:before {
    background-position: -65px -285px;
}

.baeder .contact-col:before {
    background-position: -290px -46px;
}

.baeder .email-detail.contact-col:before {
    background-position: -353px -46px;
}

.gas .contact-col:before {
    background-position: -130px -285px
}

.gas .email-detail.contact-col:before {
    background-position: -193px -285px;
}

.strom .contact-col:before {
    background-position: -290px -123px;
}

.strom .email-detail.contact-col:before {
    background-position: -353px -123px;
}

.service .contact-col:before {
    background-position: -259px -287px;
}

.service .email-detail.contact-col:before {
    background-position: -322px -287px;
}

.energie .contact-col:before {
    background-position: -338px -199px;
}

.energie .email-detail.contact-col:before {
    background-position: -401px -199px
}

.contact-form {
    padding-bottom: 65px;
}

.important-note {
    background: rgba(231, 74, 22, 0.8);
    padding: 65px 0 50px;
}

.important-note h4 {
    font-size: 20px;
    margin-bottom: 18px;
}

.power-consumption-wrapper {
    margin-bottom: 140px;
}

.power-consumption-wrapper .button-row {
    margin-top: -20px;
}

.datepicker-col {
    background: #fff;
    position: relative;
    z-index: 2;
}

.datepicker-col:before {
    content: '';
    border-left: 1px solid #bbb;
    position: absolute;
    right: 1px;
    z-index: 1;
    top: 1px;
    width: 48px;
    height: 29px;
    background: #cccccc url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') -130px 0;
}

.datepicker-col input[type="text"] {
    background: none;
    position: relative;
    z-index: 2;
}

.datepicker-col:after {
    content: '';
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat -179px 0;
    width: 10px;
    height: 8px;
    position: absolute;
    top: 13px;
    right: 8px;
    z-index: 1;
}


/* =About Electricity page CSS
========================================================================================*/

.electricity-info-blocks {
    position: relative;
    padding-top: 20px;
    margin-bottom: 50px;
}

.electricity-info-block {
    border-radius: 15px;
    text-align: left;
    background: #e74a16;
    padding: 35px 110px 20px 25px;
    color: #fff;
}

.electricity-info-blocks .col-xs-6 {
    margin-top: 30px;
}

.about-section {
    text-align: center;
    margin-bottom: 110px;
}

.about-section .container > p {
    text-align: left;
    max-width: 680px;
    width: 100%;
    margin: 0 auto 20px;
}

.about-section h2 {
    color: #e74a16;
}

.electricity-info-blocks .sidebar {
    top: 50px;
    margin-right: 30px;
}

.download-link {
    color: #fff;
    position: relative;
    text-decoration: underline;
    clear: both;
    display: block;
    padding-left: 15px;
    word-wrap: break-word;
}

.download-link:hover {
    text-decoration: none;
}

.download-link:before {
    content: '>';
    position: absolute;
    left: 0;
}

.electricity-info-block h3 {
    margin-bottom: 25px;
    padding-right: 50px;
}

.links-box:after {
    width: 70px;
    height: 70px;
    background-position: -263px -196px;
    right: 8px;
    top: 8px;
}

.download-box.links-box ul li a {
    font-weight: 700;
}

.download-box.links-box ul li a:hover {
text-decoration:none!important;
}

.download-box.links-box ul {
    padding-right: 10px;
    padding-bottom: 10px;
}

.download-box.links-box ul li {
    margin-left: 0px;
  margin-bottom:10px;
}

.help-box figure img {
    margin: 0 0 20px;
    border-radius: 15px;
    max-width: 100%;
    height: auto;
}

.helper-name {
    display: block;
    margin-bottom: 15px;
}

.contact span {
    display: block;
}

.contact span a {
    color: #fff;
}

.contact .email {
    color: #fff;
    text-decoration: underline;
    position: relative;
    padding-left: 13px;
}

.contact .email:before {
    content: '>';
    position: absolute;
    left: 0;
}

.help-box,
.download-box {
    margin-bottom: 33px;
}

.contact .email:hover {
    text-decoration: none;
}

.help-box h4 {
    margin-bottom: 15px;
}


/* =Water page CSS
========================================================================================*/

.about-water-section {
    text-align: center;
    margin-bottom: 60px;
}

.about-water-section h2 {
    text-align: center;
    color: #52bfd5;
}

.about-water-section p {
    max-width: 720px;
    width: 100%;
    margin: 0 auto 20px;
    text-align: left;
}

.about-water-section .container {
    position: relative;
    z-index: 3;
}

.about-water-section .sidebar {
    top: 100%;
    margin-top: -100px;
}

.about-water-section .sidebar .help-box {
    margin-bottom: 0;
}

.protection-box {
    margin-bottom: 110px;
    position: relative;
    padding-top: 60px;
    color: #fff;
    z-index: 1;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/protection-bg.jpg') no-repeat 0 100%;
    -ms-background-size: cover;
    background-size: cover;
}

.protection-box:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(68, 68, 68, 0.3);
    z-index: -1;
    height: 100%;
}

.protection-box h2 {
    text-align: center;
    color: #fff;
    margin-bottom: 60px;
}

.small-container {
    max-width: 660px;
    width: 100%;
    margin: 0 auto;
}

.protection-overlay-box {
    background: rgba(82, 191, 213, 0.85);
    padding: 40px 0 25px;
    margin-top: 40px;
}

.protection-overlay-box h3 {
    font-size: 20px;
    margin-bottom: 20px;
}

.protection-overlay-box a {
    color: #fff;
}

.hardness-info-box {
    margin-bottom: 85px;
}

.hardness-info-box h2,
.construction-box h2 {
    text-align: center;
    color: #52bfd5;
}

.hardness-table {
    margin: 40px 0 55px;
}

.list {
    padding-top: 10px;
}

table.hardness-table tr:nth-child(odd) td {
    background: #52bfd5;
}

table.hardness-table th,
table.hardness-table td {
    font-weight: 400;
    padding: 30px 20px 30px 8px;
}

.quality-info-box {
    padding: 50px 0 60px;
    margin-bottom: 130px;
    color: #fff;
    position: relative;
    z-index: 1;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/quality-bg.jpg') no-repeat 50% 0;
    -ms-background-size: cover;
    background-size: cover;
}

.quality-info-box:after {
    content: '';
    position: absolute;
    top: 0;
    height: 100%;
    left: 0;
    right: 0;
    background: rgba(8, 68, 164, 0.1);
    z-index: -1;
}

.quality-info-box h2 {
    color: #fff;
    text-align: center;
}

.quality-analysis-box {
    background: #52bfd5;
    border-radius: 15px;
    padding: 25px 38px 45px;
    margin-top: 50px;
}

.quality-analysis-box h3 {
    font-size: 20px;
    text-align: center;
}

.construction-box {
    margin-bottom: 130px;
}

.construction-col {
    background: #52bfd5;
    color: #fff;
    border-radius: 15px;
    padding: 22px 25px 10px;
}

.construction-img img {
    display: none;
}

.construction-img {
    -ms-background-size: cover;
    background-size: cover;
    -webkit-background-position: 50% 0;
    -ms-background-position: 50% 0;
    background-position: 50% 0;
    border-radius: 15px;
}

.water-section input[type="submit"] {
    background: #52bfd5;
}

.water-section input[type="submit"]:hover {
    background: #0b8e36;
}

.water-section .contact-col:before {
    background-position: -290px -46px;
}

.water-section .email-detail:before {
    background-position: -353px -46px;
}


/* =marquee auf Startseite
========================================================================================*/

.marquee {
    width: 50%;
    margin: 0 auto;
    white-space: nowrap;
    overflow: hidden;
    box-sizing: border-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
}

.marquee span {
    display: inline-block;
    padding-left: 100%;
    animation: marquee 40s linear infinite;
    -webkit-animation: marquee 40s linear infinite;
}

.marquee span:hover {
    animation-play-state: paused;
    -webkit-animation-play-state: paused;
}

@keyframes marquee {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(-100%, 0);
    }
}


/* zusätzlich für Bestellformular **********************************************************************/

#strombertform .container.collapse, #strombertform .container.collapsing,
#gasbertform .container.collapse, #gasbertform .container.collapsing {
    background-color: #f5f5f5;
    margin-bottom: 10px;
    margin-top: 10px;
    padding: 15px!important;
    border-radius: 5px;
}

#strombertform div.form-block.form-inline div.button-row.containe, #gasbertform div.form-block.form-inline div.button-row.containerr {
    margin: 35px auto 0;
    cursor: pointer;
}

#strombertform div.form-block.form-inline div.button-row.container a {}


/* zusätzlich für News **********************************************************************/

.news-backlink-wrap.button {
  margin-top:30px;
}

/* zusätzlich für Akkordion **********************************************************************/

.job-accordion {
    margin-top: 40px;
    width: 100%;
}

div#accordion.panel-group div.panel-body, div#accordion2.panel-group div.panel-body, div#accordion3.panueber-uns-kachelel-group div.panel-body, div#accordion4.panel-group div.panel-body    {
  color: #444!important;
  }


.panel-heading h2.panel-title {
  font-size: 1.3em!important;
  }

.panel-heading h2.panel-title a,  .panel-heading h2.panel-title a:link {
text-decoration:none!important;
  display: block;
  }

.panel-heading h2.panel-title a:before {
    content: "⇣";
}

.panel-group .panel {
    margin-bottom: 4px!important;

}


/* zusätzlich für Kacheln **********************************************************************/

#ueber-uns-kachel
 {
    height: 280px;
    margin-left: -40px;
    margin-right: -40px;
    margin-top: -25px;
    margin-bottom: -45px;
    border-radius: 15px;
}

#ueber-uns-kachel:hover {
  background:rgba(0,68,148,0.6);
  transition: 0.2s ease-in-out;
  }

#ueber-uns-kachel-hell
 {
    height: 280px;
    margin-left: -40px;
    margin-right: -40px;
    margin-top: -25px;
    margin-bottom: -45px;
    border-radius: 15px;
  }

#ueber-uns-kachel-hell:hover {
  background:rgba(255,255,255,0.6);
  transition: 0.2s ease-in-out;
  }


/*************Tarifrechner***************************************************/
.Tx-Formhandler .small-container {
    position: relative;
}
#tarifbox {
    min-width: 321px;
    height: auto;
    position: absolute;
    top: 7px;
    left: 100%;
    font-size: 16px;
    margin-left: 15px;
    width: auto;
}
#tarifbox  .teaser {
    display: block;
    text-align: center;
    padding: 10px 30px;
    text-transform: uppercase;
    border: 1px solid #bbb;
    border-bottom: none;
}
#tarifbox .tarifdata {
    padding: 20px;
    border: 1px solid #bbb;
    border-top: none;
}
#tarifbox.strom .tarifdata {
  background: rgba(231, 74, 22, 0.6);
  color: white;
  }
#tarifbox.gas .tarifdata {
  background: rgba(254, 203, 1, 0.3);
  }
#tarifbox.wasser .tarifdata {
  background: rgba(82, 191, 213, 0.6);
  color: white;
}
#tarifbox .tarifdata .title, #tarifbox .tarifdata .value {
    width: 50%;
    display: inline-block;
}
#tarifbox .preismon .title {
    width: 100%;
    display: inline-block;
    text-align: center;
    margin-top: 30px;
}
#tarifbox .preismon .value {
    width: 100%;
    display: inline-block;
    font-size: 35px;
    font-weight: bold;
    text-align: center;
}
#tarifbox.strom .tarifdata .preismon .value {
    color: #e74a16;
}
#tarifbox.gas .tarifdata .preismon .value {
    color: #fecb01;
}
#tarifbox.wasser .tarifdata .preismon .value {
    color: #52bfd5;
}
#tarifbox.HTNT .preisarb .title, #tarifbox.HTNT .preisarb .value, #tarifbox.HTNT .preisjahr .title, #tarifbox.HTNT .preisjahr .value {
    width: 100%;
}
#tarifbox.HTNT .tarifdata .title {
    margin-top: 10px;
}
#tarifbox.HTNT .preisdetHTNT {
    font-size: 0.8em;
}
#tarifbox.HTNT .preismon .preisdetHTNT {
    font-size: 0.4em;
}
#tarifbox a.calcnew {
    display: block;
    padding: 10px 0;
    text-align: center;
    margin-top: 20px;
    text-decoration: none;
}
#tarifbox a.calcnew:hover {
    text-decoration: underline;
}
#tarifbox.strom a.calcnew {
    background-color: #e74a16;
    color: white;

}
#tarifbox.gas a.calcnew {
    background-color: #fecb01;
    color: #444;
}
#tarifbox.wasser a.calcnew {
    background-color: #52bfd5;
    color: white;
}




.tariftable {
    display:none;
}
.tarif-tab li.strom a, #strom .resulttable button.moreinfo  {
    background: rgba(231, 74, 22, 0.8);
}
.tarif-tab li.strom.active a,
.tarif-tab li.strom a:hover {
    background-color: #e74a16;
}
.tarif-tab li.gas a, #gas .resulttable button.moreinfo {
    background: rgba(254, 203, 1, 0.7);
}
.tarif-tab li.gas.active a,
.tarif-tab li.gas a:hover {
    background-color: #fecb01;
}
.tarif-tab li.wasser a, #wasser .resulttable button.moreinfo {
    background: rgba(82, 191, 213, 0.8);
}
.tarif-tab li.wasser.active a,
.tarif-tab li.wasser a:hover {
    background-color: #52bfd5;
}
.tarif-tab .tab-container{
    margin-top: -45px;
}
.tarif-tab .contentcontainer, .tarif-tab .buttoncontainer {
    color:white;
    padding: 50px 30px;
    position: relative;
}
.tarif-tab #gas .contentcontainer, .tarif-tab #gas .buttoncontainer {
    color:#444;
}
.tarif-tab .contentcontainer h1, .tarif-tab .contentcontainer h2, .tarif-tab .contentcontainer h3 {
    color:white;
    text-align: center;
}
.tarif-tab #gas .contentcontainer h2,
.tarif-tab .tabcontent#gas select.chosen-select {
    color:#444;
}
.tarif-tab .tarifform .buttoncontainer {
    margin-top: 10px;
}
.tarif-tab .tariftable .buttoncontainer {
    margin-bottom: 10px;
}
.tarif-tab button {
    border-radius: 5px;
}
.tarif-tab .tabcontent#strom .contentcontainer,
.tarif-tab .tabcontent#strom .buttoncontainer,
.tarif-tab .tabcontent#strom .buttoncontainer button:hover,
#strom .resulttable button.bestellen,
.tarif-tab .tabcontent#strom select.chosen-select{
    background-color: #e74a16;
}
.tarif-tab .tabcontent#gas .contentcontainer,
.tarif-tab .tabcontent#gas .buttoncontainer,
.tarif-tab .tabcontent#gas .buttoncontainer button:hover,
#gas .resulttable button.bestellen,
.tarif-tab .tabcontent#gas select.chosen-select{
    background-color: #fecb01;
}
.tarif-tab .tabcontent#wasser .contentcontainer,
.tarif-tab .tabcontent#wasser .buttoncontainer,
.tarif-tab .tabcontent#wasser .buttoncontainer button:hover,
#wasser .resulttable button.bestellen,
.tarif-tab .tabcontent#wasser select.chosen-select {
    background-color: #52bfd5;
}
.tarif-tab .buttoncontainer button, #wasser .resulttable button:hover, #strom .resulttable button:hover, #gas .resulttable button:hover {
    width: 100%;
    background-color: white;
    font-weight: bold;
}
.tarif-tab .tabcontent#strom .buttoncontainer button, #strom .resulttable button:hover {
    color: #e74a16;
}
.tarif-tab .tabcontent#gas .buttoncontainer button, #gas .resulttable button:hover  {
    color: #444;
}
.tarif-tab .tabcontent#wasser .buttoncontainer button, #wasser .resulttable button:hover  {
    color: #52bfd5;
}
.tarif-tab input.verbrauch-styles, .tarif-tab select.anzwohneinheiten, .tarif-tab .tabcontent#strom .buttoncontainer button:hover, .tarif-tab .tabcontent#gas .buttoncontainer button:hover, .tarif-tab .tabcontent#wasser .buttoncontainer button:hover {
    color: white;
    border: 1px solid white;
}
.tarif-tab #gas input.verbrauch-styles {
    color:#444;
}
.tarif-tab select.anzwohneinheiten option {
    color: #444;
}
.tarif-tab select.anzwohneinheiten option:hover{
    background-color: #52bfd5;
}
.tarif-tab input.verbrauch-styles, .tarif-tab select.anzwohneinheiten {
    width:100%;
    background-color:transparent;
    text-align: center;
    font-weight: bold;
}
.tarif-tab input.verbrauch-styles {
  height: 80px;
    padding: 10px;
    font-size: 1.8em;
}
.tarif-tab .nspaufteilung input.verbrauch-styles {
    font-size: 1.4em;
}
.tarif-tab select.anzwohneinheiten {
    height:66px;
}
.tarif-tab .select-styler:before {
    content: '';
    position: absolute;
    top: 50%;
    right: 23px;
    border-top: 8px solid #52bfd5;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    width: 0;
    height: 0;
    z-index: 2;
    transform: translateY(-50%);
    pointer-events: none;
}
.tarif-tab .select-styler:after {
    content: '';
    background: white;
    border-radius: 0 4px 4px 0;
    width: 25px;
    display: inline-block;
    vertical-align: middle;
    position: absolute;
    right: 16px;
    top: 0;
    bottom: 0;
    pointer-events: none;
}
.resulttable.contenttable{
    background: transparent !important;
    border-spacing: 0px 5px !important;
    border-collapse: separate !important;
    word-break: break-word;
}
.resulttable.contenttable thead tr td {
    background-color: transparent !important;
    color: white !important;
    text-align: center;
    border-bottom: 1px solid white;
    padding: 8px;
}
.resulttable.contenttable tbody tr td {
    background-color: white !important;
    color: #444 !important;
    text-align: center;
    font-weight: bold;
}
#strom .resulttable.contenttable tbody tr td {
    border-right: 2px solid #e74a16;
}
#gas .resulttable.contenttable tbody tr td {
    border-right: 2px solid #fecb01;
}
#wasser .resulttable.contenttable tbody tr td {
    border-right: 2px solid #52bfd5;
}
.resulttable.contenttable tbody tr.empfehlung td {
    background-color: #ececec !important;
}
.resulttable.contenttable tbody tr.description td {
  text-align:left;
  }

.resulttable.contenttable tbody tr td.teaser {
    max-width: 200px;
}
.resulttable.contenttable tbody tr td.vertragslaufzeit {
    max-width: 107px;
}
.resulttable.contenttable button {
    width: 100%;
    text-align: center
}
.resulttable.contenttable button.bestellen {
    margin-top:5px;
}
td.teaser {
    text-transform: uppercase;
    position: relative;
}
.home table.contenttable td .pTitle {
    font-weight: normal;
  padding-top: 10px;
}

input.verbrauch-styles.error {
    border: 1px solid orange;
    background-color: orange;
    color: white;
}
.empfehlung-box{
    position: absolute !important;
    left: -208px;
    top: 10px;
    width: 167px !important;
    text-align: left;
}
.empfehlung-box:after {
    content: '';
    position: absolute;
    right: 1px;
    top: -2px;
    background: url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/sprites.svg') no-repeat -79px -194px;
    width: 54px;
    height: 57px;
    transform: rotate(-90deg);
}
button.anzpers {
    color: #e74a16;
}
#wasser button.anzpers {
    color: #52bfd5;
}
button.anzpers {
    background-color: white;
    color: #e74a16;
    position: relative;
    padding: 14px 25px;
    transition: background-color 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
    margin-top: 10px;
}
button.anzpers i {
    transition: color 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
}
button.anzpers:hover, button.grhaushalt:hover, #wasser button.anzpers:hover,
button.anzpers.active, button.grhaushalt.active, #wasser button.anzpers.active{
    color: white;
    background-color: #444;
}
button.anzpers i.custom {
    position: absolute;
}
button.anzpers i.custom.white {
    color: white;
    right: 7px;
    bottom: 10px;
}
button.anzpers:hover i.custom.white {
    color: #cccccc;
}
button.anzpers i.custom.red {
    right: 12px;
    bottom: 15px;
    font-size: 1.5em;
}
button.anzpers:hover i.custom.red {
    color: white;
}
button.grhaushalt {
    background-color: white;
    color: #fecb01;
    position: relative;
    padding: 14px 45px;
    transition: background-color 550ms cubic-bezier(0.450, 1.000, 0.320, 1.000);
    margin-top: 10px;
}

button.grhaushalt span.haushaltlabel {
    color: #444;
    position: absolute;
    font-weight: bold;
    font-size: 1.5em;
    bottom: 16px;
    left: 50px;
    z-index: 11;
}
.grhaushalt i.fa{
    transform: scale(2.4,1.6);
}
button.grhaushalt + button.grhaushalt, button.anzpers  + button.anzpers  {
    margin-left: 20px;
}
.wasserrechner {
    transition: opacity 400ms ease;
    opacity: 1;
}
.inactive {
    opacity: 0.5;
    pointer-events: none;
}
#tarifrechner hr {
  border-bottom: 1px solid white;
  }
#tarifrechner hr:before {
  background: none;
  }
#wasser .wohnungtext {
  display:none;
  }

div.maxverbrauch { display:none; }
div.maxverbrauch.visible { display:block; }

#tx_indexedsearch {text-align:center;}



img.hover-zoom{
  max-width: 100%;

  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  cursor:zoom-in;
}

img.hover-zoom:hover {
  -moz-transform: scale(1.5);
  -webkit-transform: scale(1.5);
  transform: scale(1.5);
  cursor:zoom-in;
  }

.zoomcontainer {
  position:relative;
width:100%;


  z-index:998;
}

.zoomcontainer:hover {
  position:relative;
width:100%;

  z-index:999;
}


/* KICK TIPP */

.logoleiste-titelbild img{
display:none!important;
}


/* Produktdarstellung comBert
========================================================================================*/

.sonderrabatt-tarife {
text-align:center;
  padding-top: 10px;
  padding-bottom:10px;
  background-color:#e2006e;
  border-bottom:10px solid #fff;
}

.sonderrabatt-tarife-text {
text-align:center;
  color:#fff;
  font-weight:700;
  font-size:16px;
}

li.produktfeature {
font-size: 16px;
line-height: 1.2em;
  margin-bottom:5px;
}

.combert-hover .combert-hoverbox {

  opacity: 0.3;
  -webkit-transition: all 0.6s ease;
transition: all 0.6s ease;
}

.combert-hover:hover .combert-hoverbox {

  opacity: 1;
  -webkit-transition: all 0.6s ease;
transition: all 0.6s ease;
}

.combert-hoverbox {
position:absolute;
     font-size: 12px;
    margin-top: -10px;
    font-weight: 500;
    background-color: #e2006e;
    padding: 0 4px 0 4px;
    color: #fff;
}

.stadtrundgang-headline {
text-transform:uppercase; 
margin:0px;
padding: 0px;
color: #fff;
font-weight: bold;
font-size: 56px;
opacity: 0.5;
line-height: 58px;
}



.hoverteaser {
background-color:rgba(255,255,255, 0.7); 
    -webkit-transition: all 0.6s ease;
transition: all 0.6s ease;

padding:15px;
}

.hoverteaser:hover {
background-color:rgba(255,255,255, 1.0); 
    -webkit-transition: all 0.6s ease;
transition: all 0.6s ease;
}

.anchormenu.active .randbox {
    bottom: 0;
}
.randbox {
    display: inline-block;
    position: fixed;
    bottom: -80px;
    left: 50%;
    margin-left: -250px;
    z-index: 1000000;
   
    transform: translateY(100%) translateY(-65px);

    width: 500px;

    margin-right: -250px;
    -webkit-transition: all 0.6s ease;
    transition: all 0.6s ease;
    border-top-left-radius: 13px;
    border-top-right-radius: 13px;
  opacity:0.8;
}

.randbox.active {
       -webkit-transition: all 0.6s ease;
    transition: all 0.6s ease;
    transform: translateY(0);
    opacity:1;
}

.randbox-top {
    margin-left: 125px;
    background-color: #ff9900;
    padding: 15px;
    /* margin-top: -36px; */
    color: #fff;
    border-top-left-radius: 13px;
    border-top-right-radius: 13px;
   width: 250px;
    text-align: center;
}

.randbox-content {padding: 15px;
background-color: #ff9900;
      border-top-left-radius: 13px;
    border-top-right-radius: 13px;
}

.randbox-content a {
    width:100%;
    text-align:center;
}

.breitband .Tx-Formhandler h2{color:#fff;}



.category-nav.tabmenuContainer {
    width: 100%;
    left: 0;
}

.category-nav .tabmenu .tabs {
    display: flex;
    /* display: inline-block; */
}

.category-nav .tabmenu .tabs > div {
    display:  flex;
    align-items: center;
    justify-content: center;
    position:relative;
    width: 20%;
    opacity: 0.9;
    padding: 7px 0;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}
.category-nav .tabmenu .tabs > div.active {
    opacity: 1;
}

.category-nav .tabmenu .tabs > div.active::after {
    display: inline-block;
    content: " ";
    background-color: inherit;
    height: 25px;
    width: 25px;
    position: absolute;
    left: 50%;
    bottom: -20px;
    z-index: 10;
    transform: rotate(45deg) translateX(-50%);
}

.category-nav .tabmenu .tabs.tabs-firmen > div {
    width: 33.333%;
}


.category-nav .tabmenu .tabs img {
    height: 47px;
}

.category-nav .tabmenu .tabs span {
    margin-left: 17px;
    color: #FFFFFF;
    text-transform: initial;
    font-size: 19px;
}

.category-nav .tabmenu .tabs .wasser {
    background-color: #81bad4;
}
.category-nav .tabmenu .tabs .strom {
    background-color: #c4571c;
}
.category-nav .tabmenu .tabs .gas {
    background-color: #eecc00;
}
.category-nav .tabmenu .tabs .enservice {
    background-color: #a1bc00;
}
.category-nav .tabmenu .tabs .service {
    background-color: #033076;
}

.category-nav .tabmenu .tab-content {
    background-color: rgba(255,255,255,0.95);
    border-top: 5px solid;
    border-bottom: 5px solid;
    border-color: rgba(0,0,0,0);
    display: none;
    flex-direction: row;
}
.category-nav .tabmenu .tab-content.active {
    display: flex;
}
.category-nav .tabmenu .tab-content-wrapper > div {
    padding-top: 20px;
    padding-bottom: 20px;
}
.category-nav .single-column {
    width: 33.3%;
}
.category-nav .double-column {
    width: 66.6%;
}
.category-nav .single-buttons {
    display: flex;
    flex-direction: column;
    padding-left: 20px;
    padding-right: 20px;
}
.category-nav .single-buttons a {
    padding: 0px;
    margin: 3px;
    border-radius: 3px;
    text-align: center;
    background-color: #444;
    color: #FFF;
    text-transform: none;
}
.category-nav .tab-content-wrapper .double-buttons > div  {
    width: 50%;
    float: left;
}
.double-buttons .less-left-p {
    padding-left: 5px !important;
}
.double-buttons .less-right-p {
    padding-right: 5px !important;
}
.category-nav .tab-content-wrapper .img-text-teaser {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-right: 20px;
}
.category-nav .tab-content-wrapper .img-text-teaser .img-wrapper {
    width: 175px;
    height: 175px;
    overflow: hidden;
    border-radius: 90px;
    border: 5px solid;
    border-color:  inherit;
    align-self: flex-start;
    flex-basis: 175px;
    flex-grow: 0;
    flex-shrink: 0;
}
.category-nav .tab-content-wrapper .img-text-teaser.single-column .img-wrapper {
    width: 155px;
    height: 155px;
    flex-basis: 155px;
}
.category-nav .tab-content-wrapper .img-text-teaser .img-wrapper img {
    height: 100%;
    width: auto;
}
.category-nav .tab-content-wrapper .img-text-teaser .rt-section {
    padding-left: 15px;
    line-height: 125%;
    text-transform: none;
    font-weight: normal;
}
.category-nav .tab-content-wrapper .rt-text-centered {
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: none;
    line-height: normal;
    font-weight: normal;
    background-color: #FFF;
    margin-top: -20px;
    margin-bottom: -20px;
}
.category-nav .tab-content-wrapper .rt-text-centered p {
    margin-bottom: 5px;
}
.category-nav .tab-content-wrapper .single-img {
    margin-top: -20px;
    margin-bottom: -20px;
}
.category-nav .tab-content-wrapper .single-img img {
    width: auto;
    max-height: 265px;
}

.category-nav .tabmenu .tab-footer-links {
    background-color: rgba(255,255,255,0.9);
    box-shadow: inset 0 10px 9px -7px rgba(194,194,194,.8);
    display: flex;
    justify-content: center;
    padding: 25px 0;
    width:  100%;
}

.category-nav .tabmenu .tab-footer-links > img {
    height:  40px;
}

.category-nav .tabmenu .tab-footer-links img {
    height: 45px;
}

.category-nav .tabmenu .tab-footer-links > a {
    color: #FFFFFF;
    font-size: 18px;
    width: 23%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 0;
    margin: 0 8px;
    border-radius: 3px;
    text-transform: none;
}
.category-nav .tabmenu .tab-footer-links .breitband {
    background-color: #77ac6a;
}
.category-nav .tabmenu .tab-footer-links .energieberatung {
    background-color: #033076;
}
.category-nav .tabmenu .tab-footer-links .konto {
    background-color: #033076;
}
.category-nav .tabmenu .tab-footer-links .ansprechpartner {
    background-color: #555;
}
.category-nav .tabmenu .tab-footer-links .wasser {
    background-color: #81bad4;
}
.category-nav .tabmenu .tab-footer-links .strom {
    background-color: #c4571c;
}
.category-nav .tabmenu .tab-footer-links .gas {
    background-color: #eecc00;
}

.category-nav .tabmenu .tab-footer-links .energiedienste {
    background-color: #a1bc00;
}
.category-nav .tabmenu .tab-footer-links .service {
    background-color: #033076;
}
.category-nav .tabmenu .tab-footer-links span {
    margin-left: 8px;
}

/* Tabmenu Firmen */ 
.category-nav .tabmenu .tabs .klein {
    background-color: #aaaaaa;
}
.category-nav .tabmenu .tabs .gross {
    background-color: #969696;
}
.category-nav .tabmenu .tabs .wohnung {
    background-color: #707070;
}
.tabmenu-firmen .tab-footer-links {
    padding-left: 240px !important;
    padding-right: 50px !important;
    height: 275px;
    flex-wrap: wrap;
    justify-content: flex-start !important;
    align-items: flex-start;
    flex-direction: column;

}
.tabmenu-firmen .tab-footer-links > a {
    width: 32% !important;
    margin: 0 2px 10px 2px !important;
    justify-content: flex-start !important;
    padding-left: 17px !important;
}
.tabmenu-firmen .tab-footer-links > a span {
    padding-left: 7px;
}

.triple-column.img-text-teaser:not(.small-img) {
    width: 100%;
    padding: 0 30px;
}
.triple-column.img-text-teaser:not(.small-img) .img-wrapper {
    margin-right: 20px;
    margin-bottom: -55px;
}

/* Tabmenu Unternehmen */
.tabmenu-blank {
    position: relative;
}
.tabmenu-blank .tab-content-wrapper {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    padding: 0 15px;
}
.tabmenu-blank .tab-content {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    border: none !important;
    box-shadow: 0px 8px 6px -4px rgba(194,194,194,.8);
}
.tabmenu-blank .rt-section p {
    margin-bottom: 4px;
}
.tabmenu-blank .img-text-teaser.small-img .img-wrapper {
    width: 125px;
    height: 125px;
    flex-basis: 125px;
    margin-bottom: 0px;
}
.tabmenu-blank .img-text-teaser.small-img {
    background-color: #FFF;
    padding: 15px 30px;
}
.tabmenu-blank .tab-footer-links {
    padding-top: 185px !important;
}
.tabmenu-blank .text-button {
    text-transform: none;
    font-weight: normal;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 20px;
    background-color: #FFF;
    margin: -10px 10px;
}
.tabmenu-blank .text-button p {
    line-height: initial;
}
.tabmenu-blank .text-button a {
    padding: 0px;
    margin: 3px;
    border-radius: 3px;
    text-align: center;
    background-color: rgb(3, 48, 118);
    color: #FFF;
    text-transform: none;
    font-weight: 700;
}

/* Tabmenu Netze */

.tabmenu-netze .single-buttons a.button-large {
    width: 65%;
    padding: 0 10px;
    margin:  0 auto;
}
.tabmenu-netze .single-buttons a {
    text-align: left;
    font-size: 18px;
    padding: 4px 20px;
    width: 80%;
    margin: 5px auto;
    border-radius: 3px;
}
.tabmenu-netze .single-buttons a.wassernetz {
    background-color: #81bad4;
}
.tabmenu-netze .single-buttons a.stromnetz {
    background-color: #c4571c;
}
.tabmenu-netze .single-buttons a.gasnetz {
    background-color: #eecc00;
}
.tabmenu-netze .single-buttons img {
    padding-right: 15px;
}
/* only ipad 1024 and bigger screen landscape screen */
@media only screen and (min-width: 768px) {
  #mainmenu > ul { display:block !important; }
  .category-menu { display:block !important; }
}


/* ========== FÜR COMBERT TV =========== */
@media only screen and (min-width: 992px) {
.cbtvabstand {
margin-left: 13%!important;
}
}


@media only screen and (max-width: 1750px) {

  .sidebar { margin:0; }
  .orange-box { width:201px; padding:15px; }
  .orange-box h4 { font-size:22px; margin-bottom:15px; }

}

@media only screen and (max-width: 1600px) {
  .benefit-details p { margin-left:30px; margin-right:30px; text-align:center; }
  .sidebar { position:static; margin:30px auto 0; padding:0 15px; }
  .orange-box { max-width:300px; width:100%; margin:0 auto 30px; padding:15px 25px; }
  .tab-container .orange-box { max-width:670px; text-align:left; }

  .help-box { margin-bottom:30px; text-align:center; }
  .electricity-info-blocks .sidebar { margin-right:0; }
  .orange-box h4 br { display:none; }
  .links-box { text-align:left; }
  .about-section .button-row { text-align:center; }
  .about-water-section .sidebar { margin:40px auto 0; }
 }
@media only screen and (max-width: 1500px) {
  .empfehlung-box {
    left: -173px;
  }
}
@media only screen and (max-width: 1400px) {
  .benefit-col h3 { font-size:22px; line-height:1.15; }
  .benefit-col:hover .benefit-details p { margin-top:15px; }
  .news-box { width:22%; }
  .news-box.is-selected { width:46%; }
  .flickity-prev-next-button { left:22%; }
  .flickity-prev-next-button.next { right:22%; }
  .news-box.is-selected h3 { font-size:24px; padding:0 10%; }
  .news-box h3 { font-size:17px; padding:0 10%; }
  .empfehlung-box{
    left: -132px;
    width: 127px !important;
    padding: 10px !important;
  }
}
@media only screen and (max-width: 1360px) {
  .empfehlung-box {
    display:none;
  }
}
@media only screen and (max-width: 1360px) {
  #tarifbox {
    position: relative;
    top: 0px;
    left: 0px;
    margin-left: 0px;
    width: 100%;
  }
}
@media only screen and (max-width: 1199px) {
  .benefit-col h3 { font-size:26px; }
  .container { width:auto; }
  .news-box.is-selected { padding:30px 20px; }
  .news-box.is-selected h3, .news-box h3 { padding:0 6%; }
  .information-row { -webkit-background-size:cover; -ms-background-size:cover; background-size:cover; -webkit-background-position:25% 100%; -ms-background-position:25% 100%; background-position:25% 100%; overflow:hidden; }
  .info-box .info-col { padding-right:50px; }
  .info-box .col-sm-6 { padding:0 30px; }
  .info-box .row { margin:0 -30px; }
  #maincontent{padding:30px 20px;}
  .tarif-tab .resulttable button.moreinfo, .tarif-tab .resulttable button.bestellen {
    padding: 14px 0px;
  }
  .home-slider .item.small-item, .main-banner.small-banner.overlay-wrapper {
    padding-top: 130px;
  }

}
@media only screen and (max-width: 1350px) {

  .stoerer-overlay-text h2 {
    font-size: 32px;
    font-weight: normal;
    margin-top:0px;
  }

  .stoerer-overlay-text h3 {
    font-size: 24px;
   }

  .stoerer-overlay-text h3 {
    font-size: 20px;
   }

}

@media only screen and (max-width: 1100px) {
  #footer:before { height:221px; -webkit-background-size:100% 100% !important; -ms-background-size:100% 100% !important; background-size:100% 100% !important; }
  .benefit-details p { margin-left:20px; margin-right:20px; }
  .news-row span { margin-left:10px; }
  .news-box.is-selected h3, .news-box h3 { padding:0; }

  .banner-content p br { display:none; }
  .home-slider .owl-dots { bottom:10px; }
  .banner-content h2 { font-size:30px; }
  .banner-content h3 { font-size:18px; margin-bottom:10px; }
  .banner-content { font-size:15px; padding:15px 0 30px; }
  .navigation-row .container { padding:0; }

  .info-box { padding-left:200px; }
  .tabnav li a { font-size:30px; }
  .electricity-info-blocks { padding:0; }
}

/*==========================================================================================================*/
@media only screen and (max-width: 1000px) {
  body { min-width:320px; }
  img:not(.tarifimg){ max-width: 100%; width:auto; height:auto; }
  .wrap { width:auto; padding:0 20px; }

  .default-grid.form-grid.cols2 .col { width:auto; float:none; }
  .buttonset .col a { margin:0 5px 8px 0; }
  .default-grid.cols4 .col { width:50%; }
  .cols2.default-grid.tab-grid .col { width:100%; }

  .image-block figure { width:45%; }

  #footer:before { height:166px; }
  .footer-block { padding:55px 0; }
  .fmenu { padding-top:60px; }
  .contact-box { padding-top:105px; }
  .pools-wrapper, .news-wrapper { margin-bottom:60px; }
  .container { width:auto; }
  .pool-box h3 { font-size:22px; }
  .pool-info { font-size:17px; }
  .pool-box a.button { font-size:14px; padding-left:10px; padding-right:10px; }
  .pools-row { padding:65px 0 0; }
  .news-row { margin-top:60px; font-size:15px; padding:15px 0; }
  .news-box, .news-box.is-selected { width:100%; }
  .flickity-prev-next-button { left:0; }
  .flickity-prev-next-button.next { right:0; }
  .news-box-outer { width:auto; padding:0; }
  .news-box { margin:0 20px; }
  .news-box.is-selected { margin:0; }
  .news-slider { margin-bottom:40px; padding:0 50px; }
  .flickity-viewport { overflow:hidden; }
  .news-box.is-selected h3 { margin-bottom:15px; }
  #maincontent { padding-top:50px; }

  #mainmenu > ul > li > a { padding:0 18px; }
  .pools-list-trigger { padding:0 25px; }
  .pools-list ul li a { font-size:15px; padding:5px 15px; }
  .pools-list ul { border:1px solid #00c8f8; border-bottom:5px solid #00c8f8; }

  #logo { width:350px; margin-top:12px; }
  .banner-info-bubble h4 br { display:none; }
  .banner-info-bubble h4 { margin-bottom:10px; }
  .banner-info-bubble { top:-185px; padding:10px 15px; width:190px; height:190px; }
  .bubble-content { font-size:13px; }
  .sub-navigation { width:350px; }
  .banner-content h2 { padding:0; }

  .category-box h4 { font-size:14px; word-wrap:break-word; }
  .category-box { font-size:14px; }

  .tabnav li a { font-size:26px; padding:12px; }
  .tab-container .pool-contact-box { margin-top:30px; }
  .tab-data { padding-bottom:60px; }
  .info-box .col-sm-6 { padding:0 15px; }
  .info-box .row { margin:0 -15px; }
  .info-box { padding-left:160px; font-size:15px; }
  .information-row { -webkit-background-position:17% 100%; -ms-background-position:17% 100%; background-position:17% 100%; margin-bottom:60px; }
  #cources li h4 { padding:5px 10px; font-size:14px; text-align:center; }
  .download-box ul li { margin-bottom:10px; }
  .pool-details .button-row { text-align:right; }

  .pool-detail-box .container { padding-left:15px; }
  .pool-detail-box { padding:50px 0 25px; }
  .pool-detail-row, .pools-types { margin-bottom:55px; }
  .pools-types { text-align:center; }
  .pool-type { padding:30px 30px 10px; }
  .pool-type h3 { margin-bottom:20px; }
  .timings-table { margin-top:50px; }
  .contact-row .pool-details { padding:40px 50px 25px 0; }
  .contact-row { padding:50px 0; }
  .information-row.subscription-info { margin-bottom:0; }

  .advantages { padding-left:15px; }
  .brand-info-box { font-size:15px; padding:20px; }
  .tarif-wrapper, .power-consumption-wrapper, .rates-wrapper, .about-section { margin-bottom:60px; }
  .rates-overview { padding:50px 0; }
  .step-col { font-size:15px; padding:20px 15px; }
  .power-consumption-row { padding:30px 0 35px; margin-bottom:40px; }
  .power-consumption-wrapper .button-row { margin:0; }
  .rate-calculator { padding-top:50px; margin-bottom:40px; }
  .rate-calculator h2, .contact-form-wrapper h2 { margin-bottom:30px; }
  .rate-info-box .banner-info-bubble { right:-28px; top:-90px; }
  .contact-details { margin-top:30px; }
  .contact-form { padding-bottom:30px; }
  .important-note { padding:40px 0 25px; }
  .contact-form-wrapper { padding-top:50px; }
  .power-info-box:before { -webkit-transform:scale(0.7); -ms-transform:scale(0.7); transform:scale(0.7); -webkit-transform-origin:0 50%; -ms-transform-origin:0 50%; transform-origin:0 50%; }

  .electricity-info-block h3 { padding:0; font-size:26px; }
  .electricity-info-block { padding:25px; }
  .about-section .container > p { text-align:center; }
  .about-water-section { margin-bottom:35px; }
  .protection-box h2 { margin-bottom:35px; }
  .protection-box { padding-top:40px; margin-bottom:60px; }
  .protection-overlay-box { margin-top:20px; }
  .hardness-table { margin:0 0 30px; }
  .small-container { max-width:none; }
  .hardness-info-box, .quality-info-box, .construction-box { margin-bottom:50px; }

}
@media only screen and (max-width: 991px) {
  .stoerer-overlay .stoerer-overlay-text {
      left: 2vw;
      width: 71vw;
      max-height: calc(100% - 180px);
  }

}

/*==========================================================================================================*/
@media only screen and (max-width: 930px) {
  .category-nav, .firmenkunden .category-nav {
    width: 460px;
  }

  .category-nav {
    left: 10px;
  }
  .navunternehmen .category-nav {
    left: auto;
  }
  .sub-navigation {
    width: 300px;
  }
   .navigation-row .category-nav > ul > li.nav131,  .navigation-row .category-nav > ul > li.nav71,  .navigation-row .category-nav > ul > li.nav19,  .navigation-row .category-nav > ul > li.nav20,  .navigation-row .firmenkunden .category-nav > ul > li.nav24,  .navigation-row .firmenkunden .category-nav > ul > li.nav132,  .navigation-row .firmenkunden .category-nav > ul > li.nav133 {
    width: 59%;
  }
   .navigation-row .category-nav > ul > li.nav131,  .navigation-row .category-nav > ul > li.nav19,  .navigation-row .category-nav > ul > li.nav71,  .navigation-row .category-nav > ul > li.nav20,  .navigation-row .firmenkunden .category-nav > ul > li.nav24,  .navigation-row .firmenkunden .category-nav > ul > li.nav132,  .navigation-row .firmenkunden .category-nav > ul > li.nav133 {
    left: 41%;
  }
  .stoerer-overlay {
    min-height: 230px;
  }
}
@media only screen and (max-width: 855px) {
  #mainmenu {
    font-size: 12px;
  }
.randbox {display:none;}
}
@media only screen and (max-width: 768px) {
  .home-slider .owl-prev, .home-slider .owl-next {
    display: none !important;
  }
}
/*==========================================================================================================*/
@media only screen and (max-width: 767px) {

  .default-grid.cols3 .col { width:50%; }
  .default-grid.cols3 .col:nth-child(3) { clear:left; }


  .heading-listblock.cols2 .col, .dropcaps-box.cols2 .col { width:100%; float:none; }
  h1, h2 { font-size:40px; }
  [class*="col-"] + [class*="col-"]{margin-top: 30px;}
  .Tx-Formhandler [class*="col-"] + [class*="col-"] {margin-top:0px; }
  .home-slider .item {
    height: calc(100vh - 145px);
  }
  .banner-info-bubble {display:none;}
  .fmenu li { font-size:13px; }
  #footer h3 { font-size:28px; margin-bottom:35px; }
  h2, .benefits-wrapper h2, .pools-wrapper h2, .news-wrapper h2, .courses-wrapper h2, .pool-detail-row h2, .timings-table h2, .contact-row h2, .subscription-info.information-row h2, .energy-wrapper h2, .rates-overview h2, .tarif-wrapper h2, .rates-overview h2, .rate-calculator h2, .contact-form-wrapper h2, .about-section h2, .protection-box h2 { margin-bottom:20px; }
  h2 { font-size:28px; }
  .footer-block { padding:40px 0; }
  #footer:before { height:123px; }
  #maincontent { padding-bottom:30px; }
  .contact-wrapper { margin-bottom:45px; }
  .fmenu { padding-top:40px; }
  .benefit-col h3 { font-size:20px; }
  .benefit-details { font-size:14px; }

  .benefits-row, .pools-row, .news-slider { margin-bottom:40px; }
  .pool-box { max-width:320px; margin:0 auto; }
  .pool-box h3 { font-size:26px; }
  .pool-info { font-size:20px; }
  .pool-box a.button { padding:5px 30px 6px; font-size:16px; }
  .pools-row .col-sm-4:not(:first-child) { margin-top:30px; }
  .news-box.is-selected h3 { font-size:22px; }
  .button { font-size:15px; }
  #mainmenu > ul > li > a { padding:0 10px; }
  #mainmenu, .navigation-row .pools-list-trigger span { font-size:12px; }
  .action-box { width:200px; }
  .header-right-section { font-size:11px; padding-right:62px; }
  #menu { width:55px; }
  .topnav li a { padding:5px 8px 6px; }
  #logo { width:255px; }
  #header { position:relative; }
  .header-top { padding-top:10px; }
  #mainmenu > ul > li > a:after, .pools-list-trigger:after, .pools-list-trigger.list-open:after { border-top-width:20px; border-left-width:25px; border-right-width:25px; }
  .pools-list ul li a { font-size:14px; }
  .pools-list { padding-top:25px; }
  .category-nav { position:static; padding:0; width:auto; text-align:left; }
  .category-nav > ul { padding:0 0 25px; background:#f5f5f5; }

  .banner-content .button { float:none; }
  .home-slider .owl-dots { text-align:center; bottom:15px; }
  .banner-content { padding-bottom:40px; }
  .megamenu .col-sm-3 {  }
  .category-box { font-size:14px; }
  .category-box > ul:after { content:''; display:table; clear:both; }
  .megamenu { margin-top:5px; }
  .megamenu-col > h3 a { line-height:42px; }
  .category-menu { display:none; }
  .megamenu .col-sm-3:first-child .category-menu { display:block; }
  .megamenu-col > h3 a:after { content:''; display:inline-block; vertical-align:middle; border-top:solid 5px #004494; border-right:solid 5px transparent; border-left:solid 5px transparent; margin:-2px 0 0 6px; }
  .megamenu-col > h3 a.category-menu-open:after { -webkit-transform:rotate(180deg); -ms-transform:rotate(180deg); transform:rotate(180deg); }
  .megamenu-col.company-col > h3 a:after, .megamenu-col.pools-col > h3 a:after { border-top-color:#fff; }
  .megamenu-col > h3 { border-color:#ccc; border-right:0; }
  .megamenu-col.company-col > h3, .megamenu-col.pools-col > h3 { border:0; }

  .tabnav li a { font-size:20px; }
  .tab-heading { margin-bottom:30px; }
  .pool-details { font-size:18px; margin-bottom:10px; }
  .pool-details a.button { font-size:14px; }
  table th, table td { font-size:15px; }
  .time-info { min-width:115px; }
  .tab-data { padding-bottom:50px; }
  .information-row h2 { margin-bottom:30px; }
  .signup-box input[type="email"] { background:#d6d6d6; }
  .information-row { margin-bottom:40px; }
  #cources { margin:0; padding:0 5px; }
  #cources li { width:49.2%; }
  .tel-number { display:block; }
  .pool-contact-box p br { display:none; }
  .sidebar { font-size:15px; padding:0; }
  .news-wrapper { margin-bottom:40px; }

  .pools-types .col-sm-6:first-child { margin-bottom:30px; }
  .pool-detail-row, .pools-types { margin-bottom:30px; }
  .orange-box { max-width:none; }
  .time-table-wrapper { padding:0; }
  .deal-info { font-size:15px; }
  .deal-info p { margin-bottom:12px; }
  .pool-detail-box .banner-info-bubble { margin:0 0 10px 80px; width:150px; height:150px; }
  .pool-detail-box .banner-info-bubble h4 { font-size:14px; }
  .pool-detail-box { padding:35px 0 15px; font-size:15px; }
  .pool-type h3 { margin-bottom:15px; }

  .tarif-wrapper .row .col-sm-4:not(:first-child) { margin-top:15px; }
  .brand-info-box h4 { font-size:18px; margin-bottom:15px; }
  .advantages { padding-top:10px; }
  .tarif-wrapper, .power-consumption-wrapper, .rates-wrapper, .about-section { margin-bottom:40px; }
  .rates-overview { padding:40px 0; }
  .rates-steps-row .col-xs-6 { margin-top:20px; }
  .step-number { font-size:120px; }
  .rates-steps-row { padding-top:0; }
  .total-consuption { text-align:left; }
  .total-power { margin:0 0 12px; text-align:center; }
  .rate-info-box .banner-info-bubble { right:15px; top:-70px; }
  .rate-info-box { margin-top:20px; }

  .electricity-info-block h3 { font-size:24px; margin-bottom:15px; }
  .electricity-info-blocks { margin-bottom:40px; }
  .electricity-info-blocks .row { margin-top:-20px; }

  .hardness-info-box, .quality-info-box, .construction-box, .protection-box { margin-bottom:35px; }
  .about-water-section { margin-bottom:25px; }
  .protection-overlay-box { margin:0; padding:25px 0 5px; }
  .quality-info-box { padding:35px 0 40px; }
  .construction-img { min-height:300px; margin-top:20px; }
  .quality-analysis-box { margin-top:30px; padding:20px 25px 25px; }
  .about-water-section .sidebar { margin-top:30px; }

  .mobileLimit{max-width:100%;overflow-x: scroll}
  .mobileLimit .contenttable {width:767px;}

  .banner-info-bubble {top: -128px;padding: 10px 15px;width: 158px;height: 158px;  }



  #mainmenu > ul { display:none; position:absolute; top:100%; width:100%; left:0; }
  #mainmenu > ul > li { float:none; line-height:1.3; border-top:1px solid #8bb41b; position:relative; background:#f5f5f5; }
  #mainmenu > ul > li:first-child { border:0; }
  .navigation-row #mainmenu > ul > li > a { color:#fff; background:#98c21d; padding:12px 15px; display:block; }
  .pools-list-trigger { line-height:40px; font-size:12px; padding:0 20px; }
  #menu-trigger { margin:10px 0 0 15px; float:left; display:block; }
  #menu-trigger .menulines:before, #menu-trigger .menulines:after { background:#fff; }
  #menu-trigger .menulines { background:#fff; }


  .navtrigger { position:absolute; right:10px; top:8px; border-radius:50%; width:24px; height:24px; background:#fff; z-index: 9999999999999 !important; cursor:pointer; }
  .navtrigger:after { content:''; position:absolute; top:10px; border-top:6px solid #98c21d; border-left:6px solid transparent; border-right:6px solid transparent; left:6px; }
  .navtrigger.open:after, .category-nav > ul > li > a.show:after { -webkit-transform:rotate(180deg); -ms-transform:rotate(180deg); transform:rotate(180deg); margin-top:-2px; }
  #mainmenu > ul > li > a:after { display:none; }
  .navigation-row { margin-top:5px; background:#00c8f8; }
  .navigation-row .category-nav > ul {
    padding-bottom: 0px;
  }
  .navigation-row .category-nav > ul > li {
    display:block;width: 100% !important;
    height: auto;
    padding: 5px 0px;margin-top:5px;
  }
  .navigation-row .category-nav > ul > li.hassub{ position: relative}
  .navigation-row .category-nav > ul > li.hassub > span.subtrigger{position: absolute;height: 45px;width: 52px;right: 0px;top: 0px;cursor:pointer;z-index: 9999999999999 !important}
  .navigation-row .navunternehmen .category-nav > ul > li > a {
    text-align: left;
    padding-left: 25px;
    height: auto;
  }
   .navigation-row .category-nav > ul > li.nav131,  .navigation-row .category-nav > ul > li.nav19,  .navigation-row .category-nav > ul > li.nav71,  .navigation-row .category-nav > ul > li.nav20,  .navigation-row .firmenkunden .category-nav > ul > li.nav24,  .navigation-row .firmenkunden .category-nav > ul > li.nav132,  .navigation-row .firmenkunden .category-nav > ul > li.nav133 {
    position: relative;
    left: 0px;
    top: 0px;
  }
  .sub-navigation ul li:not(:first-child) { border-top:1px solid #ddd; }
  .firmenkunden .category-nav {width:100%;}
  .category-nav > ul > li { display:block; padding:0; text-align:left; margin:0; border-top:1px solid #e4e4e4; font-size:14px;}
  .category-nav > ul > li:first-child { border:0; }
  .category-nav > ul > li > a { position:relative; padding:10px 15px 10px 50px;height: 35px;
    width: 100%; }
  .category-nav > ul > li > a:before { position:absolute; -webkit-transform:scale(0.35); -ms-transform:scale(0.35); transform:scale(0.35); -webkit-transform-origin:0 0; -ms-transform-origin:0 0; transform-origin:0 0; left:15px; top:8px;
    background-size: 30px;
    margin-left: 0px;
    height: 65px !important;
    width: 65px !important;}
  .category-nav > ul > li > a br { display:none; }
  .navunternehmen .category-nav { width: 100% }
  .sub-navigation { position:static; width:100%; }
  .sub-navigation h4 { display:none; }
  .sub-navigation ul li a { padding:10px 15px; font-size:13px; }
  .category-nav > ul > li.hassub > a:after { content:''; position:absolute; top:17px; border-top:5px solid #004494; border-left:5px solid transparent; border-right:5px solid transparent; opacity:0.8; right:17px; }
  .category-nav > ul > li.nav19 > a, .category-nav > ul > li.nav71 > a, .category-nav > ul > li.nav19 > a, .category-nav > ul > li.nav24 > a, .category-nav > ul > li.nav131 > a, .category-nav > ul > li.nav134 > a, .category-nav > ul > li.nav133 > a{ max-width:none;  }

  .tarif-tab .tab-container{margin-top: -30px;}
  .tarif-tab [class*="col-"] + [class*="col-"] {margin-top: 0px;margin-bottom:30px;}
  .resulttable.contenttable{
    word-break: normal;
  }

.news-list-view.container .article .news-img-wrap, .news-list-view.container .article .teaser-text {display:block!important;}

  .stoerer-overlay {
     height: initial;
     min-height: 80px;
  }
  .stoerer-overlay .stoerer-overlay-text {
      max-height: 100%;
      top: 20px;
      width: 100%;
  }
  .stoerer-overlay-text p {
    margin-top: 0px;
  }

  .stoerer-overlay-text h3 {
    margin-top: 0px;
  }

  .anchor-teaser {
    padding: 15px;
  }
  .fullscreen-banner .row.heading {
    margin-top: 15px;
  }
  .fullscreen-banner h2 {
    font-size: 35px;
  }
  .fullscreen-banner h2 img {
    height: 35px; 
  }

}
@media only screen and (max-width: 674px) {

  .stoerer-overlay-text h2 {
    font-size: 28px;
    font-weight: bold;
    margin-top:0px;
  }

  .stoerer-overlay-text h3 {
    font-size: 22px;
    font-weight: bold;;
    margin-top:0px;
  }

  .stoerer-overlay-text h4 {
    font-size: 18px;
    margin-top:0px;
  }
}
/*==========================================================================================================*/
/* only iphone4 landscape & Potriat 300 by 480*/
@media only screen and (max-width: 567px) {


  .default-grid.cols3 .col, .default-grid.cols2 .col, .default-grid.cols4 .col { width:auto; float:none; }
  table.res-table tr th, table.res-table tr.tr-0 { display:none; }
  /*table.res-table tr td { display:block; position:relative; padding-left:50%; }*/
  table.res-table td:before { content: attr(data-th) ": "; font-weight: bold; width: 50%; display:block; padding-right:10px; position:absolute; left:12px; top:4px; }
  .tab-heading { display:none; }
  .tab-container { padding:0; border:none;}
  .tabMobiletrigger { background:#99e9fc; border:medium none; color:#444; font-size:18px; text-transform:uppercase; margin:0 0 7px; padding:10px 54px 10px 16px; position:relative; cursor:pointer; font-weight:bold; }
  .tabMobiletrigger:after { border-left:8px solid rgba(0, 0, 0, 0); border-right:8px solid rgba(0, 0, 0, 0); border-top:8px solid #444; content:""; margin-top:-3px; position:absolute; right:19px; top:50%; }
  .tabMobiletrigger:before { background:rgba(0, 0, 0, 0.06); content:""; height:100%; position:absolute; right:0; top:0; width:54px; z-index:1; }
  .tabcontent { padding:15px 0; margin:0; }
  .tabMobiletrigger.rotate { background:#00c8f8; }
  .tabMobiletrigger.rotate h2 { color:white; }
  .tabMobiletrigger.rotate:after { border-top-color:#272c2f; -webkit-transform:rotate(180deg); -moz-transform:rotate(180deg); -ms-transform:rotate(180deg); transform:rotate(180deg); }

  .tarif-tab .tab-container{
    margin-top: 0px;
  }
  .tarif-tab .tabcontent { padding:0; margin:0; }
  .tarif-tab .tabMobiletrigger:first-of-type { background:rgba(231, 74, 22, 0.8);color:white; }
  .tarif-tab  .tabMobiletrigger.rotate:first-of-type { background: #e74a16; color:white; }

  .tarif-tab .tabMobiletrigger:nth-of-type(2) { background:rgba(254, 203, 1, 0.7);color:white; }
  .tarif-tab  .tabMobiletrigger.rotate:nth-of-type(2) { background: #fecb01; color:white; }

  .tarif-tab .tabMobiletrigger:nth-of-type(3) { background:rgba(82, 191, 213, 0.8);color:white; }
  .tarif-tab  .tabMobiletrigger.rotate:nth-of-type(3) { background: #52bfd5; color:white; }

  button.anzpers i {
    font-size: 2em
  }


  .Tx-Formhandler [class*="col-"]{width:100%;min-height: 31px;margin-top:5px}
  .Tx-Formhandler .custom-select {margin-top: 2px;}

  .list-block.cols3 .col { width:100%; float:none; }
  .image-block figure { width:100%; float:none; padding-right:0; }
  .image-block.right-align figure { padding-left:0; }
  h1, h2 { font-size:34px; }
  .fmenu li a { text-transform:none; }
  #footer:before { height:104px; }
  .contact-box { font-size:18px; }
  .fmenu li:before { height:13px; }
  .benefit-col { float:none; width:auto; }
  .benefit-col h3 { font-size:24px; }
  .benefit-col h3 br { display:none; }
  .benefit-details { font-size:16px; }

  .news-box.is-selected h3 { font-size:20px; }
  #logo { display:block; max-width:350px; width:100%; float:none; margin:5px auto 15px; width:200px; }
  #header { padding:0; }
  .breadcrumbs { display:none; }
  .header-right-section { float:none; }
  .action-box { float:none; width:auto; }





  .banner-content h2 { font-size:26px; }
  .banner-content h3 { font-size:16px; }
  .banner-content { font-size:14px; }
  .pools-list-trigger.list-open:after { opacity:1; top:100%; }

  .pool-details .row > div { float:none; width:auto; }
  .pool-details .button-row { text-align:left; }
  .pool-image, .pool-details { margin-bottom:20px; }
  .cource-table table.res-table td:before { display:none; }
  .cource-table table.res-table td { padding-left:10px; }
  .cource-table table.res-table tr td { padding:5px 12px; }
  table.res-table tr td { display:block; position:relative; padding:5px 12px 5px 50%; }
  table.res-table tr td:first-child { padding-top:10px; }
  table.res-table tr td:last-child { padding-bottom:10px; }
  h2 { font-size:26px; }
  #cources li { width:49%; }
  .courses-container { margin-bottom:30px; }

  .timings-table table.res-table tr td:first-child, table.res-table tr td:first-child { padding-left:12px; font-size:17px; }
  .timings-table table.res-table tr td:first-child:before, table.res-table tr td:first-child:before { display:none; }
  table.res-table.usage-table tr td:first-child:before { display:block; top:8px; }
  table.res-table.usage-table tr td:first-child { padding-left:50%; font-size:12px; }
  table.res-table.usage-table tr td:last-child:before { display:none; }
  table.usage-table th, table.usage-table td { font-size:12px; }
  table.usage-table td:last-child { text-align:left; }
  .deal-box figure { float:none; margin:0 0 15px; }
  .time-table-wrapper { margin-bottom:30px; }
  .timings-table { margin-top:35px; }
  .contact-row { padding:35px 0; }
  .contact-row .pool-details { padding:25px 0 5px; }

  .total-consuption h4, .total-power { max-width:250px; }
  .total-power { padding:15px 20px; font-size:18px; }
  .rate-calculator, .contact-form-wrapper { padding-top:30px; }
  .contact-col strong { font-size:17px; }
  .contact-details { font-size:15px; }
  .contact-col { padding:9px 10px 5px 58px; }
  .contact-col:before { -webkit-transform:scale(0.75); -ms-transform:scale(0.75); transform:scale(0.75); -webkit-transform-origin:0 50%; -ms-transform-origin:0 50%; transform-origin:0 50%; }

  .form-block .form-group > label { float:none; display:block; margin:0 0 5px; padding:0; font-size:15px; }
  .power-info-box h3 { font-size:22px; }
  .power-info-box:before { top:20px; -webkit-transform:scale(0.5); -ms-transform:scale(0.5); transform:scale(0.5); -webkit-transform-origin:0 0; -ms-transform-origin:0 0; transform-origin:0 0; }

  .electricity-info-blocks .col-xs-6 { float:none; width:auto; margin-top:15px; }
  .electricity-info-blocks .row { margin-top:-10px; }
  .about-section .view-more { font-size:13px; }
  .electricity-info-block h3 { font-size:21px; }
  .download-box.links-box ul { padding-bottom:10px; padding-right:60px; }
  .links-box:after { -webkit-transform:scale(0.8); -ms-transform:scale(0.8); transform:scale(0.8); webkit-transform-origin:100% 0; -ms-transform-origin:100% 0; transform-origin:100% 0; }
  .contact-form input[type="submit"] { margin-top:15px; }

.news-list-view.container .article .news-img-wrap, .news-list-view.container .article .teaser-text {display:block!important;}

}
/*==========================================================================================================*/
/* only iphone landscape 340 by 478*/
@media only screen and (max-width: 480px) {

.news-img-wrap {display:block!important;}

}
/*==========================================================================================================*/
/* only iphone portrait 300 by 479*/
@media only screen and (max-width: 479px) {
  .contact-wrapper .col-sm-4 { float:none; width:auto; }
  #footer:before { height:78px; }
  h2 { font-size:24px; }
  .footer-block { padding:25px 0; }
  #footer h3 { font-size:24px; margin-bottom:25px; }
  .button { padding-left:20px; padding-right:20px; font-size:14px; }
  .contact-wrapper, .pools-wrapper, .news-wrapper { margin-bottom:30px; }
  .fmenu { padding-top:25px; }
  .fmenu li { margin:4px 0; }
  .benefit-col h3 { font-size:20px; }
  .benefit-details { font-size:14px; }
  .pools-row { padding:40px 0 0; }
  .news-row { margin-top:40px; }

  #maincontent { padding:40px 20px; }
  .flickity-prev-next-button { -webkit-transform:scale(0.5); -ms-transform:scale(0.5); transform:scale(0.5); -webkit-transform-origin:0 50%; -ms-transform-origin:0 50%; transform-origin:0 50%; }
  .flickity-prev-next-button.next { -webkit-transform-origin:100% 50%; -ms-transform-origin:100% 50%; transform-origin:100% 50%; }
  .news-slider { padding:0 30px; }
  .news-box.is-selected h3 { font-size:18px; }
  .news-box.is-selected, .news-box { font-size:13px; }

  .home .banner-info-bubble { position:relative; padding-bottom:12px; color:#fff; border-radius:0; width:auto; left:0; right:0; top:0; margin:0 -15px 10px; height:auto; background:#e74a16; }
  .home .banner-info-bubble:before { display:none; }
  .banner-info-bubble h4 { margin-bottom:5px; }
  .banner-content { padding-top:0; }
   .banner-content h2 { font-size:23px; }
  table th, table td { font-size:14px; }
  .pool-contact-box:after { -webkit-transform:scale(0.6); -ms-transform:scale(0.6); transform:scale(0.6); -webkit-transform-origin:100% 0; -ms-transform-origin:100% 0; transform-origin:100% 0; }
  .info-box { padding:0; }
  .information-row { margin-bottom:30px; background:#eaebed; padding:20px 0 40px; }
  .information-row h2 { color:#f39201; margin-bottom:20px; }
  #cources li { width:48.5%; }
  #cources li h4 { font-size:13px; }
  .tab-data { padding-bottom:30px; }

  .rate-info-box .banner-info-bubble { position:static; }
  .pool-detail-box .banner-info-bubble, .rate-info-box .banner-info-bubble { margin:0 auto 25px; float:none; }
  .pool-detail-box .banner-info-bubble:before, .rate-info-box .banner-info-bubble:before { display:none; }
  .pool-detail-box { text-align:center; }
  .contact-row { padding-top:25px; }
  table.res-table tr td { padding-left:12px; }
  table.res-table td:before { position:static; display:block; margin-bottom:2px; width:auto; }

  .rates-steps-row .col-xs-6 { width:auto; float:none; }
  .step-number { font-size:80px; }
  .view-more { font-size:13px; }
  .view-more:after { margin-left:10px; }
  .view-more:before { right:7px; }
  .total-consuption { text-align:center; }
  .total-consuption h4, .total-power { max-width:100%; font-size:17px; }
  .contact-col { float:none; padding-right:0; margin-top:10px; }
  .contact-details { margin-top:10px; }
  .important-note { padding:20px 0 10px; font-size:15px; }
  .important-note h4 { margin-bottom:10px; }
  table.res-table.usage-table tr td:first-child  { padding-left:12px; }
  .about-section .view-more { display:inline-flex; }
  .about-section .view-more:after { margin-top:2px; }
  .about-section .view-more:before { top:13px; right:4px; }

  .news-list-view.container .article .news-img-wrap, .news-list-view.container .article .teaser-textt {display:block!important;}
  .stoerer-overlay {
    height: initial;
    min-height: 160px;
  }

}
/*==========================================================================================================*/
/* Retina css */
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (-moz-min-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 3 / 2), only screen and (-o-min-device-pixel-ratio: 2 / 1), only screen and (min--moz-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 2) {
  .category-nav > ul > li > a:before { background-image:url('https://www.stadtwerke-velbert.de/fileadmin/images/bgi/<EMAIL>');
  -moz-background-size:300px 200px; -ms-background-size:300px 200px; -o-background-size:300px 200px; -webkit-background-size:300px 200px; background-size:300px 200px;}
}


@media only screen and (max-width: 674px) {

  .stadtrundgang-headline {
    font-size: 28px;    
line-height:30px;
  }
}