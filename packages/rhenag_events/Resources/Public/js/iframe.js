(function() {
	if (self == top) {
		document.querySelector('body').innerHTML = '<div style="font-family:sans-serif;text-align:center;margin-top:30vh;"><h1 style="color:#a00;"><span style="color:#aaa;">403</span> FORBIDDEN</h1><p style="color:#ccc;">This page is ownly allowed to be loaded into an IFRAME.</p></div>';
		return;
	}

	send({
		type: 'loading',
		loading: true
	});

	var MAIN_SELECTOR = '#iFrame';

	var ready = {
		parent: false,
		iframe: false
	};
	var running = false;

	var ASSETS = [
		{
			rel: 'stylesheet',
			type: 'text/css',
			media: 'all',
			href: './typo3conf/ext/rhenag_events/Resources/Public/css/stadtwerke-velbert.de/base.css'
		},
		{
			rel: 'stylesheet',
			type: 'text/css',
			media: 'all',
			href: './typo3conf/ext/rhenag_events/Resources/Public/css/stadtwerke-velbert.de/style.css'
		},
		{
			rel: 'stylesheet',
			type: 'text/css',
			media: 'all',
			href: './typo3conf/ext/rhenag_events/Resources/Public/css/iframe.css'
		}
		// {
		// 	rel: 'stylesheet',
		// 	type: 'text/css',
		// 	media: 'all',
		// 	href: 'https://www.stadtwerke-velbert.de/typo3temp/compressor/merged-10784169818c71a8ade8303500dcc451.1495466135.css'
		// 	// href: 'https://iframe-seminare.rhenag.vancado.develop/res/merged-10784169818c71a8ade8303500dcc451.1495466135.css'
		// },
		// {
		// 	rel: 'stylesheet',
		// 	type: 'text/css',
		// 	media: 'screen',
		// 	href: 'https://www.stadtwerke-velbert.de/typo3temp/compressor/merged-19c815411403f650853c3bdd7ab0af01.1561626259.css'
		// 	// href: 'https://iframe-seminare.rhenag.vancado.develop/res/merged-19c815411403f650853c3bdd7ab0af01.1561626259.css'
		// }
	];

	//wait for jQuery
	var initTimer = setInterval(function() {
		if (window.$) {
			clearInterval(initTimer);

			//document ready
			$(document).ready(function() {
				var cnt, i;
				cnt = ASSETS.length;

				send({
					type: 'page',
					url: location.href,
					firstPage: ($('[data-first-page="yes"]').length > 0)
				});

				$('.fluid-container').addClass('container-fluid');

				$(MAIN_SELECTOR).addClass('home').css({
					opacity: 0
				});
				for (i = 0; i < ASSETS.length; i++) {
					$('head').append(
						$('<link>').attr(
							ASSETS[i]
						).on('load', function() {
							console.log(this);
							cnt--;
							if (cnt == 0) {
								ready.iframe = true;
								send({type: 'ready'});
								send({
									type: 'loading',
									loading: false
								});
								console.log('iframe:iframe_ready');
								checkReady();
							}
						})
					);
				}

				$('[data-link]').on('click', function() {
					console.log(this);
					if ($(this).attr('data-link') == 'back') {
						send({
							type: 'navigate',
							to: 'previous'
						});
						// history.back();
					} else if ($(this).attr('data-link') == 'start') {
						send({
							type: 'navigate',
							to: 'start'
						});
					}
				});

				$('[data-link]').addClass('button btn-secondary');
				$('[data-link="back"]').addClass('contact-button');
			});

			$('a[data-toggle="collapse"]').each(function() {
				var $a = $(this);
				$a.attr('href', 'javascript:');
				$a.closest('.panel-heading').on('click', function() {
					send({
						type: 'animation',
						animating: true
					});
					$(this).closest('.panel').find('.panel-collapse')[$(this).toggleClass('expanded').hasClass('expanded') ? 'slideDown' : 'slideUp'](function() {
						send({
							type: 'animation',
							animating: false
						});
					});
				});
			});

			//post message receiver
			$(window).on('message', function(event) {
				var data;
				try {
					data = JSON.parse(event.originalEvent.data);
				} catch(ignore) {}
				if (data && (data.source == 'vnc')) {
					receive(data);
				}
			}).on('beforeunload', function() {
				send({
					type: 'loading',
					loading: true
				});
			});


		}
	}, 10);

	//send post message to parent
	function send(postData) {
		console.log('[POST] iframe -> parent:');
		console.log(postData);
		postData.source = 'vnc';
		parent.postMessage(JSON.stringify(postData), '*');
	}

	//receive post message from parent
	function receive(postData) {
		var l;
		console.log('[POST] iframe <- parent:');
		console.log(postData);
		if (postData.type == 'ready') {
			console.log('iframe:parent_ready');
			ready.parent = true;
			checkReady();
		} else if (postData.type == 'labels') {
			for (l in postData.labels) {
				$('[data-link="'+l+'"]').text(postData.labels[l]);
			}
		}
	}

	function checkReady() {
		// alert('iframe:checkReady = '+((ready.iframe && ready.parent) ? 'TRUE' : 'FALSE'));
		if (ready.iframe && ready.parent) {
			console.log('iframe:all_ready');
			run();
		}
	}

	function run() {
		if (!running) {
			console.log('iframe:run');
			// alert('iframe:run');
			running = true;

			$(MAIN_SELECTOR).fadeIn(function() {
				// alert('faded in');
			});

			$(MAIN_SELECTOR).show();
			// alert('shown');


			var FRAME_HEIGHT = 0;
			setInterval(function() {
				var height = $(MAIN_SELECTOR).outerHeight();
				if (height != FRAME_HEIGHT) {
					FRAME_HEIGHT = height;
					send({
						type: 'height',
						height: FRAME_HEIGHT
					});
				}
			}, 10);
		}
	}

})();
