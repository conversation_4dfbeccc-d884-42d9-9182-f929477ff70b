(function() {

	var CONTAINER_SELECTOR = '#vncContainer';
	var IFRAME_SELECTOR = '#vncFrame';
	var LOADER_SELECTOR = '#vncFrameLoader';
	var INTRO_SELECTOR = '#vncIntro';
	var HISTORY = [];


	var ready = {
		parent: false,
		iframe: false
	};
	var running = false;
	var intro_hidden = false;


	//wait for jQuery
	var initTimer = setInterval(function() {
		if (window.$) {
			clearInterval(initTimer);

			//document ready
			$(document).ready(function() {
				ready.parent = true;
				console.log('parent:parent_ready');
				$('head').append(
					$('<link>').attr({
						rel: 'stylesheet',
						type: 'text/css',
						media: 'all',
						href: $(IFRAME_SELECTOR).attr('data-css')
					}).on('load', function() {
						console.log(this);
						$(IFRAME_SELECTOR).attr('src', $(IFRAME_SELECTOR).attr('data-src')).on('load', function() {
							setTimeout(function() {
								send({type: 'ready'});
								// alert('parent:ready');
								checkReady();
							}, 1500); //delay ready to give slower devices some time to initialize
						});
					})
				);
			});

			//post message receiver
			$(window).on('message', function(event) {
				var data;
				try {
					data = JSON.parse(event.originalEvent.data);
				} catch(ignore) {}
				if (data && (data.source == 'vnc')) {
					receive(data);
				}
			});

		}
	}, 10);


	//send post message to iframe
	function send(postData) {
		var $frame;
		postData.source = 'vnc';
		$frame = $(IFRAME_SELECTOR);
		if ($frame.length > 0) {
			console.log('[POST] parent -> iframe:');
			console.log(postData);
			$frame.get(0).contentWindow.postMessage(JSON.stringify(postData), '*');
		}
	}

	//receive post message from iframe
	function receive(postData) {
		console.log('[POST] parent <- iframe:');
		console.log(postData);
		if (postData.type == 'ready') {
			console.log('parent:iframe_ready');
			ready.iframe = true;
			send({
				type: 'labels',
				labels: {
					back: $(IFRAME_SELECTOR).attr('data-label-back'),
					start: $(IFRAME_SELECTOR).attr('data-label-start')
				}
			});
			checkReady();
		} else if (postData.type == 'loading') {
			$(LOADER_SELECTOR).stop()[postData.loading ? 'fadeIn' : 'fadeOut']();
			if (postData.loading) {
				$(IFRAME_SELECTOR).removeClass('no-transition');
				if (ready.iframe) {
					scrollTo(IFRAME_SELECTOR);
				}
			}
		} else if (postData.type == 'navigate') {
			$(IFRAME_SELECTOR).removeClass('no-transition');
			if (postData.to == 'start') {
				$(LOADER_SELECTOR).stop().fadeIn();
				$(IFRAME_SELECTOR).attr('src', $(IFRAME_SELECTOR).attr('data-src'));
			} else if (postData.to == 'previous') {
				if (HISTORY.length > 1) {
					HISTORY.pop(); //dismiss actual page
					$(IFRAME_SELECTOR).attr('src', HISTORY.pop()); //load previous page
					console.log(HISTORY);
				}
			}
		} else if (postData.type == 'animation') {
			$(IFRAME_SELECTOR)[postData.animating ? 'addClass' : 'removeClass']('no-transition');
		} else if (postData.type == 'page') {
			if ( (HISTORY.length == 0) || (HISTORY[HISTORY.length-1] != postData.url) ) {
				HISTORY.push(postData.url);
				console.log(HISTORY);
			}
			if (postData.firstPage && intro_hidden) {
				$(INTRO_SELECTOR).slideDown(function() {
					intro_hidden = false;
					scrollTo(IFRAME_SELECTOR);
				});
			} else if (!postData.firstPage && !intro_hidden) {
				$(INTRO_SELECTOR).slideUp(function() {
					intro_hidden = true;
					scrollTo(IFRAME_SELECTOR);
				});
			}
			if (postData.firstPage) {
				scrollTo(CONTAINER_SELECTOR);
			}
		} else if (postData.type == 'height') {
			$(IFRAME_SELECTOR).height(postData.height);
		} else if (postData.type == 'scroll') {
			$('html,body').animate({
				scrollTop: postData.top
			});
		}
	}

	function checkReady() {
		if (ready.iframe && ready.parent) {
			console.log('parent:all_ready');
			run();
		}
	}

	function run() {
		if (!running) {
			console.log('parent:run');
			running = true;
			$(CONTAINER_SELECTOR).show();
			// $('#vncContainer,#vncFrameContainer').addClass('always-visible');
		}
	}

	function scrollTo(SELECTOR) {
		if (ready.parent) {
			$('html,body').stop().animate({
				scrollTop: $(SELECTOR).offset().top - $('#header').outerHeight()
			});
		}
	}
})();
