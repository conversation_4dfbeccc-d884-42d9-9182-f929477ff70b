<?php

namespace Vancado\RhenagEvents\Tests\Unit\Domain\Model;

/***************************************************************
 *  Copyright notice
 *
 *  (c) 2016 
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * Test case for class \Vancado\RhenagEvents\Domain\Model\SeminarKategorie.
 *
 * @copyright Copyright belongs to the respective authors
 * @license http://www.gnu.org/licenses/gpl.html GNU General Public License, version 3 or later
 *
 */
class SeminarKategorieTest extends \TYPO3\CMS\Core\Tests\UnitTestCase
{
	/**
	 * @var \Vancado\RhenagEvents\Domain\Model\SeminarKategorie
	 */
	protected $subject = NULL;

	public function setUp()
	{
		$this->subject = new \Vancado\RhenagEvents\Domain\Model\SeminarKategorie();
	}

	public function tearDown()
	{
		unset($this->subject);
	}

	/**
	 * @test
	 */
	public function getTitleReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getTitle()
		);
	}

	/**
	 * @test
	 */
	public function setTitleForStringSetsTitle()
	{
		$this->subject->setTitle('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'title',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getSeminarsReturnsInitialValueForSeminar()
	{
		$newObjectStorage = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();
		$this->assertEquals(
			$newObjectStorage,
			$this->subject->getSeminars()
		);
	}

	/**
	 * @test
	 */
	public function setSeminarsForObjectStorageContainingSeminarSetsSeminars()
	{
		$seminar = new \Vancado\RhenagEvents\Domain\Model\Seminar();
		$objectStorageHoldingExactlyOneSeminars = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();
		$objectStorageHoldingExactlyOneSeminars->attach($seminar);
		$this->subject->setSeminars($objectStorageHoldingExactlyOneSeminars);

		$this->assertAttributeEquals(
			$objectStorageHoldingExactlyOneSeminars,
			'seminars',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function addSeminarToObjectStorageHoldingSeminars()
	{
		$seminar = new \Vancado\RhenagEvents\Domain\Model\Seminar();
		$seminarsObjectStorageMock = $this->getMock('TYPO3\\CMS\\Extbase\\Persistence\\ObjectStorage', array('attach'), array(), '', FALSE);
		$seminarsObjectStorageMock->expects($this->once())->method('attach')->with($this->equalTo($seminar));
		$this->inject($this->subject, 'seminars', $seminarsObjectStorageMock);

		$this->subject->addSeminar($seminar);
	}

	/**
	 * @test
	 */
	public function removeSeminarFromObjectStorageHoldingSeminars()
	{
		$seminar = new \Vancado\RhenagEvents\Domain\Model\Seminar();
		$seminarsObjectStorageMock = $this->getMock('TYPO3\\CMS\\Extbase\\Persistence\\ObjectStorage', array('detach'), array(), '', FALSE);
		$seminarsObjectStorageMock->expects($this->once())->method('detach')->with($this->equalTo($seminar));
		$this->inject($this->subject, 'seminars', $seminarsObjectStorageMock);

		$this->subject->removeSeminar($seminar);

	}
}
