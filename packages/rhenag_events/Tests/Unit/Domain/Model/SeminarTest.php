<?php

namespace Vancado\RhenagEvents\Tests\Unit\Domain\Model;

/***************************************************************
 *  Copyright notice
 *
 *  (c) 2016 
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * Test case for class \Vancado\RhenagEvents\Domain\Model\Seminar.
 *
 * @copyright Copyright belongs to the respective authors
 * @license http://www.gnu.org/licenses/gpl.html GNU General Public License, version 3 or later
 *
 */
class SeminarTest extends \TYPO3\CMS\Core\Tests\UnitTestCase
{
	/**
	 * @var \Vancado\RhenagEvents\Domain\Model\Seminar
	 */
	protected $subject = NULL;

	public function setUp()
	{
		$this->subject = new \Vancado\RhenagEvents\Domain\Model\Seminar();
	}

	public function tearDown()
	{
		unset($this->subject);
	}

	/**
	 * @test
	 */
	public function getTitleReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getTitle()
		);
	}

	/**
	 * @test
	 */
	public function setTitleForStringSetsTitle()
	{
		$this->subject->setTitle('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'title',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getPriceReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getPrice()
		);
	}

	/**
	 * @test
	 */
	public function setPriceForStringSetsPrice()
	{
		$this->subject->setPrice('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'price',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getStartDateReturnsInitialValueForDateTime()
	{
		$this->assertEquals(
			NULL,
			$this->subject->getStartDate()
		);
	}

	/**
	 * @test
	 */
	public function setStartDateForDateTimeSetsStartDate()
	{
		$dateTimeFixture = new \DateTime();
		$this->subject->setStartDate($dateTimeFixture);

		$this->assertAttributeEquals(
			$dateTimeFixture,
			'startDate',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getEndDateReturnsInitialValueForDateTime()
	{
		$this->assertEquals(
			NULL,
			$this->subject->getEndDate()
		);
	}

	/**
	 * @test
	 */
	public function setEndDateForDateTimeSetsEndDate()
	{
		$dateTimeFixture = new \DateTime();
		$this->subject->setEndDate($dateTimeFixture);

		$this->assertAttributeEquals(
			$dateTimeFixture,
			'endDate',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getTimeReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getTime()
		);
	}

	/**
	 * @test
	 */
	public function setTimeForStringSetsTime()
	{
		$this->subject->setTime('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'time',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getInfoPdfReturnsInitialValueForFileReference()
	{
		$this->assertEquals(
			NULL,
			$this->subject->getInfoPdf()
		);
	}

	/**
	 * @test
	 */
	public function setInfoPdfForFileReferenceSetsInfoPdf()
	{
		$fileReferenceFixture = new \TYPO3\CMS\Extbase\Domain\Model\FileReference();
		$this->subject->setInfoPdf($fileReferenceFixture);

		$this->assertAttributeEquals(
			$fileReferenceFixture,
			'infoPdf',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getRegistrationPdfReturnsInitialValueForFileReference()
	{
		$this->assertEquals(
			NULL,
			$this->subject->getRegistrationPdf()
		);
	}

	/**
	 * @test
	 */
	public function setRegistrationPdfForFileReferenceSetsRegistrationPdf()
	{
		$fileReferenceFixture = new \TYPO3\CMS\Extbase\Domain\Model\FileReference();
		$this->subject->setRegistrationPdf($fileReferenceFixture);

		$this->assertAttributeEquals(
			$fileReferenceFixture,
			'registrationPdf',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getParticipantsGroupReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getParticipantsGroup()
		);
	}

	/**
	 * @test
	 */
	public function setParticipantsGroupForStringSetsParticipantsGroup()
	{
		$this->subject->setParticipantsGroup('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'participantsGroup',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getAimReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getAim()
		);
	}

	/**
	 * @test
	 */
	public function setAimForStringSetsAim()
	{
		$this->subject->setAim('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'aim',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getContentReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getContent()
		);
	}

	/**
	 * @test
	 */
	public function setContentForStringSetsContent()
	{
		$this->subject->setContent('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'content',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getMethodsReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getMethods()
		);
	}

	/**
	 * @test
	 */
	public function setMethodsForStringSetsMethods()
	{
		$this->subject->setMethods('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'methods',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getTrainerReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getTrainer()
		);
	}

	/**
	 * @test
	 */
	public function setTrainerForStringSetsTrainer()
	{
		$this->subject->setTrainer('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'trainer',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getDurationReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getDuration()
		);
	}

	/**
	 * @test
	 */
	public function setDurationForStringSetsDuration()
	{
		$this->subject->setDuration('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'duration',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getParticipantsCountReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getParticipantsCount()
		);
	}

	/**
	 * @test
	 */
	public function setParticipantsCountForStringSetsParticipantsCount()
	{
		$this->subject->setParticipantsCount('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'participantsCount',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getRequirementsReturnsInitialValueForString()
	{
		$this->assertSame(
			'',
			$this->subject->getRequirements()
		);
	}

	/**
	 * @test
	 */
	public function setRequirementsForStringSetsRequirements()
	{
		$this->subject->setRequirements('Conceived at T3CON10');

		$this->assertAttributeEquals(
			'Conceived at T3CON10',
			'requirements',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getPlaceReturnsInitialValueForPlace()
	{
		$this->assertEquals(
			NULL,
			$this->subject->getPlace()
		);
	}

	/**
	 * @test
	 */
	public function setPlaceForPlaceSetsPlace()
	{
		$placeFixture = new \Vancado\RhenagEvents\Domain\Model\Place();
		$this->subject->setPlace($placeFixture);

		$this->assertAttributeEquals(
			$placeFixture,
			'place',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function getContactsReturnsInitialValueForContact()
	{
		$newObjectStorage = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();
		$this->assertEquals(
			$newObjectStorage,
			$this->subject->getContacts()
		);
	}

	/**
	 * @test
	 */
	public function setContactsForObjectStorageContainingContactSetsContacts()
	{
		$contact = new \Vancado\RhenagEvents\Domain\Model\Contact();
		$objectStorageHoldingExactlyOneContacts = new \TYPO3\CMS\Extbase\Persistence\ObjectStorage();
		$objectStorageHoldingExactlyOneContacts->attach($contact);
		$this->subject->setContacts($objectStorageHoldingExactlyOneContacts);

		$this->assertAttributeEquals(
			$objectStorageHoldingExactlyOneContacts,
			'contacts',
			$this->subject
		);
	}

	/**
	 * @test
	 */
	public function addContactToObjectStorageHoldingContacts()
	{
		$contact = new \Vancado\RhenagEvents\Domain\Model\Contact();
		$contactsObjectStorageMock = $this->getMock('TYPO3\\CMS\\Extbase\\Persistence\\ObjectStorage', array('attach'), array(), '', FALSE);
		$contactsObjectStorageMock->expects($this->once())->method('attach')->with($this->equalTo($contact));
		$this->inject($this->subject, 'contacts', $contactsObjectStorageMock);

		$this->subject->addContact($contact);
	}

	/**
	 * @test
	 */
	public function removeContactFromObjectStorageHoldingContacts()
	{
		$contact = new \Vancado\RhenagEvents\Domain\Model\Contact();
		$contactsObjectStorageMock = $this->getMock('TYPO3\\CMS\\Extbase\\Persistence\\ObjectStorage', array('detach'), array(), '', FALSE);
		$contactsObjectStorageMock->expects($this->once())->method('detach')->with($this->equalTo($contact));
		$this->inject($this->subject, 'contacts', $contactsObjectStorageMock);

		$this->subject->removeContact($contact);

	}
}
