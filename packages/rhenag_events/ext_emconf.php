<?php

/***************************************************************
 * Extension Manager/Repository config file for ext: "rhenag_events"
 *
 * Auto generated by Extension Builder 2016-10-06
 *
 * Manual updates:
 * Only the data in the array - anything else is removed by next write.
 * "version" and "dependencies" must not be touched!
 ***************************************************************/

$EM_CONF['rhenag_events'] = array(
	'title' => 'Rhenag Events',
	'description' => '',
	'category' => 'plugin',
	'author' => '<PERSON>',
	'author_email' => '',
	'state' => 'stable',
	'internal' => '',
	'uploadfolder' => '1',
	'createDirs' => '',
	'clearCacheOnLoad' => 0,
	'version' => '10.4.0',
);