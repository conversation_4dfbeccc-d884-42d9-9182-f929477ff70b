<?php
if (!defined('TYPO3_MODE')) {
	die('Access denied.');
}

$_EXTKEY = 'rhenag_events';

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
	'Vancado.' . $_EXTKEY,
	'Event',
	'Events list'
);

$pluginSignature = str_replace('_','',$_EXTKEY) . '_event';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist'][$pluginSignature] = 'pi_flexform';
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue($pluginSignature, 'FILE:EXT:' . $_EXTKEY . '/Configuration/FlexForms/flexform_event.xml');

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
	'Vancado.' . $_EXTKEY,
	'Seminar',
	'Seminars list'
);

$GLOBALS['TBE_STYLES']['skins'][$_EXTKEY]['stylesheetDirectories'][] = 'EXT:rhenag_events/stylesheets/visual/';

$GLOBALS['TCA']['tx_rhenagevents_domain_model_seminardate']['ctrl']['hideTable'] = TRUE;
$GLOBALS['TCA']['tx_rhenagevents_domain_model_seminarmodule']['ctrl']['hideTable'] = TRUE;

$pluginSignature = str_replace('_','',$_EXTKEY) . '_seminar';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist'][$pluginSignature] = 'pi_flexform';
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue($pluginSignature, 'FILE:EXT:' . $_EXTKEY . '/Configuration/FlexForms/flexform_seminar.xml');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addStaticFile($_EXTKEY, 'Configuration/TypoScript', 'Rhenag Events');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_rhenagevents_domain_model_event', 'EXT:rhenag_events/Resources/Private/Language/locallang_csh_tx_rhenagevents_domain_model_event.xlf');
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_rhenagevents_domain_model_event');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_rhenagevents_domain_model_seminar', 'EXT:rhenag_events/Resources/Private/Language/locallang_csh_tx_rhenagevents_domain_model_seminar.xlf');
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_rhenagevents_domain_model_seminar');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_rhenagevents_domain_model_seminarkategorie', 'EXT:rhenag_events/Resources/Private/Language/locallang_csh_tx_rhenagevents_domain_model_seminarkategorie.xlf');
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_rhenagevents_domain_model_seminarkategorie');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_rhenagevents_domain_model_place', 'EXT:rhenag_events/Resources/Private/Language/locallang_csh_tx_rhenagevents_domain_model_place.xlf');
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_rhenagevents_domain_model_place');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_rhenagevents_domain_model_contact', 'EXT:rhenag_events/Resources/Private/Language/locallang_csh_tx_rhenagevents_domain_model_contact.xlf');
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_rhenagevents_domain_model_contact');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_rhenagevents_domain_model_partner', 'EXT:rhenag_events/Resources/Private/Language/locallang_csh_tx_rhenagevents_domain_model_partner.xlf');
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_rhenagevents_domain_model_partner');

$GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['seminar'] = 'EXT:rhenag_events/Configuration/RTE/Seminar.yaml';