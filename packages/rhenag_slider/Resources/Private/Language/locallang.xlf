<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<xliff version="1.0">
	<file source-language="en" datatype="plaintext" original="messages" product-name="rhenag_components" date="2016-08-16T11:12:38+02:00">
		<header/>
		<body>
			<trans-unit id="flux.hometeasercontainer">
				<source>Layout - Slider für die Startseite</source>
			</trans-unit>
			<trans-unit id="flux.hometeasercontainer.description">
				<source></source>
			</trans-unit>

			<trans-unit id="flux.homeTeaserSlide">
				<source>Element - Slide für die Startseite</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.description">
				<source></source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.type">
				<source>Slide Type</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.video_loop">
				<source>Slide Video Loop</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.video_position">
				<source>Slide Video Position</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.text_position">
				<source>Slide Text Position</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.header">
				<source>Slide Header</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.subheader">
				<source>Slide Subheader</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.panel_icon">
				<source>Slide Panel Icon</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.panel_header_h1">
				<source>Slide Panel Text H1?</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.panel_header">
				<source>Slide Panel Header</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.panel_text">
				<source>Slide Panel Text</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.link">
				<source>Slide Panel Link</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.link_label">
				<source>Slide Panel Link Label</source>
			</trans-unit>
			<trans-unit id="flux.homeTeaserSlide.fields.panel_highlight">
				<source>Slide Panel Highlighted</source>
			</trans-unit>
            <trans-unit id="flux.homevideowithteasers">
                <source>Homepage Video mit Teaserboxen</source>
            </trans-unit>
            <trans-unit id="flux.homevideowithteasers.description">
                <source>Hompage Hero Element mit Video und Teaserboxen</source>
            </trans-unit>
            <trans-unit id="flux.contentpagevideowithteasers">
                <source>Inhaltsseiten Video mit Teaserboxen</source>
            </trans-unit>
            <trans-unit id="flux.contentpagevideowithteasers.description">
                <source>Hero Element mit Video und Teaserboxen</source>
            </trans-unit>
		</body>
	</file>
</xliff>
