<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">
		<flux:form id="contentpagevideowithteasers">
			<flux:form.option.icon value="EXT:rhenag_slider/Resources/Public/Icons/Content/icon-element-slider-home.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:form.variable name="SvgSelectorArray" value="{settings.SvgSelector}" />
			<flux:field.input name="videourl" label="Video URL" required="1" exclude="0" enabled="1" />
			<flux:field.input name="videourlMp4" label="Video URL MP4" required="1" exclude="0" enabled="1" />
			<flux:field.inline.fal name="image" label="Poster Image" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local"
				foreignSortby="sorting_foreign" table="sys_file_reference" />
			<flux:field.input label="Intro Überschrift über Teaserboxen" name="teasers_header" required="0" exclude="0" enabled="1" />
			<flux:field.text label="Intro Text über Teaserboxen" name="teasers_copy" required="0" exclude="0" enabled="1" />
			<flux:form.sheet name="panels" label="Teaser-Boxen">
				<flux:form.section name="panels" label="Panels">
					<flux:form.object name="panel" label="Panels">
						<flux:field.select transform="string" minItems="0" maxItems="150" label="Icon" items="{settings.SvgSelector}" itemListStyle="active" inherit="0" name="panel_icon" />
						<flux:field.checkbox label="Box Anzeigen" name="show" required="0" exclude="0" default="1" enabled="1" />
						<flux:field.input label="Box Header" name="box_header" required="0" exclude="0" enabled="1" />
						<flux:field.text label="Box Text" name="box_text" required="0" exclude="0" enabled="1" />
                        <flux:field.input label="Box Link" name="box_link" config="{renderType: 'inputLink'}" />
						<flux:field.input label="Box Link Text" name="box_linklabel" maxCharacters="90" />
					</flux:form.object>
				</flux:form.section>
			</flux:form.sheet>
		</flux:form>
	</f:section>


	<f:section name="Preview">
        <f:comment>
		<h4>Teaser Boxen</h4>
		<p>EINTRÄGE:
			<v:l key="panels" /> : {panels -> f:count()}
		</p>
		<table class="table table-responsive" cellpadding="2">
			<f:if condition="{teasers_header} || {teasers_copy}">
				<tr>
					<td colspan="3">
						<h3>{teasers_header}</h3>
						<p>{teasers_copy}</p>
					</td>
				</tr>
			</f:if>
			<f:if condition="{panels}">
				<tr>
					<f:for each="{panels}" as="panel" key="teaserId" iteration="teaserIterator">
						<td valign="top" style="padding:15px;min-width:25%;max-width:33%">
							<div style="max-width:320px;padding:5px;border:1px solid black">
								<f:if condition="{panel.panel.panel_icon}">
									<f:then>
										<div class="svg-icon" style="background-color:#e97322;width:75px;height:75px;">
											<f:render partial="SvgIcon" section="{panel.panel.panel_icon}" arguments="{_all}" />
										</div>
									</f:then>
									<f:else>
										<div class="svg-icon" style="background-color:#e97322;width:65px;height:65px;padding:5px;border:1px solid #000;color:#fff;">
											<b>Kein Icon</b>
										</div>
									</f:else>
								</f:if>
							</div>
							<h2 class="teaser-switch-title">
								<f:format.nl2br>{panel.panel.box_header}</f:format.nl2br>
							</h2>
							<f:if condition="{panel.panel.box_subheader}">
								<p class="teaserbox-subheader">{panel.panel.box_subheader}</p>
							</f:if>
							<p style="min-height:75px;">
								<f:format.crop maxCharacters="175">
									<f:format.nl2br>{panel.panel.box_text}</f:format.nl2br>
								</f:format.crop>
							</p>
						</td>
					</f:for>
				</tr>
			</f:if>
		</table>
        </f:comment>
	</f:section>


	<f:section name="Main">
		<f:comment>
			<f:debug>{teaserbox}</f:debug>
		</f:comment>


		<div class="content-wrapper">
			<section class="teaser-landingpage teaser-landingpage-with-video" id="teaser-landingpage" style="touch-action: pan-y; -moz-user-select: none;">
				<div class="teaser-canvas">
					<div class="teaser-info">
						<video id="rehnag-start-video" width="600" height="300" class="video-js vjs-default-skin vjs-fluid vjs-fill" muted autoplay loop playsinline>
							<source src="{videourl}" type="application/x-mpegURL"/>
							<source src="{videourlMp4}" type="video/mp4"/>
						</video>
					</div>
				</div>
				<div class="teaser-info">
					<div class="fluid-container">
						<div class="row">
							<f:if condition="{teasers_header} || {teasers_copy}">
								<div class="col-8-center">
									<f:if condition="{teasers_header}">
										<h1 class="h2">{teasers_header}</h1>
									</f:if>
									<f:if condition="{teasers_copy}">
										<p>{teasers_copy}</p>
									</f:if>
								</div>
							</f:if>
						</div>
					</div>
					<div class="teaser-info-box-container">
						<f:if condition="{panels}">
							<f:for each="{panels}" as="panel" key="teaserId" iteration="teaserIterator">
								<f:if condition="{panel.panel.show}">
									<div class="teaser-info-box equal-height " data-item="{teaserIterator.index}">
										<div>
											<f:render section="boxContent" arguments="{_all}" />
										</div>
									</div>
								</f:if>
							</f:for>
						</f:if>
					</div>
				</div>
			</section>
		</div>
	</f:section>


	<f:section name="boxContent">
		<div class="teaser-info-box equal-height">
			<div>
				<f:if condition="{panel.panel.panel_icon}">
					<f:then>
						<div class="svg-icon">
							<f:if condition="{panel.panel.box_link}">
								<f:then>
									<f:link.typolink parameter="{panel.panel.box_link}" class="">
										<f:render partial="SvgIcon" section="{panel.panel.panel_icon}" arguments="{_all}" />
									</f:link.typolink>
								</f:then>
								<f:else>
									<f:render partial="SvgIcon" section="{panel.panel.panel_icon}" arguments="{_all}" />
								</f:else>
							</f:if>
						</div>
					</f:then>
					<f:else>
						<!-- no icon -->
					</f:else>
				</f:if>
				<f:if condition="{panel.panel.box_header}">
					<h2 class="teaser-landingpage-title text-color-primary">
						<f:format.raw>{panel.panel.box_header}</f:format.raw>
					</h2>
				</f:if>
				<f:if condition="{panel.panel.box_text}">
					<p>
						<f:format.nl2br>{panel.panel.box_text}</f:format.nl2br>
					</p>
				</f:if>
				<f:if condition="{panel.panel.box_link}">
					<p class="link-with-arrow">
                        <f:link.typolink parameter="{panel.panel.box_link}" class="">
							{panel.panel.box_linklabel}
						</f:link.typolink>
					</p>
				</f:if>
			</div>
		</div>
	</f:section>


</div>