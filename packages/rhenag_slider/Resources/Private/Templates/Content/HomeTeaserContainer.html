<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">
		<flux:form id="hometeasercontainer">
			<flux:form.option.icon value="EXT:rhenag_slider/Resources/Public/Icons/Content/icon-layout-slider-home.svg" />
			<flux:form.option.group value="Layout-Elemente" />
			<flux:grid>
				<flux:grid.row>
					<flux:grid.column colPos="0" name="teaser_home_content" label="Content">
						<flux:form.variable name="allowedContentTypes" value="rhenagslider_homevideowithteasers,rhenagslider_hometeaserslide" />

					</flux:grid.column>
				</flux:grid.row>
			</flux:grid>
		</flux:form>
	</f:section>

	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_slider/Resources/Private/Language/locallang.xlf:flux.hometeasercontainer" />
		</h4>


	</f:section>

	<f:section name="Main">

		<section class="teaser-home" id="teaser-home">
			<div class="teaser-canvas">
				<div class="teaser-progressBar">
					<span style="width: 0%"></span>
				</div>

				<flux:content.render area="teaser_home_content" as="teaserElements" render="false">

					<v:variable.register.set value="1" name="mode" />
					<f:for each="{teaserElements}" as="teaserElement" iteration="iteration">
						<f:if condition="{iteration.index} < 4">
							<f:then>
								<v:variable.register.set value="{iteration.index}" name="index" />
								<v:content.render contentUids="{0 : teaserElement.uid}" />
							</f:then>
						</f:if>
					</f:for>
			</div>


			<div class="teaser-controller">


				<div class="teaser-bullets">
					<ul>
						<v:variable.register.set value="2" name="mode" />
						<f:for each="{teaserElements}" as="teaserElement" iteration="iteration">
							<f:if condition="{iteration.index} < 4">
								<f:then>
									<v:variable.register.set value="{iteration.index}" name="index" />
									<v:content.render contentUids="{0 : teaserElement.uid}" />
								</f:then>
							</f:if>
						</f:for>
					</ul>
				</div>

				<div class="fluid-container">
					<div class="row">
						<div class="col-12 teaser-switch-box-container">
							<v:variable.register.set value="3" name="mode" />
							<f:for each="{teaserElements}" as="teaserElement" iteration="iteration">
								<f:if condition="{iteration.index} < 4">
									<f:then>
										<v:variable.register.set value="{iteration.index}" name="index" />
										<v:content.render contentUids="{0 : teaserElement.uid}" />
									</f:then>
								</f:if>
							</f:for>
						</div>
					</div>
				</div>

			</div>

			</flux:content.render>


		</section>




	</f:section>