<div xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<f:layout name="Content" />

	<f:section name="Configuration">

		<flux:form id="homeTeaserSlide">
			<flux:form.option.icon value="EXT:rhenag_slider/Resources/Public/Icons/Content/icon-element-slider-home.svg" />
			<flux:form.option.group value="Rhenag Komponente" />
			<flux:field.checkbox name="panel_asWarning" label="Als WARNELEMENT anzeigen" required="0" exclude="0" default="false" enabled="1" />
			<flux:field.inline.fal name="image" label="Bild" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local" foreignSortby="sorting_foreign"
				table="sys_file_reference" />
			<flux:field.select name="type" items="{
                       0:{0:'Image',1:'image'},
                       1:{0:'Video',1:'video'}
                       }" />
			<flux:field.file name="video_mp4" internal_type="db" label="Video MP4" required="0" exclude="0" useFalRelation="0" maxItems="1" minItems="0" showThumbnails="0" multiple="false" allowed="mp4" />
			<flux:field.file name="video_webm" internal_type="db" label="Video WEBM" required="0" exclude="0" useFalRelation="0" maxItems="1" minItems="0" showThumbnails="0" multiple="false" allowed="webm" />
			<flux:field.checkbox name="video_loop" required="0" exclude="0" default="false" enabled="1" />
			<flux:field.select name="video_position" items="{
                       0:{0:'Video center-center',1:'position-center-center'},
                       1:{0:'Video top-left',1:'position-top-left'},
                       2:{0:'Video top-right',1:'position-top-right'},
                       3:{0:'Video center-left',1:'position-center-left'},
                       4:{0:'Video center-right',1:'position-center-right'},
                       5:{0:'Video bottom-left',1:'position-bottom-left'},
                       6:{0:'Video bottom-right',1:'position-bottom-right'},
                       7:{0:'Video top-center',1:'position-top-center'},
                       8:{0:'Video bottom-center',1:'position-bottom-center'}
                       }" />
			<flux:field.select name="text_position" items="{
                       0:{0:'Text Left',1:'position-left'},
                       1:{0:'Text Right',1:'position-right'}
                       }" />
			<flux:field.text name="header" required="0" exclude="0" enabled="1" rows="3" />
			<flux:field.text name="subheader" required="0" exclude="0" enabled="1" rows="3" />

			<f:render partial="SvgSelector" arguments="{fieldname:'panel_icon'}" />

			<flux:field.checkbox name="panel_header_h1" required="0" exclude="0" default="false" enabled="1" />
			<flux:field.text name="panel_header" required="0" exclude="0" enabled="1" />
			<flux:field.text name="panel_text" required="0" exclude="0" enabled="1" />
			<flux:field.input name="link" config="{renderType: 'inputLink'}" />
			<flux:field.input name="link_label" maxCharacters="90" />
			<flux:field.checkbox name="panel_highlight" required="0" exclude="0" default="false" enabled="1" />

		</flux:form>
	</f:section>



	<f:section name="Preview">
		<h4>
			<f:translate key="LLL:EXT:rhenag_slider/Resources/Private/Language/locallang.xlf:flux.homeTeaserSlide" />
		</h4>

		<table>
			<tr>
				<td>
					<f:format.crop maxCharacters="50">
						<f:format.raw><strong>{header}</strong></f:format.raw>
					</f:format.crop>
				</td>
			</tr>
			<tr>
				<td>
					<f:format.raw>{subheader}</f:format.raw>
				</td>
			</tr>
			<tr>
				<td>
					<v:content.resources.fal field="image" as="images" record="{record}">
						<f:if condition="{images.0}">
							<f:image treatIdAsReference="1" src="{images.0.id}" title="{images.0.title}" width="100" alt="{images.0.alternative}" /><br/>
						</f:if>
					</v:content.resources.fal>
				</td>
			</tr>
		</table>

	</f:section>


	<f:section name="Main">
		<f:if condition="{v:variable.register.get(name: 'mode')} == 1">
			<f:then>
				<f:if condition="{v:variable.register.get(name: 'index')} == 0">
					<f:then>
						<v:variable.register.set value="active" name="activeClass" />
					</f:then>
					<f:else>
						<v:variable.register.set value="" name="activeClass" />
					</f:else>
				</f:if>
                <f:if condition="{v:content.resources.fal(field: 'image') -> v:iterator.first()}">
                    {v:content.resources.fal(field: 'image') -> v:iterator.first() -> v:variable.set(name: 'image')}
                    <div class="focuspoint teaser-item teaser-{type} text-{text_position} {v:variable.register.get(name: 'activeClass')}" data-focus-x="{f:if(condition:'{image.focus_point_x}', then:'{v:math.division(a: image.focus_point_x, b: 100 )}', else:'0')}" data-focus-y="{f:if(condition:'{image.focus_point_y}', then:'{v:math.division(a: image.focus_point_y, b: 100 )}', else:'0')}"
                            data-image-w="{image.width}" data-image-h="{image.height}" data-item="{v:variable.register.get(name: 'index')}">
                        <f:render section="Image" arguments="{_all}" />

                        <f:if condition="{type} == 'video'">
                            <f:then>
                                <video id="video{v:variable.register.get(name: 'index')}" poster="<f:uri.image treatIdAsReference="1" src="{image.uid}" width="1280 " height="720 " />" class="{video_position}" <f:if condition="{video_loop}">
                                <f:then>
                                    loop="loop"
                                </f:then>
                                <f:else>

                                </f:else>
                            </f:if>
                                muted="muted" autoplay="autoplay">
                                <f:if condition="{video_mp4}">
                                    <f:then>

                                        <source src="{video_mp4}" type="video/mp4" />
                                    </f:then>
                                </f:if>
                                <f:if condition="{video_webm}">
                                    <f:then>
                                        <source src="{video_webm}" type="video/webm" />
                                    </f:then>
                                </f:if>
                                </video>
                            </f:then>
                        </f:if>
                        <f:if condition="{header}">
                            <div class="heroTitle">
                                <f:format.nl2br>{header}</f:format.nl2br>
                            </div>
                        </f:if>
                        <f:if condition="{subheader}">
                            <div class="heroSubtitle">
                                <f:format.nl2br>{subheader}</f:format.nl2br>
                            </div>
                        </f:if>
                    </div>
                </f:if>
            </f:then>
			<f:else>
			</f:else>
		</f:if>
		<f:if condition="{v:variable.register.get(name: 'mode')} == 2">
			<f:then>
				<f:if condition="{v:variable.register.get(name: 'index')} == 0">
					<f:then>
						<v:variable.register.set value="active" name="activeClass" />
					</f:then>
					<f:else>
						<v:variable.register.set value="" name="activeClass" />
					</f:else>
				</f:if>
				<li class="{v:variable.register.get(name: 'activeClass')}" data-item="{v:variable.register.get(name: 'index')}"></li>
			</f:then>
			<f:else>
			</f:else>
		</f:if>
		<f:if condition="{v:variable.register.get(name: 'mode')} == 3">
			<f:then>
				<f:if condition="{v:variable.register.get(name: 'index')} == 0">
					<f:then>
						<v:variable.register.set value="active" name="activeClass" />
					</f:then>
					<f:else>
						<v:variable.register.set value="" name="activeClass" />
					</f:else>
				</f:if>
				<f:if condition="{panel_highlight}">
					<f:then>
						<v:variable.register.set value="teaser-switch-box-highlight" name="panel_highlighted" />
					</f:then>
					<f:else>
						<v:variable.register.set value="" name="panel_highlighted" />
					</f:else>
				</f:if>
				<f:if condition="{panel_asWarning}">
					<f:then>
						<v:variable.register.set value="teaser-switch-box-highlight" name="panel_asWarning" />
					</f:then>
					<f:else>
						<v:variable.register.set value="" name="panel_asWarning" />
					</f:else>
				</f:if>
				<div class="teaser-switch-box equal-height {v:variable.register.get(name: 'panel_asWarning')} {v:variable.register.get(name: 'activeClass')} {v:variable.register.get(name: 'panel_highlighted')}" data-item="{v:variable.register.get(name: 'index')}">
					<div class="teaser-switch-box--wrapper">
						<div class="svg-icon">
							<f:render partial="SvgIcon" section="{panel_icon}" arguments="{_all}" />
						</div>
						<f:if condition="{panel_header_h1}">
							<f:then>
								<h1 class="teaser-switch-title">
									<f:format.nl2br>{panel_header}</f:format.nl2br>
								</h1>
							</f:then>
							<f:else>
								<h2 class="teaser-switch-title">
									<f:format.nl2br>{panel_header}</f:format.nl2br>
								</h2>
							</f:else>
						</f:if>
						<p>
							<f:format.nl2br>{panel_text}</f:format.nl2br>
						</p>
						<f:if condition="{link_label}">
							<f:link.typolink parameter="{link}" class="btn btn-primary cta-link">
								{link_label}
							</f:link.typolink>
						</f:if>
					</div>
				</div>
				<div class="teaser-switch-box-hidden {v:variable.register.get(name: 'activeClass')}" data-item="{v:variable.register.get(name: 'index')}">
				</div>
			</f:then>
			<f:else>
			</f:else>
		</f:if>


	</f:section>


	<f:section name="Image">
		<picture>
			<!--[if IE 9]><video style="display: none;"><![endif]-->
			<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="480" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="960" /> 2x" media="(max-width: 480px)">
			<!-- 480x270-->
			<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="800" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1600 "  /> 2x" media="(max-width: 800px)">
			<!-- 800x400-->
			<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1280" />, <f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1920" /> 2x" media="(max-width: 1280px)">
			<!-- 1280x720-->
			<source srcset="<f:uri.image treatIdAsReference="true" src="{image.uid}" maxWidth="1920" />" media="(min-width: 1281px)">
			<!-- 1920x1080-->
			<!--[if IE 9]></video><![endif]-->
			<img alt="{image.alternative}" src="" />
		</picture>
	</f:section>



</div>