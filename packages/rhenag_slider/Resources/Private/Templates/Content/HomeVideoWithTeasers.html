<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers" xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers" xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

<f:layout name="Content" />

<f:section name="Configuration">
	<flux:form id="homevideowithteasers">
		<flux:form.option.icon value="EXT:rhenag_slider/Resources/Public/Icons/Content/icon-element-slider-home.svg" />
		<flux:form.option.group value="Rhenag Komponente" />

		<flux:field.input name="videourl" label="Video URL M3U8" required="1" exclude="0" enabled="1" />
		<flux:field.input name="videourlMp4" label="Video URL MP4" required="1" exclude="0" enabled="1" />
		<flux:field.inline.fal name="image" label="Poster Image" minItems="0" maxItems="0" showThumbs="1" collapseAll="1" allowedExtensions="jpg,jpeg,png" foreignField="uid_foreign" foreignLabel="uid_local" foreignSelector="uid_local" foreignUnique="uid_local"
			foreignSortby="sorting_foreign" table="sys_file_reference" />

		<flux:form.section name="teaserboxes" label="Teaser-Boxen">
			<flux:form.object name="teaserbox" label="Teaser-Box">
				<flux:form.variable name="SvgSelectorArray" value="{settings.SvgSelector}" />
				<flux:field.checkbox label="Box Anzeigen" name="show" required="0" exclude="0" default="1" enabled="1" />
				<flux:field.checkbox label="Warnhinweis Box?" name="alertbox" required="0" exclude="0" default="false" enabled="1" />
				<flux:field.input label="Preline" name="header" required="0" exclude="0" enabled="1" />
				<flux:field.input label="Subline" name="subheader" required="0" exclude="0" enabled="1" />
				<flux:field.select label="Text Position (Claim)" name="box_textposition" items="{
                       0:{0:'Text Left',1:'position-left'},
                       1:{0:'Text Right',1:'position-right'}
                       }" />
				<flux:field.select transform="string" minItems="0" maxItems="150" label="Icon" items="{settings.SvgSelector}" itemListStyle="active" inherit="0" name="panel_icon" />
				<flux:field.input label="Box Header" name="box_header" required="0" exclude="0" enabled="1" />
				<flux:field.text label="Box Text" name="box_text" required="0" exclude="0" enabled="1" />
                <flux:field.input name="box_link" config="{renderType: 'inputLink'}" />
				<flux:field.input label="Box Link Text" name="box_linklabel" maxCharacters="90" />
				<flux:field.input label="Timestamp" name="box_timestamp" required="0" exclude="0" enabled="1" />
			</flux:form.object>
		</flux:form.section>
	</flux:form>
</f:section>



<f:section name="Preview">
	<h4>Teaser Boxen</h4>

	<table class="table table-responsive" cellpadding="2">
		<f:if condition="{teaserboxes}">
			<tr>
				<f:for each="{teaserboxes}" as="teaser" key="teaserId" iteration="teaserIterator">
					<td valign="top" style="padding:15px;min-width:25%;max-width:33%">
						<f:if condition="{teaser.teaserbox.alertbox}">

						</f:if>
						<f:if condition="{teaser.teaserbox.panel_icon}">
							<f:then>
								<div class="svg-icon" style="background-color:#e97322;width:75px;height:75px;">
									<f:render partial="SvgIcon" section="{teaser.teaserbox.panel_icon}" arguments="{_all}" />
								</div>
							</f:then>
							<f:else>
								<div class="svg-icon" style="background-color:#e97322;width:65px;height:65px;padding:5px;border:1px solid #000;color:#fff;">
									<b>Kein Icon</b>
								</div>
							</f:else>
						</f:if>
						<h2 class="teaser-switch-title">
							<f:format.nl2br>{teaser.teaserbox.box_header}</f:format.nl2br>
						</h2>
						<f:if condition="{teaser.teaserbox.box_subheader}">
							<p class="teaserbox-subheader">{teaser.teaserbox.box_subheader}</p>
						</f:if>
						<p style="min-height:75px;">
							<f:format.crop maxCharacters="175">
								<f:format.nl2br>{teaser.teaserbox.box_text}</f:format.nl2br>
							</f:format.crop>
						</p>
					</td>
				</f:for>
			</tr>
		</f:if>
	</table>

</f:section>


<f:section name="Main">
	<f:comment>
		<f:debug>{teaserbox}</f:debug>
	</f:comment>

	<section class="teaser-home teaser-home-with-video" id="teaser-home" style="touch-action: pan-y; -moz-user-select: none;">

		<div class="teaser-canvas">
			<div class="teaser-home-video">
				<video id="rehnag-start-video" width="600" height="300" class="video-js vjs-default-skin vjs-fluid vjs-fill" controls muted autoplay loop playsinline>
					<source src="{videourl}" type="application/x-mpegURL" />
					<source src="{videourlMp4}" type="video/mp4" />
				</video>

				<f:if condition="{teaserboxes}">
					<f:for each="{teaserboxes}" as="teaser" key="teaserId" iteration="teaserIterator">
						<div class="heroWrapper" data-type="heroTitle" data-timestamp="{teaser.teaserbox.box_timestamp}">
							<div class="heroTitle" data-text-item="{teaserIterator.index}">{teaser.teaserbox.header}</div>
							<br/>
							<div class="heroSubtitle" data-text-item="{teaserIterator.index}">{teaser.teaserbox.subheader}</div>
						</div>
					</f:for>
				</f:if>
			</div>
		</div>
		<div class="teaser-controller">
			<div class="fluid-container">
				<div class="row">
					<div class="col-12 teaser-switch-box-container">
						<f:if condition="{teaserboxes}">

							<f:for each="{teaserboxes}" as="teaser" key="teaserId" iteration="teaserIterator">
								<f:if condition="{teaser.teaserbox.show}">

									<f:if condition="{teaser.teaserbox.alertbox}">
										<f:then>
											<div class="teaser-switch-box equal-height teaser-switch-box-highlight" data-type="teaser" data-timestamp="{teaser.teaserbox.box_timestamp}">
												<div>
													<f:render section="boxContent" arguments="{_all}" />
												</div>
											</div>
										</f:then>
										<f:else>
											<div class="teaser-switch-box equal-height" data-item="{teaserIterator.cycle}" data-type="teaser" data-timestamp="{teaser.teaserbox.box_timestamp}">
												<div>
													<f:render section="boxContent" arguments="{_all}" />
												</div>
											</div>
										</f:else>
									</f:if>
								</f:if>
							</f:for>

						</f:if>
					</div>
				</div>
			</div>

		</div>

	</section>
</f:section>


<f:section name="boxContent">

	<f:if condition="{teaser.teaserbox.panel_icon}">
		<f:then>
			<div class="svg-icon">
				<f:render partial="SvgIcon" section="{teaser.teaserbox.panel_icon}" arguments="{_all}" />
			</div>
		</f:then>
		<f:else>
			<!-- nmo icon -->
		</f:else>
	</f:if>

	<h2 class="teaser-switch-title">
		<f:format.nl2br>{teaser.teaserbox.box_header}</f:format.nl2br>
	</h2>
	<f:if condition="{teaser.teaserbox.box_subheader}">
		<p class="feaserbox-subheader">{teaser.teaserbox.box_subheader}</p>
	</f:if>
	<p>
		<f:format.nl2br>{teaser.teaserbox.box_text}</f:format.nl2br>
	</p>
	<f:if condition="{teaser.teaserbox.box_link}">
		<f:link.typolink parameter="{teaser.teaserbox.box_link}" class="btn btn-primary cta-link">
			{teaser.teaserbox.box_linklabel}
		</f:link.typolink>
	</f:if>
</f:section>


</html>