var videoFile = document.getElementById("rehnag-start-video");
if (videoFile) {
var options = {
    controls: false,
    liveui: false,
    resizeManager: false,
    languages: {
        de: de
    },
    html5: {
        //nativeControlsForTouch: true,
        hls: {
            withCredentials: false,
            preload: true,
            bandwidth: 928117100
        }
    }
};



    var player = videojs('rehnag-start-video', options);
    player.httpSourceSelector();
    player.qualityLevels();

    var timer = 0;
    var teaserLength, teasers;
    teaserLength = $('.teaser-home-with-video .teaser-switch-box').not('.teaser-switch-box-highlight').length;
    teasers = $('.teaser-home-with-video .teaser-switch-box').not('.teaser-switch-box-highlight').length - 1;
    player.on('timeupdate', function () {
        //console.log(this.currentTime());
        timer = Math.round(parseInt(this.currentTime(), 10));
        //console.log(timer);
        if (timer == 0) {
            animateTeaser(teaserLength);
            animateTitle(teaserLength);
            animateSubtitle(teaserLength);
        }
        if (timer == 6) {
            animateTeaser(1);
            animateTitle(1);
            animateSubtitle(1);
        }
        if (timer == 10) {
            animateTeaser(2);
            animateTitle(2);
            animateSubtitle(2);
        }
    });

    function animateTeaser(teaser) {
        $('.teaser-home-with-video .teaser-switch-box[data-item="' + (parseInt(teaser, 10) - 1) + '"]').animate({
                                                                                                                    'margin-top': 0
                                                                                                                }, 800, function () {
            if (teasers == (parseInt(teaser, 10) - 1)) {
                teaser = 0;
            }
            ;
            $('.teaser-home-with-video .teaser-switch-box[data-item="' + teaser + '"]').animate({
                                                                                                    'margin-top': -110
                                                                                                }, 800);
        });
    }

    function animateTitle(teaser) {
        $('.teaser-home-with-video .heroTitle[data-text-item="' + (parseInt(teaser, 10) - 1) + '"]').animate({
                                                                                                                 opacity: 0
                                                                                                             }, 800, function () {
            if (teasers == (parseInt(teaser, 10) - 1)) {
                teaser = 0;
            }
            ;
            $('.teaser-home-with-video .heroTitle[data-text-item="' + teaser + '"]').animate({
                                                                                                 opacity: 1
                                                                                             }, 800);
        });
    }

    function animateSubtitle(teaser) {
        $('.teaser-home-with-video .heroSubtitle[data-text-item="' + (parseInt(teaser, 10) - 1) + '"]').animate({
                                                                                                                    opacity: 0
                                                                                                                }, 800, function () {
            if (teasers == (parseInt(teaser, 10) - 1)) {
                teaser = 0;
            }
            ;
            $('.teaser-home-with-video .heroSubtitle[data-text-item="' + teaser + '"]').animate({
                                                                                                    opacity: 1
                                                                                                }, 800);
        });
    }
}