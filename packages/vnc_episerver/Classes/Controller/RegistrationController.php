<?php

namespace <PERSON>ca<PERSON>\VncEpiserver\Controller;

use TYPO3\CMS\Extbase\Utility\LocalizationUtility;
/***
 *
 * This file is part of the "Epi Server Formular" Extension for TYPO3 CMS.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 *  (c) 2022 Michael Pick <<EMAIL>>, Vancado AG
 *
 ***/

/**
 * RegistrationController
 */
class RegistrationController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{


    /**
     * action show
     *
     * @param \Vancado\VncEpiserver\Domain\Model\Registration $registration
     * @return void
     */
    public function showAction()
    {
        $currentRequest = $this->request->getArguments();

        $this->view->assign('request', $currentRequest);
    }

    /**
     * action new
     *
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function newAction(): \Psr\Http\Message\ResponseInterface
    {
        $arguments = $this->request->getArguments();
        $this->view->assignMultiple(
            [
                'response' => $arguments['response'],

            ]
        );

        return $this->htmlResponse();
    }

    public function registrationAction()
    {

        $data['result'] = false;
        $authorizationCode = '6PDvAFGhLRVffVxQvLqVBaYhK9XLzC7F';
        $bmOptInId = '256097550220';
        $bmOptinSource = 'Vertrieb – Rhenag-Vertrieb';

        $data = $this->request->getArguments();

        $url = 'https://api.campaign.episerver.net/http/form/' . $authorizationCode . '/subscribe';
        $url .= '?bmOptinSource=' . urlencode(utf8_decode($bmOptinSource));
        $url .= '&bmOptInId=' . $bmOptInId;
        $url .= '&bmRecipientId=' . urlencode(utf8_decode($data['email']));
        $url .= '&salutation=' . urlencode(utf8_decode($data['salutation']));
        $url .= '&firstname=' . urlencode(utf8_decode($data['firstname']));
        $url .= '&lastname=' . urlencode(utf8_decode($data['lastname']));
        $url .= '&advertising_accepted=' . urlencode(utf8_decode($data['advertising_accepted']));

        #
        $data['submitted'] = true;

        $this->view->assign('fieldvalues', $data);


        $formErrors = $this->validateFormData($data);

        if ($formErrors) {
            $this->redirect('show', null, null, ['fieldvalues' => $data, 'formErrors' => $formErrors]);
        } else {
            $result = @file_get_contents($url);
            $data['result'] = $result;
        }

        // api: optin in progress
        if ($data['result'] === 'optin_already_started') {
            $formErrors['api-error'] = LocalizationUtility::translate('LLL:EXT:vnc_episerver/Resources/Private/Language/locallang.xlf:tx_vncepiserver_domain_model_registration.error.optin_already_started');
            $this->redirect('show', null, null, ['fieldvalues' => $data, 'formErrors' => $formErrors]);
        }

        //api: duplicate
        if ($data['result'] === 'duplicate') {
            $formErrors['api-error'] = LocalizationUtility::translate('LLL:EXT:vnc_episerver/Resources/Private/Language/locallang.xlf:tx_vncepiserver_domain_model_registration.error.duplicate');
            $this->redirect('show', null, null, ['fieldvalues' => $data, 'formErrors' => $formErrors]);
        }

       # \TYPO3\CMS\Extbase\Utility\DebuggerUtility::var_dump($data);
    }

    /**
     * @param $data
     */
    private function validateFormData($data)
    {

        $formErrors = false;
        if ($data['salutation'] == '') {
            $formErrors['salutation'] = LocalizationUtility::translate('LLL:EXT:vnc_episerver/Resources/Private/Language/locallang.xlf:tx_vncepiserver_domain_model_registration.error.salutation');
        }
        if ($data['firstname'] == '') {
            $formErrors['firstname'] = LocalizationUtility::translate('LLL:EXT:vnc_episerver/Resources/Private/Language/locallang.xlf:tx_vncepiserver_domain_model_registration.error.firstname');
        }
        if (!$data['lastname']) {
            $formErrors['lastname'] = LocalizationUtility::translate('LLL:EXT:vnc_episerver/Resources/Private/Language/locallang.xlf:tx_vncepiserver_domain_model_registration.error.lastname');
        }
        if ($data['email'] == '' || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $formErrors['email'] = LocalizationUtility::translate('LLL:EXT:vnc_episerver/Resources/Private/Language/locallang.xlf:tx_vncepiserver_domain_model_registration.error.email');
        }
        if ($data['advertising_accepted'] != '1') {
            $formErrors['advertising_accepted'] = LocalizationUtility::translate('LLL:EXT:vnc_episerver/Resources/Private/Language/locallang.xlf:tx_vncepiserver_domain_model_registration.error.privacy');
        }

        return $formErrors;
    }
}
