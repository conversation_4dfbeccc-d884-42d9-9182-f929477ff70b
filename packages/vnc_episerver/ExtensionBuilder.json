{"modules": [{"config": {"position": [136, 143]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": false, "_default1_show": true, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": []}, "name": "Registration", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": true, "categorizable": false, "description": "", "mapToTable": "", "parentClass": "", "sorting": false, "type": "Entity", "uid": "178463768686"}, "propertyGroup": {"properties": []}, "relationGroup": {"relations": []}}}], "properties": {"backendModules": [], "description": "<PERSON><PERSON>r die Wärmewochen Aktion", "emConf": {"category": "plugin", "custom_category": "", "dependsOn": "typo3 => 9.5.0-9.5.99\n", "disableLocalization": false, "disableVersioning": false, "skipGenerateDocumentationTemplate": false, "sourceLanguage": "en", "state": "alpha", "targetVersion": "9.5.0-9.5.99", "version": "1.0.0"}, "extensionKey": "vnc_episerver", "name": "Epi Server Formular", "originalExtensionKey": "vnc_episerver", "originalVendorName": "Vancado", "persons": [{"company": "Vancado AG", "email": "<EMAIL>", "name": "<PERSON>", "role": "Developer"}], "plugins": [{"actions": {"controllerActionCombinations": "Registration => show,register", "noncacheableActions": "Registration => show,register", "switchableActions": ""}, "description": "Anmeldeformular für die Wäremewochen Aktion", "key": "pi1", "name": "registration"}], "vendorName": "Vancado"}, "wires": [], "storagePath": "/var/www/html/www/typo3conf/ext/", "log": {"last_modified": "2022-11-08 02:24", "extension_builder_version": "9.10.3", "be_user": "<PERSON> (1)"}}