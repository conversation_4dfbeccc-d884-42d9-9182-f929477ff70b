<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:layout name="Default"/>


<f:section name="content">

<div class="col-12">
    <div class="tx-powermail">
        <div class="container-fluid">
            <f:form action="registration">
                <div class="form-field field-full">
                    <div class="powermail_fieldwrap powermail_fieldwrap_type_select powermail_fieldwrap_titel field-full">
                        <label for="salutation" class="powermail_label" title="">{f:translate(id:'tx_vncepiserver_domain_model_registration.form.salutation')}<span class="mandatory">*</span></label>
                        <div class="powermail_field">
                            <f:form.select
                                class=""
                                required="1"
                                id="salutation"
                                name="salutation"
                                prependOptionValue=""
                                prependOptionLabel="Bitte auswählen"
                                options="{Herr: 'Herr', Frau: 'Frau', Divers: 'Divers'}"
                                value="{request.fieldvalues.salutation}" />
                            <div style="color:red;" class="field_error_container">{request.formErrors.salutation}</div>
                        </div>
                    </div>
                </div>
                <div class="form-field field-medium">
                    <div class="powermail_fieldwrap powermail_fieldwrap_type_input field-full">
                        <label for="firstname" class="powermail_label" title="">{f:translate(id:'tx_vncepiserver_domain_model_registration.form.firstname')}<span class="mandatory">*</span></label>
                        <div class="powermail_field">
                            <f:form.textfield
                                class="powermail_input"
                                id="firstname"
                                required="1"
                                value="{request.fieldvalues.firstname}"
                                name="firstname"/>
                            <div style="color:red;" class="field_error_container">{request.formErrors.firstname}</div>
                        </div>
                    </div>
                </div>
                <div class="form-field field-medium">
                    <div class="powermail_fieldwrap powermail_fieldwrap_type_input field-full ">
                        <label for="lastname" class="powermail_label" title="">{f:translate(id:'tx_vncepiserver_domain_model_registration.form.lastname')}<span class="mandatory">*</span></label>
                        <div class="powermail_field">
                            <f:form.textfield
                                class="powermail_input"
                                id="lastname"
                                required="1"
                                value="{request.fieldvalues.lastname}"
                                name="lastname"/>
                            <div style="color:red;" class="field_error_container">{request.formErrors.lastname}</div>
                        </div>
                    </div>
                </div>
                <div class="form-field field-full">
                    <div class="powermail_fieldwrap powermail_fieldwrap_type_input field-full">
                        <label for="email" class="powermail_label" title="">{f:translate(id:'tx_vncepiserver_domain_model_registration.form.email')}<span class="mandatory">*</span></label>
                        <div class="powermail_field">
                            <f:form.textfield
                                class="powermail_input"
                                id="email"
                                required="1"
                                type="email"
                                value="{request.fieldvalues.email}"
                                name="email"/>
                            <div style="color:red;" class="field_error_container">{request.formErrors.email}</div>
                        </div>
                    </div>
                </div>
                <div class="form-field field-full">
                    <div class="powermail_fieldwrap powermail_fieldwrap_type_check fieldwrap_advertising_accepted">
                        <label for="advertising_accepted" class="powermail_label" title="">{f:translate(id:'tx_vncepiserver_domain_model_registration.form.advertising_accepted')}<span class="mandatory">*</span></label>
                        <div class="powermail_field">
                            <div class="checkbox ">
                                <label>
                                    <f:form.checkbox
                                        name="advertising_accepted"
                                        value="1"
                                        multiple="false"
                                        additionalAttributes="{required:'required'}"
                                        />
                                    {f:translate(id:'tx_vncepiserver_domain_model_registration.form.advertising_accepted.claim')}
                                </label>
                                <div style="color:red;" class="field_error_container">{request.formErrors.privacy}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <f:if condition="{request.formErrors.api-error}">
                    <div style="color:red;" class="field_error_container">{request.formErrors.api-error}</div>
                </f:if>
                <div class="form-field ">
                    <div class="powermail_fieldwrap powermail_fieldwrap_type_submit">
                        <div class="powermail_field">
                            <f:form.button class="btn btn-primary">{f:translate(id:'tx_vncepiserver_domain_model_registration.form.submit')}</f:form.button>
                        </div>
                    </div>
                </div>
            </f:form>
        </div>
    </div>
</div>


</f:section>
</html>



