<?php
defined('TYPO3_MODE') || die('Access denied.');

call_user_func(
    function()
    {

        \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
            'Vancado.VncEpiserver',
            'Pi1',
            [
                'Registration' => 'show, registration, new'
            ],
            // non-cacheable actions
            [
                'Registration' => 'registration, new'
            ]
        );

        // wizards
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPageTSConfig(
            'mod {
                wizards.newContentElement.wizardItems.plugins {
                    elements {
                        pi1 {
                            iconIdentifier = vnc_episerver-plugin-pi1
                            title = LLL:EXT:vnc_episerver/Resources/Private/Language/locallang_db.xlf:tx_vnc_episerver_pi1.name
                            description = LLL:EXT:vnc_episerver/Resources/Private/Language/locallang_db.xlf:tx_vnc_episerver_pi1.description
                            tt_content_defValues {
                                CType = list
                                list_type = vncepiserver_pi1
                            }
                        }
                    }
                    show = *
                }
           }'
        );
		$iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);

			$iconRegistry->registerIcon(
				'vnc_episerver-plugin-pi1',
				\TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
				['source' => 'EXT:vnc_episerver/Resources/Public/Icons/user_plugin_pi1.svg']
			);

    }
);
