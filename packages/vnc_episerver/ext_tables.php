<?php
defined('TYPO3_MODE') || die('Access denied.');

call_user_func(
    function()
    {

        \TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
            'Vancado.VncEpiserver',
            'Pi1',
            'registration'
        );

        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addStaticFile('vnc_episerver', 'Configuration/TypoScript', 'Epi Server Formular');

        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_vncepiserver_domain_model_registration', 'EXT:vnc_episerver/Resources/Private/Language/locallang_csh_tx_vncepiserver_domain_model_registration.xlf');
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_vncepiserver_domain_model_registration');

    }
);
