<?php
namespace Vancado\VncPageHits\Controller;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Connection;


/***
 *
 * This file is part of the "Page hits" Extension for TYPO3 CMS.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 *  (c) 2021 
 *
 ***/
/**
 * PageController
 */
class PageController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{

    /**
     * action count
     * 
     * @return void
     */
    public function countAction()
    {
        $pageId = $GLOBALS['TSFE']->id;

        if ($pageId) {
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable('pages');
            $where = $queryBuilder->expr()->eq('uid', (int)$pageId);

            $hits = $queryBuilder->select('hits')->from('pages')->where($where)->execute()->fetchColumn(0);

            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable('pages');
            $queryBuilder->update('pages')->where($where)->set('hits', ++$hits)->execute();
        }

        return "";
        
    }

    /**
     * action hits
     * 
     * @return void
     */
    public function hitsAction()
    {
        $pageId = $GLOBALS['TSFE']->id;
        $hits = 0;

        if ($pageId) {
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable('pages');
            $where = $queryBuilder->expr()->eq('uid', (int)$pageId);
            $hits = $queryBuilder->select('hits')->from('pages')->where($where)->execute()->fetchColumn(0);
        }

        $this->view->assign('hits', $hits);
    }
}
