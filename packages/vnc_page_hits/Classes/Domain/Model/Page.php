<?php
namespace Vancado\VncPageHits\Domain\Model;


/***
 *
 * This file is part of the "Page hits" Extension for TYPO3 CMS.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 *  (c) 2021 
 *
 ***/
/**
 * Page
 */
class Page extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{

    /**
     * hits
     * 
     * @var string
     */
    protected $hits = '';

    /**
     * Returns the hits
     * 
     * @return string $hits
     */
    public function getHits()
    {
        return $this->hits;
    }

    /**
     * Sets the hits
     * 
     * @param string $hits
     * @return void
     */
    public function setHits($hits)
    {
        $this->hits = $hits;
    }
}
