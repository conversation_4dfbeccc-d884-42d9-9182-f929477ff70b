<?php
defined('TYPO3_MODE') || die();


$tmp_vnc_page_hits_columns = [

    'hits' => [
        'exclude' => true,
        'label' => 'LLL:EXT:vnc_page_hits/Resources/Private/Language/locallang_db.xlf:tx_vncpagehits_domain_model_page.hits',
        'config' => [
            'type' => 'input',
            'size' => 5,
            'eval' => 'int,trim'
        ],
    ],

];

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns('pages', $tmp_vnc_page_hits_columns);

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
   'pages',
   'hits',
   '',
   'after: abstract'
);