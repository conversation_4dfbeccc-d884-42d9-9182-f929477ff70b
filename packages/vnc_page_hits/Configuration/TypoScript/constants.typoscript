
plugin.tx_vncpagehits_pi1 {
    view {
        # cat=plugin.tx_vncpagehits_pi1/file; type=string; label=Path to template root (FE)
        templateRootPath = EXT:vnc_page_hits/Resources/Private/Templates/
        # cat=plugin.tx_vncpagehits_pi1/file; type=string; label=Path to template partials (FE)
        partialRootPath = EXT:vnc_page_hits/Resources/Private/Partials/
        # cat=plugin.tx_vncpagehits_pi1/file; type=string; label=Path to template layouts (FE)
        layoutRootPath = EXT:vnc_page_hits/Resources/Private/Layouts/
    }
    persistence {
        # cat=plugin.tx_vncpagehits_pi1//a; type=string; label=Default storage PID
        storagePid =
    }
}

plugin.tx_vncpagehits_pi2 {
    view {
        # cat=plugin.tx_vncpagehits_pi2/file; type=string; label=Path to template root (FE)
        templateRootPath = EXT:vnc_page_hits/Resources/Private/Templates/
        # cat=plugin.tx_vncpagehits_pi2/file; type=string; label=Path to template partials (FE)
        partialRootPath = EXT:vnc_page_hits/Resources/Private/Partials/
        # cat=plugin.tx_vncpagehits_pi2/file; type=string; label=Path to template layouts (FE)
        layoutRootPath = EXT:vnc_page_hits/Resources/Private/Layouts/
    }
    persistence {
        # cat=plugin.tx_vncpagehits_pi2//a; type=string; label=Default storage PID
        storagePid =
    }
}
