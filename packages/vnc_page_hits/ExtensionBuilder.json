{"modules": [{"config": {"position": [253, 148]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": false, "_default1_show": false, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": ["count", "hits"]}, "name": "Page", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": false, "categorizable": false, "description": "", "mapToTable": "pages", "parentClass": "", "sorting": false, "type": "Entity", "uid": "************"}, "propertyGroup": {"properties": [{"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsL10nModeExclude": false, "propertyIsRequired": false, "propertyName": "hits", "propertyType": "String", "uid": "1508436560286"}]}, "relationGroup": {"relations": []}}}], "properties": {"backendModules": [], "description": "", "emConf": {"category": "plugin", "custom_category": "", "dependsOn": "typo3 => 9.5.0-9.5.99\n", "disableLocalization": false, "disableVersioning": false, "skipGenerateDocumentationTemplate": false, "sourceLanguage": "en", "state": "alpha", "targetVersion": "9.5.0-9.5.99", "version": "1.0.0"}, "extensionKey": "vnc_page_hits", "name": "Page hits", "originalExtensionKey": "vnc_page_hits", "originalVendorName": "Vancado", "persons": [], "plugins": [{"actions": {"controllerActionCombinations": "Page => count", "noncacheableActions": "", "switchableActions": ""}, "description": "", "key": "pi1", "name": "Counter"}, {"actions": {"controllerActionCombinations": "Page => hits", "noncacheableActions": "", "switchableActions": ""}, "description": "", "key": "pi2", "name": "show hits"}], "vendorName": "Vancado"}, "wires": [], "storagePath": "/var/www/vancado/rhenag/homepage/www/typo3conf/ext/", "log": {"last_modified": "2021-01-26 01:20", "extension_builder_version": "9.10.3", "be_user": "<PERSON> (7)"}}