<?php
defined('TYPO3_MODE') || die('Access denied.');

call_user_func(
    function()
    {

        \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
            'Vancado.VncPageHits',
            'Pi1',
            [
                'Page' => 'count'
            ],
            // non-cacheable actions
            [
                'Page' => 'count'
            ]
        );

        \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
            'Vancado.VncPageHits',
            'Pi2',
            [
                'Page' => 'hits'
            ],
            // non-cacheable actions
            [
                'Page' => 'hits'
            ]
        );

        // wizards
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPageTSConfig(
            'mod {
                wizards.newContentElement.wizardItems.plugins {
                    elements {
                        pi1 {
                            iconIdentifier = vnc_page_hits-plugin-pi1
                            title = LLL:EXT:vnc_page_hits/Resources/Private/Language/locallang_db.xlf:tx_vnc_page_hits_pi1.name
                            description = LLL:EXT:vnc_page_hits/Resources/Private/Language/locallang_db.xlf:tx_vnc_page_hits_pi1.description
                            tt_content_defValues {
                                CType = list
                                list_type = vncpagehits_pi1
                            }
                        }
                        pi2 {
                            iconIdentifier = vnc_page_hits-plugin-pi2
                            title = LLL:EXT:vnc_page_hits/Resources/Private/Language/locallang_db.xlf:tx_vnc_page_hits_pi2.name
                            description = LLL:EXT:vnc_page_hits/Resources/Private/Language/locallang_db.xlf:tx_vnc_page_hits_pi2.description
                            tt_content_defValues {
                                CType = list
                                list_type = vncpagehits_pi2
                            }
                        }
                    }
                    show = *
                }
           }'
        );
		$iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
		
			$iconRegistry->registerIcon(
				'vnc_page_hits-plugin-pi1',
				\TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
				['source' => 'EXT:vnc_page_hits/Resources/Public/Icons/user_plugin_pi1.svg']
			);
		
			$iconRegistry->registerIcon(
				'vnc_page_hits-plugin-pi2',
				\TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
				['source' => 'EXT:vnc_page_hits/Resources/Public/Icons/user_plugin_pi2.svg']
			);
		
    }
);
