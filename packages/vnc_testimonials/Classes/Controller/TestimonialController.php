<?php
namespace Van<PERSON><PERSON>\VncTestimonials\Controller;


/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * TestimonialController
 */
class TestimonialController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{

    /**
     * testimonialRepository
     *
     * @var \Vancado\VncTestimonials\Domain\Repository\TestimonialRepository
     * @TYPO3\CMS\Extbase\Annotation\Inject
     */
    protected $testimonialRepository = NULL;
    
    /**
     * action list
     *
     * @return void
     */
    public function listAction()
    {
        if ($this->settings['mode'] == 'list') {
            $testimonials = $this->testimonialRepository->findAll();
        } else {

            $uids = explode(',', $this->settings['items']);
            $testimonials = array();
            foreach($uids as $id) {
                $testimonials[] = $this->testimonialRepository->findByUid($id);
            }
        }
        $this->view->assign('testimonials', $testimonials);
    }

}