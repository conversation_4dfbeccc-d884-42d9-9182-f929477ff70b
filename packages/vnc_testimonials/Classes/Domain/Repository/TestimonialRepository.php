<?php
namespace <PERSON><PERSON><PERSON>\VncTestimonials\Domain\Repository;

/***************************************************************
 *
 *  Copyright notice
 *
 *  (c) 2016
 *
 *  All rights reserved
 *
 *  This script is part of the TYPO3 project. The TYPO3 project is
 *  free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  The GNU General Public License can be found at
 *  http://www.gnu.org/copyleft/gpl.html.
 *
 *  This script is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  This copyright notice MUST APPEAR in all copies of the script!
 ***************************************************************/

/**
 * The repository for Testimonials
 */
class TestimonialRepository extends \TYPO3\CMS\Extbase\Persistence\Repository
{

/**
 * Find by multiple uids using, seperated string. Found on blog.teamgeist-medien.de
 * 
 * @param string String containing uids
 * @return \TYPO3\meineExtension\Domain\Model\meinZurueckgegebensModel Matching model records
 */
public function findByUids($uids) {
    $uidArray = explode(",", $uids);
    $query = $this->createQuery();
    foreach ($uidArray as $key => $value) {
        $constraints[] =  $query->equals('uid', $value);
    }
    return $query->matching(
        $query->logicalAnd(
            $query->logicalOr(
                $constraints
            ),
            $query->equals('hidden', 0),
            $query->equals('deleted', 0)
        )
    )->execute();
}

    
}