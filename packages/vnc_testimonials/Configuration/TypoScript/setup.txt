
plugin.tx_vnctestimonials_list {
	view {
		templateRootPaths.0 = {$plugin.tx_vnctestimonials_list.view.templateRootPath}
		partialRootPaths.0 = {$plugin.tx_vnctestimonials_list.view.partialRootPath}
		layoutRootPaths.0 = {$plugin.tx_vnctestimonials_list.view.layoutRootPath}
	}
	persistence {
		storagePid = {$plugin.tx_vnctestimonials_list.persistence.storagePid}
	}
}

plugin.tx_vnctestimonials._CSS_DEFAULT_STYLE (
	textarea.f3-form-error {
		background-color:#FF9F9F;
		border: 1px #FF0000 solid;
	}

	input.f3-form-error {
		background-color:#FF9F9F;
		border: 1px #FF0000 solid;
	}

	.tx-vnc-testimonials table {
		border-collapse:separate;
		border-spacing:10px;
	}

	.tx-vnc-testimonials table th {
		font-weight:bold;
	}

	.tx-vnc-testimonials table td {
		vertical-align:top;
	}

	.typo3-messages .message-error {
		color:red;
	}

	.typo3-messages .message-ok {
		color:green;
	}

)
