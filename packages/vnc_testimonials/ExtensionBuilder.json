{"modules": [{"config": {"position": [93, 74]}, "name": "New Model Object", "value": {"actionGroup": {"_default0_list": true, "_default1_show": false, "_default2_new_create": false, "_default3_edit_update": false, "_default4_delete": false, "customActions": []}, "name": "Testimonial", "objectsettings": {"addDeletedField": true, "addHiddenField": true, "addStarttimeEndtimeFields": true, "aggregateRoot": true, "categorizable": false, "description": "", "mapToTable": "", "parentClass": "", "sorting": false, "type": "Entity", "uid": "************"}, "propertyGroup": {"properties": [{"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": true, "propertyName": "name", "propertyType": "String", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "company", "propertyType": "String", "uid": "173791986785"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "image", "propertyType": "Image", "uid": "************"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "date", "propertyType": "NativeDate", "uid": "1244257169800"}, {"allowedFileTypes": "", "maxItems": "1", "propertyDescription": "", "propertyIsExcludeField": true, "propertyIsRequired": false, "propertyName": "text", "propertyType": "Text", "uid": "************"}]}, "relationGroup": {"relations": []}}}], "properties": {"backendModules": [], "description": "", "emConf": {"category": "plugin", "custom_category": "", "dependsOn": "typo3 => 7.6.0-7.6.99\n", "disableLocalization": false, "disableVersioning": false, "skipGenerateDocumentationTemplate": false, "sourceLanguage": "en", "state": "alpha", "targetVersion": "7.6.0-7.6.99", "version": ""}, "extensionKey": "vnc_testimonials", "name": "Testimonials", "originalExtensionKey": "vnc_testimonials", "originalVendorName": "Vancado", "persons": [], "plugins": [{"actions": {"controllerActionCombinations": "Testimonial => list", "noncacheableActions": "", "switchableActions": ""}, "key": "list", "name": "Testimonials"}], "vendorName": "Vancado"}, "wires": [], "log": {"last_modified": "2016-09-19 11:28", "extension_builder_version": "7.6.0", "be_user": " (7)"}}