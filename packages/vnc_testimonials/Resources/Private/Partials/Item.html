<div xmlns="http://www.w3.org/1999/xhtml" lang="en"
	 xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
	 xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
	 xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers">

	<v:variable.set name="imageProperties">{v:resource.file(identifier: '{testimonial.image.originalResource.originalFile.uid}', treatIdAsUid: 1, treatIdAsReference: 0 )}</v:variable.set>
	
	<div class="testimonial {f:if(condition:iterator.isEven, then: 'even')}">

		<div class="testimonial-image focuspoint"
		data-focus-x="{f:if(condition:'{imageProperties.focus_point_x}', then:'{v:math.division(a: imageProperties.focus_point_x, b: 100 )}', else:'0')}"
		data-focus-y="{f:if(condition:'{imageProperties.focus_point_y}', then:'{v:math.division(a: imageProperties.focus_point_y, b: 100 )}', else:'0')}" data-image-w="{imageProperties.width}" data-image-h="{imageProperties.height}">
		    <picture>
		    	<!--[if IE 9]><video style="display: none;"><![endif]-->
		    	<source srcset="{f:uri.image(treatIdAsReference:0, src:'{imageProperties.uid}', maxWidth:'150')} , {f:uri.image(treatIdAsReference:0, src:'{imageProperties.uid}', maxWidth:'300')} 2x" media=""> <!-- 150x150--> <!-- 300x300-->
		    	<!--[if IE 9]></video><![endif]-->
		    	<img title="{imageProperties.title}"  alt="{imageProperties.alternative}" src=""  />
		    </picture>
		</div>
		<div class="testimonial-inner">			
			<div class="testimonial-text">
				<p class="testimonial-quotation">"{testimonial.text}"</p>
				<p>{testimonial.name}<f:if condition="{testimonial.company}">, {testimonial.company}</f:if></p>
			</div>
			<div class="clearfix"></div>
		</div>
	</div>

</div>

