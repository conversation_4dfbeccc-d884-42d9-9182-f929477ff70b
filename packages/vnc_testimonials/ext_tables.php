<?php
if (!defined('TYPO3_MODE')) {
	die('Access denied.');
}

$_EXTKEY = 'vnc_testimonials';

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
	'Vancado.' . $_EXTKEY,
	'List',
	'Testimonials'
);

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addStaticFile($_EXTKEY, 'Configuration/TypoScript', 'Testimonials');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_vnctestimonials_domain_model_testimonial', 'EXT:vnc_testimonials/Resources/Private/Language/locallang_csh_tx_vnctestimonials_domain_model_testimonial.xlf');
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_vnctestimonials_domain_model_testimonial');

/**
 * FlexForms Configuration
 */
$extensionName = strtolower( \TYPO3\CMS\Core\Utility\GeneralUtility::underscoredToUpperCamelCase($_EXTKEY) );
// Widget View
$pluginSignature = strtolower($extensionName) . '_list';
$TCA['tt_content']['types']['list']['subtypes_excludelist'][$pluginSignature] = 'layout,select_key,recursive';
$TCA['tt_content']['types']['list']['subtypes_addlist'][$pluginSignature] = 'pi_flexform';
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue($pluginSignature, 'FILE:EXT:'.$_EXTKEY . '/Configuration/FlexForms/List.xml'); 
