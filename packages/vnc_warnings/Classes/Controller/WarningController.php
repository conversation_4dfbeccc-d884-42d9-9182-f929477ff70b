<?php

namespace <PERSON><PERSON><PERSON>\VncWarnings\Controller;

use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Extbase\Annotation\Inject;
use TYPO3\CMS\Extbase\Persistence\Exception\InvalidQueryException;
use Vancado\VncWarnings\Domain\Repository\WarningRepository;

/***
 *
 * This file is part of the "H2O Warnings" Extension for TYPO3 CMS.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 *  (c) 2020 Michael Pick <<EMAIL>>, Vancado
 *
 ***/




/**
 * WarningController
 */
class WarningController extends ActionController {



    /**
     * WarningRepository
     *
     * @var \Vancado\VncWarnings\Domain\Repository\WarningRepository
     */
    protected $warningRepository;




    /**
     * @param Dependency $warningRepository
     */
    public function __construct(WarningRepository $warningRepository)
    {
        $this->warningRepository = $warningRepository;
    }

    /**
     * action list
     *
     * @return void
     * @throws InvalidQueryException
     */
    public function listAction() {
        $page  = $GLOBALS['TSFE']->page['uid'];
        $warnings = $this->warningRepository->getPageWarnings($page);
        $this->view->assign( 'warnings', $warnings );
    }





    /**
     * action showModal
     *
     * @return void
     */
    public function showModalAction() {
        $page  = $GLOBALS['TSFE']->page['uid'];

        $this->view->assign('warnings', $this->warningRepository->findOneBySorting($page));
    }




}
