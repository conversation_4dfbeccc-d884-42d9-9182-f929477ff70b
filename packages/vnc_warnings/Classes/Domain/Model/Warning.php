<?php
namespace <PERSON><PERSON><PERSON>\VncWarnings\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

/***
 *
 * This file is part of the "H2O Warnings" Extension for TYPO3 CMS.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 *  (c) 2020 Michael Pick <<EMAIL>>, Vancado
 *
 ***/
/**
 * H2O Warnings
 */
class Warning extends AbstractEntity
{

    /**
     * Headline
     * 
     * @var string
     * @TYPO3\CMS\Extbase\Annotation\Validate("NotEmpty")
     */
    protected  $header = '';

    /**
     * Text
     * 
     * @var string
     */
    protected  $text = '';

    /**
     * Link
     * 
     * @var string
     */
    protected  $link = '';

    /**
     * Background
     * 
     * @var string
     * @TYPO3\CMS\Extbase\Annotation\Validate("NotEmpty")
     */
    protected  $background = '';

    /**
     * Returns the header
     * 
     * @return string $header
     */

	/**
	 * pages
	 *
	 * @var string
	 */
	protected  $pages = '';

    /**
     * ButtonText
     *
     * @var string
     */
    protected  $linkText = '';

    /**
     * LinkSecond
     *
     * @var string
     */
    protected  $linkSecond = '';

    /**
     * LinkTextSecond
     *
     * @var string
     */
    protected  $linkTextSecond = '';

    /**
     * Icon
     *
     * @var string
     */
    protected  $icon = '';

    /**
	 * Returns the pages
	 *
	 * @return string $pages
	 */
	public function getPages(): ?string
    {
		return $this->pages;
	}

    /**
     * Sets the pages
     *
     * @param string $pages
     * @return void
     */
	public function setPages(string $pages): void
	{
		$this->pages = $pages;
	}

	/**
	 * Returns the header
	 *
	 * @return string $header
	 */
    public function getHeader(): ?string
    {
        return $this->header;
    }

    /**
     * Returns the background
     * 
     * @return string $background
     */
    public function getBackground(): ?string
    {
        return $this->background;
    }

    /**
     * Sets the background
     *
     * @param string $background
     * @return void
     */
    public function setBackground(string $background)
    {
        $this->background = $background;
    }

    /**
     * Sets the header
     *
     * @param string $header
     * @return void
     */
    public function setHeader(string $header): void
    {
        $this->header = $header;
    }

    /**
     * Returns the text
     * 
     * @return string $text
     */
    public function getText(): ?string
    {
        return $this->text;
    }

    /**
     * Sets the text
     *
     * @param string $text
     * @return void
     */
    public function setText(string $text): void
    {
        $this->text = $text;
    }

    /**
     * Returns the link
     * 
     * @return string $link
     */
    public function getLink(): ?string
    {
        return $this->link;
    }

    /**
     * Sets the link
     *
     * @param string $link
     * @return void
     */
    public function setLink(string $link): void
    {
        $this->link = $link;
    }

    /**
     * @return string
     */
    public function getLinkText(): string
    {
        return $this->linkText;
    }

    /**
     * @param string $linkText
     */
    public function setLinkText(string $linkText): void
    {
        $this->linkText = $linkText;
    }

    /**
     * @return string
     */
    public function getLinkSecond(): string
    {
        return $this->linkSecond;
    }

    /**
     * @param string $linkSecond
     */
    public function setLinkSecond(string $linkSecond): void
    {
        $this->linkSecond = $linkSecond;
    }

    /**
     * @return string
     */
    public function getLinkTextSecond(): string
    {
        return $this->linkTextSecond;
    }

    /**
     * @param string $linkTextSecond
     */
    public function setLinkTextSecond(string $linkTextSecond): void
    {
        $this->linkTextSecond = $linkTextSecond;
    }

    /**
     * @return string
     */
    public function getIcon(): string
    {
        return $this->icon;
    }

    /**
     * @param string $icon
     */
    public function setIcon(string $icon): void
    {
        $this->icon = $icon;
    }
}
