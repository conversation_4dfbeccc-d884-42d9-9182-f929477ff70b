<?php

namespace Van<PERSON><PERSON>\VncWarnings\Domain\Repository;

use TYPO3\CMS\Extbase\Persistence\Exception\InvalidQueryException;
use TYPO3\CMS\Extbase\Persistence\QueryInterface;
use TYPO3\CMS\Extbase\Persistence\QueryResultInterface;
use TYPO3\CMS\Extbase\Persistence\Repository;

/***
 *
 * This file is part of the "Warnings" Extension for TYPO3 CMS.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 *  (c) 2020 Michael Pick <<EMAIL>>, Vancado
 *
 ***/

/**
 * The repository for Warnings
 */
class WarningRepository extends Repository {

    /**
     * @var array
     */
    protected $defaultOrderings = [ 'sorting' => QueryInterface::ORDER_ASCENDING ];

    /**
     * @param int $page
     * @return array|QueryResultInterface
     * @throws InvalidQueryException
     */
    public function getPageWarnings( $page = 0 ) {
        $query = $this->createQuery();
        $query->getQuerySettings()->setRespectStoragePage( false );

        return $query->matching( $query->logicalAnd( $query->contains( 'pages', $page ) ) )->execute();
    }

    /**
     * @return object|null
     */
    public function findOneBySorting($page = 1): ?object
    {
        $query = $this->createQuery();
        $query->getQuerySettings()->setRespectStoragePage( false );

        return $query->matching( $query->logicalAnd( $query->contains( 'pages', $page ) ) )->execute();
    }
}
