<?php
defined('TYPO3_MODE') || die();

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::makeCategorizable(
   'vnc_warnings',
   'tx_vncwarnings_domain_model_warning'
);

	$GLOBALS['TCA']['tx_vncwarnings_domain_model_warning']['columns']['pages'] = [
		'label' => 'Pages',
        'config' => [
            'type' => 'select',
            'renderType' => 'selectTree',
            'foreign_table' => 'pages',
            'foreign_table_where' => 'ORDER BY pages.sorting',
            'size' => 20,
            'treeConfig' => [
                'parentField' => 'pid',
                'appearance' => [
                    'expandAll' => true,
                    'showHeader' => true,
                ],
            ],
        ],
	];


$GLOBALS['TCA']['tx_vncwarnings_domain_model_warning']['grid'] = [
		'excluded_fields' => '',
		'columns' => [
			'header' => [
				'visible' => true,
				'editable' => true
			],
			'text' => [
				'visible' => true,
				'editable' => true
			],
            'link_text' => [
                'visible' => true,
                'editable' => true
            ],
			'link' => [
				'visible' => true,
				'editable' => true
			],
		]
	];
