<?php
return [
    'ctrl' => [
        'title' => 'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning',
        'label' => 'header',
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'cruser_id' => 'cruser_id',
        'sortby' => 'sorting',
        'versioningWS' => true,
        'languageField' => 'sys_language_uid',
        'transOrigPointerField' => 'l10n_parent',
        'transOrigDiffSourceField' => 'l10n_diffsource',
        'delete' => 'deleted',
        'enablecolumns' => [
            'disabled' => 'hidden',
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'searchFields' => 'header,icon,text,link,link_text,background',
        'iconfile' => 'EXT:vnc_warnings/Resources/Public/Icons/tx_vncwarnings_domain_model_warning.gif'
    ],
    'interface' => [
        'showRecordFieldList' => 'sys_language_uid, l10n_parent, l10n_diffsource, hidden, header, icon, text, link, link_text, link_second, link_text_second, icon, background, pages ',
    ],
    'types' => [
        '1' => ['showitem' => 'sys_language_uid, l10n_parent, l10n_diffsource, hidden, header, text, link_text, link, link_text_second, link_second, icon, background, pages, --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access, starttime, endtime'],
    ],
    'columns' => [
        'sys_language_uid' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.language',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'special' => 'languages',
                'items' => [
                    [
                        'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.allLanguages',
                        -1,
                        'flags-multiple'
                    ]
                ],
                'default' => 0,
            ],
        ],
        'l10n_parent' => [
            'displayCond' => 'FIELD:sys_language_uid:>:0',
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.l18n_parent',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'default' => 0,
                'items' => [
                    ['', 0],
                ],
                'foreign_table' => 'tx_vncwarnings_domain_model_warning',
                'foreign_table_where' => 'AND {#tx_vncwarnings_domain_model_warning}.{#pid}=###CURRENT_PID### AND {#tx_vncwarnings_domain_model_warning}.{#sys_language_uid} IN (-1,0)',
            ],
        ],
        'l10n_diffsource' => [
            'config' => [
                'type' => 'passthrough',
            ],
        ],
        't3ver_label' => [
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.versionLabel',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'max' => 255,
            ],
        ],
        'hidden' => [
            'exclude' => true,
            'label' => 'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning.hidden',
            'config' => [
                'type' => 'check',
                'items' => [
                    [
                        0 => '',
                        1 => '',
                        'invertStateDisplay' => true
                    ]
                ],
            ],
        ],
        'starttime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'input',
                'renderType' => 'inputDateTime',
                'eval' => 'datetime,int',
                'default' => 0,
                'behaviour' => [
                    'allowLanguageSynchronization' => true
                ]
            ],
        ],
        'endtime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'input',
                'renderType' => 'inputDateTime',
                'eval' => 'datetime,int',
                'default' => 0,
                'range' => [
                    'upper' => mktime(0, 0, 0, 1, 1, 2038)
                ],
                'behaviour' => [
                    'allowLanguageSynchronization' => true
                ]
            ],
        ],

        'header' => [
            'exclude' => true,
            'label' => 'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning.header',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim,required'
            ],
        ],
        'text' => [
            'exclude' => true,
            'label' => 'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning.text',
            'config' => [
                'type' => 'text',
                'enableRichtext' => true,
                'richtextConfiguration' => 'VncWarnings',
                'fieldControl' => [
                    'fullScreenRichtext' => [
                        'disabled' => false,
                    ],
                ],
                'cols' => 40,
                'rows' => 15,
                'eval' => 'trim',
            ],
        ],
        'link_text' => [
            'exclude' => true,
            'label' => 'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning.link_text',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'link' => [
            'exclude' => true,
            'label' => 'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning.link',
            'config' => [
	            'type' => 'input',
	            'renderType' => 'inputLink',
            ],
        ],
        'pages' => [
	        'exclude' => true,
	        'label' => 'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning.pages',
	        'config' => [
		        'type' => 'input',
		        'size' => 30,
		        'eval' => 'trim'
	        ],
        ],
    ],
];

