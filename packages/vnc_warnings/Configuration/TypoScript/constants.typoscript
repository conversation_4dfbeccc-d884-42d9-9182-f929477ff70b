plugin.tx_vncwarnings_pi1 {
    view {
        # cat=plugin.tx_vncwarnings_pi1/file; type=string; label=Path to template root (FE)
        templateRootPath = EXT:vnc_warnings/Resources/Private/Templates/
        # cat=plugin.tx_vncwarnings_pi1/file; type=string; label=Path to template partials (FE)
        partialRootPath = EXT:vnc_warnings/Resources/Private/Partials/
        # cat=plugin.tx_vncwarnings_pi1/file; type=string; label=Path to template layouts (FE)
        layoutRootPath = EXT:vnc_warnings/Resources/Private/Layouts/
    }
    persistence {
        # cat=plugin.tx_vncwarnings_pi1//a; type=string; label=Default storage PID
        storagePid = 51
    }
}

plugin.tx_vncwarnings_pi2 {
    view {
        # cat=plugin.tx_vncwarnings_pi1/file; type=string; label=Path to template root (FE)
        templateRootPath = EXT:vnc_warnings/Resources/Private/Templates/
        # cat=plugin.tx_vncwarnings_pi1/file; type=string; label=Path to template partials (FE)
        partialRootPath = EXT:vnc_warnings/Resources/Private/Partials/
        # cat=plugin.tx_vncwarnings_pi1/file; type=string; label=Path to template layouts (FE)
        layoutRootPath = EXT:vnc_warnings/Resources/Private/Layouts/
    }
    persistence {
        # cat=plugin.tx_vncwarnings_pi1//a; type=string; label=Default storage PID
        storagePid = 51
    }
}