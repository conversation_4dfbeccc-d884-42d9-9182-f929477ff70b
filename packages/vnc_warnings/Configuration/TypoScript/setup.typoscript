plugin.tx_vncwarnings_pi1 {
	view {
		templateRootPaths.0 = EXT:{extension.shortExtensionKey}/Resources/Private/Templates/
		templateRootPaths.1 = {$plugin.tx_vncwarnings_pi1.view.templateRootPath}
		partialRootPaths.0 = EXT:vnc_warnings/Resources/Private/Partials/
		partialRootPaths.1 = {$plugin.tx_vncwarnings_pi1.view.partialRootPath}
		layoutRootPaths.0 = EXT:tx_vncwarnings/Resources/Private/Layouts/
		layoutRootPaths.1 = {$plugin.tx_vncwarnings_pi1.view.layoutRootPath}
	}

	persistence {
		storagePid = {$plugin.tx_vncwarnings_pi1.persistence.storagePid}
		#recursive = 1
	}

	features {
		#skipDefaultArguments = 1
		# if set to 1, the enable fields are ignored in BE context
		ignoreAllEnableFieldsInBe = 0
		# Should be on by default, but can be disabled if all action in the plugin are uncached
		requireCHashArgumentForActionArguments = 1
	}

	mvc {
		callDefaultActionIfActionCantBeResolved = 1
	}

	settings {
		TSMode = 1
	}
}

lib.vncWarnings = USER
lib.vncWarnings {
	userFunc = TYPO3\CMS\Extbase\Core\Bootstrap->run
	extensionName = VncWarnings
	pluginName = Pi1
	vendorName = Vancado
	#controller = Warning
	#action = List
	view < plugin.tx_vncwarnings_pi1.view
	persistence < plugin.tx_vncwarnings_pi1.persistence
	settings < plugin.tx_vncwarnings_pi1.settings
}

lib.vncWarningsModal = USER
lib.vncWarningsModal {
	userFunc = TYPO3\CMS\Extbase\Core\Bootstrap->run
	extensionName = VncWarnings
	pluginName = Pi2
	vendorName = Vancado
	#controller = Warning
	action = ShowModal
	view < plugin.tx_vncwarnings_pi1.view
	persistence < plugin.tx_vncwarnings_pi1.persistence
	settings < plugin.tx_vncwarnings_pi1.settings
}

page.includeCSS.vncWarningsModal = typo3conf/ext/vnc_warnings/Resources/Public/Css/modal.css
page.includeJSFooter.vncWarningaModal = typo3conf/ext/vnc_warnings/Resources/Public/JavaScript/vnc_warning.js

page.945678 = TEXT
page.945678.value = <div id="modal-backdrop" class="modal-backdrop" style="display:none;"></div>

# inject plugin in PAGE Obj.
page = PAGE
page.27213258 < lib.vncWarningsModal