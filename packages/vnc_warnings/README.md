# vnc_warnings
# rhenag edition
retroactively modified version to work with TYPO3 version 9.5

# Installation an configuration
1. install extension
2. include static TS in rootpage TypoScript Main Template
3. Create sysfolder for warnings in page tree 
4. create a "warning" record ibid
5. set "Default storage PID" in TS constant editor (at least the _pi2 variant) to sysfolder-pid 

# Activation of plugin
warning modal is directly injected in PAGE Obj. by static setup.typoscript.



# Limitations and pitfalls
1. Pages which are set to "show content from another page" must also be included in the "Pages"-Selector inside the warning record. 
