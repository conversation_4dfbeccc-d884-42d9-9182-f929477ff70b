<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<xliff version="1.0">
	<file source-language="de" datatype="plaintext" original="messages" date="2020-04-29T17:32:26Z" product-name="vnc_warnings">
		<header/>
		<body>
			<trans-unit id="tx_vncwarnings_domain_model_warning">
				<source>Warning</source>
			</trans-unit>
			<trans-unit id="tx_vncwarnings_domain_model_warning.header">
				<source>Header</source>
			</trans-unit>
			<trans-unit id="tx_vncwarnings_domain_model_warning.icon">
				<source>Icon</source>
			</trans-unit>
			<trans-unit id="tx_vncwarnings_domain_model_warning.text">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="tx_vncwarnings_domain_model_warning.link">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="tx_vncwarnings_domain_model_warning.link_text">
				<source>Link Text</source>
			</trans-unit>
            <trans-unit id="tx_vncwarnings_domain_model_warning.hidden">
                <source>Active</source>
            </trans-unit>
            <trans-unit id="tx_vncwarnings_domain_model_warning.pages">
                <source>Pages</source>
            </trans-unit>
			<trans-unit id="tx_vnc_warnings_pi1.name">
				<source>vncwarnings</source>
			</trans-unit>
			<trans-unit id="tx_vnc_warnings_pi1.description">
				<source>Warnmeldungen</source>
			</trans-unit>
			<trans-unit id="tx_vnc_warnings_pi2.name">
				<source>Modal Warnmeldungen</source>
			</trans-unit>
			<trans-unit id="tx_vnc_warnings_pi2.description">
				<source>Warnmeldungen die als Modal ausgegeben werden</source>
			</trans-unit>
		</body>
	</file>
</xliff>