{namespace vnc=<PERSON>cado\VncWarnings\ViewHelpers}
<html
  xmlns="http://www.w3.org/1999/xhtml"
  lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
  <f:layout name="Default" />

  <f:section name="content">

    <f:if condition="{warnings}">
      <f:for each="{warnings}" as="warning" iteration="warningIterator">
        <f:if condition="{warning.uid}">
          <f:then>
            <div
              class="alert is-{warning.background}"
              id="warning{warning.uid}"
            >
              <div class="alert__icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="34"
                  height="34"
                  viewBox="0 0 34 34">
                  <path
                    fill="#111"
                    d="M17 0C7.611 0 0 7.611 0 17s7.611 17 17 17 17-7.611 17-17C33.99 7.616 26.384.01 17 0zm1.485 6.233l-.742 15.222h-1.485l-.743-15.222h2.97zM17 27.767c-.939 0-1.7-.761-1.7-1.7 0-.94.761-1.7 1.7-1.7s1.7.76 1.7 1.7c0 .939-.761 1.7-1.7 1.7z"
                  />
                </svg>
              </div>
              <span class="alert__title">{warning.header}</span>
              <div class="alert__text">
                <f:format.html>{warning.text}</f:format.html>
              </div>
              <f:link.typolink
                class="alert__link alert__link--more"
                parameter="{warning.link}"
                >Mehr dazu</f:link.typolink
              >
              <a href="#" class="alert__link js-alert-close">Schließen</a>
            </div>
          </f:then>
          <f:else>
            current page is not in pages listed by item {warning.uid} -
            {warningIterator.cycle}
          </f:else>
        </f:if>
      </f:for>
    </f:if>
  </f:section>
</html>
