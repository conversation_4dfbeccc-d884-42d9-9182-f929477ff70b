{namespace vnc=Vancado\VncWarnings\ViewHelpers}
<html
        xmlns="http://www.w3.org/1999/xhtml"
        lang="en"
        xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
        xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="Default"/>

<f:section name="content">
    <f:if condition="{warnings}">
        <f:for each="{warnings}" as="warning" iteration="warningIterator">
            <f:if condition="{warningIterator.isFirst}">
                <f:then>
                    <div class="vnc-warning" id="warning-{warning.uid}">
                        <div class="modal modal-warning fade warning-{warning.uid}" id="warningModal" tabindex="-1"
                             role="dialog" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <div class="alert__icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 27.963 27.963"
                                                 style="enable-background:new 0 0 27.963 27.963" xml:space="preserve">
                                                <path d="M13.983 0C6.261 0 .001 6.259.001 13.979c0 7.724 6.26 13.984 13.982 13.984s13.98-6.261 13.98-13.984C27.963 6.259 21.705 0 13.983 0zm0 26.531c-6.933 0-12.55-5.62-12.55-12.553 0-6.93 5.617-12.548 12.55-12.548 6.931 0 12.549 5.618 12.549 12.548-.001 6.933-5.619 12.553-12.549 12.553z"/>
                                                <path d="m15.579 17.158.612-12.579h-4.387l.61 12.579zM13.998 18.546c-1.471 0-2.5 1.029-2.5 2.526 0 1.443.999 2.528 2.444 2.528h.056c1.499 0 2.469-1.085 2.469-2.528-.026-1.497-.999-2.526-2.469-2.526z"/>
                                            </svg>
                                        </div>
                                        <f:if condition="{warning.header}">
                                            <div class="alert__title">
                                                <div>{warning.header}</div>
                                            </div>
                                        </f:if>
                                        <button type="button" class="close button js-warning-modal-close"
                                                data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>

                                    </div>
                                    <div class="modal-body">
                                        <f:if condition="{warning.header}">
                                            <!-- <span class="alert__title">{warning.header}</span> -->
                                        </f:if>
                                        <f:if condition="{warning.text}">
                                            <div class="alert__text">
                                                <f:format.html>{warning.text}</f:format.html>
                                            </div>
                                        </f:if>
                                        <f:comment>triggers chatbot if author uses the #chatbot hashtag inside link field</f:comment>
                                        <f:if condition="{warning.link} == '#chatbot'">
                                                <f:then>
                                                    <a href="#" class="btn btn-primary cta-link" id="triggerChat">{f:if(condition:
                                                        '{warning.linkText}', then: '{warning.linkText}', else: 'Chatfenster öffnen
                                                        ')}</a>
                                                </f:then>
                                                <f:else>
                                                    <f:link.typolink class="btn btn-primary cta-link"
                                                                     parameter="{warning.link}">{f:if(condition:
                                                        '{warning.linkText}', then: '{warning.linkText}', else: 'mehr
                                                        erfahren')}
                                                    </f:link.typolink>
                                                </f:else>
                                            </f:if>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </f:then>
                <f:else>
                    <!-- more warnings -->
                </f:else>
            </f:if>

        </f:for>
    </f:if>

</f:section>
</html>
