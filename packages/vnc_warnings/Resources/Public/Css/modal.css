.modal * {
    text-shadow:none !important
}
.modal-dialog {
    max-width:700px
}
.modal-content {
    background-color:#f0f0f0;
    border:none;
    border-radius:4px
}
.modal-header {
    width:100%;
    display:flex;
    align-items:center;
    justify-content:center;
    background-color:#fff;
    border-radius:4px 4px 0 0;
    padding: 1rem 1rem;
    border-bottom: 1px solid #ff8d2f;
}
.modal-header .alert__icon {
    display:inline-flex;
    font-size:2rem;
    line-height:1;
    align-items:center;
    justify-content:center;
    width:3rem;
    height:3rem;
    color:#ff8d2f
}
.modal-header .alert__title {
    width:calc(100% - 6rem);
    display:inline-flex;
    align-items:center;
    justify-content:flex-start;
    font-size: 1.5rem;
    margin: auto;
    font-family: "BlissPro-Light";
    line-height: 1.2;
    color: #140043;
    font-weight: normal;
}
.modal-header .close {
    color:#ff8d2f;
    border:2px solid #ff8d2f;
    border-radius:100%;
    background-color:#fff;
    padding:0;
    font-size:2rem;
    line-height:1;
    display:inline-flex;
    align-items:center;
    justify-content:center;
    width:3rem;
    height:3rem;
    margin:0;
    opacity:1
}

.close span {
    font-size: 1.5rem;
    display: inline-block;
    margin: 0 0 3px 0;
}


.modal-header .close:hover {
    opacity:1;
    color:#fff;
    border:2px solid #ff8d2f;
    background-color:#ff8d2f
}
.modal-body {
    border-radius:0 0 4px 4px;
    padding:3rem 4.5rem
}
@media (max-width: 767px) {
    .modal-body {
        padding:2rem
    }
}



.vnc-warning {
    padding: 0 !important;
    position: absolute;
    left: 50%;
    top: 0;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    z-index: 10000;
}

.modal-dialog {
    padding: 28px;
    margin: 0
}

@media (max-width: 768px) {
    .modal-warning .modal-dialog {
        max-width: 690px;
    }
}

@media (max-width: 480px) {
    .modal-warning .modal-dialog {
        max-width: 400px;
        width: 400px;
    }
}

.modal-dialog > .form {
    height: 100%
}

.modal-content {
    background: rgba(250, 250, 250, .95);
    height: 100%
}


.modal-header .close:focus {
    outline: none
}


.modal .close {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    margin: 0rem 0rem -1rem auto;
    position: inherit
}


.modal-warning .modal-dialog {
    margin: 1.75rem auto
}

@media (min-width: 768px) {
    .modal-warning .modal-dialog {
        max-width: 690px
    }
}

.modal-warning .modal-content {
    height: auto;
    background-color: #f4f3f2;
    border-radius: 0
}

.modal-warning .modal-content.is-dark {

}

.modal-warning .modal-content .alert__icon {

}

.modal-warning .modal-content .close {
    color: #ff8d2f;
    border: 0px solid #ff8d2f;
    border-radius: 100%;
    background-color: #fff;
    padding: 0;
    font-size: 2rem;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    margin: 0;
    opacity: 1;
}

.modal-warning .modal-body {
    text-align: left;
    padding-top: 0;
    padding-bottom: 2rem
}

.modal-warning .modal-body .alert__text {
    margin: 1.25rem auto
}

.modal-warning .modal-body .alert__title {
    color: #298cba;
    line-height: 1.25
}


.alert__icon > svg path {
    fill: #ff8d2f;
}

.alert__icon svg {
    height: 2.2rem;
    margin-right:15px;
}

.modal-backdrop.show {
    opacity: .5;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: .5;
}
