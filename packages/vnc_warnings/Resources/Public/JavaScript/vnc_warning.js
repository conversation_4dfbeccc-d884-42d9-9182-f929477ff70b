"use strict";

$(document).ready(function () {


    let $body = $("body"),
        alert = ".vnc-warning",
        cookieName = "vnc_warnings_dismissed",
        openClass = "is-open",
        backDropClass = 'modal-backdrop',
        backDropClassShow = 'show',
        backDropClassHide = 'fade',
        cookie = getCookie();

    init();


    function getCookie() {
        var name = cookieName + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(";");
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == " ") {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }

    function writeCookie(idArr) {
        document.cookie = cookieName + "=" + JSON.stringify(idArr) + "; samesite=lax;path=/";

    }

    function existId(alertId) {
        if (cookie) {
            console.log(alertId);
            return JSON.parse(cookie).includes(alertId);
        }
        return false;
    }

    function addId(alertId) {
        var arr = [];
        if (cookie) {
            arr = JSON.parse(cookie);
        }
        if (!arr.includes(alertId)) {
            arr.push(alertId);
            writeCookie(arr);
        }
    }

    function init() {
        // console.log('start process');
        if ($(alert).length) {
            console.log('one or more warnings element found');
            $(alert).each(function (i) {
                const alertId = $(alert).attr("id");
                console.log('working on: ' + alertId + ' found');
                if (!existId(alertId)) {
                    $('.' + backDropClass).show();
                    $('#' + alertId).show();
                } else {
                    $('.' + backDropClass).hide();
                    $('#' + alertId).hide();
                }
            });
        } else {
            return;
            //   console.log('no warnings element detected in page');
        }
    }

    $('.close').on("click", function (e) {
        var $this = $(this),
            $alert = $this.closest(alert),
            alertId = $alert.attr("id");
        e.preventDefault();
        $('#' + alertId).removeClass(openClass).fadeOut();
        $('.' + backDropClass).hide();
        addId(alertId);

    });

    $('.btn.btn-modal').on("click", function (e) {
        const alertId = $(this).attr('data-warning-uid');
        addId(alertId);

    });

    // rhenag special feature to trigger chatbot window.
    // see link rendering in fluid template:
    // www/typo3conf/ext/rhenag/Resources/Private/Extensions/vnc_warnings/Templates/Warning/ShowModal.html
    $('#triggerChat').on('click', function (e) {
        e.preventDefault();
        $('.eva_bubble_Chat').trigger('click');
        $('.close').trigger('click');
    })

});
