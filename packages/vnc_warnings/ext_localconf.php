<?php
defined('TYPO3_MODE') || die('Access denied.');

call_user_func(
    function()
    {

        \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
            'Vancado.VncWarnings',
            'Pi1',
            [
                'Warning' => 'list'
            ],
            // non-cacheable actions
            [
                'Warning' => 'list'
            ]
        );

        \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
            'Vancado.VncWarnings',
            'Pi2',
            [
                'Warning' => 'showModal'
            ],
            // non-cacheable actions
            [
                'Warning' => 'showModal'
            ]
        );
    // wizards
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPageTSConfig(
        'mod {
            wizards.newContentElement.wizardItems.plugins {
                elements {
                    pi1 {
                        iconIdentifier = vnc_warnings-plugin-pi1
                        title = LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vnc_warnings_pi1.name
                        description = LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vnc_warnings_pi1.description
                        tt_content_defValues {
                            CType = list
                            list_type = vncwarnings_pi1
                        }
                    }
                    pi2 {
                        iconIdentifier = vnc_warnings-plugin-pi2
                        title = LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vnc_warnings_pi2.name
                        description = LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vnc_warnings_pi2.description
                        tt_content_defValues {
                            CType = list
                            list_type = vncwarnings_pi2
                        }
                    }
                }
                show = *
            }
       }'
    );
		$iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
		
        $iconRegistry->registerIcon(
            'vnc_warnings-plugin-pi1',
            \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
            ['source' => 'EXT:vnc_warnings/Resources/Public/Icons/user_plugin_pi1.svg']
        );

        $iconRegistry->registerIcon(
            'vnc_warnings-plugin-pi2',
            \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
            ['source' => 'EXT:vnc_warnings/Resources/Public/Icons/user_plugin_pi2.svg']
        );
		
    }
);
	$GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['VncWarnings'] = 'EXT:vnc_warnings/Configuration/RTE/VncWarnings.yaml';


