<?php
defined('TYPO3_MODE') || die('Access denied.');

call_user_func(
    function()
    {

        \TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
            'Vancado.VncWarnings',
            'Pi1',
            'vncwarnings'
        );

        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addStaticFile('vnc_warnings', 'Configuration/TypoScript', 'Vancado Warnings');

        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr('tx_vncwarnings_domain_model_warning', 'EXT:vnc_warnings/Resources/Private/Language/locallang_csh_tx_vncwarnings_domain_model_warning.xlf');
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_vncwarnings_domain_model_warning');

    }
);
