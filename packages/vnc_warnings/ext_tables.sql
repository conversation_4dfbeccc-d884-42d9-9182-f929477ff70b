#
# Table structure for table 'tx_vncwarnings_domain_model_warning'
#
CREATE TABLE tx_vncwarnings_domain_model_warning
(

    uid              int(11)          NOT NULL auto_increment,
    pid              int(11)          DEFAULT '0' NOT NULL,
    tstamp           int(11)          DEFAULT '0' NOT NULL,
    crdate           int(11)          DEFAULT '0' NOT NULL,
    cruser_id        int(11)          DEFAULT '0' NOT NULL,
    deleted          tinyint(3)       DEFAULT '0',
    hidden           tinyint(4)       DEFAULT '0' NOT NULL,
    sorting          int(11)          DEFAULT '0' NOT NULL,
    starttime int(11) unsigned DEFAULT '0' NOT NULL,
    endtime int(11) unsigned DEFAULT '0' NOT NULL,

    header           varchar(255)     DEFAULT ''  NOT NULL,
    link_text        varchar(255)     DEFAULT ''  NOT NULL,
    text             text,
    link             varchar(255)     DEFAULT ''  NOT NULL,
    categories       int(11) unsigned DEFAULT '0' NOT NULL,
    pages            varchar(255)     DEFAULT ''  NOT NULL,

    sys_language_uid int(11)          DEFAULT '0' NOT NULL,
    l10n_parent      int(11)          DEFAULT '0' NOT NULL,
    l10n_diffsource  mediumblob,



    t3ver_oid        int(11)          DEFAULT '0' NOT NULL,
    t3ver_id         int(11)          DEFAULT '0' NOT NULL,
    t3ver_wsid       int(11)          DEFAULT '0' NOT NULL,
    t3ver_label      varchar(30)      DEFAULT ''  NOT NULL,
    t3ver_state      tinyint(4)       DEFAULT '0' NOT NULL,
    t3ver_stage      tinyint(4)       DEFAULT '0' NOT NULL,
    t3ver_count      int(11)          DEFAULT '0' NOT NULL,
    t3ver_tstamp     int(11)          DEFAULT '0' NOT NULL,
    t3ver_move_id    int(11)          DEFAULT '0' NOT NULL,
    t3_origuid       int(11)          DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid)
);